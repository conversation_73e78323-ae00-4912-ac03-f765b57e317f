def my_menu():
    print("*" *50)
    print ("1: Add new phone : ")
    print ("2: search phone : ")
    print ("3: Delete phone : ")
    print ("4: all list")
    print ("0: Exit")
    print("*" *50)  

my_menu()

f= open("faradars.txt","w+")
global men
men=int(input("Enter the number menu:  "))
idi=0
dik=[]

def addphon():
    addd=input("Are you add? y or n: ")
    if addd== "y":
        global idi
        idi+=1
        name=input("Enter the name:  ")
        family=input("Enter the family: ")
        phone=input("Enter the phone:   ")
        dik.append([idi,name,family,phone])
        print(dik)
        addd=input("Are you add? y or n: ")
    else:
        my_menu()
        
  
      
      
      

while men !=0 :
    if men==1:
       addphon()       
    elif men==2 :
        print("3")
    elif men==2 :
        print("3")
    elif men==4 :
     for i,n,f,p in dik.items():
     	print(i,n,f,p,end=" ")
        #print("4")
else:
    exit
