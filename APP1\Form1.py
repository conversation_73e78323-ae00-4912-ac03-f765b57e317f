# بسم الله الرحمن الرحیم
import tkinter as tk
from tkinter import ttk, messagebox
import sqlite3
import hashlib
from jdatetime import datetime as jd
from jdatetime import date as jdate

# ------------------- Database Setup -------------------
def create_connection():
    conn = None
    try:
        conn = sqlite3.connect('pharmacydrag.db')
        return conn
    except sqlite3.Error as e:
        print(e)
    return conn

def create_tables(conn):
    try:
        c = conn.cursor()
        
        # Create users table
        c.execute('''CREATE TABLE IF NOT EXISTS users
                     (id INTEGER PRIMARY KEY AUTOINCREMENT,
                      username TEXT UNIQUE NOT NULL,
                      password TEXT NOT NULL,
                      role TEXT NOT NULL)''')
        
        # Create plants table with triggers
        c.execute('''CREATE TABLE IF NOT EXISTS plants
                     (id INTEGER PRIMARY KEY AUTOINCREMENT,
                      code TEXT UNIQUE NOT NULL,
                      name TEXT NOT NULL,
                      price REAL NOT NULL,
                      shelf TEXT NOT NULL,
                      stock INTEGER NOT NULL,
                      weight REAL NOT NULL,
                      total_price REAL,
                      date TEXT NOT NULL,
                      description TEXT,
                      min_stock INTEGER DEFAULT 0)''')
        
        # Trigger for auto-updating total_price
        c.execute('''CREATE TRIGGER IF NOT EXISTS update_total_price 
                     AFTER UPDATE OF price, weight ON plants
                     BEGIN
                         UPDATE plants SET total_price = NEW.price * NEW.weight WHERE id = NEW.id;
                     END''')
        
        conn.commit()
    except sqlite3.Error as e:
        print(e)

# ------------------- Login Form -------------------
class LoginApp:
    def __init__(self, master):
        self.master = master
        self.master.geometry("300x200")
        self.master.title("ورود به سیستم")
        
        self.create_widgets()
    
    def create_widgets(self):
        tk.Label(self.master, text="نام کاربری:").pack(pady=5)
        self.username_entry = tk.Entry(self.master)
        self.username_entry.pack(pady=5)
        
        tk.Label(self.master, text="رمز عبور:").pack(pady=5)
        self.password_entry = tk.Entry(self.master, show="*")
        self.password_entry.pack(pady=5)
        
        tk.Button(self.master, text="ورود", command=self.login).pack(pady=10)
    
    def login(self):
        username = self.username_entry.get()
        password = hashlib.sha256(self.password_entry.get().encode()).hexdigest()
        
        conn = create_connection()
        cur = conn.cursor()
        cur.execute("SELECT * FROM users WHERE username=? AND password=?", (username, password))
        user = cur.fetchone()
        conn.close()
        
        if user:
            self.master.destroy()
            root = tk.Tk()
            app = MainApp(root, user[3])
            root.mainloop()
        else:
            messagebox.showerror("خطا", "اطلاعات ورود نامعتبر است")

# ------------------- Main Application -------------------
class MainApp:
    def __init__(self, master, role):
        self.master = master
        self.master.geometry("800x600")
        self.master.title("سیستم مدیریت - نقش: " + role)
        self.role = role
        
        self.create_widgets()
    
    def create_widgets(self):
        btn_frame = tk.Frame(self.master)
        btn_frame.pack(pady=20)
        
        if self.role == "admin":
            tk.Button(btn_frame, text="ورود مفردات گیاهی", 
                     command=self.open_plant_form).grid(row=0, column=0, padx=10)
            tk.Button(btn_frame, text="بانک مفردات", 
                     command=self.open_plant_bank).grid(row=0, column=1, padx=10)
            tk.Button(btn_frame, text="خروج", 
                 command=self.master.quit).grid(row=0, column=2, padx=10)
    
    def open_plant_form(self):
        PlantForm(tk.Toplevel(self.master))
    
    def open_plant_bank(self):
        PlantBank(tk.Toplevel(self.master))

# ------------------- Plant Entry Form -------------------
class PlantForm:
    def __init__(self, master):
        self.master = master
        self.master.geometry("500x600")
        self.create_widgets()
    
    def create_widgets(self):
        fields = [
            ("کد گیاه:", "code"),
            ("نام گیاه:", "name"),
            ("قیمت گیاه (ریال):", "price"),
            ("محل قفسه:", "shelf"),
            ("موجودی انبار:", "stock"),
            ("وزن گیاه (گرم):", "weight"),
            ("تاریخ خرید (شمسی):", "date"),
            ("حداقل موجودی هشدار:", "min_stock"),
            ("توضیحات:", "description")
        ]
        
        self.entries = {}
        for i, (label, field) in enumerate(fields):
            tk.Label(self.master, text=label).grid(row=i, column=0, padx=10, pady=5, sticky="e")
            entry = tk.Entry(self.master)
            entry.grid(row=i, column=1, padx=10, pady=5)
            self.entries[field] = entry
            
            # Bind validation
            if field in ["price", "stock", "weight", "min_stock"]:
                entry.config(validate="key", validatecommand=(self.master.register(self.validate_number), '%P'))
        
        # Total Price
        tk.Label(self.master, text="قیمت کل:").grid(row=len(fields), column=0, sticky="e")
        self.total_price_entry = tk.Entry(self.master, state='readonly')
        self.total_price_entry.grid(row=len(fields), column=1, padx=10, pady=5)
        
        # Bind auto-calculation
        self.entries["price"].bind("<KeyRelease>", self.calculate_total)
        self.entries["weight"].bind("<KeyRelease>", self.calculate_total)
        
        # Submit Button
        tk.Button(self.master, text="ثبت اطلاعات", 
                 command=self.submit).grid(row=len(fields)+1, columnspan=2, pady=20)
    
    def validate_number(self, value):
        if value.replace(".", "", 1).isdigit():
            return True
        return False
    
    def calculate_total(self, event):
        try:
            price = float(self.entries["price"].get())
            weight = float(self.entries["weight"].get())
            total = price * weight
            self.total_price_entry.config(state='normal')
            self.total_price_entry.delete(0, tk.END)
            self.total_price_entry.insert(0, f"{total:.2f}")
            self.total_price_entry.config(state='readonly')
        except:
            pass
    
    def submit(self):
        try:
            # Validate date
            jdate = jd.strptime(self.entries["date"].get(), "%Y/%m/%d")
            date_str = jdate.strftime("%Y/%m/%d")
            
            conn = create_connection()
            cur = conn.cursor()
            
            cur.execute('''INSERT INTO plants 
                         (code, name, price, shelf, stock, weight, date, description, min_stock)
                         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)''',
                         (
                             self.entries["code"].get(),
                             self.entries["name"].get(),
                             float(self.entries["price"].get()),
                             self.entries["shelf"].get(),
                             int(self.entries["stock"].get()),
                             float(self.entries["weight"].get()),
                             date_str,
                             self.entries["description"].get(),
                             int(self.entries["min_stock"].get() or 0)
                         ))
            conn.commit()
            conn.close()
            messagebox.showinfo("موفق", "اطلاعات با موفقیت ذخیره شد")
            self.master.destroy()
            
        except ValueError as e:
            messagebox.showerror("خطا", f"خطا در داده‌های ورودی: {str(e)}")
        except sqlite3.IntegrityError:
            messagebox.showerror("خطا", "کد گیاه تکراری است")

# ------------------- Plant Database Browser -------------------
class PlantBank:
    def __init__(self, master):
        self.master = master
        self.master.geometry("1000x600")
        self.create_widgets()
        self.load_data()
    
    def create_widgets(self):
        # Search Frame
        search_frame = tk.Frame(self.master)
        search_frame.pack(pady=10)
        
        tk.Label(search_frame, text="جستجو:").pack(side=tk.LEFT, padx=5)
        self.search_var = tk.StringVar()
        self.search_entry = tk.Entry(search_frame, textvariable=self.search_var, width=30)
        self.search_entry.pack(side=tk.LEFT, padx=5)
        self.search_entry.bind("<KeyRelease>", self.search)
        
        # Treeview
        self.tree = ttk.Treeview(self.master, columns=("code", "name", "price", "stock", "weight", "total_price", "shelf"))
        self.tree.heading("#0", text="ID")
        self.tree.heading("code", text="کد")
        self.tree.heading("name", text="نام")
        self.tree.heading("price", text="قیمت (ریال)")
        self.tree.heading("stock", text="موجودی")
        self.tree.heading("weight", text="وزن (گرم)")
        self.tree.heading("total_price", text="قیمت کل")
        self.tree.heading("shelf", text="محل قفسه")
        
        self.tree.column("#0", width=50, anchor='center')
        self.tree.column("code", width=100, anchor='center')
        self.tree.column("name", width=150, anchor='center')
        self.tree.column("price", width=100, anchor='center')
        self.tree.column("stock", width=80, anchor='center')
        self.tree.column("weight", width=100, anchor='center')
        self.tree.column("total_price", width=120, anchor='center')
        self.tree.column("shelf", width=100, anchor='center')
        
        self.tree.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Buttons
        btn_frame = tk.Frame(self.master)
        btn_frame.pack(pady=10)
        
        tk.Button(btn_frame, text="ویرایش", command=self.edit_plant).pack(side=tk.LEFT, padx=5)
        tk.Button(btn_frame, text="حذف", command=self.delete_plant).pack(side=tk.LEFT, padx=5)
        tk.Button(btn_frame, text="بروزرسانی", command=self.load_data).pack(side=tk.LEFT, padx=5)
    
    def load_data(self):
        for item in self.tree.get_children():
            self.tree.delete(item)
            
        conn = create_connection()
        cur = conn.cursor()
        cur.execute("SELECT * FROM plants")
        rows = cur.fetchall()
        
        for row in rows:
            self.tree.insert("", tk.END, text=row[0], values=(
                row[1],  # code
                row[2],  # name
                f"{row[3]:,}",  # price
                row[4],  # stock
                f"{row[6]:.2f}",  # weight
                f"{row[7]:,.2f}",  # total_price
                row[3]  # shelf
            ))
        
        conn.close()
    
    def search(self, event):
        query = self.search_var.get()
        conn = create_connection()
        cur = conn.cursor()
        cur.execute("SELECT * FROM plants WHERE code LIKE ? OR name LIKE ?", 
                   (f"%{query}%", f"%{query}%"))
        rows = cur.fetchall()
        
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        for row in rows:
            self.tree.insert("", tk.END, text=row[0], values=(
                row[1], row[2], f"{row[3]:,}", row[4],
                f"{row[6]:.2f}", f"{row[7]:,.2f}", row[3]
            ))
        
        conn.close()
    
    def edit_plant(self):
        selected = self.tree.selection()
        if selected:
            item = self.tree.item(selected[0])
            plant_id = item['text']
            EditForm(tk.Toplevel(self.master), plant_id)
    
    def delete_plant(self):
        selected = self.tree.selection()
        if selected:
            item = self.tree.item(selected[0])
            plant_id = item['text']
            
            if messagebox.askyesno("تأیید حذف", "آیا مطمئن هستید؟"):
                conn = create_connection()
                cur = conn.cursor()
                cur.execute("DELETE FROM plants WHERE id=?", (plant_id,))
                conn.commit()
                conn.close()
                self.load_data()

# ------------------- Edit Form -------------------
class EditForm(PlantForm):
    def __init__(self, master, plant_id):
        super().__init__(master)
        self.plant_id = plant_id
        self.load_data()
    
    def load_data(self):
        conn = create_connection()
        cur = conn.cursor()
        cur.execute("SELECT * FROM plants WHERE id=?", (self.plant_id,))
        plant = cur.fetchone()
        conn.close()
        
        if plant:
            fields = [
                "code", "name", "price", "shelf", "stock",
                "weight", "date", "min_stock", "description"
            ]
            
            for i, field in enumerate(fields):
                self.entries[field].insert(0, plant[i+1])
    
    def submit(self):
        try:
            # ... existing validation ...
            conn = create_connection()
            cur = conn.cursor()
            
            cur.execute('''UPDATE plants SET
                         code=?, name=?, price=?, shelf=?, stock=?,
                         weight=?, date=?, description=?, min_stock=?
                         WHERE id=?''',
                         (
                             self.entries["code"].get(),
                             self.entries["name"].get(),
                             float(self.entries["price"].get()),
                             self.entries["shelf"].get(),
                             int(self.entries["stock"].get()),
                             float(self.entries["weight"].get()),
                             jd.strptime(self.entries["date"].get(), "%Y/%m/%d").strftime("%Y/%m/%d"),
                             self.entries["description"].get(),
                             int(self.entries["min_stock"].get() or 0),
                             self.plant_id
                         ))
            
            conn.commit()
            conn.close()
            messagebox.showinfo("موفق", "اطلاعات با موفقیت بروزرسانی شد")
            self.master.destroy()
            
        except Exception as e:
            messagebox.showerror("خطا", str(e))

# ------------------- Run Application -------------------
if __name__ == "__main__":
   
    # Initialize database
    conn = create_connection()
    if conn is not None:
        create_tables(conn)
        # Create default admin user if not exists
        cur = conn.cursor()
        cur.execute("INSERT OR IGNORE INTO users (username, password, role) VALUES (?, ?, ?)",
                   ("admin", hashlib.sha256("admin123".encode()).hexdigest(), "admin"))
        conn.commit()
        conn.close()
    
    root = tk.Tk()
    login_app = LoginApp(root)
    root.mainloop()