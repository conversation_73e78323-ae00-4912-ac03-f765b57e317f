import tkinter as tk
from tkinter import ttk, messagebox
import sqlite3
from datetime import datetime

# -------------------- Database Setup --------------------
conn = sqlite3.connect('herbal_factory.db')
cursor = conn.cursor()

# ایجاد جداول
cursor.execute('''
CREATE TABLE IF NOT EXISTS Herbs (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    unit_price REAL NOT NULL,
    shelf_location TEXT,
    current_weight REAL NOT NULL,
    purchase_date TEXT,
    description TEXT
)
''')

cursor.execute('''
CREATE TABLE IF NOT EXISTS Drugs (
    id INTEGER PRIMARY KEY,
    name TEXT NOT NULL,
    production_date TEXT
)
''')

cursor.execute('''
CREATE TABLE IF NOT EXISTS DrugCompositions (
    drug_id INTEGER,
    herb_id TEXT,
    weight_used REAL,
    subtotal REAL,
    FOREIGN KEY(drug_id) REFERENCES Drugs(id),
    FOREIGN KEY(herb_id) REFERENCES Herbs(id)
)
''')

cursor.execute('''
CREATE TABLE IF NOT EXISTS Users (
    username TEXT PRIMARY KEY,
    password TEXT NOT NULL
)
''')

conn.commit()

# -------------------- GUI Application --------------------
class HerbalFactoryApp:
    def __init__(self, root):
        self.root = root
        self.root.title("کارگاه تولید داروهای گیاهی")
        self.root.geometry("1000x600")
        
        self.selected_herb_id = None # Variable to store selected herb ID for edit/delete
        
        # ایجاد تب‌ها
        self.notebook = ttk.Notebook(root)
        self.herb_tab = ttk.Frame(self.notebook)
        self.drug_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.herb_tab, text="مدیریت مفردات گیاهی")
        self.notebook.add(self.drug_tab, text="تولید دارو")
        self.notebook.pack(expand=True, fill="both")
        
        # -------------------- تب گیاهان --------------------
        # فرم ثبت گیاه
        self.herb_frame = ttk.LabelFrame(self.herb_tab, text="ثبت گیاه جدید")
        self.herb_frame.pack(pady=10, padx=10, fill="x")
        
        labels = ["کد گیاه:", "نام گیاه:", "قیمت واحد (تومان):", "محل قفسه:", "وزن (گرم):", "تاریخ خرید:", "توضیحات:"]
        self.herb_entries = {}
        for i, label in enumerate(labels):
            ttk.Label(self.herb_frame, text=label).grid(row=i, column=0, padx=5, pady=5)
            entry = ttk.Entry(self.herb_frame)
            entry.grid(row=i, column=1, padx=5, pady=5)
            self.herb_entries[label.split(":")[0]] = entry
        
        # دکمه‌های مدیریت گیاهان
        # دکمه‌های مدیریت گیاهان (در یک فریم جدا)
        self.herb_button_frame = ttk.Frame(self.herb_frame)
        self.herb_button_frame.grid(row=7, column=0, columnspan=2, pady=10)

        self.add_herb_btn = ttk.Button(self.herb_button_frame, text="ذخیره گیاه", command=self.add_herb)
        self.add_herb_btn.grid(row=0, column=0, padx=5)
        
        self.edit_herb_btn = ttk.Button(self.herb_button_frame, text="ویرایش گیاه", command=self.edit_herb)
        self.edit_herb_btn.grid(row=0, column=1, padx=5)
        
        self.delete_herb_btn = ttk.Button(self.herb_button_frame, text="حذف گیاه", command=self.delete_herb)
        self.delete_herb_btn.grid(row=0, column=2, padx=5)

        self.clear_herb_form_btn = ttk.Button(self.herb_button_frame, text="پاک کردن فرم", command=self.clear_herb_form)
        self.clear_herb_form_btn.grid(row=0, column=3, padx=5)

        # جدول نمایش گیاهان
        self.herb_tree = ttk.Treeview(self.herb_tab, columns=("id", "name", "unit_price", "shelf", "weight", "total_price", "date", "description"), show="headings") # Add "description" column
        self.herb_tree.heading("id", text="کد")
        self.herb_tree.heading("name", text="نام")
        self.herb_tree.heading("unit_price", text="قیمت واحد")
        self.herb_tree.heading("shelf", text="محل قفسه")
        self.herb_tree.heading("weight", text="وزن (گرم)")
        self.herb_tree.heading("total_price", text="قیمت محصول")
        self.herb_tree.heading("date", text="تاریخ خرید")
        self.herb_tree.heading("description", text="توضیحات") # Add heading for description
        
        # تنظیم عرض ستون‌ها
        self.herb_tree.column("id", width=80, anchor=tk.CENTER)
        self.herb_tree.column("name", width=150)
        self.herb_tree.column("unit_price", width=100, anchor=tk.E) # Align East (right)
        self.herb_tree.column("shelf", width=100)
        self.herb_tree.column("weight", width=100, anchor=tk.E) # Align East (right)
        self.herb_tree.column("total_price", width=120, anchor=tk.E) # Align East (right)
        self.herb_tree.column("date", width=100, anchor=tk.CENTER)
        self.herb_tree.column("description", width=250) # Wider column for description

        self.herb_tree.pack(fill="both", expand=True, padx=10, pady=(0, 10)) # Add padding
        self.herb_tree.bind("<<TreeviewSelect>>", self.on_herb_select) # Bind selection event
        self.load_herbs()
        
        # -------------------- تب تولید دارو --------------------
        # فرم تولید دارو
        self.drug_frame = ttk.LabelFrame(self.drug_tab, text="تولید دارو جدید")
        self.drug_frame.pack(pady=10, padx=10, fill="x")
        
        ttk.Label(self.drug_frame, text="کد دارو:").grid(row=0, column=0)
        self.drug_id_entry = ttk.Entry(self.drug_frame)
        self.drug_id_entry.grid(row=0, column=1)
        
        ttk.Label(self.drug_frame, text="نام دارو:").grid(row=1, column=0)
        self.drug_name_entry = ttk.Entry(self.drug_frame)
        self.drug_name_entry.grid(row=1, column=1)
        
        # دکمه افزودن ترکیبات
        self.add_composition_btn = ttk.Button(self.drug_frame, text="+ افزودن ترکیب", command=self.add_composition)
        self.add_composition_btn.grid(row=2, column=0, columnspan=2, pady=5)
        
        # لیست ترکیبات
        self.compositions = []
        
        # دکمه تولید دارو
        self.produce_drug_btn = ttk.Button(self.drug_frame, text="تولید دارو", command=self.produce_drug)
        self.produce_drug_btn.grid(row=3, column=0, columnspan=2, pady=10)
        
    # -------------------- توابع گیاهان --------------------
    def add_herb(self):
        try:
            data = {
                "id": self.herb_entries["کد گیاه"].get(), # Remove int() conversion
                "name": self.herb_entries["نام گیاه"].get(),
                "unit_price": float(self.herb_entries["قیمت واحد (تومان)"].get()),
                "shelf_location": self.herb_entries["محل قفسه"].get(),
                "current_weight": float(self.herb_entries["وزن (گرم)"].get()),
                "purchase_date": self.herb_entries["تاریخ خرید"].get(),
                "description": self.herb_entries["توضیحات"].get()
            }
            
            cursor.execute('''
                INSERT INTO Herbs (id, name, unit_price, shelf_location, current_weight, purchase_date, description)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', tuple(data.values()))
            conn.commit()
            self.load_herbs()
            messagebox.showinfo("موفق", "گیاه با موفقیت ثبت شد!")
            self.clear_herb_form() # Clear form after adding
        except sqlite3.IntegrityError:
             messagebox.showerror("خطا", f"کد گیاه '{data['id']}' قبلا ثبت شده است.")
        except Exception as e:
            messagebox.showerror("خطا", f"خطا در ثبت گیاه: {str(e)}")

    def edit_herb(self):
        if not self.selected_herb_id:
            messagebox.showwarning("هشدار", "لطفا ابتدا یک گیاه را از جدول انتخاب کنید.")
            return

        try:
            # Get data from entries (excluding ID, as it shouldn't be changed)
            updated_data = {
                "name": self.herb_entries["نام گیاه"].get(),
                "unit_price": float(self.herb_entries["قیمت واحد (تومان)"].get()),
                "shelf_location": self.herb_entries["محل قفسه"].get(),
                "current_weight": float(self.herb_entries["وزن (گرم)"].get()),
                "purchase_date": self.herb_entries["تاریخ خرید"].get(),
                "description": self.herb_entries["توضیحات"].get()
            }

            cursor.execute('''
                UPDATE Herbs 
                SET name = ?, unit_price = ?, shelf_location = ?, current_weight = ?, purchase_date = ?, description = ?
                WHERE id = ?
            ''', (
                updated_data["name"], updated_data["unit_price"], updated_data["shelf_location"],
                updated_data["current_weight"], updated_data["purchase_date"], updated_data["description"],
                self.selected_herb_id # Use the stored selected ID
            ))
            conn.commit()
            self.load_herbs()
            messagebox.showinfo("موفق", "اطلاعات گیاه با موفقیت ویرایش شد!")
            self.clear_herb_form() # Clear form after editing
        except Exception as e:
            messagebox.showerror("خطا", f"خطا در ویرایش گیاه: {str(e)}")

    def delete_herb(self):
        if not self.selected_herb_id:
            messagebox.showwarning("هشدار", "لطفا ابتدا یک گیاه را از جدول انتخاب کنید.")
            return

        confirm = messagebox.askyesno("تایید حذف", f"آیا از حذف گیاه با کد '{self.selected_herb_id}' مطمئن هستید؟")
        if confirm:
            try:
                # Check if herb is used in any drug composition
                usage_check = cursor.execute("SELECT COUNT(*) FROM DrugCompositions WHERE herb_id = ?", (self.selected_herb_id,)).fetchone()
                if usage_check and usage_check[0] > 0:
                    messagebox.showerror("خطا", f"امکان حذف گیاه '{self.selected_herb_id}' وجود ندارد زیرا در ترکیبات دارویی استفاده شده است.")
                    return

                cursor.execute("DELETE FROM Herbs WHERE id = ?", (self.selected_herb_id,))
                conn.commit()
                self.load_herbs()
                messagebox.showinfo("موفق", "گیاه با موفقیت حذف شد!")
                self.clear_herb_form() # Clear form after deleting
            except Exception as e:
                messagebox.showerror("خطا", f"خطا در حذف گیاه: {str(e)}")

    def on_herb_select(self, event):
        try:
            selected_item = self.herb_tree.selection()[0] # Get selected item
            item_values = self.herb_tree.item(selected_item, 'values')
            
            # item_values indices correspond to the Treeview columns:
            # 0:id, 1:name, 2:unit_price(formatted), 3:shelf, 4:weight(formatted), 5:total_price(formatted), 6:date, 7:description
            
            self.selected_herb_id = item_values[0] # Store the ID

            # Fetch raw data from DB for accurate editing (especially numbers)
            raw_data = cursor.execute("SELECT unit_price, current_weight FROM Herbs WHERE id = ?", (self.selected_herb_id,)).fetchone()
            if not raw_data: return # Should not happen if item is selected

            # Load data into entries
            self.herb_entries["کد گیاه"].delete(0, tk.END)
            self.herb_entries["کد گیاه"].insert(0, item_values[0])
            self.herb_entries["کد گیاه"].config(state='readonly') # Make ID readonly

            self.herb_entries["نام گیاه"].delete(0, tk.END)
            self.herb_entries["نام گیاه"].insert(0, item_values[1])

            self.herb_entries["قیمت واحد (تومان)"].delete(0, tk.END)
            self.herb_entries["قیمت واحد (تومان)"].insert(0, raw_data[0] if raw_data[0] is not None else "") # Use raw price

            self.herb_entries["محل قفسه"].delete(0, tk.END)
            self.herb_entries["محل قفسه"].insert(0, item_values[3])

            self.herb_entries["وزن (گرم)"].delete(0, tk.END)
            self.herb_entries["وزن (گرم)"].insert(0, raw_data[1] if raw_data[1] is not None else "") # Use raw weight

            self.herb_entries["تاریخ خرید"].delete(0, tk.END)
            self.herb_entries["تاریخ خرید"].insert(0, item_values[6])

            self.herb_entries["توضیحات"].delete(0, tk.END)
            self.herb_entries["توضیحات"].insert(0, item_values[7])

        except IndexError:
            # No item selected or selection cleared
            self.clear_herb_form()

    def clear_herb_form(self):
        """Clears all entry fields in the herb form and resets selection."""
        self.selected_herb_id = None
        self.herb_entries["کد گیاه"].config(state='normal') # Make ID writable again
        for entry in self.herb_entries.values():
            entry.delete(0, tk.END)
        if self.herb_tree.selection(): # Deselect item in treeview
            self.herb_tree.selection_remove(self.herb_tree.selection()[0])


    def load_herbs(self):
        for row in self.herb_tree.get_children():
            self.herb_tree.delete(row)
        # Fetch id, name, unit_price, shelf_location, current_weight, purchase_date, description
        herbs = cursor.execute("SELECT id, name, unit_price, shelf_location, current_weight, purchase_date, description FROM Herbs").fetchall()
        for herb in herbs:
            # herb indices: 0:id, 1:name, 2:unit_price, 3:shelf, 4:weight, 5:date, 6:description
            
            # Format numbers (unit_price, weight, total_price)
            try:
                unit_price_val = float(herb[2]) if herb[2] is not None else 0.0 # Price per unit (assumed kg)
                weight_val_grams = float(herb[4]) if herb[4] is not None else 0.0 # Weight in grams
                
                # Calculate total price based on kg (unit_price * weight_in_kg)
                total_price_val = unit_price_val * (weight_val_grams / 1000.0) 

                # Format unit_price with thousands separator
                if unit_price_val.is_integer():
                    unit_price_str = f"{int(unit_price_val):,}" # Add comma separator for integer
                else:
                    unit_price_str = f"{unit_price_val:,.2f}" # Add comma separator and keep decimals

                # Format weight (still display in grams)
                if weight_val_grams.is_integer():
                    weight_str = str(int(weight_val_grams))
                else:
                    weight_str = f"{weight_val_grams:.2f}" # Keep decimals if present
                
                # Format total_price (calculated based on kg) with thousands separator
                if total_price_val.is_integer():
                    total_price_str = f"{int(total_price_val):,}" # Add comma separator for integer
                else:
                    total_price_str = f"{total_price_val:,.2f}" # Add comma separator and keep decimals

            except (ValueError, TypeError):
                unit_price_str = "N/A"
                weight_str = "N/A" # Weight is still displayed in grams
                total_price_str = "N/A" # Total price is calculated based on kg

            # Create values tuple with formatted numbers and description
            values_with_desc = (
                herb[0],           # id
                herb[1],           # name
                unit_price_str,    # formatted unit_price
                herb[3],           # shelf
                weight_str,        # formatted weight
                total_price_str,   # formatted total_price
                herb[5],           # date
                herb[6]            # description
            )
            self.herb_tree.insert("", "end", values=values_with_desc, tags=('herb_row',)) # Add a tag for potential styling

        # Optional: Apply alternating row colors for better readability
        self.herb_tree.tag_configure('oddrow', background='#E8E8E8')
        self.herb_tree.tag_configure('evenrow', background='#FFFFFF')
        for i, item_id in enumerate(self.herb_tree.get_children()):
            if i % 2 == 0:
                self.herb_tree.item(item_id, tags=('herb_row', 'evenrow'))
            else:
                self.herb_tree.item(item_id, tags=('herb_row', 'oddrow'))

    # -------------------- توابع تولید دارو --------------------
    def add_composition(self):
        # باز کردن پنجره جدید برای افزودن ترکیب
        self.comp_window = tk.Toplevel()
        self.comp_window.title("افزودن ترکیب")
        
        ttk.Label(self.comp_window, text="کد گیاه:").grid(row=0, column=0)
        self.herb_id_entry = ttk.Entry(self.comp_window)
        self.herb_id_entry.grid(row=0, column=1)
        
        ttk.Label(self.comp_window, text="وزن مورد نیاز (گرم):").grid(row=1, column=0)
        self.herb_weight_entry = ttk.Entry(self.comp_window)
        self.herb_weight_entry.grid(row=1, column=1)
        
        ttk.Button(self.comp_window, text="افزودن", command=self.save_composition).grid(row=2, column=0, columnspan=2)
    
    def save_composition(self):
        herb_id = self.herb_id_entry.get() # Remove int() conversion
        weight = float(self.herb_weight_entry.get())
        
        # بررسی موجودی
        herb = cursor.execute("SELECT current_weight, unit_price FROM Herbs WHERE id = ?", (herb_id,)).fetchone()
        if not herb:
            messagebox.showerror("خطا", "گیاه یافت نشد!")
            return
        if herb[0] < weight:
            messagebox.showerror("خطا", "موجودی ناکافی!")
            return
        
        subtotal = weight * herb[1]
        self.compositions.append((herb_id, weight, subtotal))
        self.comp_window.destroy()
        messagebox.showinfo("موفق", "ترکیب افزوده شد!")
    
    def produce_drug(self):
        try:
            drug_id = int(self.drug_id_entry.get())
            drug_name = self.drug_name_entry.get()
            production_date = datetime.now().strftime("%Y-%m-%d")
            # ثبت دارو
            cursor.execute('''
                INSERT INTO Drugs (id, name, production_date)
                VALUES (?, ?, ?)
            ''', (drug_id, drug_name, production_date))
            
            # ثبت ترکیبات
            for comp in self.compositions:
                cursor.execute('''
                    INSERT INTO DrugCompositions (drug_id, herb_id, weight_used, subtotal)
                    VALUES (?, ?, ?, ?)
                ''', (drug_id, comp[0], comp[1], comp[2]))
                
                # کاهش موجودی گیاه
                cursor.execute('''
                    UPDATE Herbs SET current_weight = current_weight - ? WHERE id = ?
                ''', (comp[1], comp[0]))
            
            conn.commit()
            messagebox.showinfo("موفق", "دارو با موفقیت تولید شد!")
            self.compositions = []
        except Exception as e:
            conn.rollback()
            messagebox.showerror("خطا", f"خطا در تولید دارو: {str(e)}")

# -------------------- اجرای برنامه --------------------
if __name__ == "__main__":
    root = tk.Tk()
    app = HerbalFactoryApp(root)
    root.mainloop()
    conn.close()
