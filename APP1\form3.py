import tkinter as tk
from tkinter import ttk, messagebox
import sqlite3
from datetime import datetime

# -------------------- Database Setup --------------------
conn = sqlite3.connect('herbal_factory.db')
cursor = conn.cursor()

# ایجاد جداول
cursor.execute('''
CREATE TABLE IF NOT EXISTS Herbs (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    unit_price REAL NOT NULL,
    shelf_location TEXT,
    current_weight REAL NOT NULL,
    purchase_date TEXT,
    description TEXT
)
''')

cursor.execute('''
CREATE TABLE IF NOT EXISTS Drugs (
    id INTEGER PRIMARY KEY,
    name TEXT NOT NULL,
    production_date TEXT
)
''')

cursor.execute('''
CREATE TABLE IF NOT EXISTS DrugCompositions (
    drug_id INTEGER,
    herb_id TEXT,
    weight_used REAL,
    subtotal REAL,
    FOREIGN KEY(drug_id) REFERENCES Drugs(id),
    FOREIGN KEY(herb_id) REFERENCES Herbs(id)
)
''')

cursor.execute('''
CREATE TABLE IF NOT EXISTS Users (
    username TEXT PRIMARY KEY,
    password TEXT NOT NULL
)
''')

# --- New Tables for Diseases ---
cursor.execute('''
CREATE TABLE IF NOT EXISTS Diseases (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL UNIQUE 
)
''')

cursor.execute('''
CREATE TABLE IF NOT EXISTS DrugDiseases (
    drug_id INTEGER,
    disease_id INTEGER,
    FOREIGN KEY(drug_id) REFERENCES Drugs(id) ON DELETE CASCADE,
    FOREIGN KEY(disease_id) REFERENCES Diseases(id) ON DELETE CASCADE,
    PRIMARY KEY (drug_id, disease_id) 
)
''')
# --- End New Tables ---

conn.commit()

# -------------------- GUI Application --------------------
class HerbalFactoryApp:
    def __init__(self, root):
        self.root = root
        self.root.title("کارگاه تولید داروهای گیاهی")
        self.root.geometry("1000x600")
        
        self.selected_herb_id = None # Variable to store selected herb ID for edit/delete
        self.selected_disease_id = None # Variable to store selected disease ID
        self.disease_map = {} # To map disease names to IDs for combobox

        # ایجاد تب‌ها
        self.notebook = ttk.Notebook(root)
        self.herb_tab = ttk.Frame(self.notebook)
        self.drug_tab = ttk.Frame(self.notebook)
        self.disease_tab = ttk.Frame(self.notebook) # New tab for diseases
        self.report_tab = ttk.Frame(self.notebook)

        # Add tabs in REVERSE visual order for RTL appearance (Reports, Diseases, Drugs, Herbs)
        self.notebook.add(self.herb_tab, text="مدیریت مفردات گیاهی")
        self.notebook.add(self.disease_tab, text="مدیریت بیماری‌ها") # Add disease tab
        self.notebook.add(self.drug_tab, text="تولید دارو")
        self.notebook.add(self.report_tab, text="گزارشات")

        self.notebook.pack(expand=True, fill="both")

        # Setup content for each tab
        self.setup_herb_tab_content()
        self.setup_drug_tab_content()
        self.setup_disease_tab_content() # Call setup for the new tab
        self.setup_report_tab()
        # -------------------------

    # --- Helper methods to set up tab content ---
    def setup_herb_tab_content(self):
        # -------------------- تب گیاهان --------------------
        # فرم ثبت گیاه (RTL Adjustments)
        self.herb_frame = ttk.LabelFrame(self.herb_tab, text="ثبت گیاه جدید")
        self.herb_frame.pack(pady=10, padx=10, fill="x")

        labels = ["کد گیاه:", "نام گیاه:", "قیمت واحد (تومان):", "محل قفسه:", "وزن (گرم):", "تاریخ خرید:", "توضیحات:"]
        self.herb_entries = {}
        for i, label_text in enumerate(labels):
            # Label on the right (column 1), aligned right
            label_widget = ttk.Label(self.herb_frame, text=label_text, anchor=tk.E)
            label_widget.grid(row=i, column=1, padx=5, pady=5, sticky=tk.E)
            # Entry on the left (column 0)
            entry = ttk.Entry(self.herb_frame, justify=tk.RIGHT) # Justify text inside entry to the right
            entry.grid(row=i, column=0, padx=5, pady=5, sticky=tk.W+tk.E) # Stretch entry horizontally
            self.herb_entries[label_text.split(":")[0]] = entry

        # Adjust grid weights for resizing
        self.herb_frame.columnconfigure(0, weight=1) # Allow entry column to expand
        self.herb_frame.columnconfigure(1, weight=0) # Label column fixed width

        # دکمه‌های مدیریت گیاهان (در یک فریم جدا) - Centered below
        self.herb_button_frame = ttk.Frame(self.herb_frame)
        # Place button frame spanning both columns, centered
        self.herb_button_frame.grid(row=len(labels), column=0, columnspan=2, pady=10)

        # Buttons remain in their frame, layout within frame is LTR but frame is centered
        self.add_herb_btn = ttk.Button(self.herb_button_frame, text="ذخیره گیاه", command=self.add_herb)
        self.add_herb_btn.grid(row=0, column=4, padx=5) # Order within button frame can remain LTR
        
        self.edit_herb_btn = ttk.Button(self.herb_button_frame, text="ویرایش گیاه", command=self.edit_herb)
        self.edit_herb_btn.grid(row=0, column=3, padx=5)
        
        self.delete_herb_btn = ttk.Button(self.herb_button_frame, text="حذف گیاه", command=self.delete_herb)
        self.delete_herb_btn.grid(row=0, column=2, padx=5)

        self.clear_herb_form_btn = ttk.Button(self.herb_button_frame, text="پاک کردن فرم", command=self.clear_herb_form)
        self.clear_herb_form_btn.grid(row=0, column=1, padx=5)

        # جدول نمایش گیاهان (RTL)
        columns_rtl = ("description", "date", "total_price", "weight", "shelf", "unit_price", "name", "id")
        self.herb_tree = ttk.Treeview(self.herb_tab, columns=columns_rtl, show="headings")
        
        # تعریف عناوین ستون‌ها (RTL)
        self.herb_tree.heading("description", text="توضیحات")
        self.herb_tree.heading("date", text="تاریخ خرید")
        self.herb_tree.heading("total_price", text="قیمت محصول")
        self.herb_tree.heading("weight", text="وزن (گرم)")
        self.herb_tree.heading("shelf", text="محل قفسه")
        self.herb_tree.heading("unit_price", text="قیمت واحد")
        self.herb_tree.heading("name", text="نام")
        self.herb_tree.heading("id", text="کد")

        # تنظیم عرض و ترازبندی ستون‌ها (RTL)
        self.herb_tree.column("description", width=250, anchor=tk.E) # Align Right
        self.herb_tree.column("date", width=100, anchor=tk.CENTER)
        self.herb_tree.column("total_price", width=120, anchor=tk.E) # Align Right
        self.herb_tree.column("weight", width=100, anchor=tk.E) # Align Right
        self.herb_tree.column("shelf", width=100, anchor=tk.E) # Align Right
        self.herb_tree.column("unit_price", width=100, anchor=tk.E) # Align Right
        self.herb_tree.column("name", width=150, anchor=tk.E) # Align Right
        self.herb_tree.column("id", width=80, anchor=tk.CENTER)

        self.herb_tree.pack(fill="both", expand=True, padx=10, pady=(0, 10))
        self.herb_tree.bind("<<TreeviewSelect>>", self.on_herb_select)
        self.load_herbs()

    def setup_drug_tab_content(self):
        # -------------------- تب تولید دارو --------------------
        # فرم تولید دارو (RTL Adjustments)
        self.drug_frame = ttk.LabelFrame(self.drug_tab, text="تولید دارو جدید")
        self.drug_frame.pack(pady=10, padx=10, fill="x")

        # Label on right (col 1), Entry on left (col 0)
        ttk.Label(self.drug_frame, text="کد دارو:", anchor=tk.E).grid(row=0, column=1, sticky=tk.E, padx=5, pady=5)
        self.drug_id_entry = ttk.Entry(self.drug_frame, justify=tk.RIGHT)
        self.drug_id_entry.grid(row=0, column=0, sticky=tk.W+tk.E, padx=5, pady=5)

        ttk.Label(self.drug_frame, text="نام دارو:", anchor=tk.E).grid(row=1, column=1, sticky=tk.E, padx=5, pady=5)
        self.drug_name_entry = ttk.Entry(self.drug_frame, justify=tk.RIGHT)
        self.drug_name_entry.grid(row=1, column=0, sticky=tk.W+tk.E, padx=5, pady=5)

        # Disease Combobox (New)
        ttk.Label(self.drug_frame, text="بیماری مرتبط:", anchor=tk.E).grid(row=2, column=1, sticky=tk.E, padx=5, pady=5)
        self.disease_combobox = ttk.Combobox(self.drug_frame, state="readonly", justify=tk.RIGHT)
        self.disease_combobox.grid(row=2, column=0, sticky=tk.W+tk.E, padx=5, pady=5)

        # Adjust grid weights
        self.drug_frame.columnconfigure(0, weight=1)
        self.drug_frame.columnconfigure(1, weight=0)

        # دکمه افزودن ترکیبات
        self.add_composition_btn = ttk.Button(self.drug_frame, text="+ افزودن ترکیب", command=self.add_composition)
        self.add_composition_btn.grid(row=3, column=0, columnspan=2, pady=5)

        # لیست ترکیبات
        self.compositions = []

        # دکمه تولید دارو
        self.produce_drug_btn = ttk.Button(self.drug_frame, text="تولید دارو", command=self.produce_drug)
        self.produce_drug_btn.grid(row=4, column=0, columnspan=2, pady=10)

        # --- Table for Produced Drugs ---
        self.produced_drug_list_frame = ttk.LabelFrame(self.drug_tab, text="لیست داروهای تولید شده")
        self.produced_drug_list_frame.pack(pady=10, padx=10, fill="both", expand=True)

        # فریم برای دکمه‌های مدیریت دارو
        self.drug_buttons_frame = ttk.Frame(self.produced_drug_list_frame)
        self.drug_buttons_frame.pack(fill="x", pady=5)

        self.edit_drug_btn = ttk.Button(
            self.drug_buttons_frame, 
            text="ویرایش داروی انتخاب شده", 
            command=self.edit_selected_drug, 
            state='disabled'
        )
        self.edit_drug_btn.pack(side="right", padx=5)

        self.delete_drug_btn = ttk.Button(
            self.drug_buttons_frame, 
            text="حذف داروی انتخاب شده", 
            command=self.delete_selected_drug,
            state='disabled'
        )
        self.delete_drug_btn.pack(side="right", padx=5)

        # فریم برای Treeview و Scrollbar
        self.tree_frame = ttk.Frame(self.produced_drug_list_frame)
        self.tree_frame.pack(fill="both", expand=True)

        # Define columns (RTL order: Disease, Date, Name, ID, Cost)
        

        produced_columns = ("disease_name", "production_date", "drug_name", "drug_id", "total_cost")
        self.produced_drug_tree = ttk.Treeview(
            self.tree_frame, 
            columns=produced_columns, 
            show="headings",
            height=10
            )


        # تنظیم ستون‌ها
        self.produced_drug_tree.heading("disease_name", text="بیماری مرتبط")
        self.produced_drug_tree.heading("production_date", text="تاریخ تولید")
        self.produced_drug_tree.heading("drug_name", text="نام دارو")
        self.produced_drug_tree.heading("drug_id", text="کد دارو")
        #self.produced_drug_tree.heading("total_cost", text="هزینه تولید (تومان)")
        # تغییر عنوان ستون هزینه
        self.produced_drug_tree.heading("total_cost", text="هزینه تولید (تومان/کیلوگرم)")

        self.produced_drug_tree.column("disease_name", width=150, anchor=tk.E)
        self.produced_drug_tree.column("production_date", width=100, anchor=tk.CENTER)
        self.produced_drug_tree.column("drug_name", width=200, anchor=tk.E)
        self.produced_drug_tree.column("drug_id", width=80, anchor=tk.CENTER)
        self.produced_drug_tree.column("total_cost", width=120, anchor=tk.E)

        # Scrollbar
        prod_scrollbar = ttk.Scrollbar(
            self.tree_frame, 
            orient="vertical", 
            command=self.produced_drug_tree.yview
        )
        self.produced_drug_tree.configure(yscrollcommand=prod_scrollbar.set)

        # چیدمان Treeview و Scrollbar
        self.produced_drug_tree.pack(side="right", fill="both", expand=True)
        prod_scrollbar.pack(side="left", fill="y")

        # رویدادها
        self.produced_drug_tree.bind("<<TreeviewSelect>>", self.toggle_drug_buttons)
        self.produced_drug_tree.bind("<Double-1>", self.show_drug_compositions)

        # Load initial data
        self.load_produced_drugs()
          
    def toggle_drug_buttons(self, event):
        """فعال کردن دکمه‌ها وقتی دارویی انتخاب می‌شود"""
        if self.produced_drug_tree.selection():
            self.edit_drug_btn['state'] = 'normal'
            self.delete_drug_btn['state'] = 'normal'
        else:
            self.edit_drug_btn['state'] = 'disabled'
            self.delete_drug_btn['state'] = 'disabled'
       
    def edit_selected_drug(self):
        selected_item = self.produced_drug_tree.selection()
        if not selected_item:
            return
        
        drug_id = self.produced_drug_tree.item(selected_item, 'values')[3]  # کد دارو
        
        # دریافت اطلاعات فعلی دارو
        cursor.execute("SELECT name, production_date FROM Drugs WHERE id = ?", (drug_id,))
        drug_info = cursor.fetchone()
        
        # پنجره ویرایش
        edit_window = tk.Toplevel()
        edit_window.title(f"ویرایش دارو {drug_id}")
        
        # فیلدهای ویرایش
        ttk.Label(edit_window, text="نام جدید دارو:").grid(row=0, column=0, padx=5, pady=5)
        new_name_entry = ttk.Entry(edit_window)
        new_name_entry.grid(row=0, column=1, padx=5, pady=5)
        new_name_entry.insert(0, drug_info[0])
        
        # بیماری مرتبط (Combobox)
        ttk.Label(edit_window, text="بیماری مرتبط:").grid(row=1, column=0, padx=5, pady=5)
        disease_combo = ttk.Combobox(edit_window, values=list(self.disease_map.keys()))
        disease_combo.grid(row=1, column=1, padx=5, pady=5)
        
        # دریافت بیماری فعلی
        cursor.execute('''
        SELECT ds.name FROM DrugDiseases dd
        JOIN Diseases ds ON dd.disease_id = ds.id
        WHERE dd.drug_id = ?
        ''', (drug_id,))
        current_disease = cursor.fetchone()
        if current_disease:
            disease_combo.set(current_disease[0])
        
        # دکمه ذخیره
        ttk.Button(
            edit_window, 
            text="ذخیره تغییرات",
            command=lambda: self.save_drug_edits(
                drug_id,
                new_name_entry.get(),
                disease_combo.get(),
                edit_window
            )
        ).grid(row=2, column=0, columnspan=2, pady=10)

    def save_drug_edits(self, drug_id, new_name, new_disease_name, window):
        if not new_name:
            messagebox.showwarning("خطا", "نام دارو نمی‌تواند خالی باشد")
            return
        
        try:
            # به‌روزرسانی نام دارو
            cursor.execute("UPDATE Drugs SET name = ? WHERE id = ?", (new_name, drug_id))
            
            # به‌روزرسانی بیماری مرتبط
            if new_disease_name:
                new_disease_id = self.disease_map[new_disease_name]
                # حذف ارتباطات قبلی
                cursor.execute("DELETE FROM DrugDiseases WHERE drug_id = ?", (drug_id,))
                # اضافه کردن ارتباط جدید
                cursor.execute("INSERT INTO DrugDiseases (drug_id, disease_id) VALUES (?, ?)", 
                            (drug_id, new_disease_id))
            
            conn.commit()
            messagebox.showinfo("موفق", "تغییرات با موفقیت ذخیره شد")
            window.destroy()
            self.load_produced_drugs()  # به‌روزرسانی لیست
        except Exception as e:
            conn.rollback()
            messagebox.showerror("خطا", f"خطا در ذخیره تغییرات: {str(e)}")

    def delete_selected_drug(self):
        selected_item = self.produced_drug_tree.selection()
        if not selected_item:
            return
        
        drug_id = self.produced_drug_tree.item(selected_item, 'values')[3]
        drug_name = self.produced_drug_tree.item(selected_item, 'values')[2]
        
        confirm = messagebox.askyesno(
            "تایید حذف",
            f"آیا از حذف داروی '{drug_name}' (کد: {drug_id}) مطمئن هستید؟\n\nتوجه: این عمل برگشت‌ناپذیر است!"
        )
        
        if confirm:
            try:
                # شروع تراکنش
                conn.execute("BEGIN TRANSACTION")
                
                # 1. بازگرداندن مواد اولیه به انبار
                cursor.execute('''
                SELECT herb_id, weight_used 
                FROM DrugCompositions 
                WHERE drug_id = ?
                ''', (drug_id,))
                compositions = cursor.fetchall()
                
                for herb_id, weight in compositions:
                    cursor.execute('''
                    UPDATE Herbs 
                    SET current_weight = current_weight + ? 
                    WHERE id = ?
                    ''', (weight, herb_id))
                
                # 2. حذف ترکیبات دارو
                cursor.execute("DELETE FROM DrugCompositions WHERE drug_id = ?", (drug_id,))
                
                # 3. حذف ارتباط با بیماری‌ها
                cursor.execute("DELETE FROM DrugDiseases WHERE drug_id = ?", (drug_id,))
                
                # 4. حذف خود دارو
                cursor.execute("DELETE FROM Drugs WHERE id = ?", (drug_id,))
                
                conn.commit()
                messagebox.showinfo("موفق", "دارو با موفقیت حذف شد")
                self.load_produced_drugs()
                self.load_herbs()  # به‌روزرسانی موجودی انبار
            except Exception as e:
                conn.rollback()
                messagebox.showerror("خطا", f"خطا در حذف دارو: {str(e)}")


    def load_produced_drugs(self):
    # پاک کردن داده‌های موجود در Treeview
        for row in self.produced_drug_tree.get_children():
            self.produced_drug_tree.delete(row)
        
        try:
            # دریافت اطلاعات داروهای تولید شده به همراه بیماری‌های مرتبط
            query = '''
                SELECT 
                    d.id, 
                    d.name, 
                    d.production_date, 
                    GROUP_CONCAT(ds.name, ', '),
                    SUM(dc.subtotal)  -- محاسبه جمع هزینه‌ها
                FROM Drugs d
                LEFT JOIN DrugDiseases dd ON d.id = dd.drug_id
                LEFT JOIN Diseases ds ON dd.disease_id = ds.id
                LEFT JOIN DrugCompositions dc ON d.id = dc.drug_id
                GROUP BY d.id
                '''
            cursor.execute(query)
            drugs = cursor.fetchall()
            
            # افزودن هر دارو به Treeview
            for drug in drugs:
                drug_id = drug[0]
                drug_name = drug[1]
                production_date = drug[2]
                disease_names = drug[3] if drug[3] else "بدون بیماری مرتبط"
                
                # درج داده‌ها به ترتیب RTL (بیماری، تاریخ، نام، کد)
                self.produced_drug_tree.insert("", "end", values=(
                    disease_names,
                    production_date,
                    drug_name,
                    drug_id
                ))
            
            # اعمال رنگ‌های متناوب برای سطرها
            self.produced_drug_tree.tag_configure('oddrow', background='#E8E8E8')
            self.produced_drug_tree.tag_configure('evenrow', background='#FFFFFF')
            for i, item_id in enumerate(self.produced_drug_tree.get_children()):
                if i % 2 == 0:
                    self.produced_drug_tree.item(item_id, tags=('evenrow',))
                else:
                    self.produced_drug_tree.item(item_id, tags=('oddrow',))
                    
        except Exception as e:
            messagebox.showerror("خطا", f"خطا در بارگذاری داروهای تولید شده: {str(e)}")

    def show_drug_compositions(self, event):
        selected_item = self.produced_drug_tree.selection()
        if not selected_item:
            return
        
        drug_id = self.produced_drug_tree.item(selected_item, 'values')[3]
        drug_name = self.produced_drug_tree.item(selected_item, 'values')[2]
        
        # پنجره اصلی
        detail_window = tk.Toplevel()
        detail_window.title(f"مدیریت ترکیبات دارو {drug_name} (کد: {drug_id})")
        detail_window.geometry("800x600")
        
        # فریم برای Treeview ترکیبات
        tree_frame = ttk.Frame(detail_window)
        tree_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # Treeview ترکیبات
        columns = ("herb_id", "herb_name", "weight_used", "unit_price", "subtotal")
        self.comp_tree = ttk.Treeview(
            tree_frame, 
            columns=columns, 
            show="headings",
            selectmode="browse"
        )
        
        # تنظیم ستون‌ها
        self.comp_tree.heading("herb_id", text="کد گیاه")
        self.comp_tree.heading("herb_name", text="نام گیاه")
        self.comp_tree.heading("weight_used", text="وزن مصرفی (گرم)")
        self.comp_tree.heading("unit_price", text="قیمت واحد (تومان)")
        self.comp_tree.heading("subtotal", text="هزینه جزئی")
        
        self.comp_tree.column("herb_id", width=100, anchor=tk.CENTER)
        self.comp_tree.column("herb_name", width=150, anchor=tk.E)
        self.comp_tree.column("weight_used", width=120, anchor=tk.E)
        self.comp_tree.column("unit_price", width=120, anchor=tk.E)
        self.comp_tree.column("subtotal", width=120, anchor=tk.E)
        
        # Scrollbar
        scrollbar = ttk.Scrollbar(tree_frame, orient="vertical", command=self.comp_tree.yview)
        self.comp_tree.configure(yscrollcommand=scrollbar.set)
        scrollbar.pack(side="left", fill="y")
        self.comp_tree.pack(side="right", fill="both", expand=True)
        
        # فریم دکمه‌های مدیریت
        btn_frame = ttk.Frame(detail_window)
        btn_frame.pack(fill="x", padx=10, pady=5)
        
        # دکمه‌های مدیریت
        ttk.Button(
            btn_frame, 
            text="+ افزودن ترکیب جدید",
            command=lambda: self.add_new_composition(drug_id, detail_window)
        ).pack(side="right", padx=5)
        
        ttk.Button(
            btn_frame, 
            text="ویرایش ترکیب انتخاب شده",
            command=lambda: self.edit_selected_composition(drug_id, detail_window)
        ).pack(side="right", padx=5)
        
        ttk.Button(
            btn_frame, 
            text="حذف ترکیب انتخاب شده",
            command=lambda: self.delete_selected_composition(drug_id, detail_window)
        ).pack(side="right", padx=5)
        
        # نمایش جمع کل
        self.total_cost_var = tk.StringVar()
        ttk.Label(
            detail_window, 
            textvariable=self.total_cost_var,
            font=("Tahoma", 10, "bold")
        ).pack(pady=5)
        
        # بارگذاری اولیه داده‌ها
        self.load_compositions(drug_id, detail_window)
        
        # رویداد انتخاب
        self.comp_tree.bind("<<TreeviewSelect>>", lambda e: self.toggle_composition_buttons(btn_frame))

    def load_compositions(self, drug_id, window):
        """بارگذاری ترکیبات دارو در Treeview"""
        for item in self.comp_tree.get_children():
            self.comp_tree.delete(item)
        
        try:
            cursor.execute('''
            SELECT dc.herb_id, h.name, dc.weight_used, h.unit_price, dc.subtotal
            FROM DrugCompositions dc
            JOIN Herbs h ON dc.herb_id = h.id
            WHERE dc.drug_id = ?
            ''', (drug_id,))
            
            total = 0
            for comp in cursor.fetchall():
                weight_kg = comp[2] / 1000  # تبدیل گرم به کیلوگرم
                unit_price_per_kg = comp[3]  # قیمت هر کیلوگرم
                subtotal = weight_kg * unit_price_per_kg  # محاسبه مجدد برای اطمینان
                 # نمایش وزن هم به گرم و هم به کیلوگرم
                display_weight = f"{comp[2]:g} گرم ({weight_kg:.3f} کیلوگرم)"
                self.comp_tree.insert("", "end", values=(
                    comp[0],  # herb_id
                    comp[1],  # herb_name
                    display_weight,  # weight_used
                    f"{unit_price_per_kg:,.2f}",  # unit_price (per kg)
                    f"{subtotal:,.2f}"  # subtotal
                    ))
                total += subtotal
        
            self.total_cost_var.set(f"جمع کل هزینه تولید: {total:,.2f} تومان (بر اساس کیلوگرم)")
            window.update()
            
        except Exception as e:
            messagebox.showerror("خطا", f"خطا در بارگذاری ترکیبات: {str(e)}")
    

    def add_new_composition(self, drug_id, parent_window):
        """افزودن ترکیب جدید به دارو"""
        add_window = tk.Toplevel(parent_window)
        add_window.title("افزودن ترکیب جدید")
        
        # فیلدهای فرم
        ttk.Label(add_window, text="کد گیاه:").grid(row=0, column=0, padx=5, pady=5)
        herb_id_entry = ttk.Entry(add_window)
        herb_id_entry.grid(row=0, column=1, padx=5, pady=5)
        
        ttk.Label(add_window, text="وزن مصرفی (گرم):").grid(row=1, column=0, padx=5, pady=5)
        weight_entry = ttk.Entry(add_window)
        weight_entry.grid(row=1, column=1, padx=5, pady=5)
        
        def save_composition():
            try:
                herb_id = herb_id_entry.get()
                weight = float(weight_entry.get())
                
                # بررسی موجودی
                cursor.execute("SELECT current_weight, unit_price FROM Herbs WHERE id = ?", (herb_id,))
                herb_data = cursor.fetchone()
                
                if not herb_data:
                    messagebox.showerror("خطا", "کد گیاه نامعتبر است!")
                    return
                
                if herb_data[0] < weight:
                    messagebox.showerror("خطا", f"موجودی ناکافی! موجودی فعلی: {herb_data[0]} گرم")
                    return
                
                subtotal = weight * herb_data[1]
                
                # ذخیره در دیتابیس
                cursor.execute('''
                INSERT INTO DrugCompositions (drug_id, herb_id, weight_used, subtotal)
                VALUES (?, ?, ?, ?)
                ''', (drug_id, herb_id, weight, subtotal))
                
                # کاهش موجودی انبار
                cursor.execute('''
                UPDATE Herbs SET current_weight = current_weight - ? WHERE id = ?
                ''', (weight, herb_id))
                
                conn.commit()
                messagebox.showinfo("موفق", "ترکیب با موفقیت اضافه شد!")
                add_window.destroy()
                self.load_compositions(drug_id, parent_window)
                self.load_herbs()  # به‌روزرسانی موجودی انبار
                
            except ValueError:
                messagebox.showerror("خطا", "وزن باید یک عدد معتبر باشد!")
            except Exception as e:
                conn.rollback()
                messagebox.showerror("خطا", f"خطا در افزودن ترکیب: {str(e)}")
        
        ttk.Button(
            add_window, 
            text="ذخیره", 
            command=save_composition
        ).grid(row=2, column=0, columnspan=2, pady=10)

    
    def edit_selected_composition(self, drug_id, parent_window):
        """ویرایش ترکیب انتخاب شده"""
        selected = self.comp_tree.selection()
        if not selected:
            messagebox.showwarning("هشدار", "لطفاً یک ترکیب را انتخاب کنید")
            return
        
        comp_data = self.comp_tree.item(selected, 'values')
        herb_id = comp_data[0]
        current_weight_grams = float(comp_data[2].split()[0])  # استخراج مقدار گرم از نمایش
        
        edit_window = tk.Toplevel(parent_window)
        edit_window.title("ویرایش ترکیب")
        
        # فیلدهای فرم
        ttk.Label(edit_window, text=f"کد گیاه: {herb_id}").grid(row=0, column=0, padx=5, pady=5)
        
        ttk.Label(edit_window, text="وزن جدید (گرم):").grid(row=1, column=0, padx=5, pady=5)
        weight_entry = ttk.Entry(edit_window)
        weight_entry.insert(0, current_weight_grams)
        weight_entry.grid(row=1, column=1, padx=5, pady=5)
        
        def update_composition():
            try:
                new_weight_grams = float(weight_entry.get())
                weight_diff_grams = new_weight_grams - current_weight_grams
                
                # بررسی موجودی (بر حسب گرم)
                cursor.execute("SELECT current_weight, unit_price FROM Herbs WHERE id = ?", (herb_id,))
                available, unit_price_per_kg = cursor.fetchone()
                
                if available < weight_diff_grams:
                    messagebox.showerror("خطا", f"موجودی ناکافی! موجودی قابل دسترس: {available} گرم")
                    return
                
                # محاسبه هزینه جدید (بر اساس کیلوگرم)
                new_weight_kg = new_weight_grams / 1000
                new_subtotal = new_weight_kg * unit_price_per_kg
                
                # به‌روزرسانی دیتابیس
                cursor.execute('''
                UPDATE DrugCompositions 
                SET weight_used = ?, subtotal = ?
                WHERE drug_id = ? AND herb_id = ?
                ''', (new_weight_grams, new_subtotal, drug_id, herb_id))
                
                # به‌روزرسانی موجودی انبار (بر حسب گرم)
                cursor.execute('''
                UPDATE Herbs 
                SET current_weight = current_weight - ?
                WHERE id = ?
                ''', (weight_diff_grams, herb_id))
                
                conn.commit()
                messagebox.showinfo("موفق", "ترکیب با موفقیت ویرایش شد!")
                edit_window.destroy()
                self.load_compositions(drug_id, parent_window)
                self.load_herbs()
                
            except ValueError:
                messagebox.showerror("خطا", "لطفاً یک عدد معتبر وارد کنید!")
            except Exception as e:
                conn.rollback()
                messagebox.showerror("خطا", f"خطا در ویرایش ترکیب: {str(e)}")
        
        ttk.Button(
            edit_window, 
            text="ذخیره تغییرات", 
            command=update_composition
        ).grid(row=2, column=0, columnspan=2, pady=10)

    def delete_selected_composition(self, drug_id, parent_window):
        """حذف ترکیب انتخاب شده"""
        selected = self.comp_tree.selection()
        if not selected:
            messagebox.showwarning("هشدار", "لطفاً یک ترکیب را انتخاب کنید")
            return
        
        comp_data = self.comp_tree.item(selected, 'values')
        herb_id = comp_data[0]
        weight = float(comp_data[2])
        
        confirm = messagebox.askyesno(
            "تأیید حذف",
            f"آیا از حذف ترکیب با کد گیاه {herb_id} مطمئن هستید؟\nوزن مصرفی: {weight} گرم"
        )
        
        if confirm:
            try:
                # حذف از دیتابیس
                cursor.execute('''
                DELETE FROM DrugCompositions 
                WHERE drug_id = ? AND herb_id = ?
                ''', (drug_id, herb_id))
                
                # بازگرداندن موجودی به انبار
                cursor.execute('''
                UPDATE Herbs 
                SET current_weight = current_weight + ?
                WHERE id = ?
                ''', (weight, herb_id))
                
                conn.commit()
                messagebox.showinfo("موفق", "ترکیب با موفقیت حذف شد!")
                self.load_compositions(drug_id, parent_window)
                self.load_herbs()
                
            except Exception as e:
                conn.rollback()
                messagebox.showerror("خطا", f"خطا در حذف ترکیب: {str(e)}")

    def toggle_composition_buttons(self, btn_frame):
        """فعال/غیرفعال کردن دکمه‌های ویرایش و حذف بر اساس انتخاب"""
        selected = self.comp_tree.selection()
        for widget in btn_frame.winfo_children():
            if widget.winfo_class() == "TButton" and "ویرایش" in widget["text"] or "حذف" in widget["text"]:
                widget["state"] = "normal" if selected else "disabled"







    def setup_disease_tab_content(self):
        # -------------------- تب مدیریت بیماری‌ها --------------------
        # Frame for disease form
        self.disease_form_frame = ttk.LabelFrame(self.disease_tab, text="ثبت / ویرایش بیماری")
        self.disease_form_frame.pack(pady=10, padx=10, fill="x")

        # Disease Name Label and Entry (RTL)
        ttk.Label(self.disease_form_frame, text="نام بیماری:", anchor=tk.E).grid(row=0, column=1, padx=5, pady=5, sticky=tk.E)
        self.disease_name_entry = ttk.Entry(self.disease_form_frame, justify=tk.RIGHT)
        self.disease_name_entry.grid(row=0, column=0, padx=5, pady=5, sticky=tk.W+tk.E)
        self.disease_form_frame.columnconfigure(0, weight=1)
        self.disease_form_frame.columnconfigure(1, weight=0)

        # Disease Buttons Frame
        self.disease_button_frame = ttk.Frame(self.disease_form_frame)
        self.disease_button_frame.grid(row=1, column=0, columnspan=2, pady=10)

        # Disease Buttons (Add, Edit, Delete, Clear)
        self.add_disease_btn = ttk.Button(self.disease_button_frame, text="ذخیره بیماری", command=self.add_disease)
        self.add_disease_btn.grid(row=0, column=3, padx=5)
        self.edit_disease_btn = ttk.Button(self.disease_button_frame, text="ویرایش بیماری", command=self.edit_disease)
        self.edit_disease_btn.grid(row=0, column=2, padx=5)
        self.delete_disease_btn = ttk.Button(self.disease_button_frame, text="حذف بیماری", command=self.delete_disease)
        self.delete_disease_btn.grid(row=0, column=1, padx=5)
        self.clear_disease_form_btn = ttk.Button(self.disease_button_frame, text="پاک کردن فرم", command=self.clear_disease_form)
        self.clear_disease_form_btn.grid(row=0, column=0, padx=5)


        # Frame for disease list (Treeview)
        self.disease_list_frame = ttk.Frame(self.disease_tab)
        self.disease_list_frame.pack(pady=10, padx=10, fill="both", expand=True)

        # Disease Treeview
        self.disease_tree = ttk.Treeview(self.disease_list_frame, columns=("name", "id"), show="headings")
        self.disease_tree.heading("name", text="نام بیماری")
        self.disease_tree.heading("id", text="کد")
        self.disease_tree.column("name", anchor=tk.E) # Align Right
        self.disease_tree.column("id", width=80, anchor=tk.CENTER)

        # Add scrollbar
        scrollbar = ttk.Scrollbar(self.disease_list_frame, orient="vertical", command=self.disease_tree.yview)
        self.disease_tree.configure(yscrollcommand=scrollbar.set)
        scrollbar.pack(side="left", fill="y") # Place scrollbar on the left for RTL
        self.disease_tree.pack(side="right", fill="both", expand=True) # Treeview takes remaining space

        self.disease_tree.bind("<<TreeviewSelect>>", self.on_disease_select)
        self.load_diseases() # Load initial data

    # --- Functions for Disease Management ---
    def add_disease(self):
        disease_name = self.disease_name_entry.get().strip()
        if not disease_name:
            messagebox.showwarning("هشدار", "لطفا نام بیماری را وارد کنید.")
            return

        try:
            cursor.execute("INSERT INTO Diseases (name) VALUES (?)", (disease_name,))
            conn.commit()
            messagebox.showinfo("موفق", f"بیماری '{disease_name}' با موفقیت ثبت شد!")
            self.load_diseases() # Refresh list
            self.clear_disease_form()
        except sqlite3.IntegrityError:
            messagebox.showerror("خطا", f"بیماری با نام '{disease_name}' قبلا ثبت شده است.")
        except Exception as e:
            messagebox.showerror("خطا", f"خطا در ثبت بیماری: {str(e)}")

    def edit_disease(self):
        if self.selected_disease_id is None:
            messagebox.showwarning("هشدار", "لطفا ابتدا یک بیماری را از جدول انتخاب کنید.")
            return

        new_name = self.disease_name_entry.get().strip()
        if not new_name:
            messagebox.showwarning("هشدار", "لطفا نام جدید بیماری را وارد کنید.")
            return

        try:
            cursor.execute("UPDATE Diseases SET name = ? WHERE id = ?", (new_name, self.selected_disease_id))
            conn.commit()
            messagebox.showinfo("موفق", "نام بیماری با موفقیت ویرایش شد!")
            self.load_diseases()
            self.clear_disease_form()
        except sqlite3.IntegrityError:
             messagebox.showerror("خطا", f"بیماری دیگری با نام '{new_name}' وجود دارد.")
        except Exception as e:
            messagebox.showerror("خطا", f"خطا در ویرایش بیماری: {str(e)}")

    def delete_disease(self):
        if self.selected_disease_id is None:
            messagebox.showwarning("هشدار", "لطفا ابتدا یک بیماری را از جدول انتخاب کنید.")
            return

        confirm = messagebox.askyesno("تایید حذف", f"آیا از حذف بیماری انتخاب شده مطمئن هستید؟\n(توجه: تمام ارتباطات این بیماری با داروها نیز حذف خواهد شد)")
        if confirm:
            try:
                # Check usage (optional, as ON DELETE CASCADE handles it, but good for user info)
                usage_count = cursor.execute("SELECT COUNT(*) FROM DrugDiseases WHERE disease_id = ?", (self.selected_disease_id,)).fetchone()[0]
                if usage_count > 0:
                     print(f"Note: Deleting disease ID {self.selected_disease_id} will also remove {usage_count} drug links due to CASCADE.")
                     # Optionally show a more prominent warning here

                # Delete the disease (CASCADE will handle DrugDiseases links)
                cursor.execute("DELETE FROM Diseases WHERE id = ?", (self.selected_disease_id,))
                conn.commit()
                messagebox.showinfo("موفق", "بیماری با موفقیت حذف شد!")
                self.load_diseases()
                self.clear_disease_form()
            except Exception as e:
                messagebox.showerror("خطا", f"خطا در حذف بیماری: {str(e)}")

    def load_diseases(self):
        # Clear existing treeview
        for row in self.disease_tree.get_children():
            self.disease_tree.delete(row)

        # Clear map and fetch new data
        self.disease_map.clear()
        diseases = cursor.execute("SELECT id, name FROM Diseases ORDER BY name").fetchall()

        disease_names_for_combobox = []
        for disease_id, disease_name in diseases:
            # Insert into treeview (name first for RTL)
            self.disease_tree.insert("", "end", values=(disease_name, disease_id))
            # Update map and list for combobox
            self.disease_map[disease_name] = disease_id
            disease_names_for_combobox.append(disease_name)

        # Update the combobox in the drug tab (if it exists yet)
        if hasattr(self, 'disease_combobox'):
             self.disease_combobox['values'] = disease_names_for_combobox
             if disease_names_for_combobox:
                 self.disease_combobox.current(0) # Select first item by default
             else:
                 self.disease_combobox.set('') # Clear if no diseases


    def on_disease_select(self, event):
        try:
            selected_item = self.disease_tree.selection()[0]
            item_values = self.disease_tree.item(selected_item, 'values')
            # Values are (name, id)
            self.selected_disease_id = int(item_values[1])
            self.disease_name_entry.delete(0, tk.END)
            self.disease_name_entry.insert(0, item_values[0])
        except (IndexError, ValueError):
            # Selection cleared or invalid data
            self.clear_disease_form()


    def clear_disease_form(self):
        self.selected_disease_id = None
        self.disease_name_entry.delete(0, tk.END)
        if self.disease_tree.selection():
            self.disease_tree.selection_remove(self.disease_tree.selection()[0])
    # -------------------------------------------------

    # -------------------- توابع گیاهان --------------------
    # Note: add_herb, edit_herb, delete_herb, on_herb_select, clear_herb_form, load_herbs
    # are now logically part of the Herb Tab setup, but remain class methods.

    def add_herb(self):
        try:
            data = {
                "id": self.herb_entries["کد گیاه"].get(), # Remove int() conversion
                "name": self.herb_entries["نام گیاه"].get(),
                "unit_price": float(self.herb_entries["قیمت واحد (تومان)"].get()),
                "shelf_location": self.herb_entries["محل قفسه"].get(),
                "current_weight": float(self.herb_entries["وزن (گرم)"].get()),
                "purchase_date": self.herb_entries["تاریخ خرید"].get(),
                "description": self.herb_entries["توضیحات"].get()
            }
            
            cursor.execute('''
                INSERT INTO Herbs (id, name, unit_price, shelf_location, current_weight, purchase_date, description)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', tuple(data.values()))
            conn.commit()
            self.load_herbs()
            messagebox.showinfo("موفق", "گیاه با موفقیت ثبت شد!")
            self.clear_herb_form() # Clear form after adding
        except sqlite3.IntegrityError:
             messagebox.showerror("خطا", f"کد گیاه '{data['id']}' قبلا ثبت شده است.")
        except Exception as e:
            messagebox.showerror("خطا", f"خطا در ثبت گیاه: {str(e)}")

    def edit_herb(self):
        if not self.selected_herb_id:
            messagebox.showwarning("هشدار", "لطفا ابتدا یک گیاه را از جدول انتخاب کنید.")
            return

        try:
            # Get data from entries (excluding ID, as it shouldn't be changed)
            updated_data = {
                "name": self.herb_entries["نام گیاه"].get(),
                "unit_price": float(self.herb_entries["قیمت واحد (تومان)"].get()),
                "shelf_location": self.herb_entries["محل قفسه"].get(),
                "current_weight": float(self.herb_entries["وزن (گرم)"].get()),
                "purchase_date": self.herb_entries["تاریخ خرید"].get(),
                "description": self.herb_entries["توضیحات"].get()
            }

            cursor.execute('''
                UPDATE Herbs 
                SET name = ?, unit_price = ?, shelf_location = ?, current_weight = ?, purchase_date = ?, description = ?
                WHERE id = ?
            ''', (
                updated_data["name"], updated_data["unit_price"], updated_data["shelf_location"],
                updated_data["current_weight"], updated_data["purchase_date"], updated_data["description"],
                self.selected_herb_id # Use the stored selected ID
            ))
            conn.commit()
            self.load_herbs()
            messagebox.showinfo("موفق", "اطلاعات گیاه با موفقیت ویرایش شد!")
            self.clear_herb_form() # Clear form after editing
        except Exception as e:
            messagebox.showerror("خطا", f"خطا در ویرایش گیاه: {str(e)}")

    def delete_herb(self):
        if not self.selected_herb_id:
            messagebox.showwarning("هشدار", "لطفا ابتدا یک گیاه را از جدول انتخاب کنید.")
            return

        confirm = messagebox.askyesno("تایید حذف", f"آیا از حذف گیاه با کد '{self.selected_herb_id}' مطمئن هستید؟")
        if confirm:
            try:
                # Check if herb is used in any drug composition
                usage_check = cursor.execute("SELECT COUNT(*) FROM DrugCompositions WHERE herb_id = ?", (self.selected_herb_id,)).fetchone()
                if usage_check and usage_check[0] > 0:
                    messagebox.showerror("خطا", f"امکان حذف گیاه '{self.selected_herb_id}' وجود ندارد زیرا در ترکیبات دارویی استفاده شده است.")
                    return

                cursor.execute("DELETE FROM Herbs WHERE id = ?", (self.selected_herb_id,))
                conn.commit()
                self.load_herbs()
                messagebox.showinfo("موفق", "گیاه با موفقیت حذف شد!")
                self.clear_herb_form() # Clear form after deleting
            except Exception as e:
                messagebox.showerror("خطا", f"خطا در حذف گیاه: {str(e)}")

    def on_herb_select(self, event):
        try:
            selected_item = self.herb_tree.selection()[0]
            item_values = self.herb_tree.item(selected_item, 'values')

            # item_values indices correspond to the REVERSED Treeview columns (RTL):
            # 0:description, 1:date, 2:total_price(formatted), 3:weight(formatted), 4:shelf, 5:unit_price(formatted), 6:name, 7:id
            
            self.selected_herb_id = item_values[7] # Store the ID (now at index 7)

            # Fetch raw data from DB for accurate editing (especially numbers)
            raw_data = cursor.execute("SELECT unit_price, current_weight FROM Herbs WHERE id = ?", (self.selected_herb_id,)).fetchone()
            if not raw_data: return

            # Load data into entries (using new indices)
            self.herb_entries["کد گیاه"].config(state='normal') # Make ID writable before changing
            self.herb_entries["کد گیاه"].delete(0, tk.END)
            self.herb_entries["کد گیاه"].insert(0, item_values[7]) # ID is index 7
            self.herb_entries["کد گیاه"].config(state='readonly') # Make ID readonly again

            self.herb_entries["نام گیاه"].delete(0, tk.END)
            self.herb_entries["نام گیاه"].insert(0, item_values[6]) # Name is index 6

            self.herb_entries["قیمت واحد (تومان)"].delete(0, tk.END)
            self.herb_entries["قیمت واحد (تومان)"].insert(0, raw_data[0] if raw_data[0] is not None else "") # Use raw price from DB

            self.herb_entries["محل قفسه"].delete(0, tk.END)
            self.herb_entries["محل قفسه"].insert(0, item_values[4]) # Shelf is index 4

            self.herb_entries["وزن (گرم)"].delete(0, tk.END)
            self.herb_entries["وزن (گرم)"].insert(0, raw_data[1] if raw_data[1] is not None else "") # Use raw weight from DB

            self.herb_entries["تاریخ خرید"].delete(0, tk.END)
            self.herb_entries["تاریخ خرید"].insert(0, item_values[1]) # Date is index 1

            self.herb_entries["توضیحات"].delete(0, tk.END)
            self.herb_entries["توضیحات"].insert(0, item_values[0]) # Description is index 0

        except IndexError:
            # No item selected or selection cleared
            self.clear_herb_form()

    def clear_herb_form(self):
        """Clears all entry fields in the herb form and resets selection."""
        self.selected_herb_id = None
        self.herb_entries["کد گیاه"].config(state='normal') # Make ID writable again
        for entry in self.herb_entries.values():
            entry.delete(0, tk.END)
        if self.herb_tree.selection(): # Deselect item in treeview
            self.herb_tree.selection_remove(self.herb_tree.selection()[0])


    def load_herbs(self):
        for row in self.herb_tree.get_children():
            self.herb_tree.delete(row)
        # Fetch data in the original order from DB
        herbs = cursor.execute("SELECT id, name, unit_price, shelf_location, current_weight, purchase_date, description FROM Herbs").fetchall()
        for herb in herbs:
            # Original DB indices: 0:id, 1:name, 2:unit_price, 3:shelf, 4:weight, 5:date, 6:description
            
            # Format numbers (unit_price, weight, total_price)
            try:
                unit_price_val = float(herb[2]) if herb[2] is not None else 0.0 # Price per unit (assumed kg)
                weight_val_grams = float(herb[4]) if herb[4] is not None else 0.0 # Weight in grams
                
                # Calculate total price based on kg (unit_price * weight_in_kg)
                total_price_val = unit_price_val * (weight_val_grams / 1000.0) 

                # Format unit_price with thousands separator
                if unit_price_val.is_integer():
                    unit_price_str = f"{int(unit_price_val):,}" # Add comma separator for integer
                else:
                    unit_price_str = f"{unit_price_val:,.2f}" # Add comma separator and keep decimals

                # Format weight (still display in grams)
                if weight_val_grams.is_integer():
                    weight_str = str(int(weight_val_grams))
                else:
                    weight_str = f"{weight_val_grams:.2f}" # Keep decimals if present
                
                # Format total_price (calculated based on kg) with thousands separator
                if total_price_val.is_integer():
                    total_price_str = f"{int(total_price_val):,}" # Add comma separator for integer
                else:
                    total_price_str = f"{total_price_val:,.2f}" # Add comma separator and keep decimals

            except (ValueError, TypeError):
                unit_price_str = "N/A"
                weight_str = "N/A" # Weight is still displayed in grams
                total_price_str = "N/A"

            # Create values tuple in REVERSED (RTL) order for Treeview insertion
            values_rtl = (
                herb[6],            # description
                herb[5],            # date
                total_price_str,    # formatted total_price
                weight_str,         # formatted weight
                herb[3],            # shelf
                unit_price_str,     # formatted unit_price
                herb[1],            # name
                herb[0]             # id
            )
            self.herb_tree.insert("", "end", values=values_rtl, tags=('herb_row',)) # Insert RTL ordered values

        # Optional: Apply alternating row colors for better readability
        self.herb_tree.tag_configure('oddrow', background='#E8E8E8')
        self.herb_tree.tag_configure('evenrow', background='#FFFFFF')
        for i, item_id in enumerate(self.herb_tree.get_children()):
            if i % 2 == 0:
                self.herb_tree.item(item_id, tags=('herb_row', 'evenrow'))
            else:
                self.herb_tree.item(item_id, tags=('herb_row', 'oddrow'))

    # -------------------- توابع تولید دارو --------------------
    def add_composition(self):
        # باز کردن پنجره جدید برای افزودن ترکیب
        self.comp_window = tk.Toplevel()
        self.comp_window.title("افزودن ترکیب")
        
        ttk.Label(self.comp_window, text="کد گیاه:").grid(row=0, column=0)
        self.herb_id_entry = ttk.Entry(self.comp_window)
        self.herb_id_entry.grid(row=0, column=1)
        
        ttk.Label(self.comp_window, text="وزن مورد نیاز (گرم):").grid(row=1, column=0)
        self.herb_weight_entry = ttk.Entry(self.comp_window)
        self.herb_weight_entry.grid(row=1, column=1)
        
        ttk.Button(self.comp_window, text="افزودن", command=self.save_composition).grid(row=2, column=0, columnspan=2)
    '''
    def save_composition(self):
        herb_id = self.herb_id_entry.get() # Remove int() conversion
        weight = float(self.herb_weight_entry.get())
        
        # بررسی موجودی
        herb = cursor.execute("SELECT current_weight, unit_price FROM Herbs WHERE id = ?", (herb_id,)).fetchone()
        if not herb:
            messagebox.showerror("خطا", "گیاه یافت نشد!")
            return
        if herb[0] < weight:
            messagebox.showerror("خطا", "موجودی ناکافی!")
            return
        
        subtotal = weight * herb[1]
        self.compositions.append((herb_id, weight, subtotal))
        self.comp_window.destroy()
        messagebox.showinfo("موفق", "ترکیب افزوده شد!")
    '''

    def save_composition(self):
        try:
            herb_id = self.herb_id_entry.get()
            weight_grams = float(self.herb_weight_entry.get())  # وزن ورودی بر حسب گرم
            
            # تبدیل به کیلوگرم برای محاسبه هزینه
            weight_kg = weight_grams / 1000
            
            # بررسی موجودی (هنوز بر حسب گرم در دیتابیس ذخیره می‌شود)
            cursor.execute("SELECT current_weight, unit_price FROM Herbs WHERE id = ?", (herb_id,))
            herb_data = cursor.fetchone()
            
            if not herb_data:
                messagebox.showerror("خطا", "کد گیاه نامعتبر است!")
                return
            
            if herb_data[0] < weight_grams:  # مقایسه با موجودی بر حسب گرم
                messagebox.showerror("خطا", f"موجودی ناکافی! موجودی فعلی: {herb_data[0]} گرم")
                return
            
            # محاسبه هزینه بر اساس کیلوگرم
            subtotal = weight_kg * herb_data[1]  # unit_price در دیتابیس بر اساس کیلوگرم است
            
            # ذخیره در دیتابیس (وزن مصرفی همچنان بر حسب گرم ذخیره می‌شود)
            cursor.execute('''
            INSERT INTO DrugCompositions (drug_id, herb_id, weight_used, subtotal)
            VALUES (?, ?, ?, ?)
            ''', (drug_id, herb_id, weight_grams, subtotal))
            
            # کاهش موجودی انبار (بر حسب گرم)
            cursor.execute('''
            UPDATE Herbs SET current_weight = current_weight - ? WHERE id = ?
            ''', (weight_grams, herb_id))
            
            conn.commit()
            messagebox.showinfo("موفق", "ترکیب با موفقیت اضافه شد!")
            self.add_window.destroy()
            self.load_compositions(drug_id, parent_window)
            self.load_herbs()  # به‌روزرسانی موجودی انبار
            
        except ValueError:
            messagebox.showerror("خطا", "وزن باید یک عدد معتبر باشد!")
        except Exception as e:
            conn.rollback()
            messagebox.showerror("خطا", f"خطا در افزودن ترکیب: {str(e)}")




    def produce_drug(self):
        # Get data from entries
        try:
            drug_id_str = self.drug_id_entry.get()
            drug_name = self.drug_name_entry.get()
            selected_disease_name = self.disease_combobox.get()

            if not drug_id_str or not drug_name:
                 messagebox.showwarning("هشدار", "لطفا کد و نام دارو را وارد کنید.")
                 return
            if not selected_disease_name:
                 messagebox.showwarning("هشدار", "لطفا بیماری مرتبط با دارو را انتخاب کنید.")
                 return
            if not self.compositions:
                 messagebox.showwarning("هشدار", "لطفا حداقل یک ترکیب برای دارو اضافه کنید.")
                 return

            drug_id = int(drug_id_str)
            production_date = datetime.now().strftime("%Y-%m-%d")

            # Get the selected disease ID from the map
            selected_disease_id = self.disease_map.get(selected_disease_name)
            if selected_disease_id is None:
                 # This shouldn't happen if combobox is populated correctly
                 messagebox.showerror("خطای داخلی", "کد بیماری انتخاب شده یافت نشد.")
                 return

        except ValueError:
             messagebox.showerror("خطا", "کد دارو باید یک عدد صحیح باشد.")
             return
        except Exception as e:
             messagebox.showerror("خطا", f"خطای ورودی: {str(e)}")
             return

        try:
            # --- Database Operations ---
            # Start transaction
            conn.execute("BEGIN TRANSACTION") # Use conn directly for transaction control

            # 1. Insert into Drugs table
            cursor.execute('''
                INSERT INTO Drugs (id, name, production_date)
                VALUES (?, ?, ?)
            ''', (drug_id, drug_name, production_date))

            # 2. Insert into DrugCompositions and update Herb inventory
            for comp in self.compositions:
                # Check inventory again just before update (important in multi-user scenarios)
                herb_check = cursor.execute("SELECT current_weight FROM Herbs WHERE id = ?", (comp[0],)).fetchone()
                if not herb_check or herb_check[0] < comp[1]:
                     raise ValueError(f"موجودی گیاه با کد '{comp[0]}' برای تولید کافی نیست.") # Raise error to trigger rollback

                cursor.execute('''
                    INSERT INTO DrugCompositions (drug_id, herb_id, weight_used, subtotal)
                    VALUES (?, ?, ?, ?)
                ''', (drug_id, comp[0], comp[1], comp[2]))

                # Decrease herb inventory
                cursor.execute('''
                    UPDATE Herbs SET current_weight = current_weight - ? WHERE id = ?
                ''', (comp[1], comp[0]))

            # 3. Insert into DrugDiseases table (New step)
            cursor.execute('''
                INSERT INTO DrugDiseases (drug_id, disease_id)
                VALUES (?, ?)
            ''', (drug_id, selected_disease_id))

            # Commit transaction
            conn.commit()

            messagebox.showinfo("موفق", f"داروی '{drug_name}' با موفقیت تولید و به بیماری '{selected_disease_name}' مرتبط شد!")
            self.compositions = [] # Clear compositions list
            # Clear drug form fields
            self.drug_id_entry.delete(0, tk.END)
            self.drug_name_entry.delete(0, tk.END)
            self.disease_combobox.set('') # Reset combobox (or set to default)
            if self.disease_combobox['values']:
                 self.disease_combobox.current(0)


            self.load_herbs() # Refresh herb list as inventory changed
            # Optionally refresh reports if needed
            self.generate_report()

        except ValueError as ve: # Catch specific inventory error
             conn.rollback()
             messagebox.showerror("خطای موجودی", str(ve))
        except sqlite3.IntegrityError as ie:
             conn.rollback()
             if "Drugs.id" in str(ie):
                 messagebox.showerror("خطا", f"دارویی با کد '{drug_id}' قبلا ثبت شده است.")
             elif "DrugDiseases" in str(ie):
                  messagebox.showerror("خطا", f"این دارو قبلا به بیماری '{selected_disease_name}' مرتبط شده است.")
             else:
                  messagebox.showerror("خطای پایگاه داده", f"خطای یکپارچگی: {str(ie)}")
        except Exception as e:
            conn.rollback() # Rollback on any other error during transaction
            messagebox.showerror("خطا", f"خطا در تولید دارو: {str(e)}")

    # -------------------- توابع گزارشات --------------------
    def setup_report_tab(self):
        report_frame = ttk.LabelFrame(self.report_tab, text="خلاصه وضعیت انبار و تولید")
        report_frame.pack(pady=20, padx=20, fill="both", expand=True) # Fill and expand

        # StringVars for dynamic labels
        self.total_herb_types_var = tk.StringVar(value="...")
        self.total_herb_weight_var = tk.StringVar(value="...")
        self.total_inventory_value_var = tk.StringVar(value="...")
        self.total_drugs_produced_var = tk.StringVar(value="...")

        # Labels for report data (Label on right - col 1, Value on left - col 0)
        ttk.Label(report_frame, text="تعداد کل انواع گیاهان:", anchor=tk.E).grid(row=0, column=1, padx=5, pady=5, sticky="e")
        ttk.Label(report_frame, textvariable=self.total_herb_types_var, anchor=tk.W).grid(row=0, column=0, padx=5, pady=5, sticky="w") # Value aligned left

        ttk.Label(report_frame, text="مجموع وزن کل گیاهان (گرم):", anchor=tk.E).grid(row=1, column=1, padx=5, pady=5, sticky="e")
        ttk.Label(report_frame, textvariable=self.total_herb_weight_var, anchor=tk.W).grid(row=1, column=0, padx=5, pady=5, sticky="w")

        ttk.Label(report_frame, text="ارزش کل موجودی انبار (تومان):", anchor=tk.E).grid(row=2, column=1, padx=5, pady=5, sticky="e")
        ttk.Label(report_frame, textvariable=self.total_inventory_value_var, anchor=tk.W).grid(row=2, column=0, padx=5, pady=5, sticky="w")

        ttk.Label(report_frame, text="تعداد کل داروهای تولید شده:", anchor=tk.E).grid(row=3, column=1, padx=5, pady=5, sticky="e")
        ttk.Label(report_frame, textvariable=self.total_drugs_produced_var, anchor=tk.W).grid(row=3, column=0, padx=5, pady=5, sticky="w")

        # Configure column weights for resizing
        report_frame.columnconfigure(0, weight=1) # Value column expands
        report_frame.columnconfigure(1, weight=0) # Label column fixed width (prevents excessive spacing)

        # Refresh button (Centered below)
        refresh_btn = ttk.Button(report_frame, text="به‌روزرسانی گزارش", command=self.generate_report)
        refresh_btn.grid(row=4, column=0, columnspan=2, pady=15)

        # Generate initial report
        self.generate_report()

    def generate_report(self):
        try:
            # 1. Total Herb Types
            cursor.execute("SELECT COUNT(*) FROM Herbs")
            herb_count = cursor.fetchone()[0]
            self.total_herb_types_var.set(f"{herb_count:,}")

            # 2. Total Herb Weight
            cursor.execute("SELECT SUM(current_weight) FROM Herbs")
            total_weight_grams = cursor.fetchone()[0]
            if total_weight_grams is None: total_weight_grams = 0.0
            # Format weight
            if total_weight_grams.is_integer():
                 total_weight_str = f"{int(total_weight_grams):,}"
            else:
                 total_weight_str = f"{total_weight_grams:,.2f}"
            self.total_herb_weight_var.set(total_weight_str)


            # 3. Total Inventory Value
            cursor.execute("SELECT SUM(unit_price * (current_weight / 1000.0)) FROM Herbs")
            total_value = cursor.fetchone()[0]
            if total_value is None: total_value = 0.0
             # Format value
            if total_value.is_integer():
                 total_value_str = f"{int(total_value):,}"
            else:
                 total_value_str = f"{total_value:,.2f}"
            self.total_inventory_value_var.set(total_value_str)

            # 4. Total Drugs Produced
            cursor.execute("SELECT COUNT(*) FROM Drugs")
            drug_count = cursor.fetchone()[0]
            self.total_drugs_produced_var.set(f"{drug_count:,}")

        except Exception as e:
            messagebox.showerror("خطای گزارش", f"خطا در تولید گزارش: {str(e)}")
            self.total_herb_types_var.set("خطا")
            self.total_herb_weight_var.set("خطا")
            self.total_inventory_value_var.set("خطا")
            self.total_drugs_produced_var.set("خطا")

    # Note: add_composition, save_composition, produce_drug are logically part of the
    # Drug Tab setup, but remain class methods.

    # Note: setup_report_tab and generate_report are logically part of the
    # Report Tab setup, but remain class methods.

# -------------------- اجرای برنامه --------------------
if __name__ == "__main__":
    root = tk.Tk()
    app = HerbalFactoryApp(root)
    root.mainloop()
    conn.close()
