import tkinter as tk
import sqlite3
import os
from datetime import datetime
from tkinter import ttk, messagebox, filedialog, simpledialog
import hashlib # For password hashing


# --- Login Window Class ---
class LoginWindow(tk.Toplevel):
    def __init__(self, parent, conn, on_success):
        super().__init__(parent)
        self.title("ورود به سیستم")
        self.geometry("300x150")
        self.conn = conn
        self.cursor = conn.cursor()
        self.on_success = on_success
        self.parent = parent # Keep track of the root window

        # Prevent closing the main window if login is closed
        self.protocol("WM_DELETE_WINDOW", self.on_closing)

        # Center the window
        parent.update_idletasks()
        x = parent.winfo_screenwidth() // 2 - 150
        y = parent.winfo_screenheight() // 2 - 75
        self.geometry(f"+{x}+{y}")

        self.resizable(False, False)
        self.transient(parent) # Keep on top of the main window
        self.grab_set() # Modal

        # Widgets
        ttk.Label(self, text="نام کاربری:").grid(row=0, column=0, padx=10, pady=10, sticky="e")
        self.username_entry = ttk.Entry(self, justify=tk.RIGHT)
        self.username_entry.grid(row=0, column=1, padx=10, pady=10, sticky="ew")

        ttk.Label(self, text="رمز عبور:").grid(row=1, column=0, padx=10, pady=5, sticky="e")
        self.password_entry = ttk.Entry(self, show="*", justify=tk.RIGHT)
        self.password_entry.grid(row=1, column=1, padx=10, pady=5, sticky="ew")

        login_button = ttk.Button(self, text="ورود", command=self.check_login)
        login_button.grid(row=2, column=0, columnspan=2, pady=10)

        self.grid_columnconfigure(1, weight=1)

        # Bind Enter key
        self.bind("<Return>", lambda event: self.check_login())
        self.username_entry.focus()

        # Explicitly lift and focus the window
        self.lift()
        self.focus_force()

    def hash_password(self, password):
        """Hashes the password using SHA-256."""
        return hashlib.sha256(password.encode()).hexdigest()

    def check_login(self):
        username = self.username_entry.get().strip()
        password = self.password_entry.get().strip()

        if not username or not password:
            messagebox.showwarning("خطا", "نام کاربری و رمز عبور نمی‌توانند خالی باشند.", parent=self)
            return

        hashed_password = self.hash_password(password)

        self.cursor.execute('''
            SELECT password, role, is_active FROM Users WHERE username = ?
        ''', (username,))
        result = self.cursor.fetchone()

        if result and result[0] == self.hash_password(password):
            if not result[2]:  # is_active == 0
                messagebox.showerror("خطا", "حساب کاربری غیرفعال است!")
                return

            # ذخیره نقش کاربر در برنامه اصلی
            self.parent.user_role = result[1]
            self.on_success()
        else:
            messagebox.showerror("خطا", "نام کاربری یا رمز عبور اشتباه است!")

        

    def on_closing(self):
        """Handle the closing of the login window."""
        self.parent.destroy() # Close the main application if login is cancelled


# --- Database Setup ---
def setup_database():
    """Setup the SQLite database and create required tables if they don't exist."""
    db_path = 'herbal_factory_asl1.db'
    
    # Check if the database already exists
    db_exists = os.path.exists(db_path)
    
    # Connect to database (will create it if it doesn't exist)
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Create tables if they don't exist
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS Herbs (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        unit_price REAL NOT NULL,
        shelf_location TEXT,
        current_weight REAL NOT NULL,
        purchase_date TEXT,
        description TEXT
    )
    ''')

    cursor.execute('''
    CREATE TABLE IF NOT EXISTS Drugs (
        id INTEGER PRIMARY KEY,
        name TEXT NOT NULL,
        production_date TEXT,
        raw_materials_cost REAL,
        loan_interest_cost REAL,
        container_label_cost REAL,
        workshop_overhead_cost REAL,
        total_workshop_cost REAL,
        representative_price REAL,
        wholesale_price REAL,
        retail_price REAL
    )
    ''')

    cursor.execute('''
    CREATE TABLE IF NOT EXISTS DrugCompositions (
        drug_id INTEGER,
        herb_id TEXT,
        weight_used REAL,
        subtotal REAL,
        FOREIGN KEY(drug_id) REFERENCES Drugs(id),
        FOREIGN KEY(herb_id) REFERENCES Herbs(id)
    )
    ''')

    # Use TEXT for password hash
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS Users (
        username TEXT PRIMARY KEY,
        password TEXT NOT NULL                         
    )
    ''')


    # Check if Users table is empty and add default user if needed
    cursor.execute("SELECT COUNT(*) FROM Users")
    user_count = cursor.fetchone()[0]
    if user_count == 0:
        default_username = "admin"
        default_password = "admin123" # You should change this
        hashed_password = hashlib.sha256(default_password.encode()).hexdigest()
        try:
            cursor.execute("INSERT INTO Users (username, password) VALUES (?, ?)", (default_username, hashed_password))
            conn.commit()
            print(f"Default user '{default_username}' with password '{default_password}' created.")
        except sqlite3.IntegrityError:
            # Should not happen if count is 0, but handle just in case
            print("Default user already exists.")



    # ارتقای جدول Users (اگر از قبل وجود دارد)
    cursor.execute("PRAGMA table_info(Users)")
    columns = [col[1] for col in cursor.fetchall()]
    
    if 'role' not in columns:
        cursor.execute('''
            ALTER TABLE Users 
            ADD COLUMN role TEXT NOT NULL DEFAULT 'user'
            CHECK (role IN ('admin', 'manager', 'user'))
        ''')
    
    if 'is_active' not in columns:
        cursor.execute('''
            ALTER TABLE Users 
            ADD COLUMN is_active INTEGER DEFAULT 1
        ''')

    # کاربر پیش‌فرض ادمین (اگر جدول خالی است)
    cursor.execute("SELECT COUNT(*) FROM Users")
    if cursor.fetchone()[0] == 0:
        hashed_password = hashlib.sha256("admin123".encode()).hexdigest()
        cursor.execute('''
            INSERT INTO Users (username, password, role, is_active)
            VALUES (?, ?, ?, ?)
        ''', ('admin', hashed_password, 'admin', 1))



    cursor.execute('''
    CREATE TABLE IF NOT EXISTS Diseases (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL UNIQUE 
    )
    ''')

    cursor.execute('''
    CREATE TABLE IF NOT EXISTS DrugDiseases (
        drug_id INTEGER,
        disease_id INTEGER,
        FOREIGN KEY(drug_id) REFERENCES Drugs(id) ON DELETE CASCADE,
        FOREIGN KEY(disease_id) REFERENCES Diseases(id) ON DELETE CASCADE,
        PRIMARY KEY (drug_id, disease_id) 
    )
    ''')

    



    # Commit the changes
    conn.commit()
    
    return conn

# Initialize database
conn = setup_database()



class UserManager:
    def __init__(self, root, conn):
        self.root = root
        self.conn = conn
        self.cursor = conn.cursor()
        self.setup_ui()

    def setup_ui(self):
        self.window = tk.Toplevel(self.root)
        self.window.title("مدیریت کاربران")
        self.window.geometry("800x500")

        # Treeview برای نمایش کاربران
        self.tree = ttk.Treeview(self.window, columns=("username", "role", "is_active"), show="headings")
        self.tree.heading("username", text="نام کاربری")
        self.tree.heading("role", text="نقش")
        self.tree.heading("is_active", text="وضعیت")
        self.tree.pack(fill="both", expand=True, padx=10, pady=10)

        # فریم برای دکمه‌ها
        btn_frame = tk.Frame(self.window)
        btn_frame.pack(pady=10)

        ttk.Button(btn_frame, text="افزودن کاربر", command=self.add_user).pack(side="right", padx=5)
        ttk.Button(btn_frame, text="ویرایش کاربر", command=self.edit_user).pack(side="right", padx=5)
        ttk.Button(btn_frame, text="تغییر رمز", command=self.change_password).pack(side="right", padx=5)
        ttk.Button(btn_frame, text="حذف/غیرفعال", command=self.toggle_user).pack(side="right", padx=5)

        self.load_users()

    def load_users(self):
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        self.cursor.execute("SELECT username, role, is_active FROM Users")
        for user in self.cursor.fetchall():
            status = "فعال" if user[2] else "غیرفعال"
            self.tree.insert("", "end", values=(user[0], user[1], status))

    def add_user(self):
        add_window = tk.Toplevel(self.window)
        add_window.title("افزودن کاربر جدید")
        
        tk.Label(add_window, text="نام کاربری:").grid(row=0, column=0, padx=5, pady=5)
        username_entry = tk.Entry(add_window)
        username_entry.grid(row=0, column=1, padx=5, pady=5)

        tk.Label(add_window, text="رمز عبور:").grid(row=1, column=0, padx=5, pady=5)
        password_entry = tk.Entry(add_window, show="*")
        password_entry.grid(row=1, column=1, padx=5, pady=5)

        tk.Label(add_window, text="تکرار رمز:").grid(row=2, column=0, padx=5, pady=5)
        confirm_entry = tk.Entry(add_window, show="*")
        confirm_entry.grid(row=2, column=1, padx=5, pady=5)

        tk.Label(add_window, text="نقش:").grid(row=3, column=0, padx=5, pady=5)
        role_combobox = ttk.Combobox(add_window, values=["admin", "manager", "user"])
        role_combobox.grid(row=3, column=1, padx=5, pady=5)
        role_combobox.current(2)

        def save_user():
            if password_entry.get() != confirm_entry.get():
                messagebox.showerror("خطا", "رمزهای عبور مطابقت ندارند!")
                return
            
            hashed_password = hashlib.sha256(password_entry.get().encode()).hexdigest()
            
            try:
                self.cursor.execute('''
                    INSERT INTO Users (username, password, role, is_active)
                    VALUES (?, ?, ?, ?)
                ''', (username_entry.get(), hashed_password, role_combobox.get(), 1))
                self.conn.commit()
                messagebox.showinfo("موفق", "کاربر با موفقیت اضافه شد!")
                add_window.destroy()
                self.load_users()
            except sqlite3.IntegrityError:
                messagebox.showerror("خطا", "نام کاربری قبلاً استفاده شده است!")

        ttk.Button(add_window, text="ذخیره", command=save_user).grid(row=4, columnspan=2, pady=10)

     









class HerbalFactoryApp:
    def __init__(self, root, conn):
        self.root = root
        self.root.title("کارگاه تولید داروهای گیاهی")
        self.root.geometry("1400x800")
        self.conn = conn
        self.cursor = conn.cursor()
        self.user_role = None  # این متغیر پس از لاگین مقداردهی می‌شود

        # Create styles
        self.setup_styles()
        
        # Create notebook for tabs
        self.notebook = ttk.Notebook(root)
        
        # Create tabs
        self.herb_tab = ttk.Frame(self.notebook)
        self.disease_tab = ttk.Frame(self.notebook)
        self.drug_tab = ttk.Frame(self.notebook)
        self.report_tab = ttk.Frame(self.notebook)
        
        # Add tabs in proper order for RTL appearance
        self.notebook.add(self.herb_tab, text="مدیریت مفردات گیاهی")
        self.notebook.add(self.disease_tab, text="مدیریت بیماری‌ها")
        self.notebook.add(self.drug_tab, text="تولید دارو")
        self.notebook.add(self.report_tab, text="گزارشات")
        
        self.notebook.pack(expand=True, fill="both")
        
        # Initialize tab managers
        self.herb_manager = HerbTabManager(self.herb_tab, self.conn)
        self.disease_manager = DiseaseTabManager(self.disease_tab, self.conn)
        self.drug_manager = DrugTabManager(self.drug_tab, self.conn, self.disease_manager)
        self.report_manager = ReportTabManager(self.report_tab, self.conn)
        
        # Refresh data initially
        self.refresh_all_data()
        
        # Bind tab change event to refresh data
        self.notebook.bind("<<NotebookTabChanged>>", self.on_tab_changed)
    
    def setup_menu(self):
        menubar = tk.Menu(self.root)
        
        # منوی کاربر
        user_menu = tk.Menu(menubar, tearoff=0)
        user_menu.add_command(label="تغییر رمز عبور", command=self.change_password)
        
        if self.user_role == 'admin':
            user_menu.add_command(label="مدیریت کاربران", command=self.manage_users)
        
        menubar.add_cascade(label="کاربر", menu=user_menu)
        self.root.config(menu=menubar)
    
    def manage_users(self):
        UserManager(self.root, self.conn)
    
    def change_password(self):
        # پیاده‌سازی مشابه گام ۳
        pass

    def setup_styles(self):
        """Set up custom styles for the application"""
        style = ttk.Style()
        
        # Define colors
        primary_color = "#3B82F6"  # Blue
        secondary_color = "#10B981"  # Green
        accent_color = "#F97316"  # Orange
        
        # Configure styles
        style.configure("TButton", 
                        background=primary_color, 
                        foreground="red", 
                        padding=5, 
                        font=("Arial", 10))
        
        style.map("TButton",
                  background=[("active", secondary_color), ("disabled", "#cccccc")])
        
        style.configure("TLabel", 
                        font=("Arial", 10), 
                        padding=2)
        
        style.configure("TFrame", 
                        background="#f8fafc")
        
        style.configure("TLabelframe", 
                        background="#f8fafc")
        
        style.configure("TLabelframe.Label", 
                        font=("Arial", 10, "bold"))
        
        style.configure("Heading.TLabel", 
                        font=("Arial", 12, "bold"), 
                        foreground=primary_color)
        
        # Treeview styles
        style.configure("Treeview",
                        background="#ffffff",
                        fieldbackground="#ffffff",
                        font=("Arial", 9))
        
        style.configure("Treeview.Heading",
                        font=("Arial", 10, "bold"),
                        background="#e2e8f0")
        
        style.map("Treeview",
                  background=[("selected", primary_color)],
                  foreground=[("selected", "white")])
    
    def refresh_all_data(self):
        """Refresh data in all tabs"""
        self.herb_manager.load_herbs()
        self.disease_manager.load_diseases()
        self.drug_manager.load_produced_drugs()
        self.drug_manager.update_disease_combobox(self.disease_manager.disease_map)
        self.report_manager.generate_report()
    
    def on_tab_changed(self, event):
        """Handle tab change event"""
        current_tab = self.notebook.index(self.notebook.select())
        
        # Refresh data based on which tab is selected
        if current_tab == 0:  # Herb tab
            self.herb_manager.load_herbs()
        elif current_tab == 1:  # Disease tab
            self.disease_manager.load_diseases()
        elif current_tab == 2:  # Drug tab
            self.drug_manager.load_produced_drugs()
            self.drug_manager.update_disease_combobox(self.disease_manager.disease_map)
        elif current_tab == 3:  # Report tab
            self.report_manager.generate_report()


class HerbTabManager:
    def __init__(self, parent, conn):
        self.parent = parent
        self.conn = conn
        self.cursor = conn.cursor()
        self.selected_herb_id = None
        
        self.setup_herb_tab_content()
    
    def setup_herb_tab_content(self):
        # Form frame for adding herbs
        self.herb_frame = ttk.LabelFrame(self.parent, text="ثبت گیاه جدید")
        self.herb_frame.pack(pady=10, padx=10, fill="x")

        labels = ["کد گیاه:", "نام گیاه:", "قیمت واحد (تومان):", "محل قفسه:", "وزن (گرم):", "تاریخ خرید:", "توضیحات:"]
        self.herb_entries = {}
        
        for i, label_text in enumerate(labels):
            label_widget = ttk.Label(self.herb_frame, text=label_text, anchor=tk.E)
            label_widget.grid(row=i, column=1, padx=5, pady=5, sticky=tk.E)
            
            entry = ttk.Entry(self.herb_frame, justify=tk.RIGHT)
            entry.grid(row=i, column=0, padx=5, pady=5, sticky=tk.W+tk.E)
            self.herb_entries[label_text.split(":")[0]] = entry

        # Adjust grid weights for resizing
        self.herb_frame.columnconfigure(0, weight=1)
        self.herb_frame.columnconfigure(1, weight=0)

        # Button frame for herb management
        self.herb_button_frame = ttk.Frame(self.herb_frame)
        self.herb_button_frame.grid(row=len(labels), column=0, columnspan=2, pady=10)

        # Management buttons
        self.add_herb_btn = ttk.Button(self.herb_button_frame, text="ذخیره گیاه", command=self.add_herb)
        self.add_herb_btn.grid(row=0, column=4, padx=5)
        
        self.edit_herb_btn = ttk.Button(self.herb_button_frame, text="ویرایش گیاه", command=self.edit_herb)
        self.edit_herb_btn.grid(row=0, column=3, padx=5)
        
        self.delete_herb_btn = ttk.Button(self.herb_button_frame, text="حذف گیاه", command=self.delete_herb)
        self.delete_herb_btn.grid(row=0, column=2, padx=5)

        self.clear_herb_form_btn = ttk.Button(self.herb_button_frame, text="پاک کردن فرم", command=self.clear_herb_form)
        self.clear_herb_form_btn.grid(row=0, column=1, padx=5)

        # Search frame
        self.search_frame = ttk.Frame(self.parent)
        self.search_frame.pack(pady=5, padx=10, fill="x")
        
        ttk.Label(self.search_frame, text="جستجو:").pack(side=tk.RIGHT, padx=5)
        self.search_entry = ttk.Entry(self.search_frame, justify=tk.RIGHT)
        self.search_entry.pack(side=tk.RIGHT, padx=5, fill=tk.X, expand=True)
        self.search_entry.bind("<KeyRelease>", self.filter_herbs)
        
        # Treeview for herbs display
        columns_rtl = ("description", "date", "total_price", "weight", "shelf", "unit_price", "name", "id")
        self.herb_tree = ttk.Treeview(self.parent, columns=columns_rtl, show="headings")
        
        # Configure column headings
        self.herb_tree.heading("description", text="توضیحات")
        self.herb_tree.heading("date", text="تاریخ خرید")
        self.herb_tree.heading("total_price", text="قیمت محصول")
        self.herb_tree.heading("weight", text="وزن (گرم)")
        self.herb_tree.heading("shelf", text="محل قفسه")
        self.herb_tree.heading("unit_price", text="قیمت واحد")
        self.herb_tree.heading("name", text="نام")
        self.herb_tree.heading("id", text="کد")

        # Configure column widths and alignment
        self.herb_tree.column("description", width=250, anchor=tk.E)
        self.herb_tree.column("date", width=100, anchor=tk.CENTER)
        self.herb_tree.column("total_price", width=120, anchor=tk.E)
        self.herb_tree.column("weight", width=100, anchor=tk.E)
        self.herb_tree.column("shelf", width=100, anchor=tk.E)
        self.herb_tree.column("unit_price", width=100, anchor=tk.E)
        self.herb_tree.column("name", width=150, anchor=tk.E)
        self.herb_tree.column("id", width=80, anchor=tk.CENTER)

        # Add scrollbar
        herb_scrollbar = ttk.Scrollbar(self.parent, orient="vertical", command=self.herb_tree.yview)
        self.herb_tree.configure(yscrollcommand=herb_scrollbar.set)
        
        # Pack treeview and scrollbar
        herb_scrollbar.pack(side=tk.LEFT, fill="y")
        self.herb_tree.pack(side=tk.RIGHT, fill="both", expand=True, padx=10, pady=(0, 10))
        
        # Bind selection event
        self.herb_tree.bind("<<TreeviewSelect>>", self.on_herb_select)
        
        # Load initial data
        self.load_herbs()
        
        # Apply alternating row colors
        self.herb_tree.tag_configure('oddrow', background='#f3f4f6')
        self.herb_tree.tag_configure('evenrow', background='#ffffff')
    
    def add_herb(self):
        try:
            data = {
                "id": self.herb_entries["کد گیاه"].get().strip(),
                "name": self.herb_entries["نام گیاه"].get().strip(),
                "unit_price": float(self.herb_entries["قیمت واحد (تومان)"].get()),
                "shelf_location": self.herb_entries["محل قفسه"].get(),
                "current_weight": float(self.herb_entries["وزن (گرم)"].get()),
                "purchase_date": self.herb_entries["تاریخ خرید"].get(),
                "description": self.herb_entries["توضیحات"].get()
            }
            
            # Validate data
            if not data["id"]:
                messagebox.showwarning("هشدار", "کد گیاه نمی‌تواند خالی باشد")
                return
                
            if not data["name"]:
                messagebox.showwarning("هشدار", "نام گیاه نمی‌تواند خالی باشد")
                return
            
            self.cursor.execute('''
                INSERT INTO Herbs (id, name, unit_price, shelf_location, current_weight, purchase_date, description)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', tuple(data.values()))
            
            self.conn.commit()
            self.load_herbs()
            messagebox.showinfo("موفق", "گیاه با موفقیت ثبت شد!")
            self.clear_herb_form()
            
        except sqlite3.IntegrityError:
            messagebox.showerror("خطا", f"کد گیاه '{data['id']}' قبلا ثبت شده است.")
        except ValueError:
            messagebox.showerror("خطا", "لطفاً مقادیر عددی را به درستی وارد کنید.")
        except Exception as e:
            messagebox.showerror("خطا", f"خطا در ثبت گیاه: {str(e)}")

    def edit_herb(self):
        if not self.selected_herb_id:
            messagebox.showwarning("هشدار", "لطفا ابتدا یک گیاه را از جدول انتخاب کنید.")
            return

        try:
            # Get data from entries (excluding ID, as it shouldn't be changed)
            updated_data = {
                "name": self.herb_entries["نام گیاه"].get().strip(),
                "unit_price": float(self.herb_entries["قیمت واحد (تومان)"].get()),
                "shelf_location": self.herb_entries["محل قفسه"].get(),
                "current_weight": float(self.herb_entries["وزن (گرم)"].get()),
                "purchase_date": self.herb_entries["تاریخ خرید"].get(),
                "description": self.herb_entries["توضیحات"].get()
            }

            # Validate data
            if not updated_data["name"]:
                messagebox.showwarning("هشدار", "نام گیاه نمی‌تواند خالی باشد")
                return

            self.cursor.execute('''
                UPDATE Herbs 
                SET name = ?, unit_price = ?, shelf_location = ?, current_weight = ?, purchase_date = ?, description = ?
                WHERE id = ?
            ''', (
                updated_data["name"], updated_data["unit_price"], updated_data["shelf_location"],
                updated_data["current_weight"], updated_data["purchase_date"], updated_data["description"],
                self.selected_herb_id
            ))
            
            self.conn.commit()
            self.load_herbs()
            messagebox.showinfo("موفق", "اطلاعات گیاه با موفقیت ویرایش شد!")
            self.clear_herb_form()
            
        except ValueError:
            messagebox.showerror("خطا", "لطفاً مقادیر عددی را به درستی وارد کنید.")
        except Exception as e:
            messagebox.showerror("خطا", f"خطا در ویرایش گیاه: {str(e)}")

    def delete_herb(self):
        if not self.selected_herb_id:
            messagebox.showwarning("هشدار", "لطفا ابتدا یک گیاه را از جدول انتخاب کنید.")
            return

        confirm = messagebox.askyesno("تایید حذف", f"آیا از حذف گیاه با کد '{self.selected_herb_id}' مطمئن هستید؟")
        if confirm:
            try:
                # Check if herb is used in any drug composition
                usage_check = self.cursor.execute(
                    "SELECT COUNT(*) FROM DrugCompositions WHERE herb_id = ?", 
                    (self.selected_herb_id,)
                ).fetchone()
                
                if usage_check and usage_check[0] > 0:
                    messagebox.showerror(
                        "خطا", 
                        f"امکان حذف گیاه '{self.selected_herb_id}' وجود ندارد زیرا در ترکیبات دارویی استفاده شده است."
                    )
                    return

                self.cursor.execute("DELETE FROM Herbs WHERE id = ?", (self.selected_herb_id,))
                self.conn.commit()
                self.load_herbs()
                messagebox.showinfo("موفق", "گیاه با موفقیت حذف شد!")
                self.clear_herb_form()
                
            except Exception as e:
                messagebox.showerror("خطا", f"خطا در حذف گیاه: {str(e)}")

    def on_herb_select(self, event):
        try:
            selected_item = self.herb_tree.selection()[0]
            item_values = self.herb_tree.item(selected_item, 'values')

            # Store the ID
            self.selected_herb_id = item_values[7]

            # Fetch raw data from DB for accurate editing (especially numbers)
            raw_data = self.cursor.execute(
                "SELECT unit_price, current_weight FROM Herbs WHERE id = ?", 
                (self.selected_herb_id,)
            ).fetchone()
            
            if not raw_data: 
                return

            # Load data into entries
            self.herb_entries["کد گیاه"].delete(0, tk.END)
            self.herb_entries["کد گیاه"].insert(0, item_values[7])

            self.herb_entries["نام گیاه"].delete(0, tk.END)
            self.herb_entries["نام گیاه"].insert(0, item_values[6])

            self.herb_entries["قیمت واحد (تومان)"].delete(0, tk.END)
            self.herb_entries["قیمت واحد (تومان)"].insert(0, raw_data[0] if raw_data[0] is not None else "")

            self.herb_entries["محل قفسه"].delete(0, tk.END)
            self.herb_entries["محل قفسه"].insert(0, item_values[4])

            self.herb_entries["وزن (گرم)"].delete(0, tk.END)
            self.herb_entries["وزن (گرم)"].insert(0, raw_data[1] if raw_data[1] is not None else "")

            self.herb_entries["تاریخ خرید"].delete(0, tk.END)
            self.herb_entries["تاریخ خرید"].insert(0, item_values[1])

            self.herb_entries["توضیحات"].delete(0, tk.END)
            self.herb_entries["توضیحات"].insert(0, item_values[0])

        except IndexError:
            # No item selected or selection cleared
            self.clear_herb_form()

    def clear_herb_form(self):
        """Clears all entry fields in the herb form and resets selection."""
        self.selected_herb_id = None
        for entry in self.herb_entries.values():
            entry.delete(0, tk.END)
        if self.herb_tree.selection():
            self.herb_tree.selection_remove(self.herb_tree.selection()[0])

    def filter_herbs(self, event=None):
        """Filter herbs based on search input"""
        search_term = self.search_entry.get().lower()
        self.load_herbs(search_term)

    def load_herbs(self, search_term=None):
        # Clear existing data
        for row in self.herb_tree.get_children():
            self.herb_tree.delete(row)
            
        # Build query
        query = """
            SELECT id, name, unit_price, shelf_location, current_weight, purchase_date, description 
            FROM Herbs
        """
        params = []
        
        # Add search filter if provided
        if search_term:
            query += " WHERE id LIKE ? OR name LIKE ? OR description LIKE ?"
            params = [f"%{search_term}%", f"%{search_term}%", f"%{search_term}%"]
            
        query += " ORDER BY name"
        
        # Fetch herbs
        try:
            if params:
                herbs = self.cursor.execute(query, params).fetchall()
            else:
                herbs = self.cursor.execute(query).fetchall()
                
            # Insert into treeview
            for i, herb in enumerate(herbs):
                # Original DB indices: 0:id, 1:name, 2:unit_price, 3:shelf, 4:weight, 5:date, 6:description
                
                # Format numbers
                try:
                    unit_price_val = float(herb[2]) if herb[2] is not None else 0.0
                    weight_val_grams = float(herb[4]) if herb[4] is not None else 0.0
                    
                    # Calculate total price based on kg (unit_price * weight_in_kg)
                    total_price_val = unit_price_val * (weight_val_grams / 1000.0)

                    # Format with comma separators
                    unit_price_str = f"{unit_price_val:,.0f}"
                    weight_str = f"{weight_val_grams:,.0f}"
                    total_price_str = f"{total_price_val:,.0f}"

                except (ValueError, TypeError):
                    unit_price_str = "N/A"
                    weight_str = "N/A"
                    total_price_str = "N/A"

                # Create values tuple in REVERSED (RTL) order for Treeview insertion
                values_rtl = (
                    herb[6],            # description
                    herb[5],            # date
                    total_price_str,    # formatted total_price
                    weight_str,         # formatted weight
                    herb[3],            # shelf
                    unit_price_str,     # formatted unit_price
                    herb[1],            # name
                    herb[0]             # id
                )
                
                # Set row tag for alternating colors
                tag = 'evenrow' if i % 2 == 0 else 'oddrow'
                self.herb_tree.insert("", "end", values=values_rtl, tags=(tag,))
        
        except Exception as e:
            messagebox.showerror("خطا", f"خطا در بارگیری اطلاعات گیاهان: {str(e)}")


class DiseaseTabManager:
    def __init__(self, parent, conn):
        self.parent = parent
        self.conn = conn
        self.cursor = conn.cursor()
        self.selected_disease_id = None
        self.disease_map = {}  # Map disease names to IDs
        
        self.setup_disease_tab_content()
    
    def setup_disease_tab_content(self):
        # Form frame for disease management
        self.disease_form_frame = ttk.LabelFrame(self.parent, text="ثبت / ویرایش بیماری")
        self.disease_form_frame.pack(pady=10, padx=10, fill="x")

        # Disease Name Label and Entry (RTL)
        ttk.Label(self.disease_form_frame, text="نام بیماری:", anchor=tk.E).grid(row=0, column=1, padx=5, pady=5, sticky=tk.E)
        self.disease_name_entry = ttk.Entry(self.disease_form_frame, justify=tk.RIGHT)
        self.disease_name_entry.grid(row=0, column=0, padx=5, pady=5, sticky=tk.W+tk.E)
        self.disease_form_frame.columnconfigure(0, weight=1)
        self.disease_form_frame.columnconfigure(1, weight=0)

        # Disease Buttons Frame
        self.disease_button_frame = ttk.Frame(self.disease_form_frame)
        self.disease_button_frame.grid(row=1, column=0, columnspan=2, pady=10)

        # Disease Buttons (Add, Edit, Delete, Clear)
        self.add_disease_btn = ttk.Button(self.disease_button_frame, text="ذخیره بیماری", command=self.add_disease)
        self.add_disease_btn.grid(row=0, column=3, padx=5)
        self.edit_disease_btn = ttk.Button(self.disease_button_frame, text="ویرایش بیماری", command=self.edit_disease)
        self.edit_disease_btn.grid(row=0, column=2, padx=5)
        self.delete_disease_btn = ttk.Button(self.disease_button_frame, text="حذف بیماری", command=self.delete_disease)
        self.delete_disease_btn.grid(row=0, column=1, padx=5)
        self.clear_disease_form_btn = ttk.Button(self.disease_button_frame, text="پاک کردن فرم", command=self.clear_disease_form)
        self.clear_disease_form_btn.grid(row=0, column=0, padx=5)

        # Search frame
        self.search_frame = ttk.Frame(self.parent)
        self.search_frame.pack(pady=5, padx=10, fill="x")
        
        ttk.Label(self.search_frame, text="جستجو:").pack(side=tk.RIGHT, padx=5)
        self.search_entry = ttk.Entry(self.search_frame, justify=tk.RIGHT)
        self.search_entry.pack(side=tk.RIGHT, padx=5, fill=tk.X, expand=True)
        self.search_entry.bind("<KeyRelease>", self.filter_diseases)

        # Frame for disease list
        self.disease_list_frame = ttk.Frame(self.parent)
        self.disease_list_frame.pack(pady=10, padx=10, fill="both", expand=True)

        # Disease Treeview
        self.disease_tree = ttk.Treeview(self.disease_list_frame, columns=("name", "id"), show="headings")
        self.disease_tree.heading("name", text="نام بیماری")
        self.disease_tree.heading("id", text="کد")
        self.disease_tree.column("name", anchor=tk.E)
        self.disease_tree.column("id", width=80, anchor=tk.CENTER)

        # Add scrollbar
        scrollbar = ttk.Scrollbar(self.disease_list_frame, orient="vertical", command=self.disease_tree.yview)
        self.disease_tree.configure(yscrollcommand=scrollbar.set)
        scrollbar.pack(side="left", fill="y")
        self.disease_tree.pack(side="right", fill="both", expand=True)

        # Bind selection event
        self.disease_tree.bind("<<TreeviewSelect>>", self.on_disease_select)
        
        # Load initial data
        self.load_diseases()
        
        # Apply alternating row colors
        self.disease_tree.tag_configure('oddrow', background='#f3f4f6')
        self.disease_tree.tag_configure('evenrow', background='#ffffff')
    
    def add_disease(self):
        disease_name = self.disease_name_entry.get().strip()
        if not disease_name:
            messagebox.showwarning("هشدار", "لطفا نام بیماری را وارد کنید.")
            return

        try:
            self.cursor.execute("INSERT INTO Diseases (name) VALUES (?)", (disease_name,))
            self.conn.commit()
            messagebox.showinfo("موفق", f"بیماری '{disease_name}' با موفقیت ثبت شد!")
            self.load_diseases()
            self.clear_disease_form()
            
        except sqlite3.IntegrityError:
            messagebox.showerror("خطا", f"بیماری با نام '{disease_name}' قبلا ثبت شده است.")
        except Exception as e:
            messagebox.showerror("خطا", f"خطا در ثبت بیماری: {str(e)}")

    def edit_disease(self):
        if self.selected_disease_id is None:
            messagebox.showwarning("هشدار", "لطفا ابتدا یک بیماری را از جدول انتخاب کنید.")
            return

        new_name = self.disease_name_entry.get().strip()
        if not new_name:
            messagebox.showwarning("هشدار", "لطفا نام جدید بیماری را وارد کنید.")
            return

        try:
            self.cursor.execute("UPDATE Diseases SET name = ? WHERE id = ?", (new_name, self.selected_disease_id))
            self.conn.commit()
            messagebox.showinfo("موفق", "نام بیماری با موفقیت ویرایش شد!")
            self.load_diseases()
            self.clear_disease_form()
            
        except sqlite3.IntegrityError:
            messagebox.showerror("خطا", f"بیماری دیگری با نام '{new_name}' وجود دارد.")
        except Exception as e:
            messagebox.showerror("خطا", f"خطا در ویرایش بیماری: {str(e)}")

    def delete_disease(self):
        if self.selected_disease_id is None:
            messagebox.showwarning("هشدار", "لطفا ابتدا یک بیماری را از جدول انتخاب کنید.")
            return

        confirm = messagebox.askyesno(
            "تایید حذف", 
            f"آیا از حذف بیماری انتخاب شده مطمئن هستید؟\n(توجه: تمام ارتباطات این بیماری با داروها نیز حذف خواهد شد)"
        )
        
        if confirm:
            try:
                # Check usage (optional, as ON DELETE CASCADE handles it, but good for user info)
                usage_count = self.cursor.execute(
                    "SELECT COUNT(*) FROM DrugDiseases WHERE disease_id = ?", 
                    (self.selected_disease_id,)
                ).fetchone()[0]
                
                if usage_count > 0:
                    confirm_again = messagebox.askyesno(
                        "اخطار", 
                        f"این بیماری در {usage_count} دارو استفاده شده است. حذف آن باعث از بین رفتن این ارتباطات خواهد شد. آیا مطمئن هستید؟"
                    )
                    if not confirm_again:
                        return

                # Delete the disease (CASCADE will handle DrugDiseases links)
                self.cursor.execute("DELETE FROM Diseases WHERE id = ?", (self.selected_disease_id,))
                self.conn.commit()
                messagebox.showinfo("موفق", "بیماری با موفقیت حذف شد!")
                self.load_diseases()
                self.clear_disease_form()
                
            except Exception as e:
                messagebox.showerror("خطا", f"خطا در حذف بیماری: {str(e)}")

    def filter_diseases(self, event=None):
        """Filter diseases based on search input"""
        search_term = self.search_entry.get().lower()
        self.load_diseases(search_term)

    def load_diseases(self, search_term=None):
        # Clear existing treeview
        for row in self.disease_tree.get_children():
            self.disease_tree.delete(row)

        # Clear map and build query
        self.disease_map.clear()
        query = "SELECT id, name FROM Diseases"
        params = []
        
        # Add search filter if provided
        if search_term:
            query += " WHERE name LIKE ?"
            params = [f"%{search_term}%"]
            
        query += " ORDER BY name"
        
        # Execute query
        if params:
            diseases = self.cursor.execute(query, params).fetchall()
        else:
            diseases = self.cursor.execute(query).fetchall()

        # Process results
        for i, (disease_id, disease_name) in enumerate(diseases):
            # Insert into treeview
            tag = 'evenrow' if i % 2 == 0 else 'oddrow'
            self.disease_tree.insert("", "end", values=(disease_name, disease_id), tags=(tag,))
            
            # Update map
            self.disease_map[disease_name] = disease_id
        
        return self.disease_map

    def on_disease_select(self, event):
        try:
            selected_item = self.disease_tree.selection()[0]
            item_values = self.disease_tree.item(selected_item, 'values')
            # Values are (name, id)
            self.selected_disease_id = int(item_values[1])
            self.disease_name_entry.delete(0, tk.END)
            self.disease_name_entry.insert(0, item_values[0])
            
        except (IndexError, ValueError):
            # Selection cleared or invalid data
            self.clear_disease_form()

    def clear_disease_form(self):
        self.selected_disease_id = None
        self.disease_name_entry.delete(0, tk.END)
        if self.disease_tree.selection():
            self.disease_tree.selection_remove(self.disease_tree.selection()[0])


class DrugTabManager:
    def __init__(self, parent, conn, disease_manager):
        self.parent = parent
        self.conn = conn
        self.cursor = conn.cursor()
        self.disease_manager = disease_manager
        self.compositions = []  # List to store drug compositions
        
        self.setup_drug_tab_content()
    
    def setup_drug_tab_content(self):
        # Drug production form
        self.drug_frame = ttk.LabelFrame(self.parent, text="تولید دارو جدید")
        self.drug_frame.pack(pady=10, padx=10, fill="x")

        # Basic drug info
        ttk.Label(self.drug_frame, text="کد دارو:", anchor=tk.E).grid(row=0, column=1, sticky=tk.E, padx=5, pady=5)
        self.drug_id_entry = ttk.Entry(self.drug_frame, justify=tk.RIGHT)
        self.drug_id_entry.grid(row=0, column=0, sticky=tk.W+tk.E, padx=5, pady=5)

        ttk.Label(self.drug_frame, text="نام دارو:", anchor=tk.E).grid(row=1, column=1, sticky=tk.E, padx=5, pady=5)
        self.drug_name_entry = ttk.Entry(self.drug_frame, justify=tk.RIGHT)
        self.drug_name_entry.grid(row=1, column=0, sticky=tk.W+tk.E, padx=5, pady=5)

        # Disease Combobox
        ttk.Label(self.drug_frame, text="بیماری مرتبط:", anchor=tk.E).grid(row=2, column=1, sticky=tk.E, padx=5, pady=5)
        self.disease_combobox = ttk.Combobox(self.drug_frame, state="readonly", justify=tk.RIGHT)
        self.disease_combobox.grid(row=2, column=0, sticky=tk.W+tk.E, padx=5, pady=5)

        # Cost Calculation Fields
        ttk.Label(self.drug_frame, text="قیمت قرض زنی (تومان):", anchor=tk.E).grid(row=3, column=1, sticky=tk.E, padx=5, pady=5)
        self.loan_interest_entry = ttk.Entry(self.drug_frame, justify=tk.RIGHT)
        self.loan_interest_entry.grid(row=3, column=0, sticky=tk.W+tk.E, padx=5, pady=5)

        ttk.Label(self.drug_frame, text="هزینه ظرف و برچسب (تومان):", anchor=tk.E).grid(row=4, column=1, sticky=tk.E, padx=5, pady=5)
        self.container_label_entry = ttk.Entry(self.drug_frame, justify=tk.RIGHT)
        self.container_label_entry.grid(row=4, column=0, sticky=tk.W+tk.E, padx=5, pady=5)

        ttk.Label(self.drug_frame, text="هزینه سربار کارگاه (تومان):", anchor=tk.E).grid(row=5, column=1, sticky=tk.E, padx=5, pady=5)
        self.workshop_overhead_entry = ttk.Entry(self.drug_frame, justify=tk.RIGHT)
        self.workshop_overhead_entry.grid(row=5, column=0, sticky=tk.W+tk.E, padx=5, pady=5)

        # Compositions frame
        self.compositions_frame = ttk.LabelFrame(self.drug_frame, text="ترکیبات دارو")
        self.compositions_frame.grid(row=6, column=0, columnspan=2, sticky="ew", padx=5, pady=5)

        # Compositions list
        self.compositions_tree = ttk.Treeview(
            self.compositions_frame,
            columns=("herb_id", "herb_name", "weight", "cost"),
            show="headings",
            height=5
        )
        
        self.compositions_tree.heading("herb_id", text="کد گیاه")
        self.compositions_tree.heading("herb_name", text="نام گیاه")
        self.compositions_tree.heading("weight", text="وزن (گرم)")
        self.compositions_tree.heading("cost", text="هزینه (تومان)")
        
        self.compositions_tree.column("herb_id", width=80, anchor=tk.CENTER)
        self.compositions_tree.column("herb_name", width=150, anchor=tk.E)
        self.compositions_tree.column("weight", width=100, anchor=tk.E)
        self.compositions_tree.column("cost", width=120, anchor=tk.E)
        
        # Scrollbar for compositions
        comp_scrollbar = ttk.Scrollbar(
            self.compositions_frame, 
            orient="vertical", 
            command=self.compositions_tree.yview
        )
        
        self.compositions_tree.configure(yscrollcommand=comp_scrollbar.set)
        
        # Pack compositions tree and scrollbar
        comp_scrollbar.pack(side=tk.LEFT, fill=tk.Y)
        self.compositions_tree.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, pady=5)
        
        # Compositions buttons
        self.comp_buttons_frame = ttk.Frame(self.drug_frame)
        self.comp_buttons_frame.grid(row=7, column=0, columnspan=2, pady=5)
        
        self.add_composition_btn = ttk.Button(
            self.comp_buttons_frame, 
            text="+ افزودن ترکیب", 
            command=self.add_composition
        )
        self.add_composition_btn.pack(side=tk.RIGHT, padx=5)
        
        self.remove_composition_btn = ttk.Button(
            self.comp_buttons_frame, 
            text="- حذف ترکیب", 
            command=self.remove_composition,
            state="disabled"
        )
        self.remove_composition_btn.pack(side=tk.RIGHT, padx=5)
        
        # Bind selection event to enable/disable remove button
        self.compositions_tree.bind("<<TreeviewSelect>>", self.on_composition_select)

        # Produce drug button
        self.produce_drug_btn = ttk.Button(
            self.drug_frame, 
            text="تولید دارو", 
            command=self.produce_drug
        )
        self.produce_drug_btn.grid(row=8, column=0, columnspan=2, pady=10)

        # Set up the produced drugs list section
        self.setup_produced_drugs_section()
        
        # Configure column weights
        self.drug_frame.columnconfigure(0, weight=1)
        self.drug_frame.columnconfigure(1, weight=0)
        
        # Apply alternating row colors
        self.compositions_tree.tag_configure('oddrow', background='#f3f4f6')
        self.compositions_tree.tag_configure('evenrow', background='#ffffff')
    
    def setup_produced_drugs_section(self):
        # Produced drugs list frame
        self.produced_drug_list_frame = ttk.LabelFrame(self.parent, text="لیست داروهای تولید شده")
        self.produced_drug_list_frame.pack(pady=10, padx=10, fill="both", expand=True)

        # Search frame
        self.drug_search_frame = ttk.Frame(self.produced_drug_list_frame)
        self.drug_search_frame.pack(fill="x", pady=5)
        
        ttk.Label(self.drug_search_frame, text="جستجو:").pack(side=tk.RIGHT, padx=5)
        self.drug_search_entry = ttk.Entry(self.drug_search_frame, justify=tk.RIGHT)
        self.drug_search_entry.pack(side=tk.RIGHT, padx=5, fill=tk.X, expand=True)
        self.drug_search_entry.bind("<KeyRelease>", self.filter_drugs)

        # Buttons frame
        self.drug_buttons_frame = ttk.Frame(self.produced_drug_list_frame)
        self.drug_buttons_frame.pack(fill="x", pady=5)

        self.edit_drug_btn = ttk.Button(
            self.drug_buttons_frame, 
            text="ویرایش داروی انتخاب شده", 
            command=self.edit_selected_drug, 
            state='disabled'
        )
        self.edit_drug_btn.pack(side="right", padx=5)

        self.delete_drug_btn = ttk.Button(
            self.drug_buttons_frame, 
            text="حذف داروی انتخاب شده", 
            command=self.delete_selected_drug,
            state='disabled'
        )
        self.delete_drug_btn.pack(side="right", padx=5)

        self.view_details_btn = ttk.Button(
            self.drug_buttons_frame,
            text="مشاهده جزئیات",
            command=self.show_drug_compositions,
            state='disabled'
        )
        self.view_details_btn.pack(side="right", padx=5)

        # Treeview and scrollbar frame
        self.tree_frame = ttk.Frame(self.produced_drug_list_frame)
        self.tree_frame.pack(fill="both", expand=True)

        # Define columns (RTL order)
        produced_columns = ("disease_name", "production_date", "drug_name", "drug_id", "total_cost")
        self.produced_drug_tree = ttk.Treeview(
            self.tree_frame, 
            columns=produced_columns, 
            show="headings",
            height=10
        )

        # Set up columns
        self.produced_drug_tree.heading("disease_name", text="بیماری مرتبط")
        self.produced_drug_tree.heading("production_date", text="تاریخ تولید")
        self.produced_drug_tree.heading("drug_name", text="نام دارو")
        self.produced_drug_tree.heading("drug_id", text="کد دارو")
        self.produced_drug_tree.heading("total_cost", text="هزینه تولید (تومان/کیلوگرم)")

        self.produced_drug_tree.column("disease_name", width=150, anchor=tk.E)
        self.produced_drug_tree.column("production_date", width=100, anchor=tk.CENTER)
        self.produced_drug_tree.column("drug_name", width=200, anchor=tk.E)
        self.produced_drug_tree.column("drug_id", width=80, anchor=tk.CENTER)
        self.produced_drug_tree.column("total_cost", width=120, anchor=tk.E)

        # Scrollbar
        prod_scrollbar = ttk.Scrollbar(
            self.tree_frame, 
            orient="vertical", 
            command=self.produced_drug_tree.yview
        )
        self.produced_drug_tree.configure(yscrollcommand=prod_scrollbar.set)

        # Pack treeview and scrollbar
        self.produced_drug_tree.pack(side="right", fill="both", expand=True)
        prod_scrollbar.pack(side="left", fill="y")

        # Bind events
        self.produced_drug_tree.bind("<<TreeviewSelect>>", self.toggle_drug_buttons)
        self.produced_drug_tree.bind("<Double-1>", self.show_drug_compositions)

        # Apply alternating row colors
        self.produced_drug_tree.tag_configure('oddrow', background='#f3f4f6')
        self.produced_drug_tree.tag_configure('evenrow', background='#ffffff')

        # Load initial data
        self.load_produced_drugs()
    
    def update_disease_combobox(self, disease_map):
        """Update disease combobox with current disease data"""
        disease_names = list(disease_map.keys())
        self.disease_combobox['values'] = disease_names
        if disease_names:
            self.disease_combobox.current(0)
        else:
            self.disease_combobox.set('')
    
    def on_composition_select(self, event):
        """Enable/disable remove button based on selection"""
        if self.compositions_tree.selection():
            self.remove_composition_btn['state'] = 'normal'
        else:
            self.remove_composition_btn['state'] = 'disabled'
    
    def remove_composition(self):
        """Remove the selected composition from the list"""
        selected = self.compositions_tree.selection()
        if not selected:
            return
            
        item_values = self.compositions_tree.item(selected, 'values')
        herb_id = item_values[0]
        
        # Remove from compositions list
        for i, comp in enumerate(self.compositions):
            if comp[0] == herb_id:
                del self.compositions[i]
                break
        
        # Update treeview
        self.update_compositions_tree()
        
        # Disable remove button
        self.remove_composition_btn['state'] = 'disabled'
    
    def add_composition(self):
        """Open window to add a new composition"""
        self.comp_window = tk.Toplevel()
        self.comp_window.title("افزودن ترکیب")
        self.comp_window.geometry("700x550")
        self.comp_window.transient(self.parent)
        self.comp_window.grab_set()
        
        # Set up main frame
        main_frame = ttk.Frame(self.comp_window, padding="10")
        main_frame.pack(fill="both", expand=True)
        
        # Search frame
        search_frame = ttk.LabelFrame(main_frame, text="جستجوی گیاه", padding="5")
        search_frame.pack(fill="x", pady=5)
        
        ttk.Label(search_frame, text="جستجو (کد یا نام گیاه):").pack(side="right", padx=5)
        self.comp_search_entry = ttk.Entry(search_frame)
        self.comp_search_entry.pack(side="right", padx=5, fill="x", expand=True)
        self.comp_search_entry.bind("<KeyRelease>", self.filter_herbs_for_composition)
        
        # Search button
        search_btn = ttk.Button(search_frame, text="جستجو", command=lambda: self.filter_herbs_for_composition())
        search_btn.pack(side="right", padx=5)
        
        # Herb selection frame
        herb_frame = ttk.LabelFrame(main_frame, text="لیست گیاهان", padding="5")
        herb_frame.pack(fill="both", expand=True, pady=5)
        
        # Treeview for herbs
        columns = ("id", "name", "current_weight", "unit_price", "shelf")
        self.herb_selection_tree = ttk.Treeview(
            herb_frame, 
            columns=columns, 
            show="headings",
            height=8,
            selectmode="browse"
        )
        
        # Set up columns
        self.herb_selection_tree.heading("id", text="کد گیاه")
        self.herb_selection_tree.heading("name", text="نام گیاه")
        self.herb_selection_tree.heading("current_weight", text="موجودی (گرم)")
        self.herb_selection_tree.heading("unit_price", text="قیمت واحد (تومان)")
        self.herb_selection_tree.heading("shelf", text="محل قفسه")
        
        self.herb_selection_tree.column("id", width=100, anchor=tk.CENTER)
        self.herb_selection_tree.column("name", width=200, anchor=tk.E)
        self.herb_selection_tree.column("current_weight", width=120, anchor=tk.E)
        self.herb_selection_tree.column("unit_price", width=150, anchor=tk.E)
        self.herb_selection_tree.column("shelf", width=100, anchor=tk.E)
        
        # Scrollbar
        scrollbar = ttk.Scrollbar(herb_frame, orient="vertical", command=self.herb_selection_tree.yview)
        self.herb_selection_tree.configure(yscrollcommand=scrollbar.set)
        
        # Pack treeview and scrollbar
        scrollbar.pack(side="left", fill="y")
        self.herb_selection_tree.pack(side="right", fill="both", expand=True)
        
        # Weight frame for composition details
        weight_frame = ttk.LabelFrame(main_frame, text="مشخصات ترکیب", padding="5")
        weight_frame.pack(fill="x", pady=5)
        
        # Weight field
        ttk.Label(weight_frame, text="وزن مورد نیاز (گرم):").pack(side="right", padx=5)
        self.herb_weight_entry = ttk.Entry(weight_frame, width=15)
        self.herb_weight_entry.pack(side="right", padx=5)
        
        # Action buttons
        btn_frame = ttk.Frame(weight_frame)
        btn_frame.pack(side="right", padx=10)
        
        add_btn = ttk.Button(btn_frame, text="افزودن ترکیب", command=self.save_composition)
        add_btn.pack(side="right", padx=5)
        
        cancel_btn = ttk.Button(btn_frame, text="انصراف", command=self.comp_window.destroy)
        cancel_btn.pack(side="right", padx=5)
        
        # Load herbs and apply alternating colors
        self.filter_herbs_for_composition()
        self.herb_selection_tree.tag_configure('oddrow', background='#f3f4f6')
        self.herb_selection_tree.tag_configure('evenrow', background='#ffffff')
    
    def filter_herbs_for_composition(self, event=None):
        """Filter herbs for composition selection"""
        # Clear existing items
        for item in self.herb_selection_tree.get_children():
            self.herb_selection_tree.delete(item)
        
        # Get search term
        search_term = self.comp_search_entry.get().strip().lower() if hasattr(self, 'comp_search_entry') else ""
        
        # Build query
        query = """
            SELECT id, name, current_weight, unit_price, shelf_location 
            FROM Herbs
            WHERE current_weight > 0
        """
        params = []
        
        # Add search filter if provided
        if search_term:
            query += " AND (id LIKE ? OR name LIKE ? OR shelf_location LIKE ?)"
            params = [f"%{search_term}%", f"%{search_term}%", f"%{search_term}%"]
            
        query += " ORDER BY name"
        
        # Execute query
        try:
            if params:
                herbs = self.cursor.execute(query, params).fetchall()
            else:
                herbs = self.cursor.execute(query).fetchall()
                
            # Insert into treeview
            for i, herb in enumerate(herbs):
                herb_id, name, weight, price, shelf = herb
                
                # Format display values
                weight_display = f"{weight:,.0f}" if weight else "0"
                price_display = f"{price:,.0f}" if price else "0"
                shelf_display = shelf if shelf else "-"
                
                # Insert with alternating colors
                tag = 'evenrow' if i % 2 == 0 else 'oddrow'
                self.herb_selection_tree.insert("", "end", values=(
                    herb_id,
                    name,
                    weight_display,
                    price_display,
                    shelf_display
                ), tags=(tag,))
                
        except Exception as e:
            messagebox.showerror("خطا", f"خطا در بارگیری اطلاعات گیاهان: {str(e)}")
    
    def save_composition(self):
        """Save the selected herb as a composition"""
        try:
            # Get selected herb
            selected_item = self.herb_selection_tree.selection()
            if not selected_item:
                messagebox.showerror("خطا", "لطفاً یک گیاه را از لیست انتخاب کنید")
                return
                
            herb_id = self.herb_selection_tree.item(selected_item, 'values')[0]
            herb_name = self.herb_selection_tree.item(selected_item, 'values')[1]
            
            # Get weight
            try:
                weight_grams = float(self.herb_weight_entry.get())
                if weight_grams <= 0:
                    raise ValueError("وزن باید بیشتر از صفر باشد")
            except ValueError:
                messagebox.showerror("خطا", "لطفاً یک عدد مثبت برای وزن وارد کنید")
                return
            
            # Check inventory
            cursor = self.conn.cursor()
            cursor.execute("SELECT current_weight, unit_price FROM Herbs WHERE id = ?", (herb_id,))
            herb_data = cursor.fetchone()
            
            if not herb_data:
                messagebox.showerror("خطا", "کد گیاه نامعتبر است!")
                return
            
            current_weight, unit_price = herb_data
            if current_weight < weight_grams:
                messagebox.showerror("خطا", f"موجودی ناکافی! موجودی فعلی: {current_weight:,.0f} گرم")
                return
            
            # Check if herb is already in compositions
            for comp in self.compositions:
                if comp[0] == herb_id:
                    confirm = messagebox.askyesno(
                        "تایید", 
                        f"گیاه {herb_name} قبلاً به ترکیبات اضافه شده است. می‌خواهید آن را جایگزین کنید؟"
                    )
                    if confirm:
                        # Remove existing composition
                        self.compositions = [c for c in self.compositions if c[0] != herb_id]
                    else:
                        return
            
            # Calculate cost
            weight_kg = weight_grams / 1000
            subtotal = weight_kg * unit_price
            
            # Add to compositions list
            self.compositions.append((herb_id, herb_name, weight_grams, subtotal))
            
            # Update treeview
            self.update_compositions_tree()
            
            # Clear weight field
            self.herb_weight_entry.delete(0, tk.END)
            
            # Show success message
            messagebox.showinfo(
                "موفق", 
                f"ترکیب با موفقیت اضافه شد!\n{herb_name}\nوزن: {weight_grams:,.0f} گرم\nهزینه: {subtotal:,.0f} تومان"
            )
            
        except Exception as e:
            messagebox.showerror("خطا", f"خطا در افزودن ترکیب: {str(e)}")
    
    def update_compositions_tree(self):
        """Update the compositions treeview with current data"""
        # Clear existing items
        for item in self.compositions_tree.get_children():
            self.compositions_tree.delete(item)
        
        # Add compositions to treeview
        for i, comp in enumerate(self.compositions):
            herb_id, herb_name, weight_grams, subtotal = comp
            
            # Format display values
            weight_display = f"{weight_grams:,.0f}"
            subtotal_display = f"{subtotal:,.0f}"
            
            # Insert with alternating colors
            tag = 'evenrow' if i % 2 == 0 else 'oddrow'
            self.compositions_tree.insert("", "end", values=(
                herb_id,
                herb_name,
                weight_display,
                subtotal_display
            ), tags=(tag,))
    
    def produce_drug(self):
        """Produce a new drug with the current compositions"""
        try:
            # Get basic drug info
            drug_id_str = self.drug_id_entry.get().strip()
            drug_name = self.drug_name_entry.get().strip()
            selected_disease_name = self.disease_combobox.get()

            # Validate basic info
            if not drug_id_str:
                messagebox.showwarning("هشدار", "لطفاً کد دارو را وارد کنید")
                return
                
            if not drug_name:
                messagebox.showwarning("هشدار", "لطفاً نام دارو را وارد کنید")
                return
                
            if not selected_disease_name:
                messagebox.showwarning("هشدار", "لطفاً بیماری مرتبط با دارو را انتخاب کنید")
                return
                
            if not self.compositions:
                messagebox.showwarning("هشدار", "لطفاً حداقل یک ترکیب برای دارو اضافه کنید")
                return

            # Convert drug ID to integer
            try:
                drug_id = int(drug_id_str)
            except ValueError:
                messagebox.showerror("خطا", "کد دارو باید یک عدد صحیح باشد")
                return

            # Get additional costs
            try:
                loan_interest = float(self.loan_interest_entry.get() or 0)
                container_label = float(self.container_label_entry.get() or 0)
                workshop_overhead = float(self.workshop_overhead_entry.get() or 0)
            except ValueError:
                messagebox.showerror("خطا", "لطفاً مقادیر هزینه را به صورت عدد وارد کنید")
                return

            # Get disease ID
            disease_id = self.disease_manager.disease_map.get(selected_disease_name)
            if disease_id is None:
                messagebox.showerror("خطا", "خطا در دریافت اطلاعات بیماری")
                return

            # Calculate costs
            raw_materials_cost = sum(comp[3] for comp in self.compositions)
            total_workshop_cost = (
                raw_materials_cost +
                loan_interest +
                container_label +
                workshop_overhead
            )

            # Calculate selling prices
            representative_price = total_workshop_cost * 1.30  # +30% profit
            wholesale_price = total_workshop_cost * 1.20      # +20% profit
            retail_price = total_workshop_cost * 1.40         # +40% profit

            # Show summary for confirmation
            summary = f"""خلاصه اطلاعات دارو:

نام دارو: {drug_name}
کد دارو: {drug_id}
بیماری مرتبط: {selected_disease_name}

تعداد ترکیبات: {len(self.compositions)}
هزینه مواد اولیه: {raw_materials_cost:,.0f} تومان
هزینه قرض زنی: {loan_interest:,.0f} تومان
هزینه ظرف و برچسب: {container_label:,.0f} تومان
هزینه سربار کارگاه: {workshop_overhead:,.0f} تومان

قیمت تمام شده: {total_workshop_cost:,.0f} تومان
قیمت نمایندگی: {representative_price:,.0f} تومان
قیمت عمده: {wholesale_price:,.0f} تومان
قیمت خرده: {retail_price:,.0f} تومان

آیا از ثبت اطلاعات اطمینان دارید؟"""

            if not messagebox.askyesno("تأیید ثبت دارو", summary):
                return

            # Start transaction
            self.conn.execute("BEGIN TRANSACTION")

            try:
                # 1. Register drug
                self.cursor.execute('''
                    INSERT INTO Drugs (
                        id, name, production_date,
                        raw_materials_cost, loan_interest_cost,
                        container_label_cost, workshop_overhead_cost,
                        total_workshop_cost, representative_price,
                        wholesale_price, retail_price
                    )
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    drug_id, drug_name, datetime.now().strftime("%Y-%m-%d"),
                    raw_materials_cost, loan_interest,
                    container_label, workshop_overhead,
                    total_workshop_cost, representative_price,
                    wholesale_price, retail_price
                ))

                # 2. Register compositions and update inventory
                for comp in self.compositions:
                    herb_id, herb_name, weight_grams, subtotal = comp
                    
                    # Verify final inventory
                    self.cursor.execute("SELECT current_weight FROM Herbs WHERE id = ?", (herb_id,))
                    current_weight = self.cursor.fetchone()[0]
                    if current_weight < weight_grams:
                        raise ValueError(f"موجودی گیاه با کد '{herb_id}' برای تولید کافی نیست")

                    # Register composition
                    self.cursor.execute('''
                        INSERT INTO DrugCompositions (drug_id, herb_id, weight_used, subtotal)
                        VALUES (?, ?, ?, ?)
                    ''', (drug_id, herb_id, weight_grams, subtotal))

                    # Reduce inventory
                    self.cursor.execute('''
                        UPDATE Herbs 
                        SET current_weight = current_weight - ? 
                        WHERE id = ?
                    ''', (weight_grams, herb_id))

                # 3. Register disease relationship
                self.cursor.execute('''
                    INSERT INTO DrugDiseases (drug_id, disease_id)
                    VALUES (?, ?)
                ''', (drug_id, disease_id))

                # Commit transaction
                self.conn.commit()

                # Clear form
                self.clear_drug_form()

                # Show success message
                messagebox.showinfo("موفق", f"""داروی '{drug_name}' با موفقیت ثبت شد!

کد دارو: {drug_id}
تاریخ تولید: {datetime.now().strftime("%Y-%m-%d")}
قیمت تمام شده: {total_workshop_cost:,.0f} تومان""")

                # Update lists
                self.load_produced_drugs()

            except Exception as e:
                self.conn.rollback()
                raise e

        except sqlite3.IntegrityError as ie:
            if "Drugs.id" in str(ie):
                messagebox.showerror("خطا", f"دارویی با کد '{drug_id}' قبلاً ثبت شده است")
            else:
                messagebox.showerror("خطا", f"خطای یکپارچگی داده: {str(ie)}")
        except ValueError as ve:
            messagebox.showerror("خطا", str(ve))
        except Exception as e:
            messagebox.showerror("خطا", f"خطا در ثبت دارو: {str(e)}")
    
    def clear_drug_form(self):
        """Clear the drug production form"""
        self.drug_id_entry.delete(0, tk.END)
        self.drug_name_entry.delete(0, tk.END)
        self.disease_combobox.set('')
        self.loan_interest_entry.delete(0, tk.END)
        self.container_label_entry.delete(0, tk.END)
        self.workshop_overhead_entry.delete(0, tk.END)
        self.compositions = []
        self.update_compositions_tree()
        
        # Reset disease selection if available
        if self.disease_combobox['values']:
            self.disease_combobox.current(0)
    
    def toggle_drug_buttons(self, event):
        """Enable/disable buttons when a drug is selected"""
        if self.produced_drug_tree.selection():
            self.edit_drug_btn['state'] = 'normal'
            self.delete_drug_btn['state'] = 'normal'
            self.view_details_btn['state'] = 'normal'
        else:
            self.edit_drug_btn['state'] = 'disabled'
            self.delete_drug_btn['state'] = 'disabled'
            self.view_details_btn['state'] = 'disabled'
    
    def edit_selected_drug(self):
        """Edit the selected drug"""
        selected_item = self.produced_drug_tree.selection()
        if not selected_item:
            return
        
        drug_id = self.produced_drug_tree.item(selected_item, 'values')[3]  # Drug ID
        
        # Get current drug info
        self.cursor.execute("SELECT name, production_date FROM Drugs WHERE id = ?", (drug_id,))
        drug_info = self.cursor.fetchone()
        
        if not drug_info:
            messagebox.showerror("خطا", "اطلاعات دارو یافت نشد")
            return
        
        # Create edit window
        edit_window = tk.Toplevel()
        edit_window.title(f"ویرایش دارو {drug_id}")
        edit_window.transient(self.parent)
        edit_window.grab_set()
        
        # Edit fields
        frame = ttk.Frame(edit_window, padding=10)
        frame.pack(fill="both", expand=True)
        
        ttk.Label(frame, text="نام جدید دارو:").grid(row=0, column=0, padx=5, pady=5, sticky="e")
        new_name_entry = ttk.Entry(frame, width=30)
        new_name_entry.grid(row=0, column=1, padx=5, pady=5, sticky="w")
        new_name_entry.insert(0, drug_info[0])
        
        # Disease relationship (Combobox)
        ttk.Label(frame, text="بیماری مرتبط:").grid(row=1, column=0, padx=5, pady=5, sticky="e")
        disease_combo = ttk.Combobox(frame, values=list(self.disease_manager.disease_map.keys()), width=30)
        disease_combo.grid(row=1, column=1, padx=5, pady=5, sticky="w")
        
        # Get current disease
        self.cursor.execute('''
        SELECT ds.name FROM DrugDiseases dd
        JOIN Diseases ds ON dd.disease_id = ds.id
        WHERE dd.drug_id = ?
        ''', (drug_id,))
        current_disease = self.cursor.fetchone()
        if current_disease:
            disease_combo.set(current_disease[0])
        
        # Save button
        ttk.Button(
            frame, 
            text="ذخیره تغییرات",
            command=lambda: self.save_drug_edits(
                drug_id,
                new_name_entry.get(),
                disease_combo.get(),
                edit_window
            )
        ).grid(row=2, column=0, columnspan=2, pady=10)
    
    def save_drug_edits(self, drug_id, new_name, new_disease_name, window):
        """Save edits to a drug"""
        if not new_name:
            messagebox.showwarning("خطا", "نام دارو نمی‌تواند خالی باشد")
            return
        
        try:
            # Update drug name
            self.cursor.execute("UPDATE Drugs SET name = ? WHERE id = ?", (new_name, drug_id))
            
            # Update disease relationship
            if new_disease_name:
                new_disease_id = self.disease_manager.disease_map[new_disease_name]
                # Remove previous relationships
                self.cursor.execute("DELETE FROM DrugDiseases WHERE drug_id = ?", (drug_id,))
                # Add new relationship
                self.cursor.execute("INSERT INTO DrugDiseases (drug_id, disease_id) VALUES (?, ?)", 
                            (drug_id, new_disease_id))
            
            self.conn.commit()
            messagebox.showinfo("موفق", "تغییرات با موفقیت ذخیره شد")
            window.destroy()
            self.load_produced_drugs()
            
        except Exception as e:
            self.conn.rollback()
            messagebox.showerror("خطا", f"خطا در ذخیره تغییرات: {str(e)}")
    
    def delete_selected_drug(self):
        """Delete the selected drug"""
        selected_item = self.produced_drug_tree.selection()
        if not selected_item:
            return
        
        drug_id = self.produced_drug_tree.item(selected_item, 'values')[3]
        drug_name = self.produced_drug_tree.item(selected_item, 'values')[2]
        
        confirm = messagebox.askyesno(
            "تایید حذف",
            f"آیا از حذف داروی '{drug_name}' (کد: {drug_id}) مطمئن هستید؟\n\nتوجه: این عمل برگشت‌ناپذیر است!"
        )
        
        if confirm:
            try:
                # Start transaction
                self.conn.execute("BEGIN TRANSACTION")
                
                # 1. Return materials to inventory
                self.cursor.execute('''
                SELECT herb_id, weight_used 
                FROM DrugCompositions 
                WHERE drug_id = ?
                ''', (drug_id,))
                compositions = self.cursor.fetchall()
                
                for herb_id, weight in compositions:
                    self.cursor.execute('''
                    UPDATE Herbs 
                    SET current_weight = current_weight + ? 
                    WHERE id = ?
                    ''', (weight, herb_id))
                
                # 2. Remove drug compositions
                self.cursor.execute("DELETE FROM DrugCompositions WHERE drug_id = ?", (drug_id,))
                
                # 3. Remove disease relationships
                self.cursor.execute("DELETE FROM DrugDiseases WHERE drug_id = ?", (drug_id,))
                
                # 4. Remove the drug
                self.cursor.execute("DELETE FROM Drugs WHERE id = ?", (drug_id,))
                
                self.conn.commit()
                messagebox.showinfo("موفق", "دارو با موفقیت حذف شد")
                self.load_produced_drugs()
                
            except Exception as e:
                self.conn.rollback()
                messagebox.showerror("خطا", f"خطا در حذف دارو: {str(e)}")
    
    def filter_drugs(self, event=None):
        """Filter drugs based on search input"""
        search_term = self.drug_search_entry.get().lower()
        self.load_produced_drugs(search_term)
    
    def load_produced_drugs(self, search_term=None):
        """Load and display produced drugs"""
        # Clear existing data
        for row in self.produced_drug_tree.get_children():
            self.produced_drug_tree.delete(row)
        
        try:
            # Build query to get drugs with related diseases
            query = '''
                SELECT 
                    d.id, 
                    d.name, 
                    d.production_date, 
                    GROUP_CONCAT(ds.name, ', ') AS diseases,
                    d.total_workshop_cost
                FROM Drugs d
                LEFT JOIN DrugDiseases dd ON d.id = dd.drug_id
                LEFT JOIN Diseases ds ON dd.disease_id = ds.id
            '''
            
            # Add search filter if provided
            params = []
            if search_term:
                query += " WHERE d.name LIKE ? OR d.id LIKE ? OR ds.name LIKE ?"
                params = [f"%{search_term}%", f"%{search_term}%", f"%{search_term}%"]
                
            query += " GROUP BY d.id ORDER BY d.production_date DESC"
            
            # Execute query
            if params:
                drugs = self.cursor.execute(query, params).fetchall()
            else:
                drugs = self.cursor.execute(query).fetchall()
            
            # Add each drug to the treeview
            for i, drug in enumerate(drugs):
                drug_id, drug_name, production_date, disease_names, total_cost = drug
                disease_display = disease_names if disease_names else "بدون بیماری مرتبط"
                
                # Apply alternating row colors
                tag = 'evenrow' if i % 2 == 0 else 'oddrow'
                
                self.produced_drug_tree.insert("", "end", values=(
                    disease_display,
                    production_date,
                    drug_name,
                    drug_id,
                    f"{total_cost:,.0f}" if total_cost else "0"
                ), tags=(tag,))
                    
        except Exception as e:
            messagebox.showerror("خطا", f"خطا در بارگذاری داروهای تولید شده: {str(e)}")
    
    def show_drug_compositions(self, event=None):
        """Show compositions and details for the selected drug"""
        # Get selected drug
        if isinstance(event, tk.Event):  # Double-click event
            selected_item = self.produced_drug_tree.identify_row(event.y)
            if not selected_item:
                return
            self.produced_drug_tree.selection_set(selected_item)
        else:  # Button click
            selected_item = self.produced_drug_tree.selection()
            if not selected_item:
                messagebox.showwarning("هشدار", "لطفاً یک دارو را انتخاب کنید")
                return
            selected_item = selected_item[0]
            
        # Get drug info
        drug_id = self.produced_drug_tree.item(selected_item, 'values')[3]
        drug_name = self.produced_drug_tree.item(selected_item, 'values')[2]
        
        # Create detail window
        detail_window = tk.Toplevel()
        detail_window.title(f"مدیریت ترکیبات دارو {drug_name} (کد: {drug_id})")
        detail_window.geometry("800x600")
        detail_window.transient(self.parent)
        detail_window.grab_set()
        
        # Initialize total cost variable
        self.total_cost_var = tk.StringVar()
        self.total_cost_var.set("جمع کل هزینه تولید: 0 تومان")
        
        # Notebook for tabs
        detail_notebook = ttk.Notebook(detail_window)
        detail_notebook.pack(fill="both", expand=True, padx=10, pady=10)
        
        # Compositions tab
        comp_tab = ttk.Frame(detail_notebook)
        detail_notebook.add(comp_tab, text="ترکیبات دارو")
        
        # Cost tab
        cost_tab = ttk.Frame(detail_notebook)
        detail_notebook.add(cost_tab, text="اطلاعات هزینه و قیمت")
        
        # Set up compositions tab
        self.setup_compositions_tab(comp_tab, drug_id, detail_window)
        
        # Set up cost tab
        self.setup_cost_tab(cost_tab, drug_id)
        
        # Load initial data
        self.load_compositions(drug_id, detail_window)
    
    def setup_compositions_tab(self, parent, drug_id, main_window):
        """Set up the compositions tab"""
        # Frame for treeview
        tree_frame = ttk.Frame(parent)
        tree_frame.pack(fill="both", expand=True, padx=5, pady=5)
        
        # Treeview for compositions
        columns = ("herb_id", "herb_name", "weight_used", "unit_price", "subtotal")
        self.comp_tree = ttk.Treeview(
            tree_frame, 
            columns=columns, 
            show="headings",
            selectmode="browse"
        )
        
        # Set up columns
        self.comp_tree.heading("herb_id", text="کد گیاه")
        self.comp_tree.heading("herb_name", text="نام گیاه")
        self.comp_tree.heading("weight_used", text="وزن مصرفی (گرم)")
        self.comp_tree.heading("unit_price", text="قیمت واحد (تومان)")
        self.comp_tree.heading("subtotal", text="هزینه جزئی")
        
        self.comp_tree.column("herb_id", width=100, anchor=tk.CENTER)
        self.comp_tree.column("herb_name", width=150, anchor=tk.E)
        self.comp_tree.column("weight_used", width=120, anchor=tk.E)
        self.comp_tree.column("unit_price", width=120, anchor=tk.E)
        self.comp_tree.column("subtotal", width=120, anchor=tk.E)
        
        # Scrollbar
        scrollbar = ttk.Scrollbar(tree_frame, orient="vertical", command=self.comp_tree.yview)
        self.comp_tree.configure(yscrollcommand=scrollbar.set)
        scrollbar.pack(side="left", fill="y")
        self.comp_tree.pack(side="right", fill="both", expand=True)
        
        # Management buttons frame
        btn_frame = ttk.Frame(parent)
        btn_frame.pack(fill="x", padx=5, pady=5)
        
        # Total cost label
        ttk.Label(btn_frame, textvariable=self.total_cost_var, anchor=tk.E).pack(side="left", padx=5)
        
        # Management buttons
        ttk.Button(
            btn_frame, 
            text="+ افزودن ترکیب جدید",
            command=lambda: self.add_new_composition(drug_id, main_window)
        ).pack(side="right", padx=5)
        
        ttk.Button(
            btn_frame, 
            text="ویرایش ترکیب انتخاب شده",
            command=lambda: self.edit_selected_composition(drug_id, main_window)
        ).pack(side="right", padx=5)
        
        ttk.Button(
            btn_frame, 
            text="حذف ترکیب انتخاب شده",
            command=lambda: self.delete_selected_composition(drug_id, main_window)
        ).pack(side="right", padx=5)
        
        # Apply alternating row colors
        self.comp_tree.tag_configure('oddrow', background='#f3f4f6')
        self.comp_tree.tag_configure('evenrow', background='#ffffff')
    
    def setup_cost_tab(self, parent, drug_id):
        """Set up the cost tab"""
        # Frame for cost details
        cost_frame = ttk.Frame(parent, padding=10)
        cost_frame.pack(fill="both", expand=True)
        
        # Get cost data
        try:
            self.cursor.execute('''
                SELECT 
                    raw_materials_cost,
                    loan_interest_cost,
                    container_label_cost,
                    workshop_overhead_cost,
                    total_workshop_cost,
                    representative_price,
                    wholesale_price,
                    retail_price
                FROM Drugs 
                WHERE id = ?
            ''', (drug_id,))
            costs = self.cursor.fetchone()
            
            if costs:
                # Create labels for each cost
                costs_data = [
                    ("قیمت تمام شده مفردات:", costs[0]),
                    ("قیمت قرض زنی:", costs[1]),
                    ("هزینه ظرف و برچسب:", costs[2]),
                    ("هزینه سربار کارگاه:", costs[3]),
                    ("قیمت تمام شده کارگاه:", costs[4]),
                    ("قیمت نمایندگی:", costs[5]),
                    ("قیمت عمده:", costs[6]),
                    ("قیمت خرده:", costs[7])
                ]
                
                # Title
                ttk.Label(
                    cost_frame, 
                    text="اطلاعات هزینه و قیمت‌گذاری", 
                    font=("Arial", 12, "bold")
                ).grid(row=0, column=0, columnspan=2, pady=10, sticky="w")
                
                # Costs
                for i, (label, value) in enumerate(costs_data):
                    ttk.Label(
                        cost_frame, 
                        text=label, 
                        anchor=tk.E,
                        font=("Arial", 10, "bold" if i == 4 or i == 7 else "normal")
                    ).grid(row=i+1, column=1, padx=5, pady=5, sticky="e")
                    
                    ttk.Label(
                        cost_frame, 
                        text=f"{value:,.0f} تومان", 
                        anchor=tk.W,
                        font=("Arial", 10, "bold" if i == 4 or i == 7 else "normal")
                    ).grid(row=i+1, column=0, padx=5, pady=5, sticky="w")
                
                # Profit calculation
                profit = costs[7] - costs[4]  # Retail - Total cost
                profit_percent = (profit / costs[4] * 100) if costs[4] > 0 else 0
                
                ttk.Separator(cost_frame, orient="horizontal").grid(
                    row=len(costs_data)+1, column=0, columnspan=2, sticky="ew", pady=10
                )
                
                ttk.Label(
                    cost_frame, 
                    text="سود خالص:", 
                    anchor=tk.E,
                    font=("Arial", 11, "bold")
                ).grid(row=len(costs_data)+2, column=1, padx=5, pady=5, sticky="e")
                
                ttk.Label(
                    cost_frame, 
                    text=f"{profit:,.0f} تومان ({profit_percent:.1f}%)", 
                    anchor=tk.W,
                    font=("Arial", 11, "bold")
                ).grid(row=len(costs_data)+2, column=0, padx=5, pady=5, sticky="w")
                
                # Configure grid weights
                cost_frame.columnconfigure(0, weight=1)
                cost_frame.columnconfigure(1, weight=0)
                
        except Exception as e:
            messagebox.showerror("خطا", f"خطا در بارگذاری اطلاعات هزینه: {str(e)}")
    
    def load_compositions(self, drug_id, window):
        """Load compositions for a drug"""
        # Clear existing items
        for item in self.comp_tree.get_children():
            self.comp_tree.delete(item)
        
        try:
            # Get compositions
            self.cursor.execute('''
            SELECT dc.herb_id, h.name, dc.weight_used, h.unit_price, dc.subtotal
            FROM DrugCompositions dc
            JOIN Herbs h ON dc.herb_id = h.id
            WHERE dc.drug_id = ?
            ''', (drug_id,))
            
            # Process results
            total = 0
            for i, comp in enumerate(self.cursor.fetchall()):
                herb_id, herb_name, weight_grams, unit_price_per_kg, subtotal = comp
                
                # Display weight in both grams and kilograms
                weight_kg = weight_grams / 1000
                display_weight = f"{weight_grams:g} گرم ({weight_kg:.3f} کیلوگرم)"
                
                # Format unit price and subtotal
                display_unit_price = f"{unit_price_per_kg:,.0f}"
                display_subtotal = f"{subtotal:,.0f}"
                
                # Add to treeview with alternating colors
                tag = 'evenrow' if i % 2 == 0 else 'oddrow'
                self.comp_tree.insert("", "end", values=(
                    herb_id,
                    herb_name,
                    display_weight,
                    display_unit_price,
                    display_subtotal
                ), tags=(tag,))
                
                total += subtotal
            
            # Update total cost label
            self.total_cost_var.set(f"جمع کل هزینه مواد اولیه: {total:,.0f} تومان")
            window.update()
            
        except Exception as e:
            messagebox.showerror("خطا", f"خطا در بارگذاری ترکیبات: {str(e)}")
    
    def add_new_composition(self, drug_id, parent_window):
        """Add a new composition to a drug"""
        add_window = tk.Toplevel(parent_window)
        add_window.title("افزودن ترکیب جدید")
        add_window.transient(parent_window)
        add_window.grab_set()
        
        # Frame for form
        frame = ttk.Frame(add_window, padding=10)
        frame.pack(fill="both", expand=True)
        
        # Form fields
        ttk.Label(frame, text="کد گیاه:").grid(row=0, column=0, padx=5, pady=5, sticky="e")
        herb_id_entry = ttk.Entry(frame, width=20)
        herb_id_entry.grid(row=0, column=1, padx=5, pady=5, sticky="w")
        
        ttk.Label(frame, text="وزن مورد نیاز (گرم):").grid(row=1, column=0, padx=5, pady=5, sticky="e")
        weight_entry = ttk.Entry(frame, width=20)
        weight_entry.grid(row=1, column=1, padx=5, pady=5, sticky="w")
        
        # Herb selector button
        def select_herb():
            # Create herb selection window
            select_window = tk.Toplevel(add_window)
            select_window.title("انتخاب گیاه")
            select_window.transient(add_window)
            select_window.grab_set()
            select_window.geometry("600x400")
            
            # Frame for search
            search_frame = ttk.Frame(select_window, padding=5)
            search_frame.pack(fill="x")
            
            ttk.Label(search_frame, text="جستجو:").pack(side=tk.RIGHT, padx=5)
            search_entry = ttk.Entry(search_frame)
            search_entry.pack(side=tk.RIGHT, fill="x", expand=True, padx=5)
            
            # Herb treeview
            herb_tree = ttk.Treeview(
                select_window,
                columns=("id", "name", "inventory"),
                show="headings",
                height=15
            )
            
            herb_tree.heading("id", text="کد گیاه")
            herb_tree.heading("name", text="نام گیاه")
            herb_tree.heading("inventory", text="موجودی (گرم)")
            
            herb_tree.column("id", width=100, anchor=tk.CENTER)
            herb_tree.column("name", width=250, anchor=tk.E)
            herb_tree.column("inventory", width=150, anchor=tk.E)
            
            # Load herbs
            def load_herbs():
                search_term = search_entry.get().strip().lower()
                
                # Clear existing items
                for item in herb_tree.get_children():
                    herb_tree.delete(item)
                
                # Get herbs with inventory
                query = "SELECT id, name, current_weight FROM Herbs WHERE current_weight > 0"
                params = []
                
                if search_term:
                    query += " AND (id LIKE ? OR name LIKE ?)"
                    params = [f"%{search_term}%", f"%{search_term}%"]
                
                query += " ORDER BY name"
                
                try:
                    if params:
                        herbs = self.cursor.execute(query, params).fetchall()
                    else:
                        herbs = self.cursor.execute(query).fetchall()
                    
                    for herb in herbs:
                        herb_tree.insert("", "end", values=(
                            herb[0],
                            herb[1],
                            f"{herb[2]:,.0f}"
                        ))
                        
                except Exception as e:
                    messagebox.showerror("خطا", f"خطا در بارگذاری اطلاعات گیاهان: {str(e)}")
            
            # Select button action
            def on_select():
                selected = herb_tree.selection()
                if not selected:
                    messagebox.showwarning("هشدار", "لطفاً یک گیاه را انتخاب کنید")
                    return
                
                values = herb_tree.item(selected, "values")
                herb_id_entry.delete(0, tk.END)
                herb_id_entry.insert(0, values[0])
                select_window.destroy()
            
            # Button frame
            button_frame = ttk.Frame(select_window, padding=5)
            button_frame.pack(fill="x", side=tk.BOTTOM)
            
            ttk.Button(button_frame, text="انتخاب", command=on_select).pack(side=tk.RIGHT, padx=5)
            ttk.Button(button_frame, text="انصراف", command=select_window.destroy).pack(side=tk.RIGHT, padx=5)
            
            # Search button
            ttk.Button(search_frame, text="جستجو", command=load_herbs).pack(side=tk.RIGHT, padx=5)
            search_entry.bind("<Return>", lambda e: load_herbs())
            
            # Pack treeview and scrollbar
            scrollbar = ttk.Scrollbar(select_window, orient="vertical", command=herb_tree.yview)
            herb_tree.configure(yscrollcommand=scrollbar.set)
            
            scrollbar.pack(side=tk.LEFT, fill="y")
            herb_tree.pack(fill="both", expand=True, padx=5, pady=5)
            
            # Initial load
            load_herbs()
            
            # Double-click to select
            herb_tree.bind("<Double-1>", lambda e: on_select())
        
        ttk.Button(frame, text="انتخاب از لیست...", command=select_herb).grid(row=0, column=2, padx=5, pady=5)

        def save_composition():
            try:
                herb_id = herb_id_entry.get().strip()
                if not herb_id:
                    messagebox.showwarning("هشدار", "لطفاً کد گیاه را وارد کنید")
                    return
                    
                weight_grams = float(weight_entry.get())
                if weight_grams <= 0:
                    messagebox.showwarning("هشدار", "وزن باید بزرگتر از صفر باشد")
                    return
                
                # Check inventory and get unit price
                self.cursor.execute("SELECT current_weight, unit_price, name FROM Herbs WHERE id = ?", (herb_id,))
                herb_data = self.cursor.fetchone()
                
                if not herb_data:
                    messagebox.showerror("خطا", "کد گیاه نامعتبر است!")
                    return
                
                current_weight, unit_price, herb_name = herb_data
                if current_weight < weight_grams:
                    messagebox.showerror("خطا", f"موجودی ناکافی! موجودی فعلی: {current_weight:,.0f} گرم")
                    return
                
                # Check if herb is already in the drug's compositions
                self.cursor.execute(
                    "SELECT COUNT(*) FROM DrugCompositions WHERE drug_id = ? AND herb_id = ?", 
                    (drug_id, herb_id)
                )
                exists = self.cursor.fetchone()[0] > 0
                
                if exists:
                    messagebox.showerror("خطا", f"این گیاه قبلاً به ترکیبات دارو اضافه شده است")
                    return
                
                # Calculate cost
                weight_kg = weight_grams / 1000
                subtotal = weight_kg * unit_price
                
                # Start transaction
                self.conn.execute("BEGIN TRANSACTION")
                
                try:
                    # Add to compositions
                    self.cursor.execute('''
                    INSERT INTO DrugCompositions (drug_id, herb_id, weight_used, subtotal)
                    VALUES (?, ?, ?, ?)
                    ''', (drug_id, herb_id, weight_grams, subtotal))
                    
                    # Update inventory
                    self.cursor.execute('''
                    UPDATE Herbs SET current_weight = current_weight - ? WHERE id = ?
                    ''', (weight_grams, herb_id))
                    
                    # Update drug cost
                    self.cursor.execute('''
                    UPDATE Drugs 
                    SET raw_materials_cost = raw_materials_cost + ?,
                        total_workshop_cost = total_workshop_cost + ?,
                        representative_price = (raw_materials_cost + ? + loan_interest_cost + container_label_cost + workshop_overhead_cost) * 1.3,
                        wholesale_price = (raw_materials_cost + ? + loan_interest_cost + container_label_cost + workshop_overhead_cost) * 1.2,
                        retail_price = (raw_materials_cost + ? + loan_interest_cost + container_label_cost + workshop_overhead_cost) * 1.4
                    WHERE id = ?
                    ''', (subtotal, subtotal, subtotal, subtotal, subtotal, drug_id))
                    
                    self.conn.commit()
                    messagebox.showinfo("موفق", f"ترکیب با موفقیت اضافه شد!")
                    add_window.destroy()
                    
                    # Refresh compositions list
                    self.load_compositions(drug_id, parent_window)
                    self.load_produced_drugs()
                    
                except Exception as e:
                    self.conn.rollback()
                    raise e
                
            except ValueError:
                messagebox.showerror("خطا", "لطفاً یک عدد معتبر برای وزن وارد کنید!")
            except Exception as e:
                messagebox.showerror("خطا", f"خطا در افزودن ترکیب: {str(e)}")
        
        # Buttons
        button_frame = ttk.Frame(frame)
        button_frame.grid(row=2, column=0, columnspan=3, pady=10)
        
        ttk.Button(button_frame, text="ذخیره", command=save_composition).pack(side=tk.RIGHT, padx=5)
        ttk.Button(button_frame, text="انصراف", command=add_window.destroy).pack(side=tk.RIGHT, padx=5)
    
    def edit_selected_composition(self, drug_id, parent_window):
        """Edit the selected composition"""
        selected = self.comp_tree.selection()
        if not selected:
            messagebox.showwarning("هشدار", "لطفاً یک ترکیب را انتخاب کنید")
            return
        
        comp_data = self.comp_tree.item(selected, 'values')
        herb_id = comp_data[0]
        
        # Extract weight in grams from display string (e.g., "150 گرم (0.150 کیلوگرم)")
        weight_display = comp_data[2]
        current_weight_grams = float(weight_display.split()[0])
        
        # Create edit window
        edit_window = tk.Toplevel(parent_window)
        edit_window.title("ویرایش ترکیب")
        edit_window.transient(parent_window)
        edit_window.grab_set()
        
        # Form frame
        frame = ttk.Frame(edit_window, padding=10)
        frame.pack(fill="both", expand=True)
        
        # Form fields
        ttk.Label(frame, text=f"کد گیاه: {herb_id}").grid(row=0, column=0, columnspan=2, padx=5, pady=5, sticky="w")
        ttk.Label(frame, text=f"نام گیاه: {comp_data[1]}").grid(row=1, column=0, columnspan=2, padx=5, pady=5, sticky="w")
        
        ttk.Label(frame, text="وزن جدید (گرم):").grid(row=2, column=0, padx=5, pady=5, sticky="e")
        weight_entry = ttk.Entry(frame, width=15)
        weight_entry.insert(0, current_weight_grams)
        weight_entry.grid(row=2, column=1, padx=5, pady=5, sticky="w")
        
        def update_composition():
            try:
                new_weight_grams = float(weight_entry.get())
                if new_weight_grams <= 0:
                    messagebox.showwarning("هشدار", "وزن باید بزرگتر از صفر باشد")
                    return
                    
                weight_diff_grams = new_weight_grams - current_weight_grams
                
                # Check inventory
                self.cursor.execute("SELECT current_weight, unit_price FROM Herbs WHERE id = ?", (herb_id,))
                available, unit_price_per_kg = self.cursor.fetchone()
                
                if weight_diff_grams > 0 and available < weight_diff_grams:
                    messagebox.showerror("خطا", f"موجودی ناکافی! موجودی قابل استفاده: {available:,.0f} گرم")
                    return
                
                # Calculate new cost
                new_weight_kg = new_weight_grams / 1000
                new_subtotal = new_weight_kg * unit_price_per_kg
                
                # Get old subtotal
                self.cursor.execute(
                    "SELECT subtotal FROM DrugCompositions WHERE drug_id = ? AND herb_id = ?", 
                    (drug_id, herb_id)
                )
                old_subtotal = self.cursor.fetchone()[0]
                
                # Calculate cost difference
                cost_diff = new_subtotal - old_subtotal
                
                # Start transaction
                self.conn.execute("BEGIN TRANSACTION")
                
                try:
                    # Update composition
                    self.cursor.execute('''
                    UPDATE DrugCompositions 
                    SET weight_used = ?, subtotal = ?
                    WHERE drug_id = ? AND herb_id = ?
                    ''', (new_weight_grams, new_subtotal, drug_id, herb_id))
                    
                    # Update inventory
                    self.cursor.execute('''
                    UPDATE Herbs 
                    SET current_weight = current_weight - ?
                    WHERE id = ?
                    ''', (weight_diff_grams, herb_id))
                    
                    # Update drug cost
                    self.cursor.execute('''
                    UPDATE Drugs 
                    SET raw_materials_cost = raw_materials_cost + ?,
                        total_workshop_cost = total_workshop_cost + ?,
                        representative_price = (raw_materials_cost + ? + loan_interest_cost + container_label_cost + workshop_overhead_cost) * 1.3,
                        wholesale_price = (raw_materials_cost + ? + loan_interest_cost + container_label_cost + workshop_overhead_cost) * 1.2,
                        retail_price = (raw_materials_cost + ? + loan_interest_cost + container_label_cost + workshop_overhead_cost) * 1.4
                    WHERE id = ?
                    ''', (cost_diff, cost_diff, cost_diff, cost_diff, cost_diff, drug_id))
                    
                    self.conn.commit()
                    messagebox.showinfo("موفق", "ترکیب با موفقیت ویرایش شد!")
                    edit_window.destroy()
                    
                    # Refresh compositions list
                    self.load_compositions(drug_id, parent_window)
                    self.load_produced_drugs()
                    
                except Exception as e:
                    self.conn.rollback()
                    raise e
                
            except ValueError:
                messagebox.showerror("خطا", "لطفاً یک عدد معتبر برای وزن وارد کنید!")
            except Exception as e:
                messagebox.showerror("خطا", f"خطا در ویرایش ترکیب: {str(e)}")
        
        # Buttons
        button_frame = ttk.Frame(frame)
        button_frame.grid(row=3, column=0, columnspan=2, pady=10)
        
        ttk.Button(button_frame, text="ذخیره تغییرات", command=update_composition).pack(side=tk.RIGHT, padx=5)
        ttk.Button(button_frame, text="انصراف", command=edit_window.destroy).pack(side=tk.RIGHT, padx=5)
    
    def delete_selected_composition(self, drug_id, parent_window):
        """Delete the selected composition"""
        selected = self.comp_tree.selection()
        if not selected:
            messagebox.showwarning("هشدار", "لطفاً یک ترکیب را انتخاب کنید")
            return
        
        try:
            comp_data = self.comp_tree.item(selected, 'values')
            herb_id = comp_data[0]
            herb_name = comp_data[1]
            
            # Extract weight from display string
            weight_str = comp_data[2].split()[0]  # Get "150" from "150 گرم (0.150 کیلوگرم)"
            weight = float(weight_str)
            
            # Get subtotal
            self.cursor.execute(
                "SELECT subtotal FROM DrugCompositions WHERE drug_id = ? AND herb_id = ?", 
                (drug_id, herb_id)
            )
            subtotal = self.cursor.fetchone()[0]
            
            confirm = messagebox.askyesno(
                "تأیید حذف",
                f"آیا از حذف ترکیب گیاه '{herb_name}' (کد: {herb_id}) مطمئن هستید؟\nوزن مصرفی: {weight:,.0f} گرم"
            )
            
            if confirm:
                try:
                    # Start transaction
                    self.conn.execute("BEGIN TRANSACTION")
                    
                    # Delete composition
                    self.cursor.execute('''
                    DELETE FROM DrugCompositions 
                    WHERE drug_id = ? AND herb_id = ?
                    ''', (drug_id, herb_id))
                    
                    # Return inventory
                    self.cursor.execute('''
                    UPDATE Herbs 
                    SET current_weight = current_weight + ?
                    WHERE id = ?
                    ''', (weight, herb_id))
                    
                    # Update drug cost
                    self.cursor.execute('''
                    UPDATE Drugs 
                    SET raw_materials_cost = raw_materials_cost - ?,
                        total_workshop_cost = total_workshop_cost - ?,
                        representative_price = (raw_materials_cost - ? + loan_interest_cost + container_label_cost + workshop_overhead_cost) * 1.3,
                        wholesale_price = (raw_materials_cost - ? + loan_interest_cost + container_label_cost + workshop_overhead_cost) * 1.2,
                        retail_price = (raw_materials_cost - ? + loan_interest_cost + container_label_cost + workshop_overhead_cost) * 1.4
                    WHERE id = ?
                    ''', (subtotal, subtotal, subtotal, subtotal, subtotal, drug_id))
                    
                    self.conn.commit()
                    messagebox.showinfo("موفق", "ترکیب با موفقیت حذف شد!")
                    
                    # Refresh compositions list
                    self.load_compositions(drug_id, parent_window)
                    self.load_produced_drugs()
                    
                except Exception as e:
                    self.conn.rollback()
                    raise e
                    
        except Exception as e:
            messagebox.showerror("خطا", f"خطا در حذف ترکیب: {str(e)}")


class ReportTabManager:
    def __init__(self, parent, conn):
        self.parent = parent
        self.conn = conn
        self.cursor = conn.cursor()
        
        # Create notebook for report types
        self.reports_notebook = ttk.Notebook(self.parent)
        self.reports_notebook.pack(fill="both", expand=True, padx=10, pady=10)
        
        # Create report tabs
        self.summary_tab = ttk.Frame(self.reports_notebook)
        self.inventory_tab = ttk.Frame(self.reports_notebook)
        self.drugs_tab = ttk.Frame(self.reports_notebook)
        self.financial_tab = ttk.Frame(self.reports_notebook)
        
        self.reports_notebook.add(self.summary_tab, text="خلاصه وضعیت")
        self.reports_notebook.add(self.inventory_tab, text="گزارش موجودی")
        self.reports_notebook.add(self.drugs_tab, text="گزارش داروها")
        self.reports_notebook.add(self.financial_tab, text="گزارش مالی")
        
        # Set up each tab
        self.setup_summary_tab()
        self.setup_inventory_tab()
        self.setup_drugs_tab()
        self.setup_financial_tab()
        
        # Create toolbar for reports
        self.create_report_toolbar()
        
        # Generate initial reports
        self.generate_report()
    
    def setup_summary_tab(self):
        """Set up the summary tab with overview statistics"""
        report_frame = ttk.LabelFrame(self.summary_tab, text="خلاصه وضعیت انبار و تولید")
        report_frame.pack(pady=20, padx=20, fill="both", expand=True)

        # StringVars for dynamic labels
        self.total_herb_types_var = tk.StringVar(value="...")
        self.total_herb_weight_var = tk.StringVar(value="...")
        self.total_inventory_value_var = tk.StringVar(value="...")
        self.total_drugs_produced_var = tk.StringVar(value="...")
        
        # Add chart canvas
        self.chart_frame = ttk.Frame(report_frame)
        self.chart_frame.pack(fill="both", expand=True, pady=10)
        
        # Left side - Statistics
        stats_frame = ttk.Frame(self.chart_frame)
        stats_frame.pack(side=tk.RIGHT, fill="y", padx=20)

        # Labels for report data (Label on right - col 1, Value on left - col 0)
        ttk.Label(stats_frame, text="تعداد کل انواع گیاهان:", anchor=tk.E).grid(row=0, column=1, padx=5, pady=5, sticky="e")
        ttk.Label(stats_frame, textvariable=self.total_herb_types_var, anchor=tk.W).grid(row=0, column=0, padx=5, pady=5, sticky="w")

        ttk.Label(stats_frame, text="مجموع وزن کل گیاهان (گرم):", anchor=tk.E).grid(row=1, column=1, padx=5, pady=5, sticky="e")
        ttk.Label(stats_frame, textvariable=self.total_herb_weight_var, anchor=tk.W).grid(row=1, column=0, padx=5, pady=5, sticky="w")

        ttk.Label(stats_frame, text="ارزش کل موجودی انبار (تومان):", anchor=tk.E).grid(row=2, column=1, padx=5, pady=5, sticky="e")
        ttk.Label(stats_frame, textvariable=self.total_inventory_value_var, anchor=tk.W).grid(row=2, column=0, padx=5, pady=5, sticky="w")

        ttk.Label(stats_frame, text="تعداد کل داروهای تولید شده:", anchor=tk.E).grid(row=3, column=1, padx=5, pady=5, sticky="e")
        ttk.Label(stats_frame, textvariable=self.total_drugs_produced_var, anchor=tk.W).grid(row=3, column=0, padx=5, pady=5, sticky="w")

        # Configure column weights for resizing
        stats_frame.columnconfigure(0, weight=1)
        stats_frame.columnconfigure(1, weight=0)
        
        # Right side - Visual dashboard
        dashboard_frame = ttk.LabelFrame(self.chart_frame, text="نمودار آماری")
        dashboard_frame.pack(side=tk.LEFT, fill="both", expand=True)
        
        # Placeholder for chart (in a real implementation, you'd use matplotlib or another charting library)
        self.chart_canvas = tk.Canvas(dashboard_frame, bg="white", height=200)
        self.chart_canvas.pack(fill="both", expand=True, padx=10, pady=10)
        
        # Draw placeholder chart
        self.draw_placeholder_chart()
        
        # Bottom section - Latest activities
        activity_frame = ttk.LabelFrame(report_frame, text="آخرین فعالیت‌ها")
        activity_frame.pack(fill="x", pady=10, padx=20)
        
        # Activity list (most recent drugs and herbs)
        self.activity_tree = ttk.Treeview(
            activity_frame,
            columns=("date", "type", "name", "id"),
            show="headings",
            height=5
        )
        
        # Configure columns
        self.activity_tree.heading("date", text="تاریخ")
        self.activity_tree.heading("type", text="نوع فعالیت")
        self.activity_tree.heading("name", text="نام")
        self.activity_tree.heading("id", text="کد")
        
        self.activity_tree.column("date", width=100, anchor=tk.CENTER)
        self.activity_tree.column("type", width=100, anchor=tk.CENTER)
        self.activity_tree.column("name", width=200, anchor=tk.E)
        self.activity_tree.column("id", width=100, anchor=tk.CENTER)
        
        # Add scrollbar
        activity_scrollbar = ttk.Scrollbar(activity_frame, orient="vertical", command=self.activity_tree.yview)
        self.activity_tree.configure(yscrollcommand=activity_scrollbar.set)
        
        activity_scrollbar.pack(side=tk.LEFT, fill="y")
        self.activity_tree.pack(side=tk.RIGHT, fill="both", expand=True, pady=5)
        
        # Refresh button
        refresh_btn = ttk.Button(report_frame, text="به‌روزرسانی گزارش", command=self.generate_report)
        refresh_btn.pack(pady=15)
    
    def draw_placeholder_chart(self):
        """Draw a placeholder chart on the canvas"""
        self.chart_canvas.delete("all")
        
        # Get data for chart
        try:
            # Get total herb weight
            self.cursor.execute("SELECT SUM(current_weight) FROM Herbs")
            total_weight = self.cursor.fetchone()[0] or 0
            
            # Get top 5 herbs by weight
            self.cursor.execute("""
                SELECT name, current_weight
                FROM Herbs
                ORDER BY current_weight DESC
                LIMIT 5
            """)
            top_herbs = self.cursor.fetchall()
            
            # Draw chart
            width = self.chart_canvas.winfo_width() or 400
            height = self.chart_canvas.winfo_height() or 200
            
            # Draw title
            self.chart_canvas.create_text(
                width/2, 20, 
                text="گیاهان با بیشترین موجودی", 
                font=("Arial", 12, "bold"),
                fill="#3B82F6"
            )
            
            if not top_herbs:
                self.chart_canvas.create_text(
                    width/2, height/2, 
                    text="داده‌ای موجود نیست", 
                    font=("Arial", 10),
                    fill="#888888"
                )
                return
            
            # Calculate bar dimensions
            bar_width = width / (len(top_herbs) * 2)
            max_height = height - 60  # Leave space for labels
            max_value = max([herb[1] for herb in top_herbs]) if top_herbs else 1
            
            # Draw bars
            for i, (name, weight) in enumerate(top_herbs):
                # Calculate bar height (proportional to weight)
                bar_height = (weight / max_value) * max_height if max_value > 0 else 0
                
                # Calculate bar position
                x = width * (i + 1) / (len(top_herbs) + 1)
                y = height - 40  # Bottom position (leave space for labels)
                
                # Draw bar
                self.chart_canvas.create_rectangle(
                    x - bar_width/2, y - bar_height,
                    x + bar_width/2, y,
                    fill="#3B82F6",
                    outline="#2563EB"
                )
                
                # Draw value label
                self.chart_canvas.create_text(
                    x, y - bar_height - 10,
                    text=f"{weight:,.0f}",
                    font=("Arial", 8),
                    fill="#555555"
                )
                
                # Draw name label
                self.chart_canvas.create_text(
                    x, y + 10,
                    text=name if len(name) < 15 else name[:12] + "...",
                    font=("Arial", 8),
                    fill="#555555"
                )
            
        except Exception as e:
            print(f"Error drawing chart: {e}")
            self.chart_canvas.create_text(
                width/2, height/2, 
                text="خطا در نمایش نمودار", 
                font=("Arial", 10),
                fill="#FF0000"
            )
    
    def setup_inventory_tab(self):
        """Set up the inventory report tab"""
        # Control frame for filters
        control_frame = ttk.Frame(self.inventory_tab)
        control_frame.pack(fill="x", padx=10, pady=5)
        
        # Search/filter
        ttk.Label(control_frame, text="جستجو:").pack(side=tk.RIGHT, padx=5)
        self.inventory_search = ttk.Entry(control_frame, justify=tk.RIGHT)
        self.inventory_search.pack(side=tk.RIGHT, padx=5, fill="x", expand=True)
        self.inventory_search.bind("<KeyRelease>", self.filter_inventory_report)
        
        # Sort options
        ttk.Label(control_frame, text="مرتب‌سازی:").pack(side=tk.RIGHT, padx=5)
        self.inventory_sort = ttk.Combobox(
            control_frame, 
            values=["نام", "کد", "موجودی (صعودی)", "موجودی (نزولی)", "قیمت واحد"], 
            width=15,
            state="readonly"
        )
        self.inventory_sort.current(0)
        self.inventory_sort.pack(side=tk.RIGHT, padx=5)
        self.inventory_sort.bind("<<ComboboxSelected>>", self.filter_inventory_report)
        
        # Treeview for inventory report
        columns = ("id", "name", "weight", "unit_price", "total_value", "shelf", "last_update")
        self.inventory_tree = ttk.Treeview(self.inventory_tab, columns=columns, show="headings", height=15)
        
        # Set up columns
        self.inventory_tree.heading("id", text="کد گیاه")
        self.inventory_tree.heading("name", text="نام گیاه")
        self.inventory_tree.heading("weight", text="موجودی (گرم/کیلوگرم)")
        self.inventory_tree.heading("unit_price", text="قیمت واحد (تومان/کیلوگرم)")
        self.inventory_tree.heading("total_value", text="ارزش کل")
        self.inventory_tree.heading("shelf", text="محل قفسه")
        self.inventory_tree.heading("last_update", text="آخرین به‌روزرسانی")
        
        self.inventory_tree.column("id", width=80, anchor=tk.CENTER)
        self.inventory_tree.column("name", width=200, anchor=tk.E)
        self.inventory_tree.column("weight", width=150, anchor=tk.E)
        self.inventory_tree.column("unit_price", width=150, anchor=tk.E)
        self.inventory_tree.column("total_value", width=120, anchor=tk.E)
        self.inventory_tree.column("shelf", width=100, anchor=tk.E)
        self.inventory_tree.column("last_update", width=100, anchor=tk.CENTER)
        
        # Add scrollbar
        inventory_scrollbar = ttk.Scrollbar(self.inventory_tab, orient="vertical", command=self.inventory_tree.yview)
        self.inventory_tree.configure(yscrollcommand=inventory_scrollbar.set)
        
        # Pack treeview and scrollbar
        inventory_scrollbar.pack(side=tk.LEFT, fill="y")
        self.inventory_tree.pack(side=tk.RIGHT, fill="both", expand=True, padx=10, pady=5)
        
        # Apply alternating row colors
        self.inventory_tree.tag_configure('oddrow', background='#f3f4f6')
        self.inventory_tree.tag_configure('evenrow', background='#ffffff')
        
        # Summary frame (at bottom)
        summary_frame = ttk.Frame(self.inventory_tab)
        summary_frame.pack(fill="x", padx=10, pady=5)
        
        # Summary labels
        self.inventory_count_var = tk.StringVar(value="تعداد گیاهان: 0")
        self.inventory_total_weight_var = tk.StringVar(value="وزن کل: 0 گرم")
        self.inventory_total_value_var = tk.StringVar(value="ارزش کل: 0 تومان")
        
        ttk.Label(summary_frame, textvariable=self.inventory_count_var).pack(side=tk.RIGHT, padx=10)
        ttk.Label(summary_frame, textvariable=self.inventory_total_weight_var).pack(side=tk.RIGHT, padx=10)
        ttk.Label(summary_frame, textvariable=self.inventory_total_value_var).pack(side=tk.RIGHT, padx=10)
    
    def filter_inventory_report(self, event=None):
        """Filter and load inventory report based on search and sort criteria"""
        # Get search term
        search_term = self.inventory_search.get().strip().lower()
        
        # Get sort option
        sort_option = self.inventory_sort.get()
        
        # Build query
        query = """
            SELECT id, name, current_weight, unit_price, 
                (unit_price * (current_weight / 1000.0)), 
                shelf_location, purchase_date
            FROM Herbs
        """
        
        # Add search filter if provided
        params = []
        if search_term:
            query += " WHERE id LIKE ? OR name LIKE ? OR shelf_location LIKE ?"
            params = [f"%{search_term}%", f"%{search_term}%", f"%{search_term}%"]
        
        # Add sorting
        if sort_option == "نام":
            query += " ORDER BY name"
        elif sort_option == "کد":
            query += " ORDER BY id"
        elif sort_option == "موجودی (صعودی)":
            query += " ORDER BY current_weight ASC"
        elif sort_option == "موجودی (نزولی)":
            query += " ORDER BY current_weight DESC"
        elif sort_option == "قیمت واحد":
            query += " ORDER BY unit_price DESC"
        else:
            query += " ORDER BY name"  # Default sort
        
        # Execute query and update treeview
        self.load_inventory_report(query, params)
    
    def load_inventory_report(self, query, params=[]):
        """Load inventory report based on query"""
        # Clear existing items
        for item in self.inventory_tree.get_children():
            self.inventory_tree.delete(item)
        
        try:
            # Execute query
            if params:
                results = self.cursor.execute(query, params).fetchall()
            else:
                results = self.cursor.execute(query).fetchall()
            
            # Process results
            total_weight = 0
            total_value = 0
            
            for i, row in enumerate(results):
                herb_id, name, weight, price, value, shelf, date = row
                
                # Calculate kg weight
                weight_kg = weight / 1000 if weight else 0
                
                # Format display values
                weight_display = f"{weight:,.0f} گرم / {weight_kg:.2f} کیلوگرم" if weight else "0"
                price_display = f"{price:,.0f}" if price else "0"
                value_display = f"{value:,.0f} تومان" if value else "0"
                shelf_display = shelf or "نامشخص"
                date_display = date or "ثبت نشده"
                
                # Add to treeview with alternating colors
                tag = 'evenrow' if i % 2 == 0 else 'oddrow'
                self.inventory_tree.insert("", "end", values=(
                    herb_id,
                    name,
                    weight_display,
                    price_display,
                    value_display,
                    shelf_display,
                    date_display
                ), tags=(tag,))
                
                # Update totals
                total_weight += weight or 0
                total_value += value or 0
            
            # Update summary
            self.inventory_count_var.set(f"تعداد گیاهان: {len(results)}")
            self.inventory_total_weight_var.set(f"وزن کل: {total_weight:,.0f} گرم")
            self.inventory_total_value_var.set(f"ارزش کل: {total_value:,.0f} تومان")
            
        except Exception as e:
            messagebox.showerror("خطا", f"خطا در بارگیری گزارش موجودی: {str(e)}")
    
    def setup_drugs_tab(self):
        """Set up the drugs report tab"""
        # Control frame for filters
        control_frame = ttk.Frame(self.drugs_tab)
        control_frame.pack(fill="x", padx=10, pady=5)
        
        # Date range filters
        date_frame = ttk.LabelFrame(control_frame, text="بازه زمانی")
        date_frame.pack(side=tk.RIGHT, padx=5, pady=5)
        
        ttk.Label(date_frame, text="از تاریخ:").grid(row=0, column=0, padx=5, pady=2)
        self.from_date = ttk.Entry(date_frame, width=12)
        self.from_date.grid(row=0, column=1, padx=5, pady=2)
        
        ttk.Label(date_frame, text="تا تاریخ:").grid(row=0, column=2, padx=5, pady=2)
        self.to_date = ttk.Entry(date_frame, width=12)
        self.to_date.grid(row=0, column=3, padx=5, pady=2)
        
        # Disease filter
        disease_frame = ttk.Frame(control_frame)
        disease_frame.pack(side=tk.RIGHT, padx=5, pady=5)
        
        ttk.Label(disease_frame, text="بیماری:").pack(side=tk.RIGHT, padx=5)
        
        # Get diseases for combobox
        diseases = self.cursor.execute("SELECT name FROM Diseases ORDER BY name").fetchall()
        disease_names = ["همه"] + [d[0] for d in diseases]
        
        self.disease_filter = ttk.Combobox(
            disease_frame, 
            values=disease_names, 
            width=15,
            state="readonly"
        )
        self.disease_filter.current(0)
        self.disease_filter.pack(side=tk.RIGHT, padx=5)
        
        # Apply filters button
        ttk.Button(
            control_frame, 
            text="اعمال فیلتر",
            command=self.filter_drugs_report
        ).pack(side=tk.LEFT, padx=5, pady=5)
        
        # Drugs report treeview
        columns = ("id", "name", "production_date", "diseases", "compositions", "total_cost", "retail_price", "profit")
        self.drugs_tree = ttk.Treeview(self.drugs_tab, columns=columns, show="headings", height=15)
        
        # Set up columns
        self.drugs_tree.heading("id", text="کد دارو")
        self.drugs_tree.heading("name", text="نام دارو")
        self.drugs_tree.heading("production_date", text="تاریخ تولید")
        self.drugs_tree.heading("diseases", text="بیماری مرتبط")
        self.drugs_tree.heading("compositions", text="تعداد ترکیبات")
        self.drugs_tree.heading("total_cost", text="هزینه تولید")
        self.drugs_tree.heading("retail_price", text="قیمت فروش")
        self.drugs_tree.heading("profit", text="سود")
        
        self.drugs_tree.column("id", width=70, anchor=tk.CENTER)
        self.drugs_tree.column("name", width=150, anchor=tk.E)
        self.drugs_tree.column("production_date", width=100, anchor=tk.CENTER)
        self.drugs_tree.column("diseases", width=150, anchor=tk.E)
        self.drugs_tree.column("compositions", width=100, anchor=tk.CENTER)
        self.drugs_tree.column("total_cost", width=120, anchor=tk.E)
        self.drugs_tree.column("retail_price", width=120, anchor=tk.E)
        self.drugs_tree.column("profit", width=120, anchor=tk.E)
        
        # Add scrollbar
        drugs_scrollbar = ttk.Scrollbar(self.drugs_tab, orient="vertical", command=self.drugs_tree.yview)
        self.drugs_tree.configure(yscrollcommand=drugs_scrollbar.set)
        
        # Pack treeview and scrollbar
        drugs_scrollbar.pack(side=tk.LEFT, fill="y")
        self.drugs_tree.pack(side=tk.RIGHT, fill="both", expand=True, padx=10, pady=5)
        
        # Apply alternating row colors
        self.drugs_tree.tag_configure('oddrow', background='#f3f4f6')
        self.drugs_tree.tag_configure('evenrow', background='#ffffff')
        
        # Summary frame
        summary_frame = ttk.Frame(self.drugs_tab)
        summary_frame.pack(fill="x", padx=10, pady=5)
        
        # Summary labels
        self.drugs_count_var = tk.StringVar(value="تعداد داروها: 0")
        self.drugs_total_cost_var = tk.StringVar(value="هزینه کل: 0 تومان")
        self.drugs_total_profit_var = tk.StringVar(value="سود کل: 0 تومان")
        
        ttk.Label(summary_frame, textvariable=self.drugs_count_var).pack(side=tk.RIGHT, padx=10)
        ttk.Label(summary_frame, textvariable=self.drugs_total_cost_var).pack(side=tk.RIGHT, padx=10)
        ttk.Label(summary_frame, textvariable=self.drugs_total_profit_var).pack(side=tk.RIGHT, padx=10)
        
        # Load initial data
        self.filter_drugs_report()
    
    def filter_drugs_report(self):
        """Filter and load drugs report based on filters"""
        # Get filter values
        from_date = self.from_date.get().strip()
        to_date = self.to_date.get().strip()
        disease = self.disease_filter.get()
        
        # Build base query
        base_query = """
            SELECT 
                d.id,
                d.name,
                d.production_date,
                GROUP_CONCAT(ds.name, '، ') AS diseases,
                COUNT(DISTINCT dc.herb_id) AS composition_count,
                d.total_workshop_cost,
                d.retail_price,
                (d.retail_price - d.total_workshop_cost) AS profit
            FROM Drugs d
            LEFT JOIN DrugDiseases dd ON d.id = dd.drug_id
            LEFT JOIN Diseases ds ON dd.disease_id = ds.id
            LEFT JOIN DrugCompositions dc ON d.id = dc.drug_id
        """
        
        # Add filter conditions
        conditions = []
        params = []
        
        if from_date:
            conditions.append("d.production_date >= ?")
            params.append(from_date)
        
        if to_date:
            conditions.append("d.production_date <= ?")
            params.append(to_date)
        
        if disease and disease != "همه":
            conditions.append("ds.name = ?")
            params.append(disease)
        
        # Add conditions to query
        if conditions:
            base_query += " WHERE " + " AND ".join(conditions)
        
        # Add grouping and ordering
        base_query += " GROUP BY d.id ORDER BY d.production_date DESC"
        
        # Execute query and update treeview
        self.load_drugs_report(base_query, params)
    
    def load_drugs_report(self, query, params=[]):
        """Load drugs report based on query"""
        # Clear existing items
        for item in self.drugs_tree.get_children():
            self.drugs_tree.delete(item)
        
        try:
            # Execute query
            if params:
                drugs = self.cursor.execute(query, params).fetchall()
            else:
                drugs = self.cursor.execute(query).fetchall()
            
            # Process results
            total_cost = 0
            total_profit = 0
            
            for i, drug in enumerate(drugs):
                drug_id, name, prod_date, diseases, comp_count, total_cost_val, retail_price, profit = drug
                
                # Format display values
                diseases_display = diseases if diseases else "بدون بیماری مرتبط"
                cost_display = f"{total_cost_val:,.0f}" if total_cost_val else "0"
                price_display = f"{retail_price:,.0f}" if retail_price else "0"
                profit_display = f"{profit:,.0f}" if profit else "0"
                
                # Add to treeview with alternating colors
                tag = 'evenrow' if i % 2 == 0 else 'oddrow'
                self.drugs_tree.insert("", "end", values=(
                    drug_id,
                    name,
                    prod_date,
                    diseases_display,
                    comp_count,
                    cost_display,
                    price_display,
                    profit_display
                ), tags=(tag,))
                
                # Update totals
                total_cost += total_cost_val or 0
                total_profit += profit or 0
            
            # Update summary
            self.drugs_count_var.set(f"تعداد داروها: {len(drugs)}")
            self.drugs_total_cost_var.set(f"هزینه کل: {total_cost:,.0f} تومان")
            self.drugs_total_profit_var.set(f"سود کل: {total_profit:,.0f} تومان")
            
        except Exception as e:
            messagebox.showerror("خطا", f"خطا در بارگیری گزارش داروها: {str(e)}")
    
    def setup_financial_tab(self):
        """Set up the financial report tab"""
        # Create cards for financial summary
        summary_frame = ttk.Frame(self.financial_tab, padding=10)
        summary_frame.pack(fill="x", padx=10, pady=10)
        
        # Create cards
        card_data = [
            ("ارزش کل موجودی", "total_inventory_var", "#4CAF50"),
            ("هزینه کل تولید", "total_production_cost_var", "#F44336"),
            ("درآمد تخمینی", "estimated_revenue_var", "#2196F3"),
            ("سود ناخالص", "gross_profit_var", "#FFC107")
        ]
        
        # Create StringVars for dynamic updating
        self.financial_vars = {}
        for var_name in [data[1] for data in card_data]:
            self.financial_vars[var_name] = tk.StringVar(value="0 تومان")
        
        # Create cards
        for i, (title, var_name, color) in enumerate(card_data):
            card_frame = ttk.Frame(summary_frame, padding=10)
            card_frame.grid(row=0, column=i, padx=10, pady=5, sticky="nsew")
            
            ttk.Label(
                card_frame, 
                text=title,
                font=("Arial", 10, "bold")
            ).pack(anchor="center")
            
            ttk.Label(
                card_frame, 
                textvariable=self.financial_vars[var_name],
                font=("Arial", 12, "bold")
            ).pack(anchor="center", pady=5)
            
            # Add colored indicator
            indicator = tk.Canvas(card_frame, width=50, height=5, highlightthickness=0)
            indicator.pack(anchor="center", pady=5)
            indicator.create_rectangle(0, 0, 50, 5, fill=color, outline="")
        
        # Configure grid
        summary_frame.columnconfigure(0, weight=1)
        summary_frame.columnconfigure(1, weight=1)
        summary_frame.columnconfigure(2, weight=1)
        summary_frame.columnconfigure(3, weight=1)
        
        # Monthly report frame
        monthly_frame = ttk.LabelFrame(self.financial_tab, text="گزارش ماهیانه")
        monthly_frame.pack(fill="both", expand=True, padx=10, pady=5)
        
        # Monthly report treeview
        columns = ("month", "drug_count", "cost", "revenue", "profit")
        self.monthly_tree = ttk.Treeview(monthly_frame, columns=columns, show="headings", height=10)
        
        # Set up columns
        self.monthly_tree.heading("month", text="ماه")
        self.monthly_tree.heading("drug_count", text="تعداد داروهای تولید شده")
        self.monthly_tree.heading("cost", text="هزینه تولید")
        self.monthly_tree.heading("revenue", text="درآمد تخمینی")
        self.monthly_tree.heading("profit", text="سود")
        
        self.monthly_tree.column("month", width=100, anchor=tk.CENTER)
        self.monthly_tree.column("drug_count", width=150, anchor=tk.CENTER)
        self.monthly_tree.column("cost", width=150, anchor=tk.E)
        self.monthly_tree.column("revenue", width=150, anchor=tk.E)
        self.monthly_tree.column("profit", width=150, anchor=tk.E)
        
        # Add scrollbar
        monthly_scrollbar = ttk.Scrollbar(monthly_frame, orient="vertical", command=self.monthly_tree.yview)
        self.monthly_tree.configure(yscrollcommand=monthly_scrollbar.set)
        
        # Pack treeview and scrollbar
        monthly_scrollbar.pack(side=tk.LEFT, fill="y")
        self.monthly_tree.pack(side=tk.RIGHT, fill="both", expand=True, pady=5)
        
        # Apply alternating row colors
        self.monthly_tree.tag_configure('oddrow', background='#f3f4f6')
        self.monthly_tree.tag_configure('evenrow', background='#ffffff')
        
        # Year filter frame
        filter_frame = ttk.Frame(self.financial_tab)
        filter_frame.pack(fill="x", padx=10, pady=5)
        
        ttk.Label(filter_frame, text="سال:").pack(side=tk.RIGHT, padx=5)
        
        # Get unique years from database
        years = self.get_available_years()
        
        self.year_filter = ttk.Combobox(
            filter_frame, 
            values=years,
            width=10,
            state="readonly"
        )
        
        # Set default to current year or first available
        current_year = datetime.now().year
        if str(current_year) in years:
            self.year_filter.set(str(current_year))
        elif years:
            self.year_filter.current(0)
        
        self.year_filter.pack(side=tk.RIGHT, padx=5)
        self.year_filter.bind("<<ComboboxSelected>>", self.update_financial_report)
        
        # Load initial data
        self.update_financial_report()
    
    def get_available_years(self):
        """Get available years from production dates"""
        try:
            self.cursor.execute("SELECT DISTINCT substr(production_date, 1, 4) FROM Drugs ORDER BY 1 DESC")
            years = [row[0] for row in self.cursor.fetchall()]
            return years or [str(datetime.now().year)]
        except:
            return [str(datetime.now().year)]
    
    def update_financial_report(self, event=None):
        """Update financial report based on selected year"""
        year = self.year_filter.get()
        
        # Clear existing items
        for item in self.monthly_tree.get_children():
            self.monthly_tree.delete(item)
        
        try:
            # Get monthly data
            query = """
                SELECT 
                    substr(production_date, 6, 2) as month,
                    COUNT(*) as drug_count,
                    SUM(total_workshop_cost) as total_cost,
                    SUM(retail_price) as total_revenue,
                    SUM(retail_price - total_workshop_cost) as total_profit
                FROM Drugs
                WHERE substr(production_date, 1, 4) = ?
                GROUP BY month
                ORDER BY month
            """
            
            monthly_data = self.cursor.execute(query, (year,)).fetchall()
            
            # Month names mapping
            month_names = {
                "01": "فروردین",
                "02": "اردیبهشت",
                "03": "خرداد",
                "04": "تیر",
                "05": "مرداد",
                "06": "شهریور",
                "07": "مهر",
                "08": "آبان",
                "09": "آذر",
                "10": "دی",
                "11": "بهمن",
                "12": "اسفند"
            }
            
            # Process results
            annual_cost = 0
            annual_revenue = 0
            annual_profit = 0
            
            for i, data in enumerate(monthly_data):
                month_num, count, cost, revenue, profit = data
                
                # Format display values
                month_name = month_names.get(month_num, month_num)
                count_display = f"{count}"
                cost_display = f"{cost:,.0f} تومان" if cost else "0 تومان"
                revenue_display = f"{revenue:,.0f} تومان" if revenue else "0 تومان"
                profit_display = f"{profit:,.0f} تومان" if profit else "0 تومان"
                
                # Add to treeview with alternating colors
                tag = 'evenrow' if i % 2 == 0 else 'oddrow'
                self.monthly_tree.insert("", "end", values=(
                    month_name,
                    count_display,
                    cost_display,
                    revenue_display,
                    profit_display
                ), tags=(tag,))
                
                # Update annual totals
                annual_cost += cost or 0
                annual_revenue += revenue or 0
                annual_profit += profit or 0
            
            # Get inventory value
            self.cursor.execute("SELECT SUM(unit_price * (current_weight / 1000.0)) FROM Herbs")
            inventory_value = self.cursor.fetchone()[0] or 0
            
            # Update financial summary
            self.financial_vars["total_inventory_var"].set(f"{inventory_value:,.0f} تومان")
            self.financial_vars["total_production_cost_var"].set(f"{annual_cost:,.0f} تومان")
            self.financial_vars["estimated_revenue_var"].set(f"{annual_revenue:,.0f} تومان")
            self.financial_vars["gross_profit_var"].set(f"{annual_profit:,.0f} تومان")
            
        except Exception as e:
            messagebox.showerror("خطا", f"خطا در بارگیری گزارش مالی: {str(e)}")
    
    def create_report_toolbar(self):
        """Create toolbar for reports"""
        toolbar = ttk.Frame(self.parent)
        toolbar.pack(fill="x", padx=10, pady=5)
        
        buttons = [
            ("ذخیره گزارش", self.save_report),
            ("چاپ", self.print_report),
            ("خروجی PDF", self.export_pdf),
            ("خروجی Excel", self.export_excel),
            ("بارگذاری مجدد", self.reload_reports)
        ]
        
        for text, command in buttons:
            ttk.Button(toolbar, text=text, command=command).pack(side="right", padx=5)
    
    def generate_report(self):
        """Generate all reports"""
        try:
            # 1. Total Herb Types
            self.cursor.execute("SELECT COUNT(*) FROM Herbs")
            herb_count = self.cursor.fetchone()[0]
            self.total_herb_types_var.set(f"{herb_count:,}")

            # 2. Total Herb Weight
            self.cursor.execute("SELECT SUM(current_weight) FROM Herbs")
            total_weight_grams = self.cursor.fetchone()[0]
            if total_weight_grams is None: total_weight_grams = 0.0
            self.total_herb_weight_var.set(f"{total_weight_grams:,.0f}")

            # 3. Total Inventory Value
            self.cursor.execute("SELECT SUM(unit_price * (current_weight / 1000.0)) FROM Herbs")
            total_value = self.cursor.fetchone()[0]
            if total_value is None: total_value = 0.0
            self.total_inventory_value_var.set(f"{total_value:,.0f}")

            # 4. Total Drugs Produced
            self.cursor.execute("SELECT COUNT(*) FROM Drugs")
            drug_count = self.cursor.fetchone()[0]
            self.total_drugs_produced_var.set(f"{drug_count:,}")
            
            # 5. Latest activities
            self.load_latest_activities()
            
            # 6. Redraw chart
            self.draw_placeholder_chart()
            
            # 7. Refresh other reports if tabs exist
            self.filter_inventory_report()
            self.filter_drugs_report()
            self.update_financial_report()

        except Exception as e:
            messagebox.showerror("خطای گزارش", f"خطا در تولید گزارش: {str(e)}")
    
    def load_latest_activities(self):
        """Load latest activities (newest herbs and drugs)"""
        # Clear existing items
        for item in self.activity_tree.get_children():
            self.activity_tree.delete(item)
        
        try:
            # Get latest herbs
            self.cursor.execute(
                "SELECT purchase_date, 'گیاه', name, id FROM Herbs ORDER BY purchase_date DESC LIMIT 5"
            )
            latest_herbs = self.cursor.fetchall()
            
            # Get latest drugs
            self.cursor.execute(
                "SELECT production_date, 'دارو', name, id FROM Drugs ORDER BY production_date DESC LIMIT 5"
            )
            latest_drugs = self.cursor.fetchall()
            
            # Combine and sort
            activities = sorted(latest_herbs + latest_drugs, key=lambda x: x[0] if x[0] else "", reverse=True)
            
            # Display in treeview
            for i, activity in enumerate(activities[:10]):  # Show top 10
                date, act_type, name, id_val = activity
                
                # Add to treeview with alternating colors
                tag = 'evenrow' if i % 2 == 0 else 'oddrow'
                self.activity_tree.insert("", "end", values=(date, act_type, name, id_val), tags=(tag,))
                
            # Apply alternating row colors
            self.activity_tree.tag_configure('oddrow', background='#f3f4f6')
            self.activity_tree.tag_configure('evenrow', background='#ffffff')
            
        except Exception as e:
            print(f"Error loading activities: {e}")
    
    def save_report(self):
        """Save the current report"""
        # Get current tab
        current_tab = self.reports_notebook.index("current")
        tab_names = {
            0: "خلاصه_وضعیت",
            1: "موجودی_انبار",
            2: "داروهای_تولید_شده",
            3: "گزارش_مالی"
        }
        report_type = tab_names.get(current_tab, "گزارش")
        
        # Ask for file format
        formats = [
            ("Excel Files", "*.xlsx"),
            ("CSV Files", "*.csv"),
            ("Text Files", "*.txt")
        ]
        
        file_path = filedialog.asksaveasfilename(
            defaultextension=".xlsx",
            filetypes=formats,
            title="ذخیره گزارش",
            initialfile=f"{report_type}_{datetime.now().strftime('%Y%m%d_%H%M')}"
        )
        
        if not file_path:
            return  # User cancelled
        
        # Determine file type and save
        ext = file_path.split('.')[-1].lower()
        
        try:
            if ext == 'xlsx':
                messagebox.showinfo("اطلاع", "برای صدور به Excel به کتابخانه‌های خاص نیاز است که ممکن است نصب نباشند.")
                self._save_as_text(file_path.replace('.xlsx', '.txt'), current_tab)
            elif ext == 'csv':
                self._save_as_csv(file_path, current_tab)
            elif ext == 'txt':
                self._save_as_text(file_path, current_tab)
            
            messagebox.showinfo("موفق", f"گزارش با موفقیت در مسیر زیر ذخیره شد:\n{file_path}")
            
        except Exception as e:
            messagebox.showerror("خطا", f"خطا در ذخیره گزارش: {str(e)}")
    
    def _save_as_csv(self, file_path, report_type):
        """Save report as CSV"""
        # Prepare data based on report type
        if report_type == 0:  # Summary
            headers = ["عنوان", "مقدار"]
            data = [
                ["تعداد کل انواع گیاهان", self.total_herb_types_var.get()],
                ["مجموع وزن کل گیاهان", self.total_herb_weight_var.get()],
                ["ارزش کل موجودی انبار", self.total_inventory_value_var.get()],
                ["تعداد کل داروهای تولید شده", self.total_drugs_produced_var.get()]
            ]
        elif report_type == 1:  # Inventory
            headers = ["کد گیاه", "نام گیاه", "موجودی", "قیمت واحد", "ارزش کل", "محل قفسه", "آخرین به‌روزرسانی"]
            data = []
            for item in self.inventory_tree.get_children():
                values = self.inventory_tree.item(item, 'values')
                data.append(list(values))
        elif report_type == 2:  # Drugs
            headers = ["کد دارو", "نام دارو", "تاریخ تولید", "بیماری مرتبط", "تعداد ترکیبات", "هزینه تولید", "قیمت فروش", "سود"]
            data = []
            for item in self.drugs_tree.get_children():
                values = self.drugs_tree.item(item, 'values')
                data.append(list(values))
        elif report_type == 3:  # Financial
            headers = ["ماه", "تعداد داروها", "هزینه", "درآمد", "سود"]
            data = []
            for item in self.monthly_tree.get_children():
                values = self.monthly_tree.item(item, 'values')
                data.append(list(values))
        
        # Write to CSV
        with open(file_path, 'w', newline='', encoding='utf-8-sig') as f:
            import csv
            writer = csv.writer(f)
            writer.writerow(headers)
            writer.writerows(data)
    
    def _save_as_text(self, file_path, report_type):
        """Save report as plain text"""
        # Get report title based on type
        titles = {
            0: "گزارش خلاصه وضعیت",
            1: "گزارش موجودی انبار",
            2: "گزارش داروهای تولید شده",
            3: "گزارش مالی"
        }
        title = titles.get(report_type, "گزارش")
        
        # Prepare data
        lines = [
            title,
            f"تاریخ تولید گزارش: {datetime.now().strftime('%Y/%m/%d %H:%M')}",
            "-" * 80,
            ""
        ]
        
        # Add report data based on type
        if report_type == 0:  # Summary
            lines.extend([
                f"تعداد کل انواع گیاهان: {self.total_herb_types_var.get()}",
                f"مجموع وزن کل گیاهان: {self.total_herb_weight_var.get()}",
                f"ارزش کل موجودی انبار: {self.total_inventory_value_var.get()}",
                f"تعداد کل داروهای تولید شده: {self.total_drugs_produced_var.get()}"
            ])
        elif report_type == 1:  # Inventory
            # Add header
            headers = ["کد گیاه", "نام گیاه", "موجودی", "قیمت واحد", "ارزش کل", "محل قفسه"]
            lines.append("\t".join(headers))
            lines.append("-" * 80)
            
            # Add data rows
            for item in self.inventory_tree.get_children():
                values = self.inventory_tree.item(item, 'values')
                lines.append("\t".join([str(v) for v in values[:6]]))
        elif report_type == 2:  # Drugs
            # Add header
            headers = ["کد دارو", "نام دارو", "تاریخ تولید", "بیماری مرتبط", "تعداد ترکیبات", "هزینه تولید", "قیمت فروش", "سود"]
            lines.append("\t".join(headers))
            lines.append("-" * 80)
            
            # Add data rows
            for item in self.drugs_tree.get_children():
                values = self.drugs_tree.item(item, 'values')
                lines.append("\t".join([str(v) for v in values]))
        elif report_type == 3:  # Financial
            # Add financial summary
            lines.extend([
                "خلاصه مالی:",
                f"ارزش کل موجودی: {self.financial_vars['total_inventory_var'].get()}",
                f"هزینه کل تولید: {self.financial_vars['total_production_cost_var'].get()}",
                f"درآمد تخمینی: {self.financial_vars['estimated_revenue_var'].get()}",
                f"سود ناخالص: {self.financial_vars['gross_profit_var'].get()}",
                "",
                "گزارش ماهیانه:",
                "-" * 80
            ])
            
            # Add header
            headers = ["ماه", "تعداد داروها", "هزینه", "درآمد", "سود"]
            lines.append("\t".join(headers))
            lines.append("-" * 80)
            
            # Add data rows
            for item in self.monthly_tree.get_children():
                values = self.monthly_tree.item(item, 'values')
                lines.append("\t".join([str(v) for v in values]))
        
        # Write to file
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write("\n".join(lines))
    
    def print_report(self):
        """Create a printable version of the report (save as PDF)"""
        self.export_pdf()
    
    def export_pdf(self):
        """Export the current report as PDF"""
        messagebox.showinfo(
            "اطلاع",
            "برای صدور به PDF به کتابخانه‌های خاص نیاز است که ممکن است نصب نباشند. لطفاً فایل متنی را ذخیره کنید."
        )
        
        # Get current tab
        current_tab = self.reports_notebook.index("current")
        
        # Save as text instead
        file_path = filedialog.asksaveasfilename(
            defaultextension=".txt",
            filetypes=[("Text Files", "*.txt")],
            title="ذخیره گزارش به صورت متن"
        )
        
        if file_path:
            self._save_as_text(file_path, current_tab)
            messagebox.showinfo("موفق", f"گزارش به صورت متنی در مسیر زیر ذخیره شد:\n{file_path}")
    
    def export_excel(self):
        """Export the current report as Excel"""
        messagebox.showinfo(
            "اطلاع",
            "برای صدور به Excel به کتابخانه‌های خاص نیاز است که ممکن است نصب نباشند. لطفاً فایل CSV را ذخیره کنید."
        )
        
        # Get current tab
        current_tab = self.reports_notebook.index("current")
        
        # Save as CSV instead
        file_path = filedialog.asksaveasfilename(
            defaultextension=".csv",
            filetypes=[("CSV Files", "*.csv")],
            title="ذخیره گزارش به صورت CSV"
        )
        
        if file_path:
            self._save_as_csv(file_path, current_tab)
            messagebox.showinfo("موفق", f"گزارش به صورت CSV در مسیر زیر ذخیره شد:\n{file_path}")
    
    def reload_reports(self):
        """Reload all reports"""
        self.generate_report()


# -------------------- Application Startup Logic --------------------
def run_main_app():
    """Creates and runs the main application window."""
    root.deiconify() # Show the main window
    app = HerbalFactoryApp(root, conn)

if __name__ == "__main__":
    root = tk.Tk()
    root.update() # Force update of the root window state before creating Toplevel

    try:
        conn = setup_database() # Setup DB and add default user if needed
        # Show login window FIRST
        #print("Attempting to create LoginWindow...") # Debug print
        login_window = LoginWindow(root, conn, run_main_app)
        #print("LoginWindow instance should be created.") # Debug print
        #root.withdraw() # Don't withdraw the root window here anymore
        root.mainloop()
    except Exception as e:
        messagebox.showerror("خطای برنامه", f"خطای غیرمنتظره رخ داد:\n{e}")
    finally:
        if 'conn' in locals() and conn:
            conn.close()
            #print("Database connection closed.")
