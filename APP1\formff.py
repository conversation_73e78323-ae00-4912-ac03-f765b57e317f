#                           بسم الله الرحمن الرحیم
import tkinter as tk
import sqlite3
import os
import csv
import sys
import subprocess
import tempfile
from datetime import datetime
from tkinter import ttk, messagebox, filedialog
from hashlib import sha256
from enum import Enum
import jdatetime
import shutil
import customtkinter



# Imports for PDF/Excel export
try:
    from reportlab.lib.pagesizes import letter, inch
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
    from reportlab.lib.styles import getSampleStyleSheet
    REPORTLAB_AVAILABLE = True
    from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
    from reportlab.lib.styles import getSampleStyleSheet
    from reportlab.lib import colors
    from reportlab.lib.units import inch
    from reportlab.pdfbase import pdfmetrics
    from reportlab.pdfbase.ttfonts import TTFont
    PERSIAN_FONT_NAME = 'Helvetica' # Default if font registration is commented out
    REPORTLAB_AVAILABLE = True
except ImportError:
    REPORTLAB_AVAILABLE = False
    print("Warning: reportlab library not found. PDF export will not be available.")

try:
    import openpyxl
    from openpyxl.styles import Font, Alignment
    from openpyxl.utils import get_column_letter
    OPENPYXL_AVAILABLE = True
except ImportError:
    OPENPYXL_AVAILABLE = False
    print("Warning: openpyxl library not found. Excel export will not be available.")

def setup_database():
    """Setup the SQLite database and create required tables if they don't exist."""
    db_path = 'herbal_factory2.db'

    # Check if the database already exists
    db_exists = os.path.exists(db_path)

    # Connect to database (will create it if it doesn't exist)
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    # Create tables if they don't exist
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS Herbs (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        unit_price REAL NOT NULL,
        shelf_location TEXT,
        current_weight REAL NOT NULL,
        purchase_date TEXT,
        description TEXT
    )
    ''')

    cursor.execute('''
    CREATE TABLE IF NOT EXISTS Drugs (
        id INTEGER PRIMARY KEY,
        name TEXT NOT NULL,
        production_date TEXT,
        raw_materials_cost REAL,
        loan_interest_cost REAL,
        container_label_cost REAL,
        workshop_overhead_cost REAL,
        total_workshop_cost REAL,
        representative_price REAL,
        wholesale_price REAL,
        retail_price REAL,
        drug_type_id INTEGER, -- Added column
        waste_percentage REAL DEFAULT 0, -- Added column
        units_per_package INTEGER, -- Added column
        honey_weight REAL, -- Added column
        FOREIGN KEY(drug_type_id) REFERENCES DrugTypes(type_id) -- Added constraint
    )
    ''')

    cursor.execute('''
    CREATE TABLE IF NOT EXISTS DrugCompositions (
        drug_id INTEGER,
        herb_id TEXT,
        weight_used REAL,
        subtotal REAL,
        FOREIGN KEY(drug_id) REFERENCES Drugs(id),
        FOREIGN KEY(herb_id) REFERENCES Herbs(id)
    )
    ''')

    cursor.execute('''
    CREATE TABLE IF NOT EXISTS DrugTypes (
        type_id INTEGER PRIMARY KEY,
        name TEXT NOT NULL,  -- پودری، گرانول، فله‌ای، بسته‌بندی
        has_loan_interest BOOLEAN DEFAULT 1,  -- آیا هزینه قرض زنی دارد؟
        has_container_label BOOLEAN DEFAULT 1, -- آیا هزینه ظرف و برچسب دارد؟
        needs_packing BOOLEAN DEFAULT 0  -- آیا نیاز به بسته‌بندی دارد؟
    )
    ''')

    cursor.execute('''
    CREATE TABLE IF NOT EXISTS Users (
        username TEXT PRIMARY KEY,
        password TEXT NOT NULL,
        role TEXT NOT NULL DEFAULT 'user',
        accessible_tabs TEXT, -- Add column for accessible tabs
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
    ''')

    # Add role column if it doesn't exist
    cursor.execute("PRAGMA table_info(Users)")
    columns = [column[1] for column in cursor.fetchall()]
    default_tabs = "herbs,diseases,drugs,reports,backup" # Default access for existing/new users

    if 'role' not in columns:
        cursor.execute("ALTER TABLE Users ADD COLUMN role TEXT NOT NULL DEFAULT 'user'")
    if 'accessible_tabs' not in columns:
        # Add the column and set a default for existing users
        cursor.execute(f"ALTER TABLE Users ADD COLUMN accessible_tabs TEXT")
        cursor.execute(f"UPDATE Users SET accessible_tabs = ? WHERE accessible_tabs IS NULL", (default_tabs,))
    if 'created_at' not in columns:
        cursor.execute("ALTER TABLE Users ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP")

    cursor.execute('''
    CREATE TABLE IF NOT EXISTS Diseases (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL UNIQUE
    )
    ''')

    cursor.execute('''
    CREATE TABLE IF NOT EXISTS DrugDiseases (
        drug_id INTEGER,
        disease_id INTEGER,
        FOREIGN KEY(drug_id) REFERENCES Drugs(id) ON DELETE CASCADE,
        FOREIGN KEY(disease_id) REFERENCES Diseases(id) ON DELETE CASCADE,
        PRIMARY KEY (drug_id, disease_id)
    )
    ''')

    # Create AppSettings table if it doesn't exist
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS AppSettings (
        key TEXT PRIMARY KEY,
        value TEXT NOT NULL,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
    ''')

    # Check if honey price setting exists, if not add it
    cursor.execute("SELECT COUNT(*) FROM AppSettings WHERE key = 'honey_price_per_gram'")
    if cursor.fetchone()[0] == 0:
        cursor.execute(
            "INSERT INTO AppSettings (key, value) VALUES (?, ?)",
            ('honey_price_per_gram', '100')
        )

    # Commit the changes
    conn.commit()

    return conn

# Initialize database
conn = setup_database()

class UserRole(Enum):
    ADMIN = "admin"
    MANAGER = "manager"
    USER = "user"

class LoginWindow:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("ورود به سیستم")
        self.root.geometry("400x300")
        self.root.configure(bg="#f4dddd")

        # Center window
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        x = (screen_width - 400) // 2
        y = (screen_height - 300) // 2
        self.root.geometry(f"400x300+{x}+{y}")

        # Create main frame
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.pack(fill="both", expand=True)

        # Title
        title_label = ttk.Label(
            main_frame,
            text="سیستم مدیریت کارگاه تولید داروهای گیاهی",
            font=("Tahoma", 13, "bold")
        )
        title_label.pack(pady=(0, 20))

        # Username
        username_frame = ttk.Frame(main_frame)
        username_frame.pack(fill="x", pady=5)

        ttk.Label(username_frame, text=":نام کاربری").pack(side="right", padx=5)
        self.username_entry = ttk.Entry(username_frame, justify="right",width=20)
        self.username_entry.pack(side="left", fill="x", expand=True)

        # Password
        password_frame = ttk.Frame(main_frame)
        password_frame.pack(fill="x", pady=5)

        ttk.Label(password_frame, text=":رمز عبور").pack(side="right", padx=5)
        self.password_entry = ttk.Entry(password_frame, show="*", justify="right")
        self.password_entry.pack(side="left", fill="x", expand=True)

        # Login button
        ttk.Button(
            main_frame,
            text="ورود به سیستم",
            command=self.login,
            style="Accent.TButton"
        ).pack(pady=20)

        # Error label
        self.error_label = ttk.Label(
            main_frame,
            text="",
            foreground="red",
            justify="center",
            wraplength=350
        )
        self.error_label.pack(pady=5)

        # Style configuration
        style = ttk.Style()
        style.configure(
            "Accent.TButton",
            font=("Tahoma", 11),
            padding=10
        )

        # Initialize database
        self.init_database()

        # Bind Enter key to login
        self.root.bind("<Return>", lambda e: self.login())

        # Focus username entry
        self.username_entry.focus()

        # Store logged in user info
        self.current_user = None

    def init_database(self):
        """Initialize the database and create admin user if not exists"""
        conn = sqlite3.connect('herbal_factory2.db')
        cursor = conn.cursor()

        # Create users table if not exists
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS Users (
            username TEXT PRIMARY KEY,
            password TEXT NOT NULL,
            role TEXT NOT NULL DEFAULT 'user',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        ''')


        # Check if admin user exists
        cursor.execute("SELECT COUNT(*) FROM Users WHERE username = 'admin'")
        if cursor.fetchone()[0] == 0:
            # Create default admin user (username: admin, password: admin)
            hashed_password = sha256('admin'.encode()).hexdigest()
            cursor.execute(
                "INSERT INTO Users (username, password, role) VALUES (?, ?, ?)",
                ('admin', hashed_password, UserRole.ADMIN.value)
            )

        conn.commit()
        conn.close()

    def login(self):
        """Handle login attempt"""
        username = self.username_entry.get().strip()
        password = self.password_entry.get().strip()

        if not username or not password:
            self.error_label.config(text="لطفاً نام کاربری و رمز عبور را وارد کنید")
            return

        try:
            conn = sqlite3.connect('herbal_factory2.db')
            cursor = conn.cursor()

            # Get user from database, including accessible tabs
            cursor.execute(
                "SELECT password, role, accessible_tabs FROM Users WHERE username = ?",
                (username,)
            )
            result = cursor.fetchone()

            if result and result[0] == sha256(password.encode()).hexdigest():
                # Store user info, including accessible tabs
                self.current_user = {
                    'username': username,
                    'role': result[1],
                    'accessible_tabs': result[2] or "" # Store tabs as string, default to empty if NULL
                }
                # Login successful
                self.root.destroy()
                # Return the user dictionary on success
                return self.current_user
            else:
                self.error_label.config(text="نام کاربری یا رمز عبور اشتباه است")
                self.password_entry.delete(0, tk.END)
                # Return None on failure
                return None

        except Exception as e:
            self.error_label.config(text=f"خطا در ورود به سیستم: {str(e)}")
        finally:
            conn.close()

        return None # Return None if login fails for other reasons

    def run(self):
        """Run the login window and return the user dictionary if login successful, otherwise None"""
        self.root.mainloop()
        # Return the user dictionary or None
        return self.current_user

class UserManager:
    def __init__(self, parent, current_user):
        self.parent = parent
        self.current_user = current_user

        # Create main frame
        self.frame = ttk.LabelFrame(parent, text="مدیریت کاربران",labelanchor="ne")
        self.frame.pack(fill="both", expand=True, padx=10, pady=5)

        # Create user form
        self.create_user_form()

        # Create user list
        self.create_user_list()

        # Load users
        self.load_users()

    def create_user_form(self):
        """Create the user management form"""
        form_frame = ttk.Frame(self.frame)
        form_frame.pack(fill="x", padx=10, pady=5)

        # Username
        ttk.Label(form_frame, text=":نام کاربری").grid(row=0, column=2, padx=5, pady=5)
        self.username_entry = ttk.Entry(form_frame)
        self.username_entry.grid(row=0, column=1, padx=5, pady=5)

        # Password
        ttk.Label(form_frame, text=":رمز عبور").grid(row=1, column=2, padx=5, pady=5)
        self.password_entry = ttk.Entry(form_frame, show="*")
        self.password_entry.grid(row=1, column=1, padx=5, pady=5)

        # Role
        ttk.Label(form_frame, text=":نقش کاربری").grid(row=2, column=2, padx=5, pady=5)
        self.role_combobox = ttk.Combobox(
            form_frame,
            values=[role.value for role in UserRole],
            state="readonly"
        )
        self.role_combobox.set(UserRole.USER.value)
        self.role_combobox.grid(row=2, column=1, padx=5, pady=5)

        # Tab Access Control Frame
        access_frame = ttk.LabelFrame(form_frame, text="دسترسی به تب‌ها",labelanchor="ne")
        access_frame.grid(row=3, column=0, columnspan=3, padx=5, pady=10, sticky="ew")

        self.tab_access_vars = {}
        tab_names = {
            "herbs": "مفردات گیاهی",
            "diseases": "گروه دارویی",
            "drugs": "تولید دارو",
            "reports": "گزارشات",
            "backup": "پشتیبان‌گیری",
            "planner": "برنامه‌ریزی تولید"  # Add new planner tab
        }

        col_count = 0
        for key, text in tab_names.items():
            var = tk.BooleanVar(value=True) # Default to accessible
            chk = ttk.Checkbutton(access_frame, text=text, variable=var)
            chk.grid(row=0, column=col_count, padx=5, pady=5, sticky="e")
            self.tab_access_vars[key] = var
            col_count += 1

        # Buttons
        button_frame = ttk.Frame(form_frame)
        button_frame.grid(row=4, column=1, columnspan=2, pady=10) # Adjusted row index

        ttk.Button(
            button_frame,
            text="افزودن کاربر",
            command=self.add_user
        ).pack(side="left", padx=5)

        ttk.Button(
            button_frame,
            text="ویرایش کاربر",
            command=self.edit_user
        ).pack(side="left", padx=5)

        ttk.Button(
            button_frame,
            text="حذف کاربر",
            command=self.delete_user
        ).pack(side="left", padx=5)

        # Configure grid
        form_frame.columnconfigure(1, weight=1)

    def create_user_list(self):
        """Create the user list treeview"""
        # Create frame for treeview
        tree_frame = ttk.Frame(self.frame)
        tree_frame.pack(fill="both", expand=True, padx=10, pady=5)

        # Create treeview
        columns = ("username", "role", "created_at", "accessible_tabs")
        self.tree = ttk.Treeview(tree_frame, columns=columns, show="headings")

        # Configure columns
        self.tree.heading("username", text="نام کاربری")
        self.tree.heading("role", text="نقش کاربری")
        self.tree.heading("created_at", text="تاریخ ایجاد")
        self.tree.heading("accessible_tabs", text="دسترسی‌ها")

        self.tree.column("username", width=150)
        self.tree.column("role", width=100)
        self.tree.column("created_at", width=150)
        self.tree.column("accessible_tabs", width=200)

        # Add scrollbar
        scrollbar = ttk.Scrollbar(tree_frame, orient="vertical", command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)

        # Pack widgets
        scrollbar.pack(side="right", fill="y")
        self.tree.pack(side="left", fill="both", expand=True)

        # Bind selection event
        self.tree.bind("<<TreeviewSelect>>", self.on_select)

    def load_users(self):
        """Load users into treeview"""
        # Clear existing items
        for item in self.tree.get_children():
            self.tree.delete(item)

        try:
            conn = sqlite3.connect('herbal_factory2.db')
            cursor = conn.cursor()

            cursor.execute("SELECT username, role, created_at, accessible_tabs FROM Users ORDER BY username")
            users = cursor.fetchall()

            for user in users:
                self.tree.insert("", "end", values=user)

        except Exception as e:
            messagebox.showerror("خطا", f"خطا در بارگیری لیست کاربران: {str(e)}")
        finally:
            conn.close()


    def add_user(self):
        username = self.username_entry.get().strip()
        password = self.password_entry.get().strip()
        role = self.role_combobox.get()

        if not username or not password:
            messagebox.showwarning("هشدار", "لطفاً نام کاربری و رمز عبور را وارد کنید")
            return

        # Get selected tabs
        accessible_tabs = []
        for tab_name, var in self.tab_access_vars.items():
            if var.get():  # If checkbox is checked
                accessible_tabs.append(tab_name)
        accessible_tabs_str = ",".join(accessible_tabs)

        try:
            conn = sqlite3.connect('herbal_factory2.db')
            cursor = conn.cursor()

            # Hash password
            hashed_password = sha256(password.encode()).hexdigest()

            # Insert user with accessible_tabs
            cursor.execute(
                "INSERT INTO Users (username, password, role, accessible_tabs) VALUES (?, ?, ?, ?)",
                (username, hashed_password, role, accessible_tabs_str)
            )

            conn.commit()
            messagebox.showinfo("موفق", "کاربر با موفقیت ایجاد شد")

            # Clear form
            self.username_entry.delete(0, tk.END)
            self.password_entry.delete(0, tk.END)
            self.role_combobox.set(UserRole.USER.value)

            # Reset tab checkboxes
            for var in self.tab_access_vars.values():
                var.set(True)

            # Reload users
            self.load_users()

        except sqlite3.IntegrityError:
            messagebox.showerror("خطا", "این نام کاربری قبلاً ثبت شده است")
        except Exception as e:
            messagebox.showerror("خطا", f"خطا در ایجاد کاربر: {str(e)}")
        finally:
            conn.close()


    def edit_user(self):
        """Edit selected user"""
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("هشدار", "لطفاً یک کاربر را انتخاب کنید")
            return

        username = self.tree.item(selected[0])['values'][0]

        # Don't allow editing admin if not admin
        if username == 'admin' and self.current_user['role'] != UserRole.ADMIN.value:
            messagebox.showerror("خطا", "شما مجاز به ویرایش کاربر admin نیستید")
            return

        # Get new values
        new_password = self.password_entry.get().strip()
        new_role = self.role_combobox.get()

        # Get selected tabs
        accessible_tabs = []
        for tab_name, var in self.tab_access_vars.items():
            if var.get():  # If checkbox is checked
                accessible_tabs.append(tab_name)
        accessible_tabs_str = ",".join(accessible_tabs)

        try:
            conn = sqlite3.connect('herbal_factory2.db')
            cursor = conn.cursor()

            if new_password:
                # Update password, role and accessible_tabs
                hashed_password = sha256(new_password.encode()).hexdigest()
                cursor.execute(
                    "UPDATE Users SET password = ?, role = ?, accessible_tabs = ? WHERE username = ?",
                    (hashed_password, new_role, accessible_tabs_str, username)
                )
            else:
                # Update only role and accessible_tabs
                cursor.execute(
                    "UPDATE Users SET role = ?, accessible_tabs = ? WHERE username = ?",
                    (new_role, accessible_tabs_str, username)
                )

            conn.commit()
            messagebox.showinfo("موفق", "اطلاعات کاربر با موفقیت بروزرسانی شد")

            # Clear form
            self.username_entry.delete(0, tk.END)
            self.password_entry.delete(0, tk.END)
            self.role_combobox.set(UserRole.USER.value)

            # Reset tab checkboxes
            for var in self.tab_access_vars.values():
                var.set(True)

            # Reload users
            self.load_users()

        except Exception as e:
            messagebox.showerror("خطا", f"خطا در ویرایش کاربر: {str(e)}")
        finally:
            conn.close()



    def delete_user(self):
        """Delete selected user"""
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("هشدار", "لطفاً یک کاربر را انتخاب کنید")
            return

        username = self.tree.item(selected[0])['values'][0]

        # Don't allow deleting admin
        if username == 'admin':
            messagebox.showerror("خطا", "حذف کاربر admin امکان‌پذیر نیست")
            return

        # Don't allow deleting self
        if username == self.current_user['username']:
            messagebox.showerror("خطا", "شما نمی‌توانید حساب کاربری خود را حذف کنید")
            return

        if messagebox.askyesno("تأیید حذف", f"آیا از حذف کاربر {username} اطمینان دارید؟"):
            try:
                conn = sqlite3.connect('herbal_factory2.db')
                cursor = conn.cursor()

                cursor.execute("DELETE FROM Users WHERE username = ?", (username,))
                conn.commit()

                messagebox.showinfo("موفق", "کاربر با موفقیت حذف شد")

                # Clear form
                self.username_entry.delete(0, tk.END)
                self.password_entry.delete(0, tk.END)
                self.role_combobox.set(UserRole.USER.value)

                # Reload users
                self.load_users()

            except Exception as e:
                messagebox.showerror("خطا", f"خطا در حذف کاربر: {str(e)}")
            finally:
                conn.close()


    def on_select(self, event):
        """Handle user selection"""
        selected = self.tree.selection()
        if not selected:
            return

        # Get selected user data
        values = self.tree.item(selected[0])['values']

        try:
            conn = sqlite3.connect('herbal_factory2.db')
            cursor = conn.cursor()

            # Get user's accessible tabs
            cursor.execute("SELECT accessible_tabs FROM Users WHERE username = ?", (values[0],))
            result = cursor.fetchone()
            accessible_tabs = result[0].split(",") if result and result[0] else []

            # Update form
            self.username_entry.delete(0, tk.END)
            self.username_entry.insert(0, values[0])
            self.role_combobox.set(values[1])

            # Update tab checkboxes
            for tab_name, var in self.tab_access_vars.items():
                var.set(tab_name in accessible_tabs)

        except Exception as e:
            messagebox.showerror("خطا", f"خطا در بارگیری اطلاعات کاربر: {str(e)}")
        finally:
            conn.close()

        # Clear password (for security)
        self.password_entry.delete(0, tk.END)

class AppSettingsManager:
    def __init__(self, parent, conn):
        self.parent = parent
        self.conn = conn
        self.cursor = conn.cursor()

        # Create main frame
        self.settings_frame = ttk.LabelFrame(parent, text="تنظیمات سیستم",labelanchor="ne")
        self.settings_frame.pack(side="right",fill="both", expand=True, padx=10, pady=5)

        # Create settings form
        self.create_settings_form()

        # Load current settings
        self.load_settings()

    def create_settings_form(self):
        """Create the settings management form"""
        # Main container
        self.main_frame = ttk.Frame(self.settings_frame, padding=10)
        self.main_frame.pack(side="right",fill="x", expand=True)

        # Title
        title_label = ttk.Label(
            self.main_frame,
            text="تنظیمات پارامترهای محاسبه", 
            font=("Tahoma", 12, "bold")
        )
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 15), sticky="e")

        # Honey price setting
        ttk.Label(self.main_frame, text="قیمت هر گرم عسل (تومان)",
                 font=("Tahoma", 10)).grid(row=1, column=1, padx=5, pady=5, sticky="e")

        self.honey_price_entry = ttk.Entry(self.main_frame, width=15, justify="left")
        self.honey_price_entry.grid(row=1, column=0, padx=5, pady=5, sticky="w")

        # Default waste percentage
        ttk.Label(self.main_frame, text="درصد ضایعات پیش فرض",
                 font=("Tahoma", 10)).grid(row=2, column=1, padx=5, pady=5, sticky="e")

        self.waste_percentage_entry = ttk.Entry(self.main_frame, width=15, justify="left")
        self.waste_percentage_entry.grid(row=2, column=0, padx=5, pady=5, sticky="w")

        # Default costs
        costs_frame = ttk.LabelFrame(self.main_frame, text="هزینه‌های پیش فرض (تومان)",labelanchor="ne")
        costs_frame.grid(row=3, column=0, columnspan=2, padx=5, pady=10, sticky="ew")

        ttk.Label(costs_frame, text="هزینه قرض زنی").grid(row=0, column=1, padx=5, pady=5, sticky="e")
        self.loan_interest_entry = ttk.Entry(costs_frame, width=15, justify="left")
        self.loan_interest_entry.grid(row=0, column=0, padx=5, pady=5, sticky="w")

        ttk.Label(costs_frame, text="هزینه ظرف و برچسب").grid(row=1, column=1, padx=5, pady=5, sticky="e")
        self.container_label_entry = ttk.Entry(costs_frame, width=15, justify="left")
        self.container_label_entry.grid(row=1, column=0, padx=5, pady=5, sticky="w")

        ttk.Label(costs_frame, text="هزینه سربار کارگاه").grid(row=2, column=1, padx=5, pady=5, sticky="e")
        self.workshop_overhead_entry = ttk.Entry(costs_frame, width=15, justify="left")
        self.workshop_overhead_entry.grid(row=2, column=0, padx=5, pady=5, sticky="w")

        # Save button
        save_button = ttk.Button(
            self.main_frame,
            text="ذخیره تنظیمات",
            command=self.save_settings
        )
        save_button.grid(row=4, column=0, columnspan=2, pady=15)

        # Status message
        self.status_label = ttk.Label(
            self.main_frame,
            text="",
            foreground="green",
            font=("Tahoma", 10, "italic")
        )
        self.status_label.grid(row=5, column=0, columnspan=2, pady=5)

    def load_settings(self):
        """Load current settings from database"""
        try:
            # Honey price
            self.cursor.execute("SELECT value FROM AppSettings WHERE key = 'honey_price_per_gram'")
            result = self.cursor.fetchone()
            honey_price = result[0] if result else "100"
            self.honey_price_entry.delete(0, tk.END)
            self.honey_price_entry.insert(0, honey_price)

            # Default waste percentage
            self.cursor.execute("SELECT value FROM AppSettings WHERE key = 'default_waste_percentage'")
            result = self.cursor.fetchone()
            waste_percentage = result[0] if result else "0"
            self.waste_percentage_entry.delete(0, tk.END)
            self.waste_percentage_entry.insert(0, waste_percentage)

            # Default loan interest
            self.cursor.execute("SELECT value FROM AppSettings WHERE key = 'default_loan_interest'")
            result = self.cursor.fetchone()
            loan_interest = result[0] if result else "0"
            self.loan_interest_entry.delete(0, tk.END)
            self.loan_interest_entry.insert(0, loan_interest)

            # Default container label cost
            self.cursor.execute("SELECT value FROM AppSettings WHERE key = 'default_container_label_cost'")
            result = self.cursor.fetchone()
            container_label_cost = result[0] if result else "0"
            self.container_label_entry.delete(0, tk.END)
            self.container_label_entry.insert(0, container_label_cost)

            # Default workshop overhead
            self.cursor.execute("SELECT value FROM AppSettings WHERE key = 'default_workshop_overhead'")
            result = self.cursor.fetchone()
            workshop_overhead = result[0] if result else "0"
            self.workshop_overhead_entry.delete(0, tk.END)
            self.workshop_overhead_entry.insert(0, workshop_overhead)

        except Exception as e:
            messagebox.showerror("خطا", f"خطا در بارگذاری تنظیمات: {str(e)}")

    def save_settings(self):
        """Save settings to database"""
        try:
            # Validate inputs
            try:
                honey_price = float(self.honey_price_entry.get())
                waste_percentage = float(self.waste_percentage_entry.get())
                loan_interest = float(self.loan_interest_entry.get())
                container_label_cost = float(self.container_label_entry.get())
                workshop_overhead = float(self.workshop_overhead_entry.get())

                if honey_price < 0 or waste_percentage < 0 or loan_interest < 0 or container_label_cost < 0 or workshop_overhead < 0:
                    raise ValueError("تمامی مقادیر باید بزرگتر یا مساوی صفر باشند")

            except ValueError as e:
                if str(e).startswith("تمامی مقادیر"):
                    messagebox.showerror("خطا", str(e))
                else:
                    messagebox.showerror("خطا", "لطفاً مقادیر عددی معتبر وارد کنید")
                return

            # Start transaction
            self.conn.execute("BEGIN TRANSACTION")

            # Update honey price
            self.update_setting('honey_price_per_gram', str(honey_price))

            # Update default waste percentage
            self.update_setting('default_waste_percentage', str(waste_percentage))

            # Update default costs
            self.update_setting('default_loan_interest', str(loan_interest))
            self.update_setting('default_container_label_cost', str(container_label_cost))
            self.update_setting('default_workshop_overhead', str(workshop_overhead))

            # Commit changes
            self.conn.commit()

            # Update status message
            self.status_label.config(text="تنظیمات با موفقیت ذخیره شدند")

            # Clear message after 3 seconds
            self.parent.after(3000, lambda: self.status_label.config(text=""))

        except Exception as e:
            # Rollback in case of error
            self.conn.rollback()
            messagebox.showerror("خطا", f"خطا در ذخیره تنظیمات: {str(e)}")

    def update_setting(self, key, value):
        """Update or insert a setting"""
        # Check if setting exists
        self.cursor.execute("SELECT COUNT(*) FROM AppSettings WHERE key = ?", (key,))
        if self.cursor.fetchone()[0] > 0:
            # Update existing setting
            self.cursor.execute(
                "UPDATE AppSettings SET value = ?, updated_at = CURRENT_TIMESTAMP WHERE key = ?",
                (value, key)
            )
        else:
            # Insert new setting
            self.cursor.execute(
                "INSERT INTO AppSettings (key, value) VALUES (?, ?)",
                (key, value)
            )

class ProductionPlannerManager:
    def __init__(self, parent, conn):
        self.parent = parent
        self.conn = conn
        self.cursor = conn.cursor()
        self.selected_drug_id = None
        self.compositions = []
        self.current_results = None  # Store current calculation results for export

        # Create main frame
        self.setup_planner_tab()

    def setup_planner_tab(self):
        """Set up the production planner tab with improved UI"""
        self.planner_frame = ttk.LabelFrame(self.parent, text="برنامه‌ریزی تولید",labelanchor="n")
        self.planner_frame.pack(fill="both", expand=True, padx=10, pady=5)

        # Description with better styling
        #description = ttk.Label(self.planner_frame,text="در این بخش می‌توانید مقدار مواد اولیه لازم برای تولید تعداد مشخصی از یک دارو را محاسبه کنید و موجودی انبار را بررسی کنید.",wraplength=800,justify="center",font=("Tahoma", 10))
        #description.pack(pady=10)

        # Split frame into two sections (left and right)
        content_frame = ttk.Frame(self.planner_frame)
        content_frame.pack(fill="both", expand=True, padx=5, pady=5)

        # Left frame - drug selection
        left_frame = ttk.LabelFrame(content_frame, text="انتخاب دارو",labelanchor="ne")
        left_frame.pack(side="left", fill="both", expand=True, padx=5, pady=5)

        # Search frame with improved styling
        search_frame = ttk.Frame(left_frame)
        search_frame.pack(fill="x", padx=5, pady=5)

        search_icon_label = ttk.Label(search_frame, text="🔍")
        search_icon_label.pack(side="right", padx=(5,0))

        ttk.Label(search_frame, text="جستجو").pack(side="right", padx=5)
        self.drug_search_entry = ttk.Entry(search_frame)
        self.drug_search_entry.pack(side="right", fill="x", expand=True, padx=5)
        self.drug_search_entry.bind("<KeyRelease>", self.filter_drugs)

        # Add clear search button
        clear_btn = ttk.Button(
            search_frame,
            text="✕",
            width=2,
            command=lambda: [self.drug_search_entry.delete(0, tk.END), self.load_drugs()]
        )
        clear_btn.pack(side="left", padx=2)

        # Drug list with improved styling
        self.drug_tree = ttk.Treeview(
            left_frame,
            columns=("type", "name", "id"),
            show="headings",
            height=10
        )

        self.drug_tree.heading("id", text="کد")
        self.drug_tree.heading("name", text="نام دارو")
        self.drug_tree.heading("type", text="نوع")

        self.drug_tree.column("id", width=50, anchor="center")
        self.drug_tree.column("name", width=150, anchor="center")
        self.drug_tree.column("type", width=100, anchor="center")

        # Add scrollbar
        drug_scrollbar = ttk.Scrollbar(left_frame, orient="vertical", command=self.drug_tree.yview)
        self.drug_tree.configure(yscrollcommand=drug_scrollbar.set)

        # Pack widgets
        drug_scrollbar.pack(side="right", fill="y")
        self.drug_tree.pack(side="left", fill="both", expand=True, padx=5, pady=5)

        # Bind selection event
        self.drug_tree.bind("<<TreeviewSelect>>", self.on_drug_select)
        self.drug_tree.bind("<Double-1>", lambda e: self.calculate_requirements())

        # Right frame - quantity and calculation
        right_frame = ttk.LabelFrame(content_frame,height=20, text="اطلاعات تولید",labelanchor="n")
        right_frame.pack(side="right", fill="both", expand=True, padx=5, pady=5)

        # Selected drug info with card-like appearance
        self.drug_info_frame = ttk.LabelFrame(right_frame, text="اطلاعات دارو",labelanchor="ne")
        self.drug_info_frame.pack(fill="x", padx=5, pady=5)

        self.drug_name_var = tk.StringVar(value="-")
        self.drug_id_var = tk.StringVar(value="-")
        self.drug_type_var = tk.StringVar(value="-")

        # Create a more visually appealing drug info display
        info_grid = ttk.Frame(self.drug_info_frame)
        info_grid.pack(side="right",fill="x", padx=10, pady=10)

        # Name row
        name_label = ttk.Label(info_grid, text="نام دارو", font=("Tahoma", 10, "bold"))
        name_label.grid(row=0, column=1, padx=5, pady=5, sticky="e")
        name_value = ttk.Label(info_grid, textvariable=self.drug_name_var, font=("Tahoma", 10))
        name_value.grid(row=0, column=0, padx=5, pady=5, sticky="w")

        # ID row
        id_label = ttk.Label(info_grid, text="کد دارو", font=("Tahoma", 10, "bold"))
        id_label.grid(row=1, column=1, padx=5, pady=5, sticky="e")
        id_value = ttk.Label(info_grid, textvariable=self.drug_id_var, font=("Tahoma", 10))
        id_value.grid(row=1, column=0, padx=5, pady=5, sticky="w")

        # Type row
        type_label = ttk.Label(info_grid, text="نوع دارو", font=("Tahoma", 10, "bold"))
        type_label.grid(row=2, column=1, padx=5, pady=5, sticky="e")
        type_value = ttk.Label(info_grid, textvariable=self.drug_type_var, font=("Tahoma", 10))
        type_value.grid(row=2, column=0, padx=5, pady=5, sticky="w")

        # Quantity frame with improved styling
        quantity_frame = ttk.LabelFrame(right_frame, text="تنظیمات محاسبه",labelanchor="ne")
        quantity_frame.pack(fill="x", padx=5, pady=10)

        # Inner frame for better layout
        inner_quantity_frame = ttk.Frame(quantity_frame)
        inner_quantity_frame.pack(side="right",fill="x", padx=10, pady=10)

        # Quantity input with spinbox for better UX
        ttk.Label(inner_quantity_frame, text="تعداد مورد نیاز",
                 font=("Tahoma", 10)).grid(row=0, column=1, padx=5, pady=5, sticky="e")

        # Use a spinbox instead of entry for better UX
        self.quantity_var = tk.StringVar(value="1")
        self.quantity_entry = ttk.Spinbox(
            inner_quantity_frame,
            from_=1,
            to=1000,
            increment=1,
            textvariable=self.quantity_var,
            width=10,
            justify="center"
        )
        self.quantity_entry.grid(row=0, column=0, padx=5, pady=5, sticky="w")

        # Quick quantity buttons
        quick_frame = ttk.Frame(inner_quantity_frame)
        quick_frame.grid(row=1, column=0, columnspan=2, pady=5)

        for qty in [10, 50, 100, 500]:
            btn = ttk.Button(
                quick_frame,
                text=str(qty),
                width=4,
                command=lambda q=qty: self.quantity_var.set(str(q))
            )
            btn.pack(side="left", padx=2)

        # Add comparison mode checkbox
        self.comparison_mode_var = tk.BooleanVar(value=False)
        comparison_check = ttk.Checkbutton(
            inner_quantity_frame,
            text="حالت مقایسه‌ای",
            variable=self.comparison_mode_var,
            command=self.toggle_comparison_mode
        )
        comparison_check.grid(row=2, column=0, columnspan=2, pady=(10, 0), sticky="w")

        # Add tooltip/help text for comparison mode
        comparison_help = ttk.Label(
            inner_quantity_frame,
            text="در حالت مقایسه‌ای می‌توانید چند دارو را انتخاب و نیازمندی‌های آنها را با هم مقایسه کنید",
            font=("Tahoma", 8),
            foreground="#6B7280",
            wraplength=250,
            justify="right"
        )
        comparison_help.grid(row=3, column=0, columnspan=2, pady=(0, 10), sticky="w")

        # Buttons frame
        buttons_frame = ttk.Frame(inner_quantity_frame)
        buttons_frame.grid(row=4, column=0, columnspan=2, pady=10)

        # Calculate button with improved styling
        self.calculate_btn = ttk.Button(
            buttons_frame,
            text="محاسبه نیازمندی‌ها",
            command=self.calculate_requirements,
            state="disabled",
            width=20
        )
        self.calculate_btn.pack(side="left", padx=5)

        # Compare button (initially hidden)
        self.compare_btn = ttk.Button(
            buttons_frame,
            text="مقایسه داروها",
            command=self.compare_drugs,
            state="disabled",
            width=15
        )

        # Selected drugs for comparison (initially empty)
        self.comparison_drugs = []

        # Keyboard shortcut for calculation
        self.parent.bind("<Return>", lambda e: self.calculate_if_ready())

        # Results frame (initially empty, will be populated when calculation is done)
        self.results_frame = ttk.LabelFrame(self.planner_frame, text="نتایج محاسبه",labelanchor="ne")

        # Load drugs
        self.load_drugs()

    def calculate_if_ready(self):
        """Calculate if a drug is selected and the calculate button is enabled"""
        if self.selected_drug_id and self.calculate_btn['state'] != 'disabled':
            self.calculate_requirements()

    
    
    def toggle_comparison_mode(self):
        """Toggle between normal and comparison mode"""
        is_comparison_mode = self.comparison_mode_var.get()

        if is_comparison_mode:
            # Switch to comparison mode
            self.compare_btn.pack(side="left", padx=5)
            self.calculate_btn.configure(text="افزودن به مقایسه")

            # Create comparison list if it doesn't exist
            if not hasattr(self, 'comparison_list_frame'):
                self.create_comparison_list_frame()
            else:
                self.comparison_list_frame.pack(fill="x", padx=5, pady=5)

            # Reset comparison drugs if list is empty
            if not self.comparison_drugs:
                self.comparison_drugs = []

        else:
            # Switch back to normal mode
            self.compare_btn.pack_forget()
            self.calculate_btn.configure(text="محاسبه نیازمندی‌ها")

            # Hide comparison list if it exists
            if hasattr(self, 'comparison_list_frame') and self.comparison_list_frame.winfo_exists():
                self.comparison_list_frame.pack_forget()

    def create_comparison_list_frame(self):
        """Create a frame to display drugs selected for comparison"""
        self.comparison_list_frame = ttk.LabelFrame(self.planner_frame, text="داروهای انتخاب شده برای مقایسه",labelanchor="ne")
        self.comparison_list_frame.pack(fill="x", padx=10, pady=5) # Removed before= argument

        # Create a frame for the list
        list_frame = ttk.Frame(self.comparison_list_frame)
        list_frame.pack(fill="both", expand=True, padx=5, pady=5)

        # Create a listbox to display selected drugs
        self.comparison_listbox = tk.Listbox(
            list_frame,
            height=4,
            font=("Tahoma", 10),
            selectmode=tk.SINGLE
        )
        self.comparison_listbox.pack(side="left", fill="both", expand=True, padx=(0, 5))

        # Add scrollbar
        scrollbar = ttk.Scrollbar(list_frame, orient="vertical", command=self.comparison_listbox.yview)
        self.comparison_listbox.configure(yscrollcommand=scrollbar.set)
        scrollbar.pack(side="right", fill="y")

        # Add buttons for managing the list
        button_frame = ttk.Frame(self.comparison_list_frame)
        button_frame.pack(fill="x", padx=5, pady=5)

        remove_btn = ttk.Button(
            button_frame,
            text="حذف از لیست",
            command=self.remove_from_comparison,
            width=15
        )
        remove_btn.pack(side="left", padx=5)

        clear_btn = ttk.Button(
            button_frame,
            text="پاک کردن لیست",
            command=self.clear_comparison_list,
            width=15
        )
        clear_btn.pack(side="left", padx=5)

    def add_to_comparison(self):
        """Add current drug to comparison list"""
        if not self.selected_drug_id:
            messagebox.showwarning("هشدار", "لطفاً ابتدا یک دارو را انتخاب کنید")
            return

        # Check if drug is already in the list
        drug_name = self.drug_name_var.get()
        drug_id = self.selected_drug_id
        quantity = int(self.quantity_var.get())

        # Check if this drug is already in the comparison list
        for i, drug in enumerate(self.comparison_drugs):
            if drug['id'] == drug_id:
                # Update quantity if drug already exists
                self.comparison_drugs[i]['quantity'] = quantity
                self.update_comparison_listbox()
                messagebox.showinfo("اطلاعات", f"تعداد دارو '{drug_name}' در لیست مقایسه به {quantity} بروزرسانی شد")
                return

        # Add new drug to comparison list
        self.comparison_drugs.append({
            'id': drug_id,
            'name': drug_name,
            'type': self.drug_type_var.get(),
            'quantity': quantity
        })

        # Update the listbox
        self.update_comparison_listbox()

        # Enable compare button if we have at least 2 drugs
        if len(self.comparison_drugs) >= 2:
            self.compare_btn.configure(state="normal")

        messagebox.showinfo("اطلاعات", f"دارو '{drug_name}' به لیست مقایسه اضافه شد")

    def update_comparison_listbox(self):
        """Update the comparison listbox with current drugs"""
        if not hasattr(self, 'comparison_listbox'):
            return

        # Clear the listbox
        self.comparison_listbox.delete(0, tk.END)

        # Add each drug to the listbox
        for drug in self.comparison_drugs:
            self.comparison_listbox.insert(tk.END, f"{drug['name']} - {drug['quantity']} عدد")

    def remove_from_comparison(self):
        """Remove selected drug from comparison list"""
        if not hasattr(self, 'comparison_listbox'):
            return

        selected = self.comparison_listbox.curselection()
        if not selected:
            messagebox.showwarning("هشدار", "لطفاً ابتدا یک دارو را از لیست انتخاب کنید")
            return

        # Remove the drug from the list
        index = selected[0]
        removed_drug = self.comparison_drugs.pop(index)

        # Update the listbox
        self.update_comparison_listbox()

        # Disable compare button if we have less than 2 drugs
        if len(self.comparison_drugs) < 2:
            self.compare_btn.configure(state="disabled")

        messagebox.showinfo("اطلاعات", f"دارو '{removed_drug['name']}' از لیست مقایسه حذف شد")

    def clear_comparison_list(self):
        """Clear all drugs from comparison list"""
        if not self.comparison_drugs:
            return

        if messagebox.askyesno("تأیید", "آیا مطمئن هستید که می‌خواهید لیست مقایسه را پاک کنید؟"):
            self.comparison_drugs = []
            self.update_comparison_listbox()
            self.compare_btn.configure(state="disabled")
            messagebox.showinfo("اطلاعات", "لیست مقایسه پاک شد")

    def compare_drugs(self):
        """Compare the requirements of selected drugs"""
        if len(self.comparison_drugs) < 2:
            messagebox.showwarning("هشدار", "برای مقایسه حداقل به دو دارو نیاز است")
            return

        try:
            # Show calculation in progress
            self.compare_btn.config(state="disabled", text="در حال محاسبه...")
            self.parent.update()  # Update UI to show the button state change

            # Collect requirements for all drugs
            all_requirements = []
            all_herbs = {}  # Dictionary to track all herbs across all drugs

            for drug in self.comparison_drugs:
                drug_id = drug['id']
                drug_name = drug['name']
                quantity = drug['quantity']

                # Fetch compositions for this drug
                self.cursor.execute("""
                    SELECT dc.herb_id, h.name, dc.weight_used, h.current_weight, h.unit_price
                    FROM DrugCompositions dc
                    JOIN Herbs h ON dc.herb_id = h.id
                    WHERE dc.drug_id = ?
                """, (drug_id,))

                compositions = self.cursor.fetchall()

                if not compositions:
                    messagebox.showinfo("اطلاعات", f"هیچ ترکیبی برای داروی '{drug_name}' ثبت نشده است")
                    continue

                # Calculate requirements for this drug
                drug_requirements = []
                total_required = 0
                total_cost = 0

                for comp in compositions:
                    herb_id, herb_name, weight_per_unit, available, unit_price = comp

                    # Calculate total required for the desired quantity
                    required = weight_per_unit * quantity
                    total_required += required

                    # Check if inventory is sufficient
                    sufficient = available >= required

                    # Calculate shortage
                    shortage = max(0, required - available)

                    # Calculate cost
                    cost = (required / 1000) * unit_price
                    total_cost += cost

                    # Add to requirements list
                    drug_requirements.append({
                        'herb_id': herb_id,
                        'herb_name': herb_name,
                        'required': required,
                        'available': available,
                        'shortage': shortage,
                        'cost': cost,
                        'sufficient': sufficient
                    })

                    # Track this herb in the all_herbs dictionary
                    if herb_id not in all_herbs:
                        all_herbs[herb_id] = {
                            'herb_name': herb_name,
                            'available': available,
                            'unit_price': unit_price,
                            'total_required': 0,
                            'total_cost': 0,
                            'total_shortage': 0
                        }

                # Add this drug's requirements to the list
                all_requirements.append({
                    'drug_id': drug_id,
                    'drug_name': drug_name,
                    'quantity': quantity,
                    'requirements': drug_requirements,
                    'total_required': total_required,
                    'total_cost': total_cost
                })

            # Calculate combined requirements across all drugs
            for drug_req in all_requirements:
                for herb_req in drug_req['requirements']:
                    herb_id = herb_req['herb_id']
                    all_herbs[herb_id]['total_required'] += herb_req['required']
                    all_herbs[herb_id]['total_cost'] += herb_req['cost']

                    # Recalculate shortage based on total required
                    all_herbs[herb_id]['total_shortage'] = max(0, all_herbs[herb_id]['total_required'] - all_herbs[herb_id]['available'])

            # Show comparison results
            self.show_comparison_results(all_requirements, all_herbs)

            # Reset button state
            self.compare_btn.config(state="normal", text="مقایسه داروها")

        except Exception as e:
            self.compare_btn.config(state="normal", text="مقایسه داروها")
            messagebox.showerror("خطا", f"خطا در مقایسه داروها: {str(e)}")

    def show_comparison_results(self, all_requirements, all_herbs):
        """Display the comparison results in a new window"""
        # Create a new top-level window for comparison results
        comparison_results_window = tk.Toplevel(self.parent)
        comparison_results_window.title("نتایج مقایسه داروها")
        comparison_results_window.geometry("1200x600")  # Adjusted size for better viewing
        comparison_results_window.transient(self.parent)
        comparison_results_window.grab_set()

        # Main frame for the new window
        main_comparison_frame = ttk.Frame(comparison_results_window, padding=10)
        main_comparison_frame.pack(fill="both", expand=True)

        # Create notebook for tabs (one tab per drug + combined tab)
        results_notebook = ttk.Notebook(main_comparison_frame)
        results_notebook.pack(fill="both", expand=True, padx=5, pady=5)

        # Create a tab for each drug
        for drug_req in all_requirements:
            drug_name = drug_req['drug_name']
            quantity = drug_req['quantity']
            requirements = drug_req['requirements']
            total_required = drug_req['total_required']
            total_cost = drug_req['total_cost']

            # Create a frame for this drug's tab
            drug_frame = ttk.Frame(results_notebook)
            results_notebook.add(drug_frame, text=f"{drug_name} ({quantity})")

            # Create a summary frame
            summary_frame = ttk.LabelFrame(drug_frame, text="خلاصه",labelanchor="ne")
            summary_frame.pack(fill="x", padx=5, pady=5)

            # Create a grid layout for summary information
            summary_grid = ttk.Frame(summary_frame)
            summary_grid.pack(fill="x", padx=10, pady=10)

            # Summary cards
            required_frame = ttk.Frame(summary_grid, relief="solid", borderwidth=1)
            required_frame.grid(row=0, column=0, padx=10, pady=5, sticky="ew")
            ttk.Label(required_frame, text="مجموع مقدار مورد نیاز",
                     font=("Tahoma", 9)).pack(pady=(5,0))
            ttk.Label(required_frame, text=f"{total_required:,.0f} گرم",
                     font=("Tahoma", 11, "bold")).pack(pady=(0,5))

            cost_frame = ttk.Frame(summary_grid, relief="solid", borderwidth=1)
            cost_frame.grid(row=0, column=1, padx=10, pady=5, sticky="ew")
            ttk.Label(cost_frame, text="مجموع هزینه",
                     font=("Tahoma", 9)).pack(pady=(5,0))
            ttk.Label(cost_frame, text=f"{total_cost:,.0f} تومان",
                     font=("Tahoma", 11, "bold")).pack(pady=(0,5))

            # Set equal column weights
            summary_grid.columnconfigure(0, weight=1)
            summary_grid.columnconfigure(1, weight=1)

            # Create a table for requirements
            table_frame = ttk.Frame(drug_frame)
            table_frame.pack(fill="both", expand=True, padx=5, pady=5)

            # Create treeview
            columns = ("status", "cost", "shortage", "available", "required", "herb_name", "herb_id")
            req_tree = ttk.Treeview(table_frame, columns=columns, show="headings")

            # Configure headings and columns
            req_tree.heading("herb_id", text="کد گیاه")
            req_tree.heading("herb_name", text="نام گیاه")
            req_tree.heading("required", text="مقدار مورد نیاز (گرم)")
            req_tree.heading("available", text="موجودی انبار (گرم)")
            req_tree.heading("shortage", text="کمبود (گرم)")
            req_tree.heading("cost", text="هزینه (تومان)")
            req_tree.heading("status", text="وضعیت")

            req_tree.column("herb_id", width=50, anchor="center")
            req_tree.column("herb_name", width=100, anchor="center")
            req_tree.column("required", width=120, anchor="center")
            req_tree.column("available", width=120, anchor="center")
            req_tree.column("shortage", width=100, anchor="center")
            req_tree.column("cost", width=120, anchor="center")
            req_tree.column("status", width=60, anchor="center")

            # Add scrollbar
            req_scrollbar = ttk.Scrollbar(table_frame, orient="vertical", command=req_tree.yview)
            req_tree.configure(yscrollcommand=req_scrollbar.set)

            # Pack widgets
            req_scrollbar.pack(side="right", fill="y")
            req_tree.pack(side="left", fill="both", expand=True)

            # Add data to tree
            for i, req in enumerate(requirements):
                status = "کافی" if req['sufficient'] else "ناکافی"
                status_tag = "sufficient" if req['sufficient'] else "insufficient"
                row_tag = "evenrow" if i % 2 == 0 else "oddrow"

                req_tree.insert("", "end", values=(
                    status,
                    f"{req['cost']:,.0f}",
                    f"{req['shortage']:,.0f}",
                    f"{req['available']:,.0f}",
                    f"{req['required']:,.0f}",
                    req['herb_name'],
                    req['herb_id']
                ), tags=(status_tag, row_tag))

            # Configure tags
            req_tree.tag_configure('sufficient', foreground='#047857')
            req_tree.tag_configure('insufficient', foreground='#b91c1c')
            req_tree.tag_configure('evenrow', background='#ffffff')
            req_tree.tag_configure('oddrow', background='#f3f4f6')

        # Create a combined tab
        combined_frame = ttk.Frame(results_notebook)
        results_notebook.add(combined_frame, text="مقایسه کلی")

        # Create a table for combined requirements
        combined_table_frame = ttk.Frame(combined_frame)
        combined_table_frame.pack(fill="both", expand=True, padx=5, pady=5)

        # Create columns for each drug plus herb info
        columns = ["herb_id", "herb_name", "available"]
        column_names = ["کد گیاه", "نام گیاه", "موجودی انبار (گرم)"]

        # Add a column for each drug
        for drug_req in all_requirements:
            drug_name = drug_req['drug_name']
            columns.append(f"required_{drug_req['drug_id']}")
            column_names.append(f"{drug_name} ({drug_req['quantity']})")

        # Add combined columns
        columns.extend(["total_required", "total_shortage", "total_cost", "status"])
        column_names.extend(["مجموع نیاز (گرم)", "مجموع کمبود (گرم)", "مجموع هزینه (تومان)", "وضعیت"])

        # Create treeview
        combined_tree = ttk.Treeview(combined_table_frame, columns=columns, show="headings")

        # Configure headings and columns
        for i, col in enumerate(columns):
            combined_tree.heading(col, text=column_names[i])
            width = 150 if "required_" in col else 100
            combined_tree.column(col, width=width, anchor="center")

        # Add scrollbar
        combined_scrollbar = ttk.Scrollbar(combined_table_frame, orient="vertical", command=combined_tree.yview)
        combined_tree.configure(yscrollcommand=combined_scrollbar.set)

        # Pack widgets
        combined_scrollbar.pack(side="right", fill="y")
        combined_tree.pack(side="left", fill="both", expand=True)

        # Add data to tree
        i = 0
        for herb_id, herb_data in all_herbs.items():
            # Create a row with herb info
            row_values = {
                "herb_id": herb_id,
                "herb_name": herb_data['herb_name'],
                "available": f"{herb_data['available']:,.0f}",
                "total_required": f"{herb_data['total_required']:,.0f}",
                "total_shortage": f"{herb_data['total_shortage']:,.0f}",
                "total_cost": f"{herb_data['total_cost']:,.0f}",
                "status": "کافی" if herb_data['total_shortage'] == 0 else "ناکافی"
            }

            # Add requirements for each drug
            for drug_req in all_requirements:
                drug_id = drug_req['drug_id']
                col_name = f"required_{drug_id}"
                row_values[col_name] = "-"  # Default value

                # Find this herb in the drug's requirements
                for req in drug_req['requirements']:
                    if req['herb_id'] == herb_id:
                        row_values[col_name] = f"{req['required']:,.0f}"
                        break

            # Determine row tags
            status_tag = "sufficient" if herb_data['total_shortage'] == 0 else "insufficient"
            row_tag = "evenrow" if i % 2 == 0 else "oddrow"

            # Insert the row
            values = [row_values[col] for col in columns]
            combined_tree.insert("", "end", values=values, tags=(status_tag, row_tag))
            i += 1

        # Configure tags
        combined_tree.tag_configure('sufficient', foreground='#047857')
        combined_tree.tag_configure('insufficient', foreground='#b91c1c')
        combined_tree.tag_configure('evenrow', background='#ffffff')
        combined_tree.tag_configure('oddrow', background='#f3f4f6')

        # Create a summary frame for combined results
        combined_summary_frame = ttk.LabelFrame(combined_frame, text="خلاصه مقایسه",labelanchor="ne")
        combined_summary_frame.pack(fill="x", padx=5, pady=5)

        # Create a grid for drug comparisons
        comparison_grid = ttk.Frame(combined_summary_frame)
        comparison_grid.pack(fill="x", padx=10, pady=10)

        # Add headers
        ttk.Label(comparison_grid, text="نام دارو", font=("Tahoma", 10, "bold")).grid(row=0, column=0, padx=5, pady=5, sticky="w")
        ttk.Label(comparison_grid, text="تعداد", font=("Tahoma", 10, "bold")).grid(row=0, column=1, padx=5, pady=5, sticky="w")
        ttk.Label(comparison_grid, text="مقدار مورد نیاز (گرم)", font=("Tahoma", 10, "bold")).grid(row=0, column=2, padx=5, pady=5, sticky="w")
        ttk.Label(comparison_grid, text="هزینه (تومان)", font=("Tahoma", 10, "bold")).grid(row=0, column=3, padx=5, pady=5, sticky="w")

        # Add a row for each drug
        for i, drug_req in enumerate(all_requirements):
            row = i + 1
            ttk.Label(comparison_grid, text=drug_req['drug_name']).grid(row=row, column=0, padx=5, pady=2, sticky="w")
            ttk.Label(comparison_grid, text=str(drug_req['quantity'])).grid(row=row, column=1, padx=5, pady=2, sticky="w")
            ttk.Label(comparison_grid, text=f"{drug_req['total_required']:,.0f}").grid(row=row, column=2, padx=5, pady=2, sticky="w")
            ttk.Label(comparison_grid, text=f"{drug_req['total_cost']:,.0f}").grid(row=row, column=3, padx=5, pady=2, sticky="w")

        # Calculate totals
        total_row = len(all_requirements) + 1
        ttk.Separator(comparison_grid, orient="horizontal").grid(row=total_row, column=0, columnspan=4, sticky="ew", pady=5)

        total_row += 1
        ttk.Label(comparison_grid, text="مجموع", font=("Tahoma", 10, "bold")).grid(row=total_row, column=0, padx=5, pady=5, sticky="w")
        ttk.Label(comparison_grid, text="-").grid(row=total_row, column=1, padx=5, pady=5, sticky="w")

        total_required = sum(drug_req['total_required'] for drug_req in all_requirements)
        total_cost = sum(drug_req['total_cost'] for drug_req in all_requirements)

        ttk.Label(comparison_grid, text=f"{total_required:,.0f}", font=("Tahoma", 10, "bold")).grid(row=total_row, column=2, padx=5, pady=5, sticky="w")
        ttk.Label(comparison_grid, text=f"{total_cost:,.0f}", font=("Tahoma", 10, "bold")).grid(row=total_row, column=3, padx=5, pady=5, sticky="w")

    def load_drugs(self, search_term=None):
        """Load drugs into the drug tree with improved search and display"""
        # Clear existing items
        for item in self.drug_tree.get_children():
            self.drug_tree.delete(item)

        try:
            # Build query with improved search
            query = """
                SELECT d.id, d.name, dt.name as type_name,
                       (SELECT COUNT(*) FROM DrugCompositions WHERE drug_id = d.id) as composition_count
                FROM Drugs d
                LEFT JOIN DrugTypes dt ON d.drug_type_id = dt.type_id
            """

            params = []
            if search_term:
                # Improved search to include drug type and more flexible matching
                query += """
                WHERE d.name LIKE ?
                   OR d.id LIKE ?
                   OR dt.name LIKE ?
                   OR EXISTS (
                       SELECT 1 FROM DrugCompositions dc
                       JOIN Herbs h ON dc.herb_id = h.id
                       WHERE dc.drug_id = d.id AND h.name LIKE ?
                   )
                """
                search_param = f"%{search_term}%"
                params = [search_param, search_param, search_param, search_param]

            # Order by name for consistent display
            query += " ORDER BY d.name"

            # Execute query
            if params:
                drugs = self.cursor.execute(query, params).fetchall()
            else:
                drugs = self.cursor.execute(query).fetchall()

            # Show search results count
            if search_term:
                result_count = len(drugs)
                if result_count == 0:
                    self.drug_tree.insert("", "end", values=("", f"هیچ نتیجه‌ای برای '{search_term}' یافت نشد", ""), tags=('no_results',))
                    self.drug_tree.tag_configure('no_results', foreground='gray')
                    return

            # Add to tree with improved tags
            for i, drug in enumerate(drugs):
                drug_id, name, drug_type, composition_count = drug
                drug_type = drug_type or "نامشخص"  # Handle None type

                # Apply tags for styling
                tags = []

                # Alternating row colors
                row_tag = 'evenrow' if i % 2 == 0 else 'oddrow'
                tags.append(row_tag)

                # Tag for drugs with no compositions
                if composition_count == 0:
                    tags.append('no_composition')
                    name = f"{name} (بدون ترکیب)"

                # Highlight search term if present
                if search_term and search_term.lower() in name.lower():
                    tags.append('search_match')

                self.drug_tree.insert("", "end", values=(drug_type, name, drug_id), tags=tuple(tags))

            # Configure tags for visual styling
            self.drug_tree.tag_configure('oddrow', background='#f3f4f6')
            self.drug_tree.tag_configure('evenrow', background='#ffffff')
            self.drug_tree.tag_configure('no_composition', foreground='#9ca3af')  # Gray text for drugs with no compositions
            self.drug_tree.tag_configure('search_match', background='#fef3c7')  # Light yellow highlight for search matches

            # Select first item if available
            if self.drug_tree.get_children():
                first_item = self.drug_tree.get_children()[0]
                self.drug_tree.selection_set(first_item)
                self.drug_tree.focus(first_item)
                self.on_drug_select(None)  # Trigger selection event

        except Exception as e:
            messagebox.showerror("خطا", f"خطا در بارگیری داروها: {str(e)}")

    def filter_drugs(self, event=None):
        """Filter drugs based on search input with improved feedback"""
        search_term = self.drug_search_entry.get().strip()

        # Clear selection and reset drug info when search changes
        if self.selected_drug_id:
            self.selected_drug_id = None
            self.drug_name_var.set("-")
            self.drug_id_var.set("-")
            self.drug_type_var.set("-")
            self.calculate_btn.config(state="disabled")

        # Load drugs with search term
        self.load_drugs(search_term)

    def on_drug_select(self, event):
        """Handle drug selection"""
        selected = self.drug_tree.selection()
        if not selected:
            return

        # Get selected drug data
        values = self.drug_tree.item(selected[0], 'values')
        drug_id = values[2]
        self.selected_drug_id = drug_id

        # Update drug info
        self.drug_name_var.set(values[1])
        self.drug_id_var.set(values[2])
        self.drug_type_var.set(values[0])

        # Enable calculate button
        self.calculate_btn.config(state="normal")

    def calculate_requirements(self):
        """Calculate material requirements for the selected drug with improved user feedback"""
        if not self.selected_drug_id:
            messagebox.showwarning("هشدار", "لطفاً ابتدا یک دارو را انتخاب کنید")
            return

        try:
            # Validate quantity input
            try:
                quantity = int(self.quantity_entry.get())
                if quantity <= 0:
                    messagebox.showwarning("هشدار", "تعداد باید بزرگتر از صفر باشد")
                    return
            except ValueError:
                messagebox.showwarning("هشدار", "لطفاً یک عدد صحیح برای تعداد وارد کنید")
                return

            # Check if we're in comparison mode
            if self.comparison_mode_var.get():
                # Add to comparison instead of calculating directly
                self.add_to_comparison()
                return

            # Show calculation in progress
            original_text = self.calculate_btn['text']
            self.calculate_btn.config(state="disabled", text="در حال محاسبه...")
            self.parent.update()  # Update UI to show the button state change

            # Fetch compositions
            self.cursor.execute("""
                SELECT dc.herb_id, h.name, dc.weight_used, h.current_weight, h.unit_price
                FROM DrugCompositions dc
                JOIN Herbs h ON dc.herb_id = h.id
                WHERE dc.drug_id = ?
            """, (self.selected_drug_id,))

            compositions = self.cursor.fetchall()

            if not compositions:
                messagebox.showinfo("اطلاعات", "هیچ ترکیبی برای این دارو ثبت نشده است")
                self.calculate_btn.config(state="normal", text=original_text)
                return

            # Calculate requirements and check inventory
            requirements = []
            total_required = 0
            total_cost = 0
            all_sufficient = True

            for comp in compositions:
                herb_id, herb_name, weight_per_unit, available, unit_price = comp

                # Calculate total required for the desired quantity
                required = weight_per_unit * quantity
                total_required += required

                # Check if inventory is sufficient
                sufficient = available >= required
                if not sufficient:
                    all_sufficient = False

                # Calculate shortage
                shortage = max(0, required - available)

                # Calculate cost (unit_price is per kg, so convert grams to kg)
                cost = (required / 1000) * unit_price
                total_cost += cost

                # Add to requirements list
                requirements.append({
                    'herb_id': herb_id,
                    'herb_name': herb_name,
                    'required': required,
                    'available': available,
                    'shortage': shortage,
                    'cost': cost,
                    'sufficient': sufficient,
                    'percent_available': min(100, (available / required * 100) if required > 0 else 100)  # For visual indicators
                })

            # Sort requirements by sufficiency (insufficient first) and then by name
            requirements.sort(key=lambda x: (x['sufficient'], x['herb_name']))

            # Show results in a new window
            self.show_results_in_new_window(requirements, total_required, total_cost, quantity, all_sufficient)

            # Save current results for potential export
            self.current_results = {
                'drug_name': self.drug_name_var.get(),
                'quantity': quantity,
                'requirements': requirements,
                'total_required': total_required,
                'total_cost': total_cost,
                'total_shortage': sum(req['shortage'] for req in requirements),
                'all_sufficient': all_sufficient
            }

            # Reset button state
            self.calculate_btn.config(state="normal", text=original_text)

        except Exception as e:
            self.calculate_btn.config(state="normal", text="محاسبه نیازمندی‌ها")
            messagebox.showerror("خطا", f"خطا در محاسبه نیازمندی‌ها: {str(e)}")

    def show_results_in_new_window(self, requirements, total_required, total_cost, quantity, all_sufficient):
        """Display the calculation results in a new window with enhanced visualization and export options"""
        # Create a new top-level window
        results_window = tk.Toplevel(self.parent)
        results_window.title(f"نتایج محاسبه برای {self.drug_name_var.get()}")
        results_window.geometry("1000x600")
        results_window.transient(self.parent)
        results_window.grab_set()

        # Main frame for the new window
        main_results_frame = ttk.Frame(results_window, padding=10)
        main_results_frame.pack(fill="both", expand=True)

        # Top control frame for status and export buttons
        top_frame = ttk.Frame(main_results_frame)
        top_frame.pack(fill="x", padx=5, pady=5)

        # Status indicator with icon
        status_frame = ttk.Frame(top_frame)
        status_frame.pack(side="right", fill="y", padx=10)

        if all_sufficient:
            status_label = ttk.Label(
                status_frame,
                text="موجودی انبار کافی است ✓",
                foreground="green",
                font=("Tahoma", 12, "bold")
            )
        else:
            status_label = ttk.Label(
                status_frame,
                text="⚠️ کمبود موجودی",
                foreground="red",
                font=("Tahoma", 12, "bold")
            )
        status_label.pack(side="right")

        # Export buttons frame
        export_frame = ttk.Frame(top_frame)
        export_frame.pack(side="left", fill="y", padx=10)

        # Save calculation results for export
        self.current_results = {
            'drug_name': self.drug_name_var.get(),
            'quantity': quantity,
            'requirements': requirements,
            'total_required': total_required,
            'total_cost': total_cost,
            'total_shortage': sum(req['shortage'] for req in requirements),
            'all_sufficient': all_sufficient
        }

        # Export buttons
        if REPORTLAB_AVAILABLE:
            pdf_btn = ttk.Button(
                export_frame,
                text="خروجی PDF",
                command=self.export_to_pdf
            )
            pdf_btn.pack(side="left", padx=5)

        if OPENPYXL_AVAILABLE:
            excel_btn = ttk.Button(
                export_frame,
                text="خروجی Excel",
                command=self.export_to_excel
            )
            excel_btn.pack(side="left", padx=5)

        # Export to CSV (doesn't require external libraries)
        csv_btn = ttk.Button(
            export_frame,
            text="خروجی CSV",
            command=self.export_to_csv
        )
        csv_btn.pack(side="left", padx=5)

        # Requirements table with improved styling
        table_frame = ttk.Frame(main_results_frame)
        table_frame.pack(fill="both", expand=True, padx=5, pady=5)

        # Create treeview with right-to-left column order
        columns = ("status", "cost", "shortage", "available", "required", "herb_name", "herb_id")
        # Use a local variable for the treeview in the new window
        req_tree = ttk.Treeview(table_frame, columns=columns, show="headings")

        # Configure headings and columns
        req_tree.heading("herb_id", text="کد گیاه")
        req_tree.heading("herb_name", text="نام گیاه")
        req_tree.heading("required", text="مقدار مورد نیاز (گرم)")
        req_tree.heading("available", text="موجودی انبار (گرم)")
        req_tree.heading("shortage", text="کمبود (گرم)")
        req_tree.heading("cost", text="هزینه (تومان)")
        req_tree.heading("status", text="وضعیت")

        req_tree.column("herb_id", width=80, anchor="center")
        req_tree.column("herb_name", width=150, anchor="center")
        req_tree.column("required", width=120, anchor="center")
        req_tree.column("available", width=120, anchor="center")
        req_tree.column("shortage", width=100, anchor="center")
        req_tree.column("cost", width=120, anchor="center")
        req_tree.column("status", width=80, anchor="center")

        # Add scrollbar
        req_scrollbar = ttk.Scrollbar(table_frame, orient="vertical", command=req_tree.yview)
        req_tree.configure(yscrollcommand=req_scrollbar.set)

        # Pack widgets
        req_scrollbar.pack(side="right", fill="y")
        req_tree.pack(side="left", fill="both", expand=True) # Use local req_tree

        # Add data to tree with improved formatting
        total_shortage = 0
        for i, req in enumerate(requirements):
            status = "کافی" if req['sufficient'] else "ناکافی"
            status_tag = "sufficient" if req['sufficient'] else "insufficient"
            row_tag = "evenrow" if i % 2 == 0 else "oddrow"  # Alternating row colors

            req_tree.insert("", "end", values=(
                status,
                f"{req['cost']:,.0f}",
                f"{req['shortage']:,.0f}",
                f"{req['available']:,.0f}",
                f"{req['required']:,.0f}",
                req['herb_name'],
                req['herb_id']
            ), tags=(status_tag, row_tag))

            total_shortage += req['shortage']

        # Configure tags for better visual distinction
        req_tree.tag_configure('sufficient', foreground='#047857')  # Dark green text
        req_tree.tag_configure('insufficient', foreground='#b91c1c')  # Dark red text
        req_tree.tag_configure('evenrow', background='#ffffff')
        req_tree.tag_configure('oddrow', background='#f3f4f6')

        # Enhanced summary frame with card-like appearance
        summary_frame = ttk.LabelFrame(main_results_frame, text="خلاصه محاسبات",labelanchor="ne")
        summary_frame.pack(fill="x", padx=5, pady=10)

        # Create a grid layout for summary information
        summary_grid = ttk.Frame(summary_frame)
        summary_grid.pack(fill="x", padx=10, pady=10)

        # Summary cards with more visual appeal
        required_frame = ttk.Frame(summary_grid, relief="solid", borderwidth=1)
        required_frame.grid(row=0, column=0, padx=10, pady=5, sticky="ew")
        ttk.Label(required_frame, text="مجموع مقدار مورد نیاز",
                 font=("Tahoma", 9)).pack(pady=(5,0))
        ttk.Label(required_frame, text=f"{total_required:,.0f} گرم",
                 font=("Tahoma", 11, "bold")).pack(pady=(0,5))

        cost_frame = ttk.Frame(summary_grid, relief="solid", borderwidth=1)
        cost_frame.grid(row=0, column=1, padx=10, pady=5, sticky="ew")
        ttk.Label(cost_frame, text="مجموع هزینه",
                 font=("Tahoma", 9)).pack(pady=(5,0))
        ttk.Label(cost_frame, text=f"{total_cost:,.0f} تومان",
                 font=("Tahoma", 11, "bold")).pack(pady=(0,5))

        if total_shortage > 0:
            shortage_frame = ttk.Frame(summary_grid, relief="solid", borderwidth=1)
            shortage_frame.grid(row=0, column=2, padx=10, pady=5, sticky="ew")
            ttk.Label(shortage_frame, text="مجموع کمبود",
                     font=("Tahoma", 9)).pack(pady=(5,0))
            ttk.Label(shortage_frame, text=f"{total_shortage:,.0f} گرم",
                     font=("Tahoma", 11, "bold"), foreground="red").pack(pady=(0,5))

        # Set equal column weights
        summary_grid.columnconfigure(0, weight=1)
        summary_grid.columnconfigure(1, weight=1)
        summary_grid.columnconfigure(2, weight=1)

        # Enhanced conclusion frame
        conclusion_frame = ttk.LabelFrame(main_results_frame, text="نتیجه‌گیری",labelanchor="ne")
        conclusion_frame.pack(fill="x", padx=5, pady=5)

        # Use the drug name passed or from the instance variable
        drug_name = self.drug_name_var.get()

        if all_sufficient:
            conclusion_text = f"برای تولید {quantity} عدد {drug_name} نیاز به {total_required:,.0f} گرم مفردات گیاهی با هزینه کل {total_cost:,.0f} تومان می‌باشد که موجودی انبار برای تمامی مفردات کافی است."
        else:
            conclusion_text = f"برای تولید {quantity} عدد {drug_name} نیاز به {total_required:,.0f} گرم مفردات گیاهی با هزینه کل {total_cost:,.0f} تومان می‌باشد ولی با کمبود {total_shortage:,.0f} گرم در برخی مفردات مواجه هستیم."

        conclusion_label = ttk.Label(
            conclusion_frame,
            text=conclusion_text,
            wraplength=600,
            justify="center",
            padding=10,
            font=("Tahoma", 10)
        )
        conclusion_label.pack(fill="x")

    def export_to_csv(self):
        """Export calculation results to CSV file"""
        if not self.current_results:
            messagebox.showwarning("هشدار", "هیچ نتیجه‌ای برای خروجی گرفتن وجود ندارد")
            return

        try:
            # Ask user for save location
            file_path = filedialog.asksaveasfilename(
                defaultextension=".csv",
                filetypes=[("CSV Files", "*.csv"), ("All Files", "*.*")],
                title="ذخیره نتایج به صورت CSV"
            )

            if not file_path:
                return  # User cancelled

            # Get data from current results
            drug_name = self.current_results['drug_name']
            quantity = self.current_results['quantity']
            requirements = self.current_results['requirements']
            total_required = self.current_results['total_required']
            total_cost = self.current_results['total_cost']
            total_shortage = self.current_results['total_shortage']
            all_sufficient = self.current_results['all_sufficient']

            # Write to CSV
            with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                writer = csv.writer(csvfile)

                # Write header
                writer.writerow(['گزارش نیازمندی‌های تولید'])
                writer.writerow([f'نام دارو: {drug_name}', f'تعداد: {quantity}'])
                writer.writerow([])  # Empty row

                # Write table header
                writer.writerow(['کد گیاه', 'نام گیاه', 'مقدار مورد نیاز (گرم)', 'موجودی انبار (گرم)',
                                'کمبود (گرم)', 'هزینه (تومان)', 'وضعیت'])

                # Write requirements data
                for req in requirements:
                    writer.writerow([
                        req['herb_id'],
                        req['herb_name'],
                        f"{req['required']:,.0f}",
                        f"{req['available']:,.0f}",
                        f"{req['shortage']:,.0f}",
                        f"{req['cost']:,.0f}",
                        "کافی" if req['sufficient'] else "ناکافی"
                    ])

                # Write summary
                writer.writerow([])  # Empty row
                writer.writerow(['خلاصه محاسبات'])
                writer.writerow(['مجموع مقدار مورد نیاز (گرم)', f"{total_required:,.0f}"])
                writer.writerow(['مجموع هزینه (تومان)', f"{total_cost:,.0f}"])
                if total_shortage > 0:
                    writer.writerow(['مجموع کمبود (گرم)', f"{total_shortage:,.0f}"])

                # Write conclusion
                writer.writerow([])  # Empty row
                if all_sufficient:
                    conclusion = f"برای تولید {quantity} عدد {drug_name} نیاز به {total_required:,.0f} گرم مفردات گیاهی با هزینه کل {total_cost:,.0f} تومان می‌باشد که موجودی انبار برای تمامی مفردات کافی است."
                else:
                    conclusion = f"برای تولید {quantity} عدد {drug_name} نیاز به {total_required:,.0f} گرم مفردات گیاهی با هزینه کل {total_cost:,.0f} تومان می‌باشد ولی با کمبود {total_shortage:,.0f} گرم در برخی مفردات مواجه هستیم."
                writer.writerow(['نتیجه‌گیری', conclusion])

            messagebox.showinfo("موفق", f"خروجی CSV با موفقیت در مسیر زیر ذخیره شد:\n{file_path}")

        except Exception as e:
            messagebox.showerror("خطا", f"خطا در ایجاد خروجی CSV: {str(e)}")

    def export_to_excel(self):
        """Export calculation results to Excel file"""
        if not self.current_results or not OPENPYXL_AVAILABLE:
            messagebox.showwarning("هشدار", "هیچ نتیجه‌ای برای خروجی گرفتن وجود ندارد یا کتابخانه openpyxl نصب نیست")
            return

        try:
            # Ask user for save location
            file_path = filedialog.asksaveasfilename(
                defaultextension=".xlsx",
                filetypes=[("Excel Files", "*.xlsx"), ("All Files", "*.*")],
                title="ذخیره نتایج به صورت Excel"
            )

            if not file_path:
                return  # User cancelled

            # Get data from current results
            drug_name = self.current_results['drug_name']
            quantity = self.current_results['quantity']
            requirements = self.current_results['requirements']
            total_required = self.current_results['total_required']
            total_cost = self.current_results['total_cost']
            total_shortage = self.current_results['total_shortage']
            all_sufficient = self.current_results['all_sufficient']

            # Create workbook and sheet
            wb = openpyxl.Workbook()
            ws = wb.active
            ws.title = "نیازمندی‌های تولید"

            # Set right-to-left direction for the sheet
            ws.sheet_view.rightToLeft = True

            # Define styles
            title_font = Font(name='Arial', size=14, bold=True)
            header_font = Font(name='Arial', size=12, bold=True)
            data_font = Font(name='Arial', size=11)

            # Title
            ws['A1'] = "گزارش نیازمندی‌های تولید"
            ws['A1'].font = title_font
            ws.merge_cells('A1:G1')
            ws['A1'].alignment = Alignment(horizontal='center')

            # Drug info
            ws['A2'] = f"نام دارو: {drug_name}"
            ws['A2'].font = header_font
            ws['C2'] = f"تعداد: {quantity}"
            ws['C2'].font = header_font

            # Table headers (row 4)
            headers = ['کد گیاه', 'نام گیاه', 'مقدار مورد نیاز (گرم)', 'موجودی انبار (گرم)',
                      'کمبود (گرم)', 'هزینه (تومان)', 'وضعیت']

            for col_idx, header in enumerate(headers, 1):
                cell = ws.cell(row=4, column=col_idx, value=header)
                cell.font = header_font
                cell.alignment = Alignment(horizontal='center', vertical='center')
                # Set column width based on header length
                ws.column_dimensions[get_column_letter(col_idx)].width = max(15, len(header) * 1.2)

            # Add data rows
            row_idx = 5
            for req in requirements:
                ws.cell(row=row_idx, column=1, value=req['herb_id'])
                ws.cell(row=row_idx, column=2, value=req['herb_name'])
                ws.cell(row=row_idx, column=3, value=req['required'])
                ws.cell(row=row_idx, column=4, value=req['available'])
                ws.cell(row=row_idx, column=5, value=req['shortage'])
                ws.cell(row=row_idx, column=6, value=req['cost'])
                ws.cell(row=row_idx, column=7, value="کافی" if req['sufficient'] else "ناکافی")

                # Apply formatting
                for col_idx in range(1, 8):
                    cell = ws.cell(row=row_idx, column=col_idx)
                    cell.font = data_font
                    cell.alignment = Alignment(horizontal='center')

                    # Color coding for status
                    if col_idx == 7:  # Status column
                        if req['sufficient']:
                            cell.font = Font(name='Arial', size=11, color='008000')  # Green
                        else:
                            cell.font = Font(name='Arial', size=11, color='FF0000')  # Red

                row_idx += 1

            # Add summary section
            row_idx += 2  # Skip a row

            ws.cell(row=row_idx, column=1, value="خلاصه محاسبات")
            ws.cell(row=row_idx, column=1).font = header_font
            ws.merge_cells(f'A{row_idx}:G{row_idx}')
            ws.cell(row=row_idx, column=1).alignment = Alignment(horizontal='center')

            row_idx += 1
            ws.cell(row=row_idx, column=1, value="مجموع مقدار مورد نیاز (گرم)")
            ws.cell(row=row_idx, column=2, value=total_required)

            row_idx += 1
            ws.cell(row=row_idx, column=1, value="مجموع هزینه (تومان)")
            ws.cell(row=row_idx, column=2, value=total_cost)

            if total_shortage > 0:
                row_idx += 1
                ws.cell(row=row_idx, column=1, value="مجموع کمبود (گرم)")
                ws.cell(row=row_idx, column=2, value=total_shortage)
                ws.cell(row=row_idx, column=2).font = Font(name='Arial', size=11, color='FF0000')

            # Add conclusion
            row_idx += 2
            ws.cell(row=row_idx, column=1, value="نتیجه‌گیری")
            ws.cell(row=row_idx, column=1).font = header_font
            ws.merge_cells(f'A{row_idx}:G{row_idx}')
            ws.cell(row=row_idx, column=1).alignment = Alignment(horizontal='center')

            row_idx += 1
            if all_sufficient:
                conclusion = f"برای تولید {quantity} عدد {drug_name} نیاز به {total_required:,.0f} گرم مفردات گیاهی با هزینه کل {total_cost:,.0f} تومان می‌باشد که موجودی انبار برای تمامی مفردات کافی است."
            else:
                conclusion = f"برای تولید {quantity} عدد {drug_name} نیاز به {total_required:,.0f} گرم مفردات گیاهی با هزینه کل {total_cost:,.0f} تومان می‌باشد ولی با کمبود {total_shortage:,.0f} گرم در برخی مفردات مواجه هستیم."

            ws.cell(row=row_idx, column=1, value=conclusion)
            ws.merge_cells(f'A{row_idx}:G{row_idx}')
            ws.cell(row=row_idx, column=1).alignment = Alignment(horizontal='right', wrap_text=True)
            ws.row_dimensions[row_idx].height = 60  # Increase row height for wrapped text

            # Save the workbook
            wb.save(file_path)
            messagebox.showinfo("موفق", f"خروجی Excel با موفقیت در مسیر زیر ذخیره شد:\n{file_path}")

        except Exception as e:
            messagebox.showerror("خطا", f"خطا در ایجاد خروجی Excel: {str(e)}")

    def export_to_pdf(self):
        """Export calculation results to PDF file"""
        if not self.current_results or not REPORTLAB_AVAILABLE:
            messagebox.showwarning("هشدار", "هیچ نتیجه‌ای برای خروجی گرفتن وجود ندارد یا کتابخانه reportlab نصب نیست")
            return

        try:
            # Ask user for save location
            file_path = filedialog.asksaveasfilename(
                defaultextension=".pdf",
                filetypes=[("PDF Files", "*.pdf"), ("All Files", "*.*")],
                title="ذخیره نتایج به صورت PDF"
            )

            if not file_path:
                return  # User cancelled

            # Get data from current results
            drug_name = self.current_results['drug_name']
            quantity = self.current_results['quantity']
            requirements = self.current_results['requirements']
            total_required = self.current_results['total_required']
            total_cost = self.current_results['total_cost']
            total_shortage = self.current_results['total_shortage']
            all_sufficient = self.current_results['all_sufficient']

            # Create PDF document
            doc = SimpleDocTemplate(
                file_path,
                pagesize=letter,
                rightMargin=inch/2,
                leftMargin=inch/2,
                topMargin=inch/2,
                bottomMargin=inch/2
            )

            # Get styles
            styles = getSampleStyleSheet()
            title_style = styles["Title"]
            heading_style = styles["Heading2"]
            normal_style = styles["Normal"]

            # Create document elements
            elements = []

            # Title
            title = Paragraph("گزارش نیازمندی‌های تولید", title_style)
            elements.append(title)
            elements.append(Spacer(1, 12))

            # Drug info
            drug_info = Paragraph(f"نام دارو: {drug_name} - تعداد: {quantity}", heading_style)
            elements.append(drug_info)
            elements.append(Spacer(1, 12))

            # Create table data
            table_data = [
                ['وضعیت', 'هزینه (تومان)', 'کمبود (گرم)', 'موجودی انبار (گرم)', 'مقدار مورد نیاز (گرم)', 'نام گیاه', 'کد گیاه']
            ]

            # Add requirements data
            for req in requirements:
                status = "کافی" if req['sufficient'] else "ناکافی"
                table_data.append([
                    status,
                    f"{req['cost']:,.0f}",
                    f"{req['shortage']:,.0f}",
                    f"{req['available']:,.0f}",
                    f"{req['required']:,.0f}",
                    req['herb_name'],
                    req['herb_id']
                ])

            # Create table
            table = Table(table_data, repeatRows=1)

            # Style the table
            table_style = TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.black),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 12),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ])

            # Add row colors
            for i in range(1, len(table_data)):
                if i % 2 == 0:
                    table_style.add('BACKGROUND', (0, i), (-1, i), colors.whitesmoke)

                # Color status column
                if table_data[i][0] == "کافی":
                    table_style.add('TEXTCOLOR', (0, i), (0, i), colors.green)
                else:
                    table_style.add('TEXTCOLOR', (0, i), (0, i), colors.red)

            table.setStyle(table_style)
            elements.append(table)
            elements.append(Spacer(1, 20))

            # Summary section
            elements.append(Paragraph("خلاصه محاسبات", heading_style))
            elements.append(Spacer(1, 6))

            summary_data = [
                ["مجموع مقدار مورد نیاز (گرم)", f"{total_required:,.0f}"],
                ["مجموع هزینه (تومان)", f"{total_cost:,.0f}"]
            ]

            if total_shortage > 0:
                summary_data.append(["مجموع کمبود (گرم)", f"{total_shortage:,.0f}"])

            summary_table = Table(summary_data, colWidths=[3*inch, 2*inch])
            summary_style = TableStyle([
                ('ALIGN', (0, 0), (0, -1), 'RIGHT'),
                ('ALIGN', (1, 0), (1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ])

            # Color shortage row if exists
            if total_shortage > 0:
                summary_style.add('TEXTCOLOR', (1, 2), (1, 2), colors.red)

            summary_table.setStyle(summary_style)
            elements.append(summary_table)
            elements.append(Spacer(1, 20))

            # Conclusion
            elements.append(Paragraph("نتیجه‌گیری", heading_style))
            elements.append(Spacer(1, 6))

            if all_sufficient:
                conclusion = f"برای تولید {quantity} عدد {drug_name} نیاز به {total_required:,.0f} گرم مفردات گیاهی با هزینه کل {total_cost:,.0f} تومان می‌باشد که موجودی انبار برای تمامی مفردات کافی است."
            else:
                conclusion = f"برای تولید {quantity} عدد {drug_name} نیاز به {total_required:,.0f} گرم مفردات گیاهی با هزینه کل {total_cost:,.0f} تومان می‌باشد ولی با کمبود {total_shortage:,.0f} گرم در برخی مفردات مواجه هستیم."

            conclusion_para = Paragraph(conclusion, normal_style)
            elements.append(conclusion_para)

            # Build the PDF
            doc.build(elements)
            messagebox.showinfo("موفق", f"خروجی PDF با موفقیت در مسیر زیر ذخیره شد:\n{file_path}")

        except Exception as e:
            messagebox.showerror("خطا", f"خطا در ایجاد خروجی PDF: {str(e)}")

class HerbalFactoryApp:
    def __init__(self, root, conn, current_user):
        self.root = root
        self.conn = conn
        self.current_user = current_user
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill="both", expand=True)
        # ... سایر تب‌ها ...


        # Create styles
        self.setup_styles()

        # Create notebook for tabs
        self.herb_tab = ttk.Frame(self.notebook)
        self.disease_tab = ttk.Frame(self.notebook)
        self.drug_tab = ttk.Frame(self.notebook)
        self.report_tab = ttk.Frame(self.notebook)
        self.user_tab = ttk.Frame(self.notebook)
        self.backup_tab = ttk.Frame(self.notebook)
        self.settings_tab = ttk.Frame(self.notebook)  # New settings tab
        self.planner_tab = ttk.Frame(self.notebook)   # New planner tab

        # Get user's accessible tabs
        self.accessible_tabs = self.current_user.get('accessible_tabs', '').split(',')

        # Always add the planner tab to accessible tabs if it doesn't exist
        if 'planner' not in self.accessible_tabs:
            self.accessible_tabs.append('planner')

        # Add accessible tabs to notebook
        if 'herbs' in self.accessible_tabs:
            self.notebook.add(self.herb_tab, text="مدیریت مفردات گیاهی")
        if 'diseases' in self.accessible_tabs:
            self.notebook.add(self.disease_tab, text="مدیریت گروه دارویی")
        if 'drugs' in self.accessible_tabs:
            self.notebook.add(self.drug_tab, text="تولید دارو")
        if 'planner' in self.accessible_tabs:
            self.notebook.add(self.planner_tab, text="برنامه‌ریزی تولید")
        if 'reports' in self.accessible_tabs:
            self.notebook.add(self.report_tab, text="گزارشات")
        if 'backup' in self.accessible_tabs:
            self.notebook.add(self.backup_tab, text="پشتیبان‌گیری")

        # Always add settings tab
        self.notebook.add(self.settings_tab, text="تنظیمات سیستم")

        # Always show user management tab for admins
        if self.current_user['role'] == UserRole.ADMIN.value:
            self.notebook.add(self.user_tab, text="مدیریت کاربران")

        self.notebook.pack(expand=True, fill="both")

        # Initialize tab managers only for accessible tabs
        if 'herbs' in self.accessible_tabs:
            self.herb_manager = HerbTabManager(self.herb_tab, self.conn)
        if 'diseases' in self.accessible_tabs:
            self.disease_manager = DiseaseTabManager(self.disease_tab, self.conn)
        if 'drugs' in self.accessible_tabs:
            self.drug_manager = DrugTabManager(self.drug_tab, self.conn, self.disease_manager if 'diseases' in self.accessible_tabs else None)
        if 'reports' in self.accessible_tabs:
            self.report_manager = ReportTabManager(self.report_tab, self.conn)
        if 'backup' in self.accessible_tabs:
            self.backup_manager = BackupTabManager(self.backup_tab, self.conn)
        if 'planner' in self.accessible_tabs:
            self.planner_manager = ProductionPlannerManager(self.planner_tab, self.conn)

        # Initialize settings manager
        self.settings_manager = AppSettingsManager(self.settings_tab, self.conn)

        # Initialize user manager if admin
        if self.current_user['role'] == UserRole.ADMIN.value:
            self.user_manager = UserManager(self.user_tab, self.current_user)

        # Refresh data initially for accessible tabs
        self.refresh_all_data()

        # Bind tab change event to refresh data
        self.notebook.bind("<<NotebookTabChanged>>", self.on_tab_changed)

    def refresh_all_data(self):
        """Refresh data in all accessible tabs"""
        if hasattr(self, 'herb_manager'):
            self.herb_manager.load_herbs()

        if hasattr(self, 'disease_manager'):
            self.disease_manager.load_diseases()

        if hasattr(self, 'drug_manager'):
            self.drug_manager.load_produced_drugs()
            if hasattr(self, 'disease_manager'):
                self.drug_manager.update_disease_combobox(self.disease_manager.disease_map)

        if hasattr(self, 'report_manager'):
            self.report_manager.generate_report()

        if hasattr(self, 'planner_manager'):
            self.planner_manager.load_drugs()

        if hasattr(self, 'settings_manager'):
            self.settings_manager.load_settings()

    def setup_styles(self):
        """Set up custom styles for the application with improved aesthetics"""
        style = ttk.Style()

        # Define colors with a more modern palette
        primary_color = "#4F46E5"  # Indigo
        secondary_color = "#10B981"  # Emerald
        accent_color = "#F59E0B"  # Amber
        background_color = "#F9FAFB"  # Light gray
        card_color = "#FFFFFF"  # White
        text_color = "#1F2937"  # Dark gray
        muted_text_color = "#6B7280"  # Medium gray
        border_color = "#E5E7EB"  # Light border

        # Configure button styles with more modern look
        style.configure("TButton",
                        background=primary_color,
                        foreground="red",
                        borderwidth=0,
                        focusthickness=3,
                        focuscolor=primary_color,
                        lightcolor=primary_color,
                        darkcolor=primary_color,
                        relief="flat",
                        padding=(10, 5),
                        font=("Tahoma", 11))

        style.map("TButton",
                  background=[("active", secondary_color),
                             ("disabled", "#D1D5DB")],
                  foreground=[("disabled", "#9CA3AF")])

        # Accent button style (for export buttons)
        style.configure("Accent.TButton",
                       background=accent_color,
                       foreground="white")

        style.map("Accent.TButton",
                 background=[("active", "#F97316")])

        # Label styles
        style.configure("TLabel",
                        font=("Tahoma", 11),
                        background=background_color,
                        foreground=text_color,
                        padding=2)

        # Frame styles for better visual hierarchy
        style.configure("TFrame",
                        background=background_color)
        
        style.configure("Centered.TLabelframe", labelanchor="ne")
        
        # LabelFrame with subtle borders
        style.configure("TLabelframe",
                        background=card_color,
                        borderwidth=1,
                        relief="solid",
                        labelanchor="ne",
                        bordercolor=border_color)

        style.configure("TLabelframe.Label",
                        font=("Tahoma", 11, "bold"),
                        background=background_color,
                        foreground=primary_color)

        # Heading style
        style.configure("Heading.TLabel",
                        font=("Tahoma", 14, "bold"),
                        foreground=primary_color,
                        background=background_color)

        # Entry and Spinbox styles
        style.configure("TEntry",
                       fieldbackground="white",
                       bordercolor=border_color,
                       lightcolor=border_color,
                       darkcolor=border_color,
                       borderwidth=1,
                       font=("Tahoma", 11))

        style.map("TEntry",
                 fieldbackground=[("focus", "#EFF6FF")],
                 bordercolor=[("focus", primary_color)])

        style.configure("TSpinbox",
                       fieldbackground="white",
                       bordercolor=border_color,
                       arrowcolor=primary_color,
                       font=("Tahoma", 11))

        # Treeview styles with improved aesthetics
        style.configure("Treeview",
                        background=card_color,
                        fieldbackground=card_color,
                        font=("Tahoma", 10),
                        borderwidth=1,
                        relief="solid")

        style.configure("Treeview.Heading",
                        font=("Tahoma", 10, "bold"),
                        background="#EEF2FF",  # Light indigo
                        foreground=primary_color,
                        relief="flat",
                        borderwidth=1)

        style.map("Treeview",
                  background=[("selected", "#818CF8")],  # Light indigo
                  foreground=[("selected", "white")])

        # Notebook (tab) styles
        style.configure("TNotebook",
                       background=background_color,
                       borderwidth=0)

        style.configure("TNotebook.Tab",
                       background="#E5E7EB",
                       foreground=muted_text_color,
                       padding=(10, 5),
                       font=("Tahoma", 10))

        style.map("TNotebook.Tab",
                 background=[("selected", primary_color)],
                 foreground=[("selected", "red")],
                 expand=[("selected", [1, 1, 1, 0])])

        # Scrollbar style
        style.configure("TScrollbar",
                       background=background_color,
                       troughcolor=background_color,
                       bordercolor=border_color,
                       arrowcolor=primary_color,
                       borderwidth=1)

        try:
            # Use master or parent attribute instead of root
            self.root.configure(background=background_color)
            for child in self.root.winfo_children():
                if isinstance(child, ttk.Frame) or isinstance(child, ttk.LabelFrame):
                    child.configure(style="TFrame")
        except Exception as e:
            # Silently handle the exception if master attribute is not available
            # or if there's an issue with configuring the background
            print(f"Error configuring background: {e}")



    def on_tab_changed(self, event):
        """Handle tab change event"""
        current_tab = self.notebook.index(self.notebook.select())
        tab_text = self.notebook.tab(current_tab, 'text')

        # Refresh data based on which tab is selected
        if tab_text == "مدیریت مفردات گیاهی" and hasattr(self, 'herb_manager'):
            self.herb_manager.load_herbs()
        elif tab_text == "مدیریت گروه دارویی" and hasattr(self, 'disease_manager'):
            self.disease_manager.load_diseases()
        elif tab_text == "تولید دارو" and hasattr(self, 'drug_manager'):
            self.drug_manager.load_produced_drugs()
            if hasattr(self, 'disease_manager'):
                self.drug_manager.update_disease_combobox(self.disease_manager.disease_map)
        elif tab_text == "گزارشات" and hasattr(self, 'report_manager'):
            self.report_manager.generate_report()
        elif tab_text == "برنامه‌ریزی تولید" and hasattr(self, 'planner_manager'):
            self.planner_manager.load_drugs()
        elif tab_text == "تنظیمات سیستم" and hasattr(self, 'settings_manager'):
            self.settings_manager.load_settings()

class BackupTabManager:
    def __init__(self, parent, conn):
        self.conn = conn
        label = ttk.Label(parent, text="در این بخش می‌توانید از دیتابیس برنامه پشتیبان تهیه کنید یا آن را بازیابی نمایید.", font=("Arial", 12))
        label.pack(pady=20)

        button_frame = ttk.Frame(parent)
        button_frame.pack(pady=40)

        backup_btn = ttk.Button(button_frame, text="پشتیبان‌گیری از دیتابیس", command=self.backup_database)
        backup_btn.pack(side="right", padx=10)

        restore_btn = ttk.Button(button_frame, text="بازیابی دیتابیس", command=self.restore_database)
        restore_btn.pack(side="right", padx=10)

    def backup_database(self):
        try:
            db_path = 'herbal_factory2.db'
            if not os.path.exists(db_path):
                messagebox.showerror("خطا", "فایل دیتابیس پیدا نشد!")
                return
            file_path = filedialog.asksaveasfilename(
                defaultextension=".db",
                filetypes=[("SQLite Database", "*.db"), ("All Files", "*.*")],
                title="ذخیره پشتیبان دیتابیس"
            )
            if file_path:
                shutil.copyfile(db_path, file_path)
                messagebox.showinfo("موفق", "پشتیبان دیتابیس با موفقیت ذخیره شد.")
        except Exception as e:
            messagebox.showerror("خطا", f"خطا در پشتیبان‌گیری: {str(e)}")

    def restore_database(self):
        try:
            db_path = 'herbal_factory2.db'
            file_path = filedialog.askopenfilename(
                filetypes=[("SQLite Database", "*.db"), ("All Files", "*.*")],
                title="انتخاب فایل پشتیبان برای بازیابی"
            )
            if file_path:
                if not messagebox.askyesno("تایید بازیابی", "آیا مطمئن هستید که می‌خواهید دیتابیس فعلی را با این فایل جایگزین کنید؟ این کار غیرقابل بازگشت است!"):
                    return
                shutil.copyfile(file_path, db_path)
                messagebox.showinfo("موفق", "بازیابی دیتابیس با موفقیت انجام شد.\nبرنامه را مجدداً راه‌اندازی کنید.")
        except Exception as e:
            messagebox.showerror("خطا", f"خطا در بازیابی: {str(e)}")


class HerbTabManager:
    def __init__(self, parent, conn):
        self.parent = parent
        self.conn = conn
        self.cursor = conn.cursor()
        self.selected_herb_id = None

        self.setup_herb_tab_content()

    def create_persian_date_picker(self, parent):
        """Create a Persian date picker with year, month, and day dropdowns"""
        date_frame = ttk.Frame(parent)
        date_frame.pack(fill="x", expand=True)

        # Get today's date in Persian calendar
        today = jdatetime.date.today()

        # Create variables for year, month, and day
        year_var = tk.StringVar(value=str(today.year))
        month_var = tk.StringVar(value=str(today.month))
        day_var = tk.StringVar(value=str(today.day))

        # Variable to store the display text for the month combobox
        month_display_var = tk.StringVar()

        # Create comboboxes for year, month, and day
        # Years (from 1390 to 1410)
        years = [str(y) for y in range(1400, 1500)]
        year_combo = ttk.Combobox(date_frame, textvariable=year_var, values=years, width=6, state="readonly")
        year_combo.pack(side="right", padx=2)

        # Month names in Persian
        month_names = {
            "1": "فروردین", "2": "اردیبهشت", "3": "خرداد",
            "4": "تیر", "5": "مرداد", "6": "شهریور",
            "7": "مهر", "8": "آبان", "9": "آذر",
            "10": "دی", "11": "بهمن", "12": "اسفند"
        }

        # Create a list of month names for display
        month_display = [f"{k} - {v}" for k, v in month_names.items()]
        month_combo = ttk.Combobox(date_frame, textvariable=month_var, values=month_display, width=15, state="readonly")

        # Function to handle month selection
        def on_month_select(event):
            selected = month_combo.get()
            # Extract the month number from the selection (e.g., "1 - فروردین" -> "1")
            month_num = selected.split(" - ")[0]
            month_var.set(month_num)

        # Bind the function to the combobox selection event
        month_combo.bind("<<ComboboxSelected>>", on_month_select)
        month_combo.pack(side="right", padx=2)

        # Days (1 to 31)
        days = [str(d) for d in range(1, 32)]
        day_combo = ttk.Combobox(date_frame, textvariable=day_var, values=days, width=6, state="readonly")
        day_combo.pack(side="right", padx=2)

        # Function to update days based on selected month and year
        def update_days(*args):
            try:
                year = int(year_var.get())
                month = int(month_var.get())

                # Get the number of days in the selected month
                if month <= 6:
                    max_days = 31
                elif month <= 11:
                    max_days = 30
                else:  # month == 12
                    # Check if it's a leap year
                    if jdatetime.date(year, 1, 1).isleap():
                        max_days = 30
                    else:
                        max_days = 29

                # Update days combobox
                days = [str(d) for d in range(1, max_days + 1)]
                day_combo['values'] = days

                # Adjust selected day if it's out of range
                current_day = int(day_var.get()) if day_var.get() else 1
                if current_day > max_days:
                    day_var.set(str(max_days))
            except (ValueError, TypeError):
                pass

        # Bind the update function to year and month changes
        year_var.trace_add("write", update_days)
        month_var.trace_add("write", update_days)

        # Call update_days initially to set the correct number of days
        update_days()

        # Create a dictionary to store the date components
        date_picker = {
            'year': year_var,
            'month': month_var,
            'day': day_var,
            'get_date': lambda: f"{year_var.get()}-{month_var.get().zfill(2)}-{day_var.get().zfill(2)}",
            'set_date': lambda date_str: self.set_date_picker_value(date_str, year_var, month_var, day_var)
        }

        return date_picker

    def set_date_picker_value(self, date_str, year_var, month_var, day_var):
        """Set the date picker value from a date string (YYYY-MM-DD)"""
        if not date_str:
            # Set to today's date if empty
            today = jdatetime.date.today()
            year_var.set(str(today.year))
            month_var.set(str(today.month))
            day_var.set(str(today.day))
            return

        try:
            # Parse the Gregorian date string
            parts = date_str.split('-')
            if len(parts) == 3:
                g_year, g_month, g_day = map(int, parts)

                # Convert Gregorian date to Persian date
                persian_date = jdatetime.date.fromgregorian(year=g_year, month=g_month, day=g_day)

                # Set the Persian date components
                year_var.set(str(persian_date.year))
                month_var.set(str(persian_date.month))  # No leading zeros
                day_var.set(str(persian_date.day))      # No leading zeros
            else:
                raise ValueError("Invalid date format")
        except Exception as e:
            print(f"Error setting date: {e}")
            # Set to today's date if there's an error
            today = jdatetime.date.today()
            year_var.set(str(today.year))
            month_var.set(str(today.month))
            day_var.set(str(today.day))

    def setup_herb_tab_content(self):
        # Form frame for adding herbs
        self.herb_frame = ttk.LabelFrame(self.parent, text="ثبت گیاه جدید",labelanchor="ne")
        self.herb_frame.pack(side="right",pady=10, padx=10, fill="both", expand=True)

        labels = ["کد گیاه", "نام گیاه", "قیمت واحد (تومان)", "محل قفسه", "وزن (گرم)", "تاریخ خرید", "توضیحات"]
        self.herb_entries = {}

        for i, label_text in enumerate(labels):
            label_widget = ttk.Label(self.herb_frame, text=label_text, anchor=tk.E)
            label_widget.grid(row=i, column=1, padx=5, pady=5, sticky=tk.E)

            if label_text == "تاریخ خرید":
                # Create a frame for the date picker
                date_frame = ttk.Frame(self.herb_frame)
                date_frame.grid(row=i, column=0, padx=5, pady=5, sticky=tk.E)

                # Create the date picker components
                self.herb_entries[label_text] = self.create_persian_date_picker(date_frame)
            else:
                entry = ttk.Entry(self.herb_frame, width=40, justify=tk.RIGHT)
                entry.grid(row=i, column=0, padx=5, pady=5, sticky=tk.E)
                self.herb_entries[label_text.split(":")[0]] = entry

        # Adjust grid weights for resizing
        self.herb_frame.columnconfigure(0, weight=1)
        self.herb_frame.columnconfigure(1, weight=0)

        # Button frame for herb management
        self.herb_button_frame = ttk.Frame(self.herb_frame,)
        self.herb_button_frame.grid(row=len(labels), column=0, columnspan=2, pady=10, sticky=tk.E)

        # Management buttons
        self.add_herb_btn = ttk.Button(self.herb_button_frame, text="ذخیره گیاه", command=self.add_herb)
        self.add_herb_btn.grid(row=0, column=4, padx=5)

        self.edit_herb_btn = ttk.Button(self.herb_button_frame, text="ویرایش گیاه", command=self.edit_herb)
        self.edit_herb_btn.grid(row=0, column=3, padx=5)

        self.delete_herb_btn = ttk.Button(self.herb_button_frame, text="حذف گیاه", command=self.delete_herb)
        self.delete_herb_btn.grid(row=0, column=2, padx=5)

        self.clear_herb_form_btn = ttk.Button(self.herb_button_frame, text="پاک کردن فرم", command=self.clear_herb_form)
        self.clear_herb_form_btn.grid(row=0, column=1, padx=5)

        self.herb_frame2 = ttk.LabelFrame(self.parent, text=" گیاهان موجود ",labelanchor="ne")
        self.herb_frame2.pack(pady=10, padx=10, fill="both", expand=True)
       
        # Search frame
        self.search_frame = ttk.Frame(self.herb_frame2)
        self.search_frame.pack(pady=5, padx=10, fill="x")

        ttk.Label(self.search_frame, text="جستجو").pack(side=tk.RIGHT, padx=5)
        self.search_entry = ttk.Entry(self.search_frame, justify=tk.RIGHT)
        self.search_entry.pack(side=tk.RIGHT, padx=5, fill=tk.X, expand=True)
        self.search_entry.bind("<KeyRelease>", self.filter_herbs)

        # Treeview for herbs display
        columns_rtl = ("description", "date", "total_price", "weight", "shelf", "unit_price", "name", "id")
        self.herb_tree = ttk.Treeview(self.herb_frame2, columns=columns_rtl, show="headings")
    
        # Configure column headings
        self.herb_tree.heading("description", text="توضیحات")
        self.herb_tree.heading("date", text="تاریخ خرید")
        self.herb_tree.heading("total_price", text="قیمت محصول")
        self.herb_tree.heading("weight", text="وزن (گرم)")
        self.herb_tree.heading("shelf", text="محل قفسه")
        self.herb_tree.heading("unit_price", text="قیمت واحد")
        self.herb_tree.heading("name", text="نام")
        self.herb_tree.heading("id", text="کد")

        # Configure column widths and alignment
        self.herb_tree.column("description", width=150, anchor=tk.CENTER)
        self.herb_tree.column("date", width=80, anchor=tk.CENTER)
        self.herb_tree.column("total_price", width=100, anchor=tk.CENTER)
        self.herb_tree.column("weight", width=80, anchor=tk.CENTER)
        self.herb_tree.column("shelf", width=80, anchor=tk.CENTER)
        self.herb_tree.column("unit_price", width=100, anchor=tk.CENTER)
        self.herb_tree.column("name", width=120, anchor=tk.CENTER)
        self.herb_tree.column("id", width=50, anchor=tk.CENTER)

        # Configure column weights for resizing
        #self.herb_frame2.columnconfigure(0, weight=1)
        #self.herb_frame2.columnconfigure(1, weight=0)
        # Add scrollbar
        herb_scrollbar = ttk.Scrollbar(self.herb_frame2, orient="vertical", command=self.herb_tree.yview)
        self.herb_tree.configure(yscrollcommand=herb_scrollbar.set)

        # Pack treeview and scrollbar
        herb_scrollbar.pack(side=tk.LEFT, fill="y")
        self.herb_tree.pack(side=tk.RIGHT, fill="both", expand=True, padx=10, pady=(0, 10))

        # Bind selection event
        self.herb_tree.bind("<<TreeviewSelect>>", self.on_herb_select)

        # Load initial data
        self.load_herbs()

        # Apply alternating row colors
        self.herb_tree.tag_configure('oddrow', background='#f3f4f6')
        self.herb_tree.tag_configure('evenrow', background='#ffffff')

    def add_herb(self):
        try:
            # Get the date from the date picker in Persian format (YYYY-MM-DD)
            persian_date = self.herb_entries["تاریخ خرید"]["get_date"]()

            # Convert Persian date to Gregorian for storage
            gregorian_date = ""
            if persian_date:
                try:
                    # Parse Persian date
                    p_year, p_month, p_day = map(int, persian_date.split('-'))

                    # Convert to Gregorian
                    g_date = jdatetime.date(p_year, p_month, p_day).togregorian()
                    gregorian_date = g_date.strftime("%Y-%m-%d")
                except Exception as e:
                    print(f"Error converting date: {e}")
                    gregorian_date = persian_date  # Fallback to original if conversion fails

            data = {
                "id": self.herb_entries["کد گیاه"].get().strip(),
                "name": self.herb_entries["نام گیاه"].get().strip(),
                "unit_price": float(self.herb_entries["قیمت واحد (تومان)"].get()),
                "shelf_location": self.herb_entries["محل قفسه"].get(),
                "current_weight": float(self.herb_entries["وزن (گرم)"].get()),
                "purchase_date": gregorian_date,  # Store the Gregorian date
                "description": self.herb_entries["توضیحات"].get()
            }

            # Validate data
            if not data["id"]:
                messagebox.showwarning("هشدار", "کد گیاه نمی‌تواند خالی باشد")
                return

            if not data["name"]:
                messagebox.showwarning("هشدار", "نام گیاه نمی‌تواند خالی باشد")
                return

            self.cursor.execute('''
                INSERT INTO Herbs (id, name, unit_price, shelf_location, current_weight, purchase_date, description)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', tuple(data.values()))

            self.conn.commit()
            self.load_herbs()
            messagebox.showinfo("موفق", "گیاه با موفقیت ثبت شد!")
            self.clear_herb_form()

        except sqlite3.IntegrityError:
            messagebox.showerror("خطا", f"کد گیاه '{data['id']}' قبلا ثبت شده است.")
        except ValueError:
            messagebox.showerror("خطا", "لطفاً مقادیر عددی را به درستی وارد کنید.")
        except Exception as e:
            messagebox.showerror("خطا", f"خطا در ثبت گیاه: {str(e)}")

    def edit_herb(self):
        if not self.selected_herb_id:
            messagebox.showwarning("هشدار", "لطفا ابتدا یک گیاه را از جدول انتخاب کنید.")
            return

        try:
            # Get the date from the date picker in Persian format (YYYY-MM-DD)
            persian_date = self.herb_entries["تاریخ خرید"]["get_date"]()

            # Convert Persian date to Gregorian for storage
            gregorian_date = ""
            if persian_date:
                try:
                    # Parse Persian date
                    p_year, p_month, p_day = map(int, persian_date.split('-'))

                    # Convert to Gregorian
                    g_date = jdatetime.date(p_year, p_month, p_day).togregorian()
                    gregorian_date = g_date.strftime("%Y-%m-%d")
                except Exception as e:
                    print(f"Error converting date: {e}")
                    gregorian_date = persian_date  # Fallback to original if conversion fails

            # Get data from entries (excluding ID, as it shouldn't be changed)
            updated_data = {
                "name": self.herb_entries["نام گیاه"].get().strip(),
                "unit_price": float(self.herb_entries["قیمت واحد (تومان)"].get()),
                "shelf_location": self.herb_entries["محل قفسه"].get(),
                "current_weight": float(self.herb_entries["وزن (گرم)"].get()),
                "purchase_date": gregorian_date,  # Store the Gregorian date
                "description": self.herb_entries["توضیحات"].get()
            }

            # Validate data
            if not updated_data["name"]:
                messagebox.showwarning("هشدار", "نام گیاه نمی‌تواند خالی باشد")
                return

            self.cursor.execute('''
                UPDATE Herbs
                SET name = ?, unit_price = ?, shelf_location = ?, current_weight = ?, purchase_date = ?, description = ?
                WHERE id = ?
            ''', (
                updated_data["name"], updated_data["unit_price"], updated_data["shelf_location"],
                updated_data["current_weight"], updated_data["purchase_date"], updated_data["description"],
                self.selected_herb_id
            ))

            self.conn.commit()
            self.load_herbs()
            messagebox.showinfo("موفق", "اطلاعات گیاه با موفقیت ویرایش شد!")
            self.clear_herb_form()

        except ValueError:
            messagebox.showerror("خطا", "لطفاً مقادیر عددی را به درستی وارد کنید.")
        except Exception as e:
            messagebox.showerror("خطا", f"خطا در ویرایش گیاه: {str(e)}")

    def delete_herb(self):
        if not self.selected_herb_id:
            messagebox.showwarning("هشدار", "لطفا ابتدا یک گیاه را از جدول انتخاب کنید.")
            return

        confirm = messagebox.askyesno("تایید حذف", f"آیا از حذف گیاه با کد '{self.selected_herb_id}' مطمئن هستید؟")
        if confirm:
            try:
                # Check if herb is used in any drug composition
                usage_check = self.cursor.execute(
                    "SELECT COUNT(*) FROM DrugCompositions WHERE herb_id = ?",
                    (self.selected_herb_id,)
                ).fetchone()

                if usage_check and usage_check[0] > 0:
                    messagebox.showerror(
                        "خطا",
                        f"امکان حذف گیاه '{self.selected_herb_id}' وجود ندارد زیرا در ترکیبات دارویی استفاده شده است."
                    )
                    return

                self.cursor.execute("DELETE FROM Herbs WHERE id = ?", (self.selected_herb_id,))
                self.conn.commit()
                self.load_herbs()
                messagebox.showinfo("موفق", "گیاه با موفقیت حذف شد!")
                self.clear_herb_form()

            except Exception as e:
                messagebox.showerror("خطا", f"خطا در حذف گیاه: {str(e)}")

    def on_herb_select(self, event):
        try:
            selected_item = self.herb_tree.selection()[0]
            item_values = self.herb_tree.item(selected_item, 'values')

            # Store the ID
            self.selected_herb_id = item_values[7]

            # Fetch raw data from DB for accurate editing (especially numbers)
            raw_data = self.cursor.execute(
                "SELECT unit_price, current_weight, purchase_date FROM Herbs WHERE id = ?",
                (self.selected_herb_id,)
            ).fetchone()

            if not raw_data:
                return

            # Load data into entries
            self.herb_entries["کد گیاه"].delete(0, tk.END)
            self.herb_entries["کد گیاه"].insert(0, item_values[7])

            self.herb_entries["نام گیاه"].delete(0, tk.END)
            self.herb_entries["نام گیاه"].insert(0, item_values[6])

            self.herb_entries["قیمت واحد (تومان)"].delete(0, tk.END)
            self.herb_entries["قیمت واحد (تومان)"].insert(0, raw_data[0] if raw_data[0] is not None else "")

            self.herb_entries["محل قفسه"].delete(0, tk.END)
            self.herb_entries["محل قفسه"].insert(0, item_values[4])

            self.herb_entries["وزن (گرم)"].delete(0, tk.END)
            self.herb_entries["وزن (گرم)"].insert(0, raw_data[1] if raw_data[1] is not None else "")

            # Set the date picker value
            purchase_date = raw_data[2] if raw_data[2] else ""
            self.herb_entries["تاریخ خرید"]["set_date"](purchase_date)

            self.herb_entries["توضیحات"].delete(0, tk.END)
            self.herb_entries["توضیحات"].insert(0, item_values[0])

        except IndexError:
            # No item selected or selection cleared
            self.clear_herb_form()

    def clear_herb_form(self):
        """Clears all entry fields in the herb form and resets selection."""
        self.selected_herb_id = None

        # Clear regular entry fields
        for key, entry in self.herb_entries.items():
            if key == "تاریخ خرید":
                # Reset date picker to today's date
                today = jdatetime.date.today()
                entry["set_date"]("")  # This will set to today's date
            else:
                entry.delete(0, tk.END)

        if self.herb_tree.selection():
            self.herb_tree.selection_remove(self.herb_tree.selection()[0])

    def filter_herbs(self, event=None):
        """Filter herbs based on search input"""
        search_term = self.search_entry.get().lower()
        self.load_herbs(search_term)

    def load_herbs(self, search_term=None):
        # Clear existing data
        for row in self.herb_tree.get_children():
            self.herb_tree.delete(row)

        # Build query
        query = """
            SELECT id, name, unit_price, shelf_location, current_weight, purchase_date, description
            FROM Herbs
        """
        params = []

        # Add search filter if provided
        if search_term:
            query += " WHERE id LIKE ? OR name LIKE ? OR description LIKE ?"
            params = [f"%{search_term}%", f"%{search_term}%", f"%{search_term}%"]

        query += " ORDER BY name"

        # Fetch herbs
        try:
            if params:
                herbs = self.cursor.execute(query, params).fetchall()
            else:
                herbs = self.cursor.execute(query).fetchall()

            # Insert into treeview
            for i, herb in enumerate(herbs):
                # Original DB indices: 0:id, 1:name, 2:unit_price, 3:shelf, 4:weight, 5:date, 6:description

                # Format numbers
                try:
                    unit_price_val = float(herb[2]) if herb[2] is not None else 0.0
                    weight_val_grams = float(herb[4]) if herb[4] is not None else 0.0

                    # Calculate total price based on kg (unit_price * weight_in_kg)
                    total_price_val = unit_price_val * (weight_val_grams / 1000.0)

                    # Format with comma separators
                    unit_price_str = f"{unit_price_val:,.0f}"
                    weight_str = f"{weight_val_grams:,.0f}"
                    total_price_str = f"{total_price_val:,.0f}"

                except (ValueError, TypeError):
                    unit_price_str = "N/A"
                    weight_str = "N/A"
                    total_price_str = "N/A"

                # Convert date to Persian format
                purchase_date = herb[5]  # Get the purchase date
                if purchase_date:
                    try:
                        # Parse the Gregorian date
                        y, m, d = map(int, purchase_date.split('-'))
                        # Convert to Persian date
                        shamsi_date = jdatetime.date.fromgregorian(day=d, month=m, year=y).strftime("%Y/%m/%d")
                    except Exception:
                        shamsi_date = purchase_date  # Keep original if conversion fails
                else:
                    shamsi_date = ""

                # Create values tuple in REVERSED (RTL) order for Treeview insertion
                values_rtl = (
                    herb[6],            # description
                    shamsi_date,        # date in Persian format
                    total_price_str,    # formatted total_price
                    weight_str,         # formatted weight
                    herb[3],            # shelf
                    unit_price_str,     # formatted unit_price
                    herb[1],            # name
                    herb[0]             # id
                )

                # Set row tag for alternating colors
                tag = 'evenrow' if i % 2 == 0 else 'oddrow'
                self.herb_tree.insert("", "end", values=values_rtl, tags=(tag,))

        except Exception as e:
            messagebox.showerror("خطا", f"خطا در بارگیری اطلاعات گیاهان: {str(e)}")

class DiseaseTabManager:
    def __init__(self, parent, conn):
        self.parent = parent
        self.conn = conn
        self.cursor = conn.cursor()
        self.selected_disease_id = None
        self.disease_map = {}  # Map disease names to IDs

        self.setup_disease_tab_content()

    def setup_disease_tab_content(self):
        # Form frame for disease management
        self.disease_form_frame = ttk.LabelFrame(self.parent, text="ثبت / ویرایش گروه دارویی",labelanchor="ne")
        self.disease_form_frame.pack(side="right",pady=10, padx=10, fill="both", expand=True)

        # Disease Name Label and Entry (RTL)
        ttk.Label(self.disease_form_frame, text=" گروه دارویی", anchor=tk.E).grid(row=0, column=1, padx=5, pady=5, sticky=tk.E)
        self.disease_name_entry = ttk.Entry(self.disease_form_frame,width=60, justify=tk.RIGHT)
        self.disease_name_entry.grid(row=0, column=0, padx=5, pady=5, sticky=tk.E)
        self.disease_form_frame.columnconfigure(0, weight=1)
        self.disease_form_frame.columnconfigure(1, weight=0)

        # Disease Buttons Frame
        self.disease_button_frame = ttk.Frame(self.disease_form_frame)
        self.disease_button_frame.grid(row=1, column=0, columnspan=2, pady=10, sticky=tk.E)

        # Disease Buttons (Add, Edit, Delete, Clear)
        self.add_disease_btn = ttk.Button(self.disease_button_frame, text="ذخیره گروه دارویی", command=self.add_disease)
        self.add_disease_btn.grid(row=0, column=3, padx=5)
        self.edit_disease_btn = ttk.Button(self.disease_button_frame, text="ویرایش گروه دارویی", command=self.edit_disease)
        self.edit_disease_btn.grid(row=0, column=2, padx=5)
        self.delete_disease_btn = ttk.Button(self.disease_button_frame, text="حذف گروه دارویی", command=self.delete_disease)
        self.delete_disease_btn.grid(row=0, column=1, padx=5)
        self.clear_disease_form_btn = ttk.Button(self.disease_button_frame, text="پاک کردن فرم", command=self.clear_disease_form)
        self.clear_disease_form_btn.grid(row=0, column=0, padx=5)

        self.disease_form_frame2 = ttk.LabelFrame(self.parent, text=" گروه های دارویی ثبت شده",labelanchor="ne")
        self.disease_form_frame2.pack(side="left",pady=10, padx=10, fill="both", expand=True)
        # Search frame
        self.search_frame = ttk.Frame(self.disease_form_frame2)
        self.search_frame.pack(pady=5, padx=10, fill="x")

        ttk.Label(self.search_frame, text="جستجو").pack(side=tk.RIGHT, padx=5)
        self.search_entry = ttk.Entry(self.search_frame, justify=tk.RIGHT)
        self.search_entry.pack(side=tk.RIGHT, padx=5, fill=tk.X, expand=True)
        self.search_entry.bind("<KeyRelease>", self.filter_diseases)

        # Frame for disease list
        self.disease_list_frame = ttk.Frame(self.disease_form_frame2)
        self.disease_list_frame.pack(pady=10, padx=10, fill="both", expand=True)

        # Disease Treeview
        self.disease_tree = ttk.Treeview(self.disease_list_frame, columns=("name", "id"), show="headings")
        self.disease_tree.heading("name", text=" گروه دارویی")
        self.disease_tree.heading("id", text="کد")
        self.disease_tree.column("name", anchor=tk.E)
        self.disease_tree.column("id", width=80, anchor=tk.CENTER)

        # Add scrollbar
        scrollbar = ttk.Scrollbar(self.disease_list_frame, orient="vertical", command=self.disease_tree.yview)
        self.disease_tree.configure(yscrollcommand=scrollbar.set)
        scrollbar.pack(side="left", fill="y")
        self.disease_tree.pack(side="right", fill="both", expand=True)

        # Bind selection event
        self.disease_tree.bind("<<TreeviewSelect>>", self.on_disease_select)

        # Load initial data
        self.load_diseases()

        # Apply alternating row colors
        self.disease_tree.tag_configure('oddrow', background='#f3f4f6')
        self.disease_tree.tag_configure('evenrow', background='#ffffff')

    def add_disease(self):
        disease_name = self.disease_name_entry.get().strip()
        if not disease_name:
            messagebox.showwarning("هشدار", "لطفا نام 'گروه دارویی' را وارد کنید.")
            return

        try:
            self.cursor.execute("INSERT INTO Diseases (name) VALUES (?)", (disease_name,))
            self.conn.commit()
            messagebox.showinfo("موفق", f"گروه دارویی '{disease_name}' با موفقیت ثبت شد!")
            self.load_diseases()
            self.clear_disease_form()

        except sqlite3.IntegrityError:
            messagebox.showerror("خطا", f"گروه دارویی با نام '{disease_name}' قبلا ثبت شده است.")
        except Exception as e:
            messagebox.showerror("خطا", f"خطا در ثبت گروه دارویی: {str(e)}")

    def edit_disease(self):
        if self.selected_disease_id is None:
            messagebox.showwarning("هشدار", "لطفا ابتدا یک گروه دارویی را از جدول انتخاب کنید.")
            return

        new_name = self.disease_name_entry.get().strip()
        if not new_name:
            messagebox.showwarning("هشدار", "لطفا نام جدید گروه دارویی را وارد کنید.")
            return

        try:
            self.cursor.execute("UPDATE Diseases SET name = ? WHERE id = ?", (new_name, self.selected_disease_id))
            self.conn.commit()
            messagebox.showinfo("موفق", "نام گروه دارویی با موفقیت ویرایش شد!")
            self.load_diseases()
            self.clear_disease_form()

        except sqlite3.IntegrityError:
            messagebox.showerror("خطا", f"گروه دارویی دیگری با نام '{new_name}' وجود دارد.")
        except Exception as e:
            messagebox.showerror("خطا", f"خطا در ویرایش گروه دارویی: {str(e)}")

    def delete_disease(self):
        if self.selected_disease_id is None:
            messagebox.showwarning("هشدار", "لطفا ابتدا یک گروه دارویی را از جدول انتخاب کنید.")
            return

        confirm = messagebox.askyesno(
            "تایید حذف",
            f"آیا از حذف گروه دارویی انتخاب شده مطمئن هستید؟\n(توجه: تمام ارتباطات این بیماری با داروها نیز حذف خواهد شد)"
        )

        if confirm:
            try:
                # Check usage (optional, as ON DELETE CASCADE handles it, but good for user info)
                usage_count = self.cursor.execute(
                    "SELECT COUNT(*) FROM DrugDiseases WHERE disease_id = ?",
                    (self.selected_disease_id,)
                ).fetchone()[0]

                if usage_count > 0:
                    confirm_again = messagebox.askyesno(
                        "اخطار",
                        f"این گروه دارویی در {usage_count} دارو استفاده شده است. حذف آن باعث از بین رفتن این ارتباطات خواهد شد. آیا مطمئن هستید؟"
                    )
                    if not confirm_again:
                        return

                # Delete the disease (CASCADE will handle DrugDiseases links)
                self.cursor.execute("DELETE FROM Diseases WHERE id = ?", (self.selected_disease_id,))
                self.conn.commit()
                messagebox.showinfo("موفق", "گروه دارویی با موفقیت حذف شد!")
                self.load_diseases()
                self.clear_disease_form()

            except Exception as e:
                messagebox.showerror("خطا", f"خطا در حذف گروه دارویی: {str(e)}")

    def filter_diseases(self, event=None):
        """Filter diseases based on search input"""
        search_term = self.search_entry.get().lower()
        self.load_diseases(search_term)

    def load_diseases(self, search_term=None):
        # Clear existing treeview
        for row in self.disease_tree.get_children():
            self.disease_tree.delete(row)

        # Clear map and build query
        self.disease_map.clear()
        query = "SELECT id, name FROM Diseases"
        params = []

        # Add search filter if provided
        if search_term:
            query += " WHERE name LIKE ?"
            params = [f"%{search_term}%"]

        query += " ORDER BY name"

        # Execute query
        if params:
            diseases = self.cursor.execute(query, params).fetchall()
        else:
            diseases = self.cursor.execute(query).fetchall()

        # Process results
        for i, (disease_id, disease_name) in enumerate(diseases):
            # Insert into treeview
            tag = 'evenrow' if i % 2 == 0 else 'oddrow'
            self.disease_tree.insert("", "end", values=(disease_name, disease_id), tags=(tag,))

            # Update map
            self.disease_map[disease_name] = disease_id

        return self.disease_map

    def on_disease_select(self, event):
        try:
            selected_item = self.disease_tree.selection()[0]
            item_values = self.disease_tree.item(selected_item, 'values')
            # Values are (name, id)
            self.selected_disease_id = int(item_values[1])
            self.disease_name_entry.delete(0, tk.END)
            self.disease_name_entry.insert(0, item_values[0])

        except (IndexError, ValueError):
            # Selection cleared or invalid data
            self.clear_disease_form()

    def clear_disease_form(self):
        self.selected_disease_id = None
        self.disease_name_entry.delete(0, tk.END)
        if self.disease_tree.selection():
            self.disease_tree.selection_remove(self.disease_tree.selection()[0])

class DrugTabManager:
    def __init__(self, parent, conn, disease_manager):
        self.parent = parent
        self.conn = conn
        self.cursor = conn.cursor()
        self.disease_manager = disease_manager
        self.compositions = []  # List to store drug compositions

        self.setup_drug_tab_content()
    
    
    def setup_drug_tab_content(self):
        # Drug production form
        
        self.drug_frame = ttk.LabelFrame(self.parent, text="تولید دارو جدید",labelanchor="ne")
        self.drug_frame.pack(side="right",pady=10, padx=10, fill="both", expand=True)

        # Add
        #self.t_frame = ttk.Frame(self.drug_frame)
        #self.t_frame.pack(fill="both", expand=True, pady=10)

        # Left side -
        l_frame = ttk.Frame(self.drug_frame)
        l_frame.pack(side=tk.RIGHT, fill="both",expand=True, padx=20)

        # Basic drug info
        ttk.Label(l_frame, text="کد دارو", anchor=tk.E).grid(row=0, column=1, sticky=tk.E, padx=5, pady=5)
        self.drug_id_entry = ttk.Entry(l_frame,width=20, justify=tk.RIGHT)
        self.drug_id_entry.grid(row=0, column=0, sticky=tk.E, padx=5, pady=2)

        ttk.Label(l_frame, text="نام دارو", anchor=tk.E).grid(row=1, column=1, sticky=tk.E, padx=5, pady=5)
        self.drug_name_entry = ttk.Entry(l_frame,width=20, justify=tk.RIGHT)
        self.drug_name_entry.grid(row=1, column=0, sticky=tk.E, padx=5, pady=2)

        # Disease Combobox
        ttk.Label(l_frame, text=" گروه دارویی", anchor=tk.E).grid(row=2, column=1, sticky=tk.E, padx=5, pady=5)
        self.disease_combobox = ttk.Combobox(l_frame,width=17, state="readonly", justify=tk.RIGHT)
        self.disease_combobox.grid(row=2, column=0, sticky=tk.E, padx=5, pady=2)

        # Cost Calculation Fields
        ttk.Label(l_frame, text="قیمت قرض زنی (تومان)", anchor=tk.E).grid(row=3, column=1, sticky=tk.E, padx=5, pady=5)
        self.loan_interest_entry = ttk.Entry(l_frame,width=20, justify=tk.RIGHT)
        self.loan_interest_entry.grid(row=3, column=0, sticky=tk.E, padx=5, pady=2)

        ttk.Label(l_frame, text="هزینه ظرف و برچسب (تومان)", anchor=tk.E).grid(row=4, column=1, sticky=tk.E, padx=5, pady=5)
        self.container_label_entry = ttk.Entry(l_frame,width=20, justify=tk.RIGHT)
        self.container_label_entry.grid(row=4, column=0, sticky=tk.E, padx=5, pady=2)

        ttk.Label(l_frame, text="هزینه سربار کارگاه (تومان)", anchor=tk.E).grid(row=5, column=1, sticky=tk.E, padx=5, pady=5)
        self.workshop_overhead_entry = ttk.Entry(l_frame,width=20, justify=tk.RIGHT)
        self.workshop_overhead_entry.grid(row=5, column=0, sticky=tk.E, padx=5, pady=2)

        # Configure column weights for resizing
        l_frame.columnconfigure(0, weight=1)
        l_frame.columnconfigure(1, weight=0)


        ttk.Label(l_frame, text="درصد قیمت نمایندگی", anchor=tk.E).grid(row=6, column=1, sticky=tk.E, padx=5, pady=2)
        self.rep_percent_entry = ttk.Entry(l_frame, width=20, justify=tk.RIGHT)
        self.rep_percent_entry.insert(0, "30")  # مقدار پیش‌فرض
        self.rep_percent_entry.grid(row=6, column=0, sticky=tk.E, padx=5, pady=5)

        ttk.Label(l_frame, text="درصد قیمت عمده", anchor=tk.E).grid(row=7, column=1, sticky=tk.E, padx=5, pady=2)
        self.wholesale_percent_entry = ttk.Entry(l_frame, width=20, justify=tk.RIGHT)
        self.wholesale_percent_entry.insert(0, "20")  # مقدار پیش‌فرض
        self.wholesale_percent_entry.grid(row=7, column=0, sticky=tk.E, padx=5, pady=5)

        ttk.Label(l_frame, text="درصد قیمت خرده", anchor=tk.E).grid(row=8, column=1, sticky=tk.E, padx=5, pady=2)
        self.retail_percent_entry = ttk.Entry(l_frame, width=20, justify=tk.RIGHT)
        self.retail_percent_entry.insert(0, "40")  # مقدار پیش‌فرض
        self.retail_percent_entry.grid(row=8, column=0, sticky=tk.E, padx=5, pady=2)


        # در متد setup_drug_tab_content:
        ttk.Label(l_frame, text="نوع تولید").grid(row=9, column=1, sticky=tk.E, padx=5, pady=2)
        self.drug_type_combobox = ttk.Combobox(l_frame, width=17, values=["پودری", "گرانول", "فله‌ای", "بسته‌بندی"], state="readonly", justify=tk.RIGHT)
        self.drug_type_combobox.grid(row=9, column=0, sticky=tk.E, padx=5, pady=2)
        self.drug_type_combobox.bind("<<ComboboxSelected>>", self.update_drug_type_fields)

        # فیلدهای جدید:
        ttk.Label(l_frame, text="درصد ضایعات").grid(row=10, column=1, sticky=tk.E, padx=5, pady=5)
        self.waste_percentage_entry = ttk.Entry(l_frame)
        self.waste_percentage_entry.grid(row=10, column=0, sticky=tk.E, padx=5, pady=2)
        self.waste_percentage_entry.insert(0, "0")  # مقدار پیش‌فرض

        # این فیلد فقط برای نوع بسته‌بندی نمایش داده شود
        self.units_per_package_label = ttk.Label(l_frame, text="تعداد در هر بسته")
        self.units_per_package_entry = ttk.Entry(l_frame)
        self.units_per_package_entry.insert(0, "1")

        # این فیلد فقط برای نوع گرانول نمایش داده شود
        self.honey_weight_label = ttk.Label(l_frame, text="وزن عسل (گرم)")
        self.honey_weight_entry = ttk.Entry(l_frame)

        # Load default honey price from settings
        try:
            cursor = self.conn.cursor()
            cursor.execute("SELECT value FROM AppSettings WHERE key = 'honey_price_per_gram'")
            result = cursor.fetchone()
            if result:
                self.honey_price_per_gram = float(result[0])
            else:
                self.honey_price_per_gram = 100  # Default fallback
        except Exception as e:
            print(f"Error loading honey price: {e}")
            self.honey_price_per_gram = 100  # Default fallback

        # Add honey price info label
        self.honey_price_info_label = ttk.Label(
            l_frame,
            text=f"قیمت عسل {self.honey_price_per_gram} ت",
            font=("Tahoma", 8),
            foreground="gray"
        )

        # Load default values from settings
        self.load_default_values()


        # Compositions frame
        self.compositions_frame = ttk.LabelFrame(self.parent, text="ترکیبات دارو",labelanchor="ne")
        self.compositions_frame.pack(side=tk.RIGHT, fill="both", expand=True, padx=10)

        # Compositions list
        self.compositions_tree = ttk.Treeview(
            self.compositions_frame,
            columns=("cost", "weight", "herb_name", "herb_id"),
            show="headings",
            height=5
        )
        # Corrected heading order to match columns
        self.compositions_tree.heading("herb_id", text="کد گیاه")
        self.compositions_tree.heading("herb_name", text="نام گیاه")
        self.compositions_tree.heading("weight", text="وزن (گرم)")
        self.compositions_tree.heading("cost", text="هزینه (تومان)")

        # Corrected column configuration order to match columns
        self.compositions_tree.column("herb_id", width=50, anchor=tk.CENTER)
        self.compositions_tree.column("herb_name", width=120, anchor=tk.CENTER)
        self.compositions_tree.column("weight", width=100, anchor=tk.CENTER)
        self.compositions_tree.column("cost", width=100, anchor=tk.CENTER)

        # Scrollbar for compositions
        comp_scrollbar = ttk.Scrollbar(
            self.compositions_frame,
            orient="vertical",
            command=self.compositions_tree.yview
        )

        self.compositions_tree.configure(yscrollcommand=comp_scrollbar.set)

        # Pack compositions tree and scrollbar
        comp_scrollbar.pack(side=tk.LEFT, fill=tk.Y)
        self.compositions_tree.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, pady=5)

        # Buttons with better styling and icons
        btn_frame = ttk.Frame(l_frame)
        btn_frame.grid(row=13, column=0, columnspan=3, pady=10, sticky='e')

        self.add_composition_btn = ttk.Button(
            btn_frame,
            text="افزودن ترکیب",
            command=self.add_composition,
            style='Primary.TButton'
        )
        self.add_composition_btn.pack(side=tk.RIGHT, padx=5)

        self.remove_composition_btn = ttk.Button(
            btn_frame,
            text="حذف ترکیب",
            command=self.remove_composition,
            state="disabled"
        )
        self.remove_composition_btn.pack(side=tk.RIGHT, padx=5)

        self.produce_drug_btn = ttk.Button(
            btn_frame,
            text="تولید دارو",
            command=self.produce_drug,
            style='Primary.TButton'
        )
        self.produce_drug_btn.pack(side=tk.RIGHT, padx=5)


        # Compositions buttons
        #self.comp_buttons_frame = ttk.Frame(self.drug_frame)
        #self.comp_buttons_frame.pack(fill=tk.X, pady=5)

       
        # Set up the produced drugs list section
        self.compositions_tree.bind("<<TreeviewSelect>>", self.on_composition_select)
        self.setup_produced_drugs_section()

        # Apply alternating row colors
        self.compositions_tree.tag_configure('oddrow', background='#f3f4f6')
        self.compositions_tree.tag_configure('evenrow', background='#ffffff')
    
    

    def load_default_values(self):
        """Load default values from settings"""
        try:
            cursor = self.conn.cursor()

            # Load default waste percentage
            cursor.execute("SELECT value FROM AppSettings WHERE key = 'default_waste_percentage'")
            result = cursor.fetchone()
            if result:
                self.waste_percentage_entry.delete(0, tk.END)
                self.waste_percentage_entry.insert(0, result[0])

            # Load default loan interest
            cursor.execute("SELECT value FROM AppSettings WHERE key = 'default_loan_interest'")
            result = cursor.fetchone()
            if result:
                self.loan_interest_entry.delete(0, tk.END)
                self.loan_interest_entry.insert(0, result[0])

            # Load default container label cost
            cursor.execute("SELECT value FROM AppSettings WHERE key = 'default_container_label_cost'")
            result = cursor.fetchone()
            if result:
                self.container_label_entry.delete(0, tk.END)
                self.container_label_entry.insert(0, result[0])

            # Load default workshop overhead
            cursor.execute("SELECT value FROM AppSettings WHERE key = 'default_workshop_overhead'")
            result = cursor.fetchone()
            if result:
                self.workshop_overhead_entry.delete(0, tk.END)
                self.workshop_overhead_entry.insert(0, result[0])

        except Exception as e:
            print(f"Error loading default values: {e}")



    def update_drug_type_fields(self, event=None):
        drug_type = self.drug_type_combobox.get()

        # مخفی کردن همه فیلدهای خاص
        self.units_per_package_label.grid_remove()
        self.units_per_package_entry.grid_remove()
        self.honey_weight_label.grid_remove()
        self.honey_weight_entry.grid_remove()
        self.honey_price_info_label.grid_remove()

        if drug_type == "بسته‌بندی":
            self.units_per_package_label.grid(row=11, column=1, sticky=tk.E, padx=5, pady=5)
            self.units_per_package_entry.grid(row=11, column=0, sticky=tk.E, padx=5, pady=5)
        elif drug_type == "گرانول":
            self.honey_weight_label.grid(row=11, column=1, sticky=tk.E, padx=5, pady=5)
            self.honey_weight_entry.grid(row=11, column=0, sticky=tk.E, padx=5, pady=5)
            self.honey_price_info_label.grid(row=12, column=0, sticky=tk.E, padx=5)

    def calculate_costs(self):
        try:
            # Attempt to load the current honey price from the database
            try:
                self.cursor.execute("SELECT value FROM AppSettings WHERE key = 'honey_price_per_gram'")
                result = self.cursor.fetchone()
                if result:
                    self.honey_price_per_gram = float(result[0])
                # If not found, honey_price_per_gram will use the previously loaded or default value
            except Exception as e:
                print(f"Error refreshing honey price: {e}")
                # Continue with existing value

            drug_type = self.drug_type_combobox.get()
            waste_percentage = float(self.waste_percentage_entry.get())

            # محاسبه هزینه مواد اولیه با احتساب ضایعات
            raw_materials_cost = sum(comp[3] for comp in self.compositions) * (1 + waste_percentage/100)

            # هزینه‌های خاص بر اساس نوع تولید
            if drug_type == "پودری":
                loan_interest = float(self.loan_interest_entry.get() or 0)
                container_label = float(self.container_label_entry.get() or 0)
                honey_weight = 0
                honey_cost = 0
                units_per_package = 1
            elif drug_type == "گرانول":
                loan_interest = float(self.loan_interest_entry.get() or 0)
                container_label = float(self.container_label_entry.get() or 0)
                honey_weight = float(self.honey_weight_entry.get() or 0)
                honey_cost = honey_weight * self.honey_price_per_gram  # قیمت هر گرم عسل
                raw_materials_cost += honey_cost
                units_per_package = 1
            elif drug_type == "فله‌ای":
                loan_interest = 0
                container_label = 0
                honey_weight = 0
                honey_cost = 0
                units_per_package = 1
            elif drug_type == "بسته‌بندی":
                loan_interest = float(self.loan_interest_entry.get() or 0)
                container_label = float(self.container_label_entry.get() or 0)
                units_per_package = int(self.units_per_package_entry.get() or 1)
                honey_weight = 0
                honey_cost = 0
            else:
                # Fallback for unexpected drug type
                loan_interest = float(self.loan_interest_entry.get() or 0)
                container_label = float(self.container_label_entry.get() or 0)
                honey_weight = 0
                honey_cost = 0
                units_per_package = 1

            workshop_overhead = float(self.workshop_overhead_entry.get() or 0)

            total_workshop_cost = (raw_materials_cost + loan_interest +
                                container_label + workshop_overhead)

            # محاسبه قیمت‌های فروش با احتساب درصدها
            rep_percent = float(self.rep_percent_entry.get())
            wholesale_percent = float(self.wholesale_percent_entry.get())
            retail_percent = float(self.retail_percent_entry.get())

            representative_price = total_workshop_cost * (1 + rep_percent/100)
            wholesale_price = total_workshop_cost * (1 + wholesale_percent/100)
            retail_price = total_workshop_cost * (1 + retail_percent/100)

            return {
                'raw_materials_cost': raw_materials_cost,
                'loan_interest': loan_interest,
                'container_label': container_label,
                'workshop_overhead': workshop_overhead,
                'total_cost': total_workshop_cost,
                'representative_price': representative_price,
                'wholesale_price': wholesale_price,
                'retail_price': retail_price,
                'drug_type': drug_type,
                'waste_percentage': waste_percentage,
                'units_per_package': units_per_package if drug_type == "بسته‌بندی" else None,
                'honey_weight': honey_weight if drug_type == "گرانول" else None,
                'honey_cost': honey_cost
            }

        except ValueError as e:
            messagebox.showerror("خطا", f"مقادیر وارد شده نامعتبر هستند: {str(e)}")
            return None



    def setup_produced_drugs_section(self):
        # Produced drugs list frame
        self.produced_drug_list_frame = ttk.LabelFrame(self.parent, text="لیست داروهای تولید شده",labelanchor="ne")
        self.produced_drug_list_frame.pack(pady=10, padx=10, fill="both", expand=True)

        # Search frame
        self.drug_search_frame = ttk.Frame(self.produced_drug_list_frame)
        self.drug_search_frame.pack(fill="x", pady=5)

        ttk.Label(self.drug_search_frame, text="جستجو").pack(side=tk.RIGHT, padx=5)
        self.drug_search_entry = ttk.Entry(self.drug_search_frame, justify=tk.RIGHT)
        self.drug_search_entry.pack(side=tk.RIGHT, padx=5, fill=tk.X, expand=True)
        self.drug_search_entry.bind("<KeyRelease>", self.filter_drugs)

        # Buttons frame
        self.drug_buttons_frame = ttk.Frame(self.produced_drug_list_frame)
        self.drug_buttons_frame.pack(fill="x", pady=5)

        self.edit_drug_btn = ttk.Button(
            self.drug_buttons_frame,
            text="ویرایش دارو",
            command=self.edit_selected_drug,
            state='disabled'
        )
        self.edit_drug_btn.pack(side="right", padx=5)

        self.delete_drug_btn = ttk.Button(
            self.drug_buttons_frame,
            text="حذف دارو",
            command=self.delete_selected_drug,
            state='disabled'
        )
        self.delete_drug_btn.pack(side="right", padx=5)

        self.view_details_btn = ttk.Button(
            self.drug_buttons_frame,
            text="مشاهده جزئیات",
            command=self.show_drug_compositions,
            state='disabled'
        )
        self.view_details_btn.pack(side="right", padx=5)

        # Treeview and scrollbar frame
        self.tree_frame = ttk.Frame(self.produced_drug_list_frame)
        self.tree_frame.pack(fill="both", expand=True)



        # Define columns (RTL order)
        produced_columns = ("total_cost" ,"disease_name", "production_date", "drug_name","drug_id")
        self.produced_drug_tree = ttk.Treeview(
            self.tree_frame,
            columns=produced_columns,
            show="headings",
        )

        # Set up columns
        self.produced_drug_tree.heading("total_cost", text="هزینه تولید (تومان/کیلوگرم)")
        self.produced_drug_tree.heading("disease_name", text="گروه  دارویی")
        self.produced_drug_tree.heading("production_date", text="تاریخ تولید")
        self.produced_drug_tree.heading("drug_name", text="نام دارو")
        self.produced_drug_tree.heading("drug_id", text="کد دارو")

        self.produced_drug_tree.column("total_cost", width=100, anchor=tk.CENTER)
        self.produced_drug_tree.column("disease_name", width=150, anchor=tk.CENTER)
        self.produced_drug_tree.column("production_date", width=100, anchor=tk.CENTER)
        self.produced_drug_tree.column("drug_name", width=100, anchor=tk.CENTER)
        self.produced_drug_tree.column("drug_id", width=50, anchor=tk.CENTER)


        # Scrollbar
        prod_scrollbar = ttk.Scrollbar(
            self.tree_frame,
            orient="vertical",
            command=self.produced_drug_tree.yview
        )
        self.produced_drug_tree.configure(yscrollcommand=prod_scrollbar.set)

        # Pack treeview and scrollbar
        self.produced_drug_tree.pack(side="right", fill="both", expand=True)
        prod_scrollbar.pack(side="left", fill="y")

        # Bind events
        self.produced_drug_tree.bind("<<TreeviewSelect>>", self.toggle_drug_buttons)
        self.produced_drug_tree.bind("<Double-1>", self.show_drug_compositions)

        # Apply alternating row colors
        self.produced_drug_tree.tag_configure('oddrow', background='#f3f4f6')
        self.produced_drug_tree.tag_configure('evenrow', background='#ffffff')

        # Load initial data
        self.load_produced_drugs()

    def update_disease_combobox(self, disease_map):
        """Update disease combobox with current disease data"""
        disease_names = list(disease_map.keys())
        self.disease_combobox['values'] = disease_names
        if disease_names:
            self.disease_combobox.current(0)
        else:
            self.disease_combobox.set('')

    def on_composition_select(self, event):
        """Enable/disable remove button based on selection"""
        if self.compositions_tree.selection():
            self.remove_composition_btn['state'] = 'normal'
        else:
            self.remove_composition_btn['state'] = 'disabled'

    def remove_composition(self):
        """Remove the selected composition from the list"""
        selected = self.compositions_tree.selection()
        if not selected:
            return

        item_values = self.compositions_tree.item(selected, 'values')
        herb_id = item_values[3]

        # Remove from compositions list
        for i, comp in enumerate(self.compositions):
            if comp[0] == herb_id:
                del self.compositions[i]
                break

        # Update treeview
        self.update_compositions_tree()

        # Disable remove button
        self.remove_composition_btn['state'] = 'disabled'

    def add_composition(self):
        """Open window to add a new composition"""
        self.comp_window = tk.Toplevel()
        self.comp_window.title("افزودن ترکیب")
        self.comp_window.geometry("800x600")
        self.comp_window.transient(self.parent)
        self.comp_window.grab_set()

        # Set up main frame
        main_frame = ttk.Frame(self.comp_window, padding="10")
        main_frame.pack(fill="both", expand=True)

        # Search frame
        search_frame = ttk.LabelFrame(main_frame, text="جستجوی گیاه", padding="5",labelanchor="ne")
        search_frame.pack(fill="x", pady=5)

        ttk.Label(search_frame, text="جستجو (کد یا نام گیاه)").pack(side="right", padx=5)
        self.comp_search_entry = ttk.Entry(search_frame)
        self.comp_search_entry.pack(side="right", padx=5, fill="x", expand=True)
        self.comp_search_entry.bind("<KeyRelease>", self.filter_herbs_for_composition)

        # Search button
        search_btn = ttk.Button(search_frame, text="جستجو", command=lambda: self.filter_herbs_for_composition())
        search_btn.pack(side="right", padx=5)

        # Herb selection frame
        herb_frame = ttk.LabelFrame(main_frame, text="لیست گیاهان", padding="5",labelanchor="ne")
        herb_frame.pack(fill="both", expand=True, pady=5)

        # Treeview for herbs
        #columns = ("id", "name", "current_weight", "unit_price", "shelf")
        columns = ("shelf", "unit_price", "current_weight", "name", "id")
        self.herb_selection_tree = ttk.Treeview(
            herb_frame,
            columns=columns,
            show="headings",
            height=8,
            selectmode="browse"
        )

        # Set up columns
        self.herb_selection_tree.heading("id", text="کد گیاه")
        self.herb_selection_tree.heading("name", text="نام گیاه")
        self.herb_selection_tree.heading("current_weight", text="موجودی (گرم)")
        self.herb_selection_tree.heading("unit_price", text="قیمت واحد (تومان)")
        self.herb_selection_tree.heading("shelf", text="محل قفسه")

        self.herb_selection_tree.column("id", width=100, anchor=tk.CENTER)
        self.herb_selection_tree.column("name", width=200, anchor=tk.CENTER)
        self.herb_selection_tree.column("current_weight", width=120, anchor=tk.CENTER)
        self.herb_selection_tree.column("unit_price", width=150, anchor=tk.CENTER)
        self.herb_selection_tree.column("shelf", width=100, anchor=tk.CENTER)

        # Scrollbar
        scrollbar = ttk.Scrollbar(herb_frame, orient="vertical", command=self.herb_selection_tree.yview)
        self.herb_selection_tree.configure(yscrollcommand=scrollbar.set)

        # Pack treeview and scrollbar
        scrollbar.pack(side="left", fill="y")
        self.herb_selection_tree.pack(side="right", fill="both", expand=True)

        # Weight frame for composition details
        weight_frame = ttk.LabelFrame(main_frame, text="مشخصات ترکیب", padding="5",labelanchor="ne")
        weight_frame.pack(fill="x", pady=5)

        # Weight field
        ttk.Label(weight_frame, text="وزن مورد نیاز (گرم):").pack(side="right", padx=5)
        self.herb_weight_entry = ttk.Entry(weight_frame, width=15)
        self.herb_weight_entry.pack(side="right", padx=5)

        # Action buttons
        btn_frame = ttk.Frame(weight_frame)
        btn_frame.pack(side="right", padx=10)

        add_btn = ttk.Button(btn_frame, text="افزودن ترکیب", command=self.save_composition)
        add_btn.pack(side="right", padx=5)

        cancel_btn = ttk.Button(btn_frame, text="انصراف", command=self.comp_window.destroy)
        cancel_btn.pack(side="right", padx=5)

        # Load herbs and apply alternating colors
        self.filter_herbs_for_composition()
        self.herb_selection_tree.tag_configure('oddrow', background='#f3f4f6')
        self.herb_selection_tree.tag_configure('evenrow', background='#ffffff')

    def filter_herbs_for_composition(self, event=None):
        """Filter herbs for composition selection"""
        # Clear existing items
        for item in self.herb_selection_tree.get_children():
            self.herb_selection_tree.delete(item)

        # Get search term
        search_term = self.comp_search_entry.get().strip().lower() if hasattr(self, 'comp_search_entry') else ""

        # Build query
        query = """
            SELECT id, name, current_weight, unit_price, shelf_location
            FROM Herbs
            WHERE current_weight > 0
        """
        params = []

        # Add search filter if provided
        if search_term:
            query += " AND (id LIKE ? OR name LIKE ? OR shelf_location LIKE ?)"
            params = [f"%{search_term}%", f"%{search_term}%", f"%{search_term}%"]

        query += " ORDER BY name"

        # Execute query
        try:
            if params:
                herbs = self.cursor.execute(query, params).fetchall()
            else:
                herbs = self.cursor.execute(query).fetchall()

            # Insert into treeview
            for i, herb in enumerate(herbs):
                herb_id, name, weight, price, shelf = herb

                # Format display values
                weight_display = f"{weight:,.0f}" if weight else "0"
                price_display = f"{price:,.0f}" if price else "0"
                shelf_display = shelf if shelf else "-"

                # Insert with alternating colors
                tag = 'evenrow' if i % 2 == 0 else 'oddrow'
                self.herb_selection_tree.insert("", "end", values=(
                    shelf_display,
                    price_display,
                    weight_display,
                    name,
                    herb_id
                ), tags=(tag,))

        except Exception as e:
            messagebox.showerror("خطا", f"خطا در بارگیری اطلاعات گیاهان: {str(e)}")

    def save_composition(self):
        """Save the selected herb as a composition"""
        try:
            # Get selected herb
            selected_item = self.herb_selection_tree.selection()
            if not selected_item:
                messagebox.showerror("خطا", "لطفاً یک گیاه را از لیست انتخاب کنید")
                return

            herb_id = self.herb_selection_tree.item(selected_item, 'values')[4]
            herb_name = self.herb_selection_tree.item(selected_item, 'values')[3]

            # Get weight
            try:
                weight_grams = float(self.herb_weight_entry.get())
                if weight_grams <= 0:
                    raise ValueError("وزن باید بیشتر از صفر باشد")
            except ValueError:
                messagebox.showerror("خطا", "لطفاً یک عدد مثبت برای وزن وارد کنید")
                return

            # Check inventory
            cursor = self.conn.cursor()
            cursor.execute("SELECT current_weight, unit_price FROM Herbs WHERE id = ?", (herb_id,))
            herb_data = cursor.fetchone()

            if not herb_data:
                messagebox.showerror("خطا", "کد گیاه نامعتبر است!")
                return

            current_weight, unit_price = herb_data
            if current_weight < weight_grams:
                messagebox.showerror("خطا", f"موجودی ناکافی! موجودی فعلی: {current_weight:,.0f} گرم")
                return

            # Check if herb is already in compositions
            for comp in self.compositions:
                if comp[0] == herb_id:
                    confirm = messagebox.askyesno(
                        "تایید",
                        f"گیاه {herb_name} قبلاً به ترکیبات اضافه شده است. می‌خواهید آن را جایگزین کنید؟"
                    )
                    if confirm:
                        # Remove existing composition
                        self.compositions = [c for c in self.compositions if c[0] != herb_id]
                    else:
                        return

            # Calculate cost
            weight_kg = weight_grams / 1000
            subtotal = weight_kg * unit_price

            # Add to compositions list
            self.compositions.append((herb_id, herb_name, weight_grams, subtotal))

            # Update treeview
            self.update_compositions_tree()

            # Clear weight field
            self.herb_weight_entry.delete(0, tk.END)

            # Show success message
            messagebox.showinfo(
                "موفق",
                f"ترکیب با موفقیت اضافه شد!\n{herb_name}\nوزن: {weight_grams:,.0f} گرم\nهزینه: {subtotal:,.0f} تومان"
            )

        except Exception as e:
            messagebox.showerror("خطا", f"خطا در افزودن ترکیب: {str(e)}")

    def update_compositions_tree(self):
        """Update the compositions treeview with current data"""
        # Clear existing items
        for item in self.compositions_tree.get_children():
            self.compositions_tree.delete(item)

        # Add compositions to treeview
        for i, comp in enumerate(self.compositions):
            herb_id, herb_name, weight_grams, subtotal = comp

            # Format display values
            weight_display = f"{weight_grams:,.0f}"
            subtotal_display = f"{subtotal:,.0f}"

            # Insert with alternating colors
            tag = 'evenrow' if i % 2 == 0 else 'oddrow'
            self.compositions_tree.insert("", "end", values=(
                subtotal_display,
                weight_display,
                herb_name,
                herb_id
            ), tags=(tag,))


    def produce_drug(self):
        cost_data = self.calculate_costs()
        if not cost_data:
            return
        try:
            # Get basic drug info
            drug_id_str = self.drug_id_entry.get().strip()
            drug_name = self.drug_name_entry.get().strip()
            selected_disease_name = self.disease_combobox.get()

            # Validate basic info
            if not drug_id_str:
                messagebox.showwarning("هشدار", "لطفاً کد دارو را وارد کنید")
                return

            if not drug_name:
                messagebox.showwarning("هشدار", "لطفاً نام دارو را وارد کنید")
                return

            if not selected_disease_name:
                messagebox.showwarning("هشدار", "لطفاً بیماری مرتبط با دارو را انتخاب کنید")
                return

            if not self.compositions:
                messagebox.showwarning("هشدار", "لطفاً حداقل یک ترکیب برای دارو اضافه کنید")
                return

            # Convert drug ID to integer
            try:
                drug_id = int(drug_id_str)
            except ValueError:
                messagebox.showerror("خطا", "کد دارو باید یک عدد صحیح باشد")
                return

            # Get additional costs
            try:
                loan_interest = float(self.loan_interest_entry.get() or 0)
                container_label = float(self.container_label_entry.get() or 0)
                workshop_overhead = float(self.workshop_overhead_entry.get() or 0)
            except ValueError:
                messagebox.showerror("خطا", "لطفاً مقادیر هزینه را به صورت عدد وارد کنید")
                return

            # Get disease ID
            disease_id = self.disease_manager.disease_map.get(selected_disease_name)
            if disease_id is None:
                messagebox.showerror("خطا", "خطا در دریافت اطلاعات بیماری")
                return

            # Calculate costs
            raw_materials_cost = sum(comp[3] for comp in self.compositions)
            total_workshop_cost = (
                raw_materials_cost +
                loan_interest +
                container_label +
                workshop_overhead
            )

            try:
                rep_percent = float(self.rep_percent_entry.get())
                wholesale_percent = float(self.wholesale_percent_entry.get())
                retail_percent = float(self.retail_percent_entry.get())

                if any(p < 0 for p in [rep_percent, wholesale_percent, retail_percent]):
                    raise ValueError("درصدها باید اعداد مثبت باشند")
            except ValueError:
                messagebox.showerror("خطا", "لطفاً درصدهای قیمت‌گذاری را به صورت عدد وارد کنید")
                return

            # Calculate selling prices with user-defined percentages
            representative_price = total_workshop_cost * (1 + rep_percent/100)
            wholesale_price = total_workshop_cost * (1 + wholesale_percent/100)
            retail_price = total_workshop_cost * (1 + retail_percent/100)

            # Calculate selling prices
            #representative_price = total_workshop_cost * 1.30  # +30% profit
            #wholesale_price = total_workshop_cost * 1.20      # +20% profit
            #retail_price = total_workshop_cost * 1.40         # +40% profit

            # Show summary for confirmation
            summary = f"""خلاصه اطلاعات دارو:

نام دارو: {drug_name}
کد دارو: {drug_id}
 گروه دارویی: {selected_disease_name}

تعداد ترکیبات: {len(self.compositions)}
هزینه مواد اولیه: {raw_materials_cost:,.0f} تومان
هزینه قرض زنی: {loan_interest:,.0f} تومان
هزینه ظرف و برچسب: {container_label:,.0f} تومان
هزینه سربار کارگاه: {workshop_overhead:,.0f} تومان

قیمت تمام شده: {total_workshop_cost:,.0f} تومان
درصد نمایندگی: {rep_percent}%
قیمت نمایندگی: {representative_price:,.0f} تومان
درصد عمده: {wholesale_percent}%
قیمت عمده: {wholesale_price:,.0f} تومان
درصد خرده: {retail_percent}%
قیمت خرده: {retail_price:,.0f} تومان

آیا از ثبت اطلاعات اطمینان دارید؟"""

            if not messagebox.askyesno("تأیید ثبت دارو", summary):
                return

            # Start transaction
            self.conn.execute("BEGIN TRANSACTION")

            try:
                # 1. Register drug
                self.cursor.execute('''
                    INSERT INTO Drugs (
                        id, name, production_date, drug_type_id,
                        raw_materials_cost, loan_interest_cost,
                        container_label_cost, workshop_overhead_cost,
                        total_workshop_cost, representative_price,
                        wholesale_price, retail_price, waste_percentage,
                        units_per_package, honey_weight
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                drug_id, drug_name, datetime.now().strftime("%Y-%m-%d"),
                self.get_drug_type_id(cost_data['drug_type']),
                cost_data['raw_materials_cost'], cost_data['loan_interest'],
                cost_data['container_label'], cost_data['workshop_overhead'],
                cost_data['total_cost'], cost_data['representative_price'],
                cost_data['wholesale_price'], cost_data['retail_price'],
                cost_data['waste_percentage'],
                cost_data['units_per_package'], cost_data['honey_weight']
            ))

                # 2. Register compositions and update inventory
                for comp in self.compositions:
                    herb_id, herb_name, weight_grams, subtotal = comp

                    # Verify final inventory
                    self.cursor.execute("SELECT current_weight FROM Herbs WHERE id = ?", (herb_id,))
                    current_weight = self.cursor.fetchone()[0]
                    if current_weight < weight_grams:
                        raise ValueError(f"موجودی گیاه با کد '{herb_id}' برای تولید کافی نیست")

                    # Register composition
                    self.cursor.execute('''
                        INSERT INTO DrugCompositions (drug_id, herb_id, weight_used, subtotal)
                        VALUES (?, ?, ?, ?)
                    ''', (drug_id, herb_id, weight_grams, subtotal))

                    # Reduce inventory
                    self.cursor.execute('''
                        UPDATE Herbs
                        SET current_weight = current_weight - ?
                        WHERE id = ?
                    ''', (weight_grams, herb_id))

                # 3. Register disease relationship
                self.cursor.execute('''
                    INSERT INTO DrugDiseases (drug_id, disease_id)
                    VALUES (?, ?)
                ''', (drug_id, disease_id))

                # Commit transaction
                self.conn.commit()

                # Clear form
                self.clear_drug_form()

                # Show success message
                messagebox.showinfo("موفق", f"""داروی '{drug_name}' با موفقیت ثبت شد!

کد دارو: {drug_id}
تاریخ تولید: {datetime.now().strftime("%Y-%m-%d")}
قیمت تمام شده: {total_workshop_cost:,.0f} تومان""")

                # Update lists
                self.load_produced_drugs()

            except Exception as e:
                self.conn.rollback()
                raise e

        except sqlite3.IntegrityError as ie:
            if "Drugs.id" in str(ie):
                messagebox.showerror("خطا", f"دارویی با کد '{drug_id}' قبلاً ثبت شده است")
            else:
                messagebox.showerror("خطا", f"خطای یکپارچگی داده: {str(ie)}")
        except ValueError as ve:
            messagebox.showerror("خطا", str(ve))
        except Exception as e:
            messagebox.showerror("خطا", f"خطا در ثبت دارو: {str(e)}")

    def get_drug_type_id(self, type_name):
        """Get the ID for a given drug type name."""
        try:
            self.cursor.execute("SELECT type_id FROM DrugTypes WHERE name = ?", (type_name,))
            result = self.cursor.fetchone()
            if result:
                return result[0]
            else:
                # Optionally, handle case where type doesn't exist (e.g., add it or raise error)
                print(f"Warning: Drug type '{type_name}' not found in DrugTypes table.")
                # For now, let's try adding it if it doesn't exist
                try:
                    self.cursor.execute("INSERT INTO DrugTypes (name) VALUES (?)", (type_name,))
                    self.conn.commit()
                    print(f"Added new drug type: '{type_name}'")
                    return self.cursor.lastrowid # Return the newly inserted ID
                except Exception as insert_e:
                     messagebox.showerror("خطا", f"خطا در افزودن نوع داروی جدید '{type_name}': {insert_e}")
                     return None # Indicate failure
        except Exception as e:
            messagebox.showerror("خطا", f"خطا در دریافت کد نوع دارو: {str(e)}")
            return None


    def clear_drug_form(self):
        """Clear the drug production form"""
        self.drug_id_entry.delete(0, tk.END)
        self.drug_name_entry.delete(0, tk.END)
        self.disease_combobox.set('')
        self.loan_interest_entry.delete(0, tk.END)
        self.container_label_entry.delete(0, tk.END)
        self.workshop_overhead_entry.delete(0, tk.END)
        self.rep_percent_entry.delete(0, tk.END)
        self.rep_percent_entry.insert(0, "30")
        self.wholesale_percent_entry.delete(0, tk.END)
        self.wholesale_percent_entry.insert(0, "20")
        self.retail_percent_entry.delete(0, tk.END)
        self.retail_percent_entry.insert(0, "40")
        self.compositions = []
        self.update_compositions_tree()

        # Reset disease selection if available
        if self.disease_combobox['values']:
            self.disease_combobox.current(0)

    def toggle_drug_buttons(self, event):
        """Enable/disable buttons when a drug is selected"""
        if self.produced_drug_tree.selection():
            self.edit_drug_btn['state'] = 'normal'
            self.delete_drug_btn['state'] = 'normal'
            self.view_details_btn['state'] = 'normal'
        else:
            self.edit_drug_btn['state'] = 'disabled'
            self.delete_drug_btn['state'] = 'disabled'
            self.view_details_btn['state'] = 'disabled'

    def edit_selected_drug(self):
        """Edit the selected drug"""
        selected_item = self.produced_drug_tree.selection()
        if not selected_item:
            return

        drug_id = self.produced_drug_tree.item(selected_item, 'values')[4]  # Drug ID

        # Get current drug info
        self.cursor.execute("SELECT name, production_date FROM Drugs WHERE id = ?", (drug_id,))
        drug_info = self.cursor.fetchone()

        if not drug_info:
            messagebox.showerror("خطا", "اطلاعات دارو یافت نشد")
            return

        # Create edit window
        edit_window = tk.Toplevel()
        edit_window.title(f"ویرایش دارو {drug_id}")
        edit_window.transient(self.parent)
        edit_window.grab_set()

        # Edit fields
        frame = ttk.Frame(edit_window, padding=10)
        frame.pack(fill="both", expand=True)

        ttk.Label(frame, text="نام جدید دارو:").grid(row=0, column=0, padx=5, pady=5, sticky="e")
        new_name_entry = ttk.Entry(frame, width=30)
        new_name_entry.grid(row=0, column=1, padx=5, pady=5, sticky="w")
        new_name_entry.insert(0, drug_info[0])

        # Disease relationship (Combobox)
        ttk.Label(frame, text=" گروه دارویی:").grid(row=1, column=0, padx=5, pady=5, sticky="e")
        disease_combo = ttk.Combobox(frame, values=list(self.disease_manager.disease_map.keys()), width=30)
        disease_combo.grid(row=1, column=1, padx=5, pady=5, sticky="w")

        # Get current disease
        self.cursor.execute('''
        SELECT ds.name FROM DrugDiseases dd
        JOIN Diseases ds ON dd.disease_id = ds.id
        WHERE dd.drug_id = ?
        ''', (drug_id,))
        current_disease = self.cursor.fetchone()
        if current_disease:
            disease_combo.set(current_disease[0])

        # Save button
        ttk.Button(
            frame,
            text="ذخیره تغییرات",
            command=lambda: self.save_drug_edits(
                drug_id,
                new_name_entry.get(),
                disease_combo.get(),
                edit_window
            )
        ).grid(row=2, column=0, columnspan=2, pady=10)

    def save_drug_edits(self, drug_id, new_name, new_disease_name, window):
        """Save edits to a drug"""
        if not new_name:
            messagebox.showwarning("خطا", "نام دارو نمی‌تواند خالی باشد")
            return

        try:
            # Update drug name
            self.cursor.execute("UPDATE Drugs SET name = ? WHERE id = ?", (new_name, drug_id))

            # Update disease relationship
            if new_disease_name:
                new_disease_id = self.disease_manager.disease_map[new_disease_name]
                # Remove previous relationships
                self.cursor.execute("DELETE FROM DrugDiseases WHERE drug_id = ?", (drug_id,))
                # Add new relationship
                self.cursor.execute("INSERT INTO DrugDiseases (drug_id, disease_id) VALUES (?, ?)",
                            (drug_id, new_disease_id))

            self.conn.commit()
            messagebox.showinfo("موفق", "تغییرات با موفقیت ذخیره شد")
            window.destroy()
            self.load_produced_drugs()

        except Exception as e:
            self.conn.rollback()
            messagebox.showerror("خطا", f"خطا در ذخیره تغییرات: {str(e)}")

    def delete_selected_drug(self):
        """Delete the selected drug"""
        selected_item = self.produced_drug_tree.selection()
        if not selected_item:
            return

        drug_id = self.produced_drug_tree.item(selected_item, 'values')[4]
        drug_name = self.produced_drug_tree.item(selected_item, 'values')[3]

        confirm = messagebox.askyesno(
            "تایید حذف",
            f"آیا از حذف داروی '{drug_name}' (کد: {drug_id}) مطمئن هستید؟\n\nتوجه: این عمل برگشت‌ناپذیر است!"
        )

        if confirm:
            try:
                # Start transaction
                self.conn.execute("BEGIN TRANSACTION")

                # 1. Return materials to inventory
                self.cursor.execute('''
                SELECT herb_id, weight_used
                FROM DrugCompositions
                WHERE drug_id = ?
                ''', (drug_id,))
                compositions = self.cursor.fetchall()

                for herb_id, weight in compositions:
                    self.cursor.execute('''
                    UPDATE Herbs
                    SET current_weight = current_weight + ?
                    WHERE id = ?
                    ''', (weight, herb_id))

                # 2. Remove drug compositions
                self.cursor.execute("DELETE FROM DrugCompositions WHERE drug_id = ?", (drug_id,))

                # 3. Remove disease relationships
                self.cursor.execute("DELETE FROM DrugDiseases WHERE drug_id = ?", (drug_id,))

                # 4. Remove the drug
                self.cursor.execute("DELETE FROM Drugs WHERE id = ?", (drug_id,))

                self.conn.commit()
                messagebox.showinfo("موفق", "دارو با موفقیت حذف شد")
                self.load_produced_drugs()

            except Exception as e:
                self.conn.rollback()
                messagebox.showerror("خطا", f"خطا در حذف دارو: {str(e)}")

    def filter_drugs(self, event=None):
        """Filter drugs based on search input"""
        search_term = self.drug_search_entry.get().lower()
        self.load_produced_drugs(search_term)

    def load_produced_drugs(self, search_term=None):
        """Load and display produced drugs"""
        # Clear existing data
        for row in self.produced_drug_tree.get_children():
            self.produced_drug_tree.delete(row)

        try:
            # Build query to get drugs with related diseases
            query = '''
                SELECT
                    d.id,
                    d.name,
                    d.production_date,
                    GROUP_CONCAT(ds.name, ', ') AS diseases,
                    d.total_workshop_cost
                FROM Drugs d
                LEFT JOIN DrugDiseases dd ON d.id = dd.drug_id
                LEFT JOIN Diseases ds ON dd.disease_id = ds.id
            '''

            # Add search filter if provided
            params = []
            if search_term:
                query += " WHERE d.name LIKE ? OR d.id LIKE ? OR ds.name LIKE ?"
                params = [f"%{search_term}%", f"%{search_term}%", f"%{search_term}%"]

            query += " GROUP BY d.id ORDER BY d.production_date DESC"

            # Execute query
            if params:
                drugs = self.cursor.execute(query, params).fetchall()
            else:
                drugs = self.cursor.execute(query).fetchall()

            # Add each drug to the treeview
            for i, drug in enumerate(drugs):
                drug_id, drug_name, production_date, disease_names, total_cost = drug
                disease_display = disease_names if disease_names else "بدون  گروه داوریی"

                # Apply alternating row colors
                tag = 'evenrow' if i % 2 == 0 else 'oddrow'

                miladi_date = drug[2]  # فرض بر این است که تاریخ تولید در ستون سوم است
                if miladi_date:
                    try:
                        y, m, d = map(int, miladi_date.split('-'))
                        shamsi_date = jdatetime.date.fromgregorian(day=d, month=m, year=y).strftime("%Y-%m-%d")
                    except Exception:
                        shamsi_date = miladi_date
                else:
                    shamsi_date = ""

                self.produced_drug_tree.insert("", "end", values=(
                    f"{total_cost:,.0f}" if total_cost else "0",
                    disease_display,
                    shamsi_date,
                    drug_name,
                    drug_id

                ), tags=(tag,))

        except Exception as e:
            messagebox.showerror("خطا", f"خطا در بارگذاری داروهای تولید شده: {str(e)}")

    def show_drug_compositions(self, event=None):
        """Show compositions and details for the selected drug"""
        # Get selected drug
        if isinstance(event, tk.Event):  # Double-click event
            selected_item = self.produced_drug_tree.identify_row(event.y)
            if not selected_item:
                return
            self.produced_drug_tree.selection_set(selected_item)
        else:  # Button click
            selected_item = self.produced_drug_tree.selection()
            if not selected_item:
                messagebox.showwarning("هشدار", "لطفاً یک دارو را انتخاب کنید")
                return
            selected_item = selected_item[0]

        # Get drug info
        drug_id = self.produced_drug_tree.item(selected_item, 'values')[4]
        drug_name = self.produced_drug_tree.item(selected_item, 'values')[3]

        # Create detail window
        detail_window = tk.Toplevel()
        detail_window.title(f"مدیریت ترکیبات دارو {drug_name} (کد: {drug_id})")
        detail_window.geometry("700x600")
        detail_window.transient(self.parent)
        detail_window.grab_set()

        # Initialize total cost variable
        self.total_cost_var = tk.StringVar()
        self.total_cost_var.set("جمع کل هزینه تولید: 0 تومان")

        # Notebook for tabs
        detail_notebook = ttk.Notebook(detail_window)
        detail_notebook.pack(fill="both", expand=True, padx=10, pady=10)

        # Compositions tab
        comp_tab = ttk.Frame(detail_notebook)
        detail_notebook.add(comp_tab, text="ترکیبات دارو")

        # Cost tab
        cost_tab = ttk.Frame(detail_notebook)
        detail_notebook.add(cost_tab, text="اطلاعات هزینه و قیمت")

        # Set up compositions tab
        self.setup_compositions_tab(comp_tab, drug_id, detail_window)

        # Set up cost tab
        self.setup_cost_tab(cost_tab, drug_id)

        # Load initial data
        self.load_compositions(drug_id, detail_window)

    def setup_compositions_tab(self, parent, drug_id, main_window):
        """Set up the compositions tab"""
        # Frame for treeview
        tree_frame = ttk.Frame(parent)
        tree_frame.pack(fill="both", expand=True, padx=5, pady=5)

        # Treeview for compositions
        #columns = ("herb_id", "herb_name", "weight_used", "unit_price", "subtotal")
        columns = ("subtotal", "unit_price", "weight_used", "herb_name", "herb_id")
        self.comp_tree = ttk.Treeview(
            tree_frame,
            columns=columns,
            show="headings",
            selectmode="browse"
        )

        # Set up columns
        self.comp_tree.heading("herb_id", text="کد گیاه")
        self.comp_tree.heading("herb_name", text="نام گیاه")
        self.comp_tree.heading("weight_used", text="وزن مصرفی (گرم)")
        self.comp_tree.heading("unit_price", text="قیمت واحد (تومان)")
        self.comp_tree.heading("subtotal", text="هزینه جزئی")

        self.comp_tree.column("herb_id", width=80, anchor=tk.CENTER)
        self.comp_tree.column("herb_name", width=120, anchor=tk.CENTER)
        self.comp_tree.column("weight_used", width=150, anchor=tk.CENTER)
        self.comp_tree.column("unit_price", width=120, anchor=tk.CENTER)
        self.comp_tree.column("subtotal", width=120, anchor=tk.CENTER)

        # Scrollbar
        scrollbar = ttk.Scrollbar(tree_frame, orient="vertical", command=self.comp_tree.yview)
        self.comp_tree.configure(yscrollcommand=scrollbar.set)
        scrollbar.pack(side="left", fill="y")
        self.comp_tree.pack(side="right", fill="both", expand=True)

        # Management buttons frame
        btn_frame = ttk.Frame(parent)
        btn_frame.pack(fill="x", padx=5, pady=5)

        # Total cost label
        ttk.Label(btn_frame, textvariable=self.total_cost_var, anchor=tk.E).pack(side="left", padx=5)

        # Management buttons
        ttk.Button(
            btn_frame,
            text="+ افزودن ترکیب جدید",
            command=lambda: self.add_new_composition(drug_id, main_window)
        ).pack(side="right", padx=5)

        ttk.Button(
            btn_frame,
            text="ویرایش ترکیب انتخاب شده",
            command=lambda: self.edit_selected_composition(drug_id, main_window)
        ).pack(side="right", padx=5)

        ttk.Button(
            btn_frame,
            text="حذف ترکیب انتخاب شده",
            command=lambda: self.delete_selected_composition(drug_id, main_window)
        ).pack(side="right", padx=5)

        # Apply alternating row colors
        self.comp_tree.tag_configure('oddrow', background='#f3f4f6')
        self.comp_tree.tag_configure('evenrow', background='#ffffff')

    def setup_cost_tab(self, parent, drug_id):
        """Set up the cost tab"""
        # Frame for cost details
        cost_frame = ttk.Frame(parent, padding=10)
        cost_frame.pack(fill="both", expand=True)

        # Get cost data
        try:
            self.cursor.execute('''
                SELECT
                    raw_materials_cost,
                    loan_interest_cost,
                    container_label_cost,
                    workshop_overhead_cost,
                    total_workshop_cost,
                    representative_price,
                    wholesale_price,
                    retail_price
                FROM Drugs
                WHERE id = ?
            ''', (drug_id,))
            costs = self.cursor.fetchone()

            if costs:
                # Create labels for each cost
                costs_data = [
                    ("قیمت تمام شده مفردات:", costs[0]),
                    ("قیمت قرض زنی:", costs[1]),
                    ("هزینه ظرف و برچسب:", costs[2]),
                    ("هزینه سربار کارگاه:", costs[3]),
                    ("قیمت تمام شده کارگاه:", costs[4]),
                    ("قیمت نمایندگی:", costs[5]),
                    ("قیمت عمده:", costs[6]),
                    ("قیمت خرده:", costs[7])
                ]

                # Title
                ttk.Label(
                    cost_frame,
                    text="اطلاعات هزینه و قیمت‌گذاری",
                    font=("Arial", 12, "bold")
                ).grid(row=0, column=0, columnspan=2, pady=10, sticky="w")

                # Costs
                for i, (label, value) in enumerate(costs_data):
                    ttk.Label(
                        cost_frame,
                        text=label,
                        anchor=tk.E,
                        font=("Arial", 10, "bold" if i == 4 or i == 7 else "normal")
                    ).grid(row=i+1, column=1, padx=5, pady=5, sticky="e")

                    ttk.Label(
                        cost_frame,
                        text=f"{value:,.0f} تومان",
                        anchor=tk.W,
                        font=("Arial", 10, "bold" if i == 4 or i == 7 else "normal")
                    ).grid(row=i+1, column=0, padx=5, pady=5, sticky="w")

                # Profit calculation
                profit = costs[7] - costs[4]  # Retail - Total cost
                profit_percent = (profit / costs[4] * 100) if costs[4] > 0 else 0

                ttk.Separator(cost_frame, orient="horizontal").grid(
                    row=len(costs_data)+1, column=0, columnspan=2, sticky="ew", pady=10
                )

                ttk.Label(
                    cost_frame,
                    text="سود خالص:",
                    anchor=tk.E,
                    font=("Arial", 11, "bold")
                ).grid(row=len(costs_data)+2, column=1, padx=5, pady=5, sticky="e")

                ttk.Label(
                    cost_frame,
                    text=f"{profit:,.0f} تومان ({profit_percent:.1f}%)",
                    anchor=tk.W,
                    font=("Arial", 11, "bold")
                ).grid(row=len(costs_data)+2, column=0, padx=5, pady=5, sticky="w")

                # Configure grid weights
                cost_frame.columnconfigure(0, weight=1)
                cost_frame.columnconfigure(1, weight=0)

        except Exception as e:
            messagebox.showerror("خطا", f"خطا در بارگذاری اطلاعات هزینه: {str(e)}")

    def load_compositions(self, drug_id, window):
        """Load compositions for a drug"""
        # Clear existing items
        for item in self.comp_tree.get_children():
            self.comp_tree.delete(item)

        try:
            # Get compositions
            self.cursor.execute('''
            SELECT dc.herb_id, h.name, dc.weight_used, h.unit_price, dc.subtotal
            FROM DrugCompositions dc
            JOIN Herbs h ON dc.herb_id = h.id
            WHERE dc.drug_id = ?
            ''', (drug_id,))

            # Process results
            total = 0
            for i, comp in enumerate(self.cursor.fetchall()):
                herb_id, herb_name, weight_grams, unit_price_per_kg, subtotal = comp

                # Display weight in both grams and kilograms
                weight_kg = weight_grams / 1000
                display_weight = f"{weight_grams:g} گرم ({weight_kg:.3f} کیلوگرم)"

                # Format unit price and subtotal
                display_unit_price = f"{unit_price_per_kg:,.0f}"
                display_subtotal = f"{subtotal:,.0f}"

                # Add to treeview with alternating colors
                tag = 'evenrow' if i % 2 == 0 else 'oddrow'
                self.comp_tree.insert("", "end", values=(
                    display_subtotal,
                    display_unit_price,
                    display_weight,
                    herb_name,
                    herb_id
                ), tags=(tag,))

                total += subtotal

            # Update total cost label
            self.total_cost_var.set(f"جمع کل هزینه مواد اولیه: {total:,.0f} تومان")
            window.update()

        except Exception as e:
            messagebox.showerror("خطا", f"خطا در بارگذاری ترکیبات: {str(e)}")

    def add_new_composition(self, drug_id, parent_window):
        """Add a new composition to a drug"""
        add_window = tk.Toplevel(parent_window)
        add_window.title("افزودن ترکیب جدید")
        add_window.transient(parent_window)
        add_window.grab_set()

        # Frame for form
        frame = ttk.Frame(add_window, padding=10)
        frame.pack(fill="both", expand=True)

        # Form fields
        ttk.Label(frame, text="کد گیاه:").grid(row=0, column=0, padx=5, pady=5, sticky="e")
        herb_id_entry = ttk.Entry(frame, width=20)
        herb_id_entry.grid(row=0, column=1, padx=5, pady=5, sticky="w")

        ttk.Label(frame, text="وزن مورد نیاز (گرم):").grid(row=1, column=0, padx=5, pady=5, sticky="e")
        weight_entry = ttk.Entry(frame, width=20)
        weight_entry.grid(row=1, column=1, padx=5, pady=5, sticky="w")

        # Herb selector button
        def select_herb():
            # Create herb selection window
            select_window = tk.Toplevel(add_window)
            select_window.title("انتخاب گیاه")
            select_window.transient(add_window)
            select_window.grab_set()
            select_window.geometry("600x400")

            # Frame for search
            search_frame = ttk.Frame(select_window, padding=5)
            search_frame.pack(fill="x")

            ttk.Label(search_frame, text="جستجو:").pack(side=tk.RIGHT, padx=5)
            search_entry = ttk.Entry(search_frame)
            search_entry.pack(side=tk.RIGHT, fill="x", expand=True, padx=5)

            # Herb treeview
            herb_tree = ttk.Treeview(
                select_window,
                columns=("id", "name", "inventory"),
                show="headings",
                height=15
            )

            herb_tree.heading("id", text="کد گیاه")
            herb_tree.heading("name", text="نام گیاه")
            herb_tree.heading("inventory", text="موجودی (گرم)")

            herb_tree.column("id", width=100, anchor=tk.CENTER)
            herb_tree.column("name", width=250, anchor=tk.E)
            herb_tree.column("inventory", width=150, anchor=tk.E)

            # Load herbs
            def load_herbs():
                search_term = search_entry.get().strip().lower()

                # Clear existing items
                for item in herb_tree.get_children():
                    herb_tree.delete(item)

                # Get herbs with inventory
                query = "SELECT id, name, current_weight FROM Herbs WHERE current_weight > 0"
                params = []

                if search_term:
                    query += " AND (id LIKE ? OR name LIKE ?)"
                    params = [f"%{search_term}%", f"%{search_term}%"]

                query += " ORDER BY name"

                try:
                    if params:
                        herbs = self.cursor.execute(query, params).fetchall()
                    else:
                        herbs = self.cursor.execute(query).fetchall()

                    for herb in herbs:
                        herb_tree.insert("", "end", values=(
                            herb[0],
                            herb[1],
                            f"{herb[2]:,.0f}"
                        ))

                except Exception as e:
                    messagebox.showerror("خطا", f"خطا در بارگذاری اطلاعات گیاهان: {str(e)}")

            # Select button action
            def on_select():
                selected = herb_tree.selection()
                if not selected:
                    messagebox.showwarning("هشدار", "لطفاً یک گیاه را انتخاب کنید")
                    return

                values = herb_tree.item(selected, "values")
                herb_id_entry.delete(0, tk.END)
                herb_id_entry.insert(0, values[0])
                select_window.destroy()

            # Button frame
            button_frame = ttk.Frame(select_window, padding=5)
            button_frame.pack(fill="x", side=tk.BOTTOM)

            ttk.Button(button_frame, text="انتخاب", command=on_select).pack(side=tk.RIGHT, padx=5)
            ttk.Button(button_frame, text="انصراف", command=select_window.destroy).pack(side=tk.RIGHT, padx=5)

            # Search button
            ttk.Button(search_frame, text="جستجو", command=load_herbs).pack(side=tk.RIGHT, padx=5)
            search_entry.bind("<Return>", lambda e: load_herbs())

            # Pack treeview and scrollbar
            scrollbar = ttk.Scrollbar(select_window, orient="vertical", command=herb_tree.yview)
            herb_tree.configure(yscrollcommand=scrollbar.set)

            scrollbar.pack(side=tk.LEFT, fill="y")
            herb_tree.pack(fill="both", expand=True, padx=5, pady=5)

            # Initial load
            load_herbs()

            # Double-click to select
            herb_tree.bind("<Double-1>", lambda e: on_select())

        ttk.Button(frame, text="انتخاب از لیست...", command=select_herb).grid(row=0, column=2, padx=5, pady=5)

        def save_composition():
            try:
                herb_id = herb_id_entry.get().strip()
                if not herb_id:
                    messagebox.showwarning("هشدار", "لطفاً کد گیاه را وارد کنید")
                    return

                weight_grams = float(weight_entry.get())
                if weight_grams <= 0:
                    messagebox.showwarning("هشدار", "وزن باید بزرگتر از صفر باشد")
                    return

                # Check inventory and get unit price
                self.cursor.execute("SELECT current_weight, unit_price, name FROM Herbs WHERE id = ?", (herb_id,))
                herb_data = self.cursor.fetchone()

                if not herb_data:
                    messagebox.showerror("خطا", "کد گیاه نامعتبر است!")
                    return

                current_weight, unit_price, herb_name = herb_data
                if current_weight < weight_grams:
                    messagebox.showerror("خطا", f"موجودی ناکافی! موجودی فعلی: {current_weight:,.0f} گرم")
                    return

                # Check if herb is already in the drug's compositions
                self.cursor.execute(
                    "SELECT COUNT(*) FROM DrugCompositions WHERE drug_id = ? AND herb_id = ?",
                    (drug_id, herb_id)
                )
                exists = self.cursor.fetchone()[0] > 0

                if exists:
                    messagebox.showerror("خطا", f"این گیاه قبلاً به ترکیبات دارو اضافه شده است")
                    return

                # Calculate cost
                weight_kg = weight_grams / 1000
                subtotal = weight_kg * unit_price

                # Start transaction
                self.conn.execute("BEGIN TRANSACTION")

                try:
                    # Add to compositions
                    self.cursor.execute('''
                    INSERT INTO DrugCompositions (drug_id, herb_id, weight_used, subtotal)
                    VALUES (?, ?, ?, ?)
                    ''', (drug_id, herb_id, weight_grams, subtotal))

                    # Update inventory
                    self.cursor.execute('''
                    UPDATE Herbs SET current_weight = current_weight - ? WHERE id = ?
                    ''', (weight_grams, herb_id))

                    # Update drug cost
                    self.cursor.execute('''
                    UPDATE Drugs
                    SET raw_materials_cost = raw_materials_cost + ?,
                        total_workshop_cost = total_workshop_cost + ?,
                        representative_price = (raw_materials_cost + ? + loan_interest_cost + container_label_cost + workshop_overhead_cost) * 1.3,
                        wholesale_price = (raw_materials_cost + ? + loan_interest_cost + container_label_cost + workshop_overhead_cost) * 1.2,
                        retail_price = (raw_materials_cost + ? + loan_interest_cost + container_label_cost + workshop_overhead_cost) * 1.4
                    WHERE id = ?
                    ''', (subtotal, subtotal, subtotal, subtotal, subtotal, drug_id))

                    self.conn.commit()
                    messagebox.showinfo("موفق", f"ترکیب با موفقیت اضافه شد!")
                    add_window.destroy()

                    # Refresh compositions list
                    self.load_compositions(drug_id, parent_window)
                    self.load_produced_drugs()

                except Exception as e:
                    self.conn.rollback()
                    raise e

            except ValueError:
                messagebox.showerror("خطا", "لطفاً یک عدد معتبر برای وزن وارد کنید!")
            except Exception as e:
                messagebox.showerror("خطا", f"خطا در افزودن ترکیب: {str(e)}")

        # Buttons
        button_frame = ttk.Frame(frame)
        button_frame.grid(row=2, column=0, columnspan=3, pady=10)

        ttk.Button(button_frame, text="ذخیره", command=save_composition).pack(side=tk.RIGHT, padx=5)
        ttk.Button(button_frame, text="انصراف", command=add_window.destroy).pack(side=tk.RIGHT, padx=5)

    def edit_selected_composition(self, drug_id, parent_window):
        """Edit the selected composition"""
        selected = self.comp_tree.selection()
        if not selected:
            messagebox.showwarning("هشدار", "لطفاً یک ترکیب را انتخاب کنید")
            return

        comp_data = self.comp_tree.item(selected, 'values')
        herb_id = comp_data[4]

        # Extract weight in grams from display string (e.g., "150 گرم (0.150 کیلوگرم)")
        weight_display = comp_data[2]
        current_weight_grams = float(weight_display.split()[0])

        # Create edit window
        edit_window = tk.Toplevel(parent_window)
        edit_window.title("ویرایش ترکیب")
        edit_window.transient(parent_window)
        edit_window.grab_set()

        # Form frame
        frame = ttk.Frame(edit_window, padding=10)
        frame.pack(fill="both", expand=True)

        # Form fields
        ttk.Label(frame, text=f"کد گیاه: {herb_id}").grid(row=0, column=0, columnspan=2, padx=5, pady=5, sticky="w")
        ttk.Label(frame, text=f"نام گیاه: {comp_data[3]}").grid(row=1, column=0, columnspan=2, padx=5, pady=5, sticky="w")

        ttk.Label(frame, text="وزن جدید (گرم):").grid(row=2, column=0, padx=5, pady=5, sticky="e")
        weight_entry = ttk.Entry(frame, width=15)
        weight_entry.insert(0, current_weight_grams)
        weight_entry.grid(row=2, column=1, padx=5, pady=5, sticky="w")

        def update_composition():
            try:
                new_weight_grams = float(weight_entry.get())
                if new_weight_grams <= 0:
                    messagebox.showwarning("هشدار", "وزن باید بزرگتر از صفر باشد")
                    return

                weight_diff_grams = new_weight_grams - current_weight_grams

                # Check inventory
                self.cursor.execute("SELECT current_weight, unit_price FROM Herbs WHERE id = ?", (herb_id,))
                available, unit_price_per_kg = self.cursor.fetchone()

                if weight_diff_grams > 0 and available < weight_diff_grams:
                    messagebox.showerror("خطا", f"موجودی ناکافی! موجودی قابل استفاده: {available:,.0f} گرم")
                    return

                # Calculate new cost
                new_weight_kg = new_weight_grams / 1000
                new_subtotal = new_weight_kg * unit_price_per_kg

                # Get old subtotal
                self.cursor.execute(
                    "SELECT subtotal FROM DrugCompositions WHERE drug_id = ? AND herb_id = ?",
                    (drug_id, herb_id)
                )
                old_subtotal = self.cursor.fetchone()[0]

                # Calculate cost difference
                cost_diff = new_subtotal - old_subtotal

                # Start transaction
                self.conn.execute("BEGIN TRANSACTION")

                try:
                    # Update composition
                    self.cursor.execute('''
                    UPDATE DrugCompositions
                    SET weight_used = ?, subtotal = ?
                    WHERE drug_id = ? AND herb_id = ?
                    ''', (new_weight_grams, new_subtotal, drug_id, herb_id))

                    # Update inventory
                    self.cursor.execute('''
                    UPDATE Herbs
                    SET current_weight = current_weight - ?
                    WHERE id = ?
                    ''', (weight_diff_grams, herb_id))

                    # Update drug cost
                    self.cursor.execute('''
                    UPDATE Drugs
                    SET raw_materials_cost = raw_materials_cost + ?,
                        total_workshop_cost = total_workshop_cost + ?,
                        representative_price = (raw_materials_cost + ? + loan_interest_cost + container_label_cost + workshop_overhead_cost) * 1.3,
                        wholesale_price = (raw_materials_cost + ? + loan_interest_cost + container_label_cost + workshop_overhead_cost) * 1.2,
                        retail_price = (raw_materials_cost + ? + loan_interest_cost + container_label_cost + workshop_overhead_cost) * 1.4
                    WHERE id = ?
                    ''', (cost_diff, cost_diff, cost_diff, cost_diff, cost_diff, drug_id))

                    self.conn.commit()
                    messagebox.showinfo("موفق", "ترکیب با موفقیت ویرایش شد!")
                    edit_window.destroy()

                    # Refresh compositions list
                    self.load_compositions(drug_id, parent_window)
                    self.load_produced_drugs()

                except Exception as e:
                    self.conn.rollback()
                    raise e

            except ValueError:
                messagebox.showerror("خطا", "لطفاً یک عدد معتبر برای وزن وارد کنید!")
            except Exception as e:
                messagebox.showerror("خطا", f"خطا در ویرایش ترکیب: {str(e)}")

        # Buttons
        button_frame = ttk.Frame(frame)
        button_frame.grid(row=3, column=0, columnspan=2, pady=10)

        ttk.Button(button_frame, text="ذخیره تغییرات", command=update_composition).pack(side=tk.RIGHT, padx=5)
        ttk.Button(button_frame, text="انصراف", command=edit_window.destroy).pack(side=tk.RIGHT, padx=5)

    def delete_selected_composition(self, drug_id, parent_window):
        """Delete the selected composition"""
        selected = self.comp_tree.selection()
        if not selected:
            messagebox.showwarning("هشدار", "لطفاً یک ترکیب را انتخاب کنید")
            return

        try:
            comp_data = self.comp_tree.item(selected, 'values')
            herb_id = comp_data[4]
            herb_name = comp_data[3]

            # Extract weight from display string
            weight_str = comp_data[2].split()[0]  # Get "150" from "150 گرم (0.150 کیلوگرم)"
            weight = float(weight_str)

            # Get subtotal
            self.cursor.execute(
                "SELECT subtotal FROM DrugCompositions WHERE drug_id = ? AND herb_id = ?",
                (drug_id, herb_id)
            )
            subtotal = self.cursor.fetchone()[0]

            confirm = messagebox.askyesno(
                "تأیید حذف",
                f"آیا از حذف ترکیب گیاه '{herb_name}' (کد: {herb_id}) مطمئن هستید؟\nوزن مصرفی: {weight:,.0f} گرم"
            )

            if confirm:
                try:
                    # Start transaction
                    self.conn.execute("BEGIN TRANSACTION")

                    # Delete composition
                    self.cursor.execute('''
                    DELETE FROM DrugCompositions
                    WHERE drug_id = ? AND herb_id = ?
                    ''', (drug_id, herb_id))

                    # Return inventory
                    self.cursor.execute('''
                    UPDATE Herbs
                    SET current_weight = current_weight + ?
                    WHERE id = ?
                    ''', (weight, herb_id))

                    # Update drug cost
                    self.cursor.execute('''
                    UPDATE Drugs
                    SET raw_materials_cost = raw_materials_cost - ?,
                        total_workshop_cost = total_workshop_cost - ?,
                        representative_price = (raw_materials_cost - ? + loan_interest_cost + container_label_cost + workshop_overhead_cost) * 1.3,
                        wholesale_price = (raw_materials_cost - ? + loan_interest_cost + container_label_cost + workshop_overhead_cost) * 1.2,
                        retail_price = (raw_materials_cost - ? + loan_interest_cost + container_label_cost + workshop_overhead_cost) * 1.4
                    WHERE id = ?
                    ''', (subtotal, subtotal, subtotal, subtotal, subtotal, drug_id))

                    self.conn.commit()
                    messagebox.showinfo("موفق", "ترکیب با موفقیت حذف شد!")

                    # Refresh compositions list
                    self.load_compositions(drug_id, parent_window)
                    self.load_produced_drugs()

                except Exception as e:
                    self.conn.rollback()
                    raise e

        except Exception as e:
            messagebox.showerror("خطا", f"خطا در حذف ترکیب: {str(e)}")

class ReportTabManager:
    def __init__(self, parent, conn):
        self.parent = parent
        self.conn = conn
        self.cursor = conn.cursor()

        # Create notebook for report types
        self.reports_notebook = ttk.Notebook(self.parent)
        self.reports_notebook.pack(fill="both", expand=True, padx=10, pady=10)

        # Create report tabs
        self.summary_tab = ttk.Frame(self.reports_notebook)
        self.inventory_tab = ttk.Frame(self.reports_notebook)
        self.drugs_tab = ttk.Frame(self.reports_notebook)
        self.financial_tab = ttk.Frame(self.reports_notebook)

        self.reports_notebook.add(self.summary_tab, text="خلاصه وضعیت")
        self.reports_notebook.add(self.inventory_tab, text="گزارش موجودی")
        self.reports_notebook.add(self.drugs_tab, text="گزارش داروها")
        self.reports_notebook.add(self.financial_tab, text="گزارش مالی")

        # Store references to treeviews for easier access
        self.report_treeviews = {
            0: None, # Summary tab doesn't have a main treeview like others
            1: None, # Placeholder, will be set in setup_inventory_tab
            2: None, # Placeholder, will be set in setup_drugs_tab
            3: None  # Placeholder, will be set in setup_financial_tab
        }

        # Set up each tab
        self.setup_summary_tab()
        self.setup_inventory_tab()
        self.setup_drugs_tab()
        self.setup_financial_tab()

        # Create toolbar for reports
        self.create_report_toolbar()

        # Generate initial reports
        self.generate_report()

    def setup_summary_tab(self):
        """Set up the summary tab with overview statistics"""
        report_frame = ttk.LabelFrame(self.summary_tab, text="خلاصه وضعیت انبار و تولید",labelanchor="ne")
        report_frame.pack(pady=20, padx=20, fill="both", expand=True)

        # StringVars for dynamic labels
        self.total_herb_types_var = tk.StringVar(value="...")
        self.total_herb_weight_var = tk.StringVar(value="...")
        self.total_inventory_value_var = tk.StringVar(value="...")
        self.total_drugs_produced_var = tk.StringVar(value="...")

        # Add chart canvas
        self.chart_frame = ttk.Frame(report_frame)
        self.chart_frame.pack(fill="both", expand=True, pady=10)

        # Left side - Statistics
        stats_frame = ttk.Frame(self.chart_frame)
        stats_frame.pack(side=tk.RIGHT, fill="y", padx=20)

        # Labels for report data (Label on right - col 1, Value on left - col 0)
        ttk.Label(stats_frame, text="تعداد کل انواع گیاهان", anchor=tk.E).grid(row=0, column=1, padx=5, pady=5, sticky="e")
        ttk.Label(stats_frame, textvariable=self.total_herb_types_var, anchor=tk.W).grid(row=0, column=0, padx=5, pady=5, sticky="w")

        ttk.Label(stats_frame, text="مجموع وزن کل گیاهان (گرم)", anchor=tk.E).grid(row=1, column=1, padx=5, pady=5, sticky="e")
        ttk.Label(stats_frame, textvariable=self.total_herb_weight_var, anchor=tk.W).grid(row=1, column=0, padx=5, pady=5, sticky="w")

        ttk.Label(stats_frame, text="ارزش کل موجودی انبار (تومان)", anchor=tk.E).grid(row=2, column=1, padx=5, pady=5, sticky="e")
        ttk.Label(stats_frame, textvariable=self.total_inventory_value_var, anchor=tk.W).grid(row=2, column=0, padx=5, pady=5, sticky="w")

        ttk.Label(stats_frame, text="تعداد کل داروهای تولید شده", anchor=tk.E).grid(row=3, column=1, padx=5, pady=5, sticky="e")
        ttk.Label(stats_frame, textvariable=self.total_drugs_produced_var, anchor=tk.W).grid(row=3, column=0, padx=5, pady=5, sticky="w")

        # Configure column weights for resizing
        stats_frame.columnconfigure(0, weight=1)
        stats_frame.columnconfigure(1, weight=0)

        # Right side - Visual dashboard
        dashboard_frame = ttk.LabelFrame(self.chart_frame, text="نمودار آماری",labelanchor="ne")
        dashboard_frame.pack(side=tk.LEFT, fill="both", expand=True)

        # Placeholder for chart (in a real implementation, you'd use matplotlib or another charting library)
        self.chart_canvas = tk.Canvas(dashboard_frame, bg="white", height=200)
        self.chart_canvas.pack(fill="both", expand=True, padx=10, pady=10)

        # Draw placeholder chart
        self.draw_placeholder_chart()

        # Bottom section - Latest activities
        activity_frame = ttk.LabelFrame(report_frame, text="آخرین فعالیت‌ها",labelanchor="ne")
        activity_frame.pack(fill="x", pady=1, padx=2)

        # Activity list (most recent drugs and herbs)
        self.activity_tree = ttk.Treeview(
            activity_frame,
            columns=("date", "type", "name", "id"),
            show="headings",
            height=5
        )

        # Configure columns
        self.activity_tree.heading("date", text="تاریخ")
        self.activity_tree.heading("type", text="نوع فعالیت")
        self.activity_tree.heading("name", text="نام")
        self.activity_tree.heading("id", text="کد")

        self.activity_tree.column("date", width=100, anchor=tk.CENTER)
        self.activity_tree.column("type", width=100, anchor=tk.CENTER)
        self.activity_tree.column("name", width=200, anchor=tk.CENTER)
        self.activity_tree.column("id", width=100, anchor=tk.CENTER)

        # Add scrollbar
        activity_scrollbar = ttk.Scrollbar(activity_frame, orient="vertical", command=self.activity_tree.yview)
        self.activity_tree.configure(yscrollcommand=activity_scrollbar.set)

        activity_scrollbar.pack(side=tk.LEFT, fill="y")
        self.activity_tree.pack(side=tk.RIGHT, fill="both", expand=True, pady=5)

        # Refresh button
        refresh_btn = ttk.Button(report_frame, text="به‌روزرسانی گزارش", command=self.generate_report)
        refresh_btn.pack(pady=5, padx=5)

    def draw_placeholder_chart(self):
        """Draw a placeholder chart on the canvas"""
        self.chart_canvas.delete("all")

        # Get data for chart
        try:
            # Get total herb weight
            self.cursor.execute("SELECT SUM(current_weight) FROM Herbs")
            total_weight = self.cursor.fetchone()[0] or 0

            # Get top 5 herbs by weight
            self.cursor.execute("""
                SELECT name, current_weight
                FROM Herbs
                ORDER BY current_weight DESC
                LIMIT 10
            """)
            top_herbs = self.cursor.fetchall()

            # Draw chart
            width = self.chart_canvas.winfo_width() or 40
            height = self.chart_canvas.winfo_height() or 20

            # Draw title
            self.chart_canvas.create_text(
                width/2, 20,
                text="گیاهان با بیشترین موجودی",
                font=("Arial", 12, "bold"),
                fill="#3B82F6"
            )

            if not top_herbs:
                self.chart_canvas.create_text(
                    width/2, height/2,
                    text="داده‌ای موجود نیست",
                    font=("Arial", 10),
                    fill="#888888"
                )
                return

            # Calculate bar dimensions
            bar_width = width / (len(top_herbs) * 2)
            max_height = height - 60  # Leave space for labels
            max_value = max([herb[1] for herb in top_herbs]) if top_herbs else 1

            # Draw bars
            for i, (name, weight) in enumerate(top_herbs):
                # Calculate bar height (proportional to weight)
                bar_height = (weight / max_value) * max_height if max_value > 0 else 0

                # Calculate bar position
                x = width * (i + 1) / (len(top_herbs) + 1)
                y = height - 40  # Bottom position (leave space for labels)

                # Draw bar
                self.chart_canvas.create_rectangle(
                    x - bar_width/2, y - bar_height,
                    x + bar_width/2, y,
                    fill="#3B82F6",
                    outline="#2563EB"
                )

                # Draw value label
                self.chart_canvas.create_text(
                    x, y - bar_height - 10,
                    text=f"{weight:,.0f}",
                    font=("Arial", 8),
                    fill="#555555"
                )

                # Draw name label
                self.chart_canvas.create_text(
                    x, y + 10,
                    text=name if len(name) < 15 else name[:12] + "...",
                    font=("Arial", 8),
                    fill="#555555"
                )

        except Exception as e:
            print(f"Error drawing chart: {e}")
            self.chart_canvas.create_text(
                width/2, height/2,
                text="خطا در نمایش نمودار",
                font=("Arial", 10),
                fill="#FF0000"
            )

    def setup_inventory_tab(self):
        """Set up the inventory report tab"""
        # Control frame for filters
        control_frame = ttk.Frame(self.inventory_tab)
        control_frame.pack(fill="both", padx=10, pady=5)

        # Search/filter
        ttk.Label(control_frame, text="جستجو:").pack(side=tk.RIGHT, padx=5)
        self.inventory_search = ttk.Entry(control_frame, justify=tk.RIGHT)
        self.inventory_search.pack(side=tk.RIGHT, padx=5, fill="x", expand=True)
        self.inventory_search.bind("<KeyRelease>", self.filter_inventory_report)

        # Sort options
        ttk.Label(control_frame, text="مرتب‌سازی:").pack(side=tk.RIGHT, padx=5)
        self.inventory_sort = ttk.Combobox(
            control_frame,
            values=["نام", "کد", "موجودی (صعودی)", "موجودی (نزولی)", "قیمت واحد"],
            width=15,
            state="readonly"
        )
        self.inventory_sort.current(0)
        self.inventory_sort.pack(side=tk.RIGHT, padx=5)
        self.inventory_sort.bind("<<ComboboxSelected>>", self.filter_inventory_report)

        # Treeview for inventory report
        #columns = ("id", "name", "weight", "unit_price", "total_value", "shelf", "last_update")
        columns = ("last_update", "shelf", "total_value", "unit_price", "weight", "name", "id")
        tree = ttk.Treeview(self.inventory_tab, columns=columns, show="headings", height=15)
        self.inventory_tree = tree # Assign to instance variable
        self.report_treeviews[1] = tree # Store reference

        # Set up columns
        tree.heading("id", text="کد گیاه")
        tree.heading("name", text="نام گیاه")
        tree.heading("weight", text="موجودی (گرم/کیلوگرم)")
        tree.heading("unit_price", text="قیمت واحد (تومان/کیلوگرم)")
        tree.heading("total_value", text="ارزش کل")
        tree.heading("shelf", text="محل قفسه")
        tree.heading("last_update", text="آخرین به‌روزرسانی")

        tree.column("id", width=50, anchor=tk.CENTER)
        tree.column("name", width=100, anchor=tk.CENTER)
        tree.column("weight", width=200, anchor=tk.CENTER)
        tree.column("unit_price", width=200, anchor=tk.CENTER)
        tree.column("total_value", width=120, anchor=tk.CENTER)
        tree.column("shelf", width=80, anchor=tk.CENTER)
        tree.column("last_update", width=150, anchor=tk.CENTER)

        # Add scrollbar
        inventory_scrollbar = ttk.Scrollbar(self.inventory_tab, orient="vertical", command=tree.yview)
        tree.configure(yscrollcommand=inventory_scrollbar.set)

        # Pack treeview and scrollbar
        inventory_scrollbar.pack(side=tk.LEFT, fill="y")
        tree.pack(side=tk.RIGHT, fill="both", expand=True, padx=10, pady=5)

        # Apply alternating row colors
        tree.tag_configure('oddrow', background='#f3f4f6')
        tree.tag_configure('evenrow', background='#ffffff')

        # Summary frame (at bottom)
        summary_frame = ttk.Frame(self.inventory_tab)
        summary_frame.pack(fill="y", padx=10, pady=5)

        # Summary labels
        self.inventory_count_var = tk.StringVar(value="تعداد گیاهان: 0")
        self.inventory_total_weight_var = tk.StringVar(value="وزن کل: 0 گرم")
        self.inventory_total_value_var = tk.StringVar(value="ارزش کل: 0 تومان")

        ttk.Label(summary_frame, textvariable=self.inventory_count_var).pack(anchor=tk.E, padx=10, pady=2)
        ttk.Label(summary_frame, textvariable=self.inventory_total_weight_var).pack(anchor=tk.E, padx=10, pady=2)
        ttk.Label(summary_frame, textvariable=self.inventory_total_value_var).pack(anchor=tk.E, padx=10, pady=2)

    def filter_inventory_report(self, event=None):
        """Filter and load inventory report based on search and sort criteria"""
        # Get search term
        search_term = self.inventory_search.get().strip().lower()

        # Get sort option
        sort_option = self.inventory_sort.get()

        # Build query
        query = """
            SELECT id, name, current_weight, unit_price,
                (unit_price * (current_weight / 1000.0)),
                shelf_location, purchase_date
            FROM Herbs
        """

        # Add search filter if provided
        params = []
        if search_term:
            query += " WHERE id LIKE ? OR name LIKE ? OR shelf_location LIKE ?"
            params = [f"%{search_term}%", f"%{search_term}%", f"%{search_term}%"]

        # Add sorting
        if sort_option == "نام":
            query += " ORDER BY name"
        elif sort_option == "کد":
            query += " ORDER BY id"
        elif sort_option == "موجودی (صعودی)":
            query += " ORDER BY current_weight ASC"
        elif sort_option == "موجودی (نزولی)":
            query += " ORDER BY current_weight DESC"
        elif sort_option == "قیمت واحد":
            query += " ORDER BY unit_price DESC"
        else:
            query += " ORDER BY name"  # Default sort

        # Execute query and update treeview
        self.load_inventory_report(query, params)

    def load_inventory_report(self, query, params=[]):
        """Load inventory report based on query"""
        for item in self.inventory_tree.get_children():
            self.inventory_tree.delete(item)
        try:
            if params:
                results = self.cursor.execute(query, params).fetchall()
            else:
                results = self.cursor.execute(query).fetchall()
            total_weight = 0
            total_value = 0
            for i, row in enumerate(results):
                herb_id, name, weight, price, value, shelf, date = row
                shamsi_date = gregorian_to_shamsi(date) if date else "ثبت نشده"
                weight_display = f"{weight:,.0f} گرم / {weight/1000:.2f} کیلوگرم" if weight else "0"
                price_display = f"{price:,.0f}" if price else "0"
                value_display = f"{value:,.0f} تومان" if value else "0"
                shelf_display = shelf or "نامشخص"
                tag = 'evenrow' if i % 2 == 0 else 'oddrow'
                self.inventory_tree.insert("", "end", values=(
                    shamsi_date,    # آخرین ستون (راست‌ترین)
                    shelf_display,
                    value_display,
                    price_display,
                    weight_display,
                    name,
                    herb_id
                ), tags=(tag,))
                total_weight += weight or 0
                total_value += value or 0
            self.inventory_count_var.set(f"تعداد گیاهان: {len(results)}")
            self.inventory_total_weight_var.set(f"وزن کل: {total_weight:,.0f} گرم")
            self.inventory_total_value_var.set(f"ارزش کل: {total_value:,.0f} تومان")
        except Exception as e:
            messagebox.showerror("خطا", f"خطا در بارگیری گزارش موجودی: {str(e)}")

    def setup_drugs_tab(self):
        """Set up the drugs report tab"""
        # Control frame for filters
        control_frame = ttk.Frame(self.drugs_tab)
        control_frame.pack(fill="x", padx=10, pady=5)

        # Date range filters
        date_frame = ttk.LabelFrame(control_frame, text="بازه زمانی",labelanchor="ne")
        date_frame.pack(side=tk.RIGHT, padx=5, pady=5)

        ttk.Label(date_frame, text="از تاریخ").grid(row=0, column=4, padx=5, pady=2)
        self.from_date = ttk.Entry(date_frame, width=12)
        self.from_date.grid(row=0, column=3, padx=5, pady=2)

        ttk.Label(date_frame, text="تا تاریخ").grid(row=0, column=2, padx=5, pady=2)
        self.to_date = ttk.Entry(date_frame, width=12)
        self.to_date.grid(row=0, column=1, padx=5, pady=2)

        # Disease filter
        disease_frame = ttk.Frame(control_frame)
        disease_frame.pack(side=tk.RIGHT, padx=5, pady=5)

        ttk.Label(disease_frame, text="گروه دارویی").pack(side=tk.RIGHT, padx=5)

        # Get diseases for combobox
        diseases = self.cursor.execute("SELECT name FROM Diseases ORDER BY name").fetchall()
        disease_names = ["همه"] + [d[0] for d in diseases]

        self.disease_filter = ttk.Combobox(
            disease_frame,
            values=disease_names,
            width=15,
            state="readonly"
        )
        self.disease_filter.current(0)
        self.disease_filter.pack(side=tk.RIGHT, padx=5)

        # Apply filters button
        ttk.Button(
            control_frame,
            text="اعمال فیلتر",
            command=self.filter_drugs_report
        ).pack(side=tk.LEFT, padx=5, pady=5)

        # Drugs report treeview
        #columns = ("id", "name", "production_date", "diseases", "compositions", "total_cost", "retail_price", "profit")
        #columns = ("profit", "retail_price", "total_cost", "compositions", "diseases", "production_date", "name", "id")
        columns = ("profit", "retail_price", "total_cost", "type", "compositions", "diseases", "production_date", "name", "id")
        tree = ttk.Treeview(self.drugs_tab, columns=columns, show="headings", height=15)
        self.drugs_tree = tree # Assign to instance variable
        self.report_treeviews[2] = tree # Store reference

        # Set up columns
        tree.heading("id", text="کد دارو")
        tree.heading("name", text="نام دارو")
        tree.heading("production_date", text="تاریخ تولید")
        tree.heading("diseases", text=" گروه دارویی")
        tree.heading("compositions", text="تعداد ترکیبات")
        tree.heading("type", text="نوع تولید")
        tree.heading("total_cost", text="هزینه تولید")
        tree.heading("retail_price", text="قیمت فروش")
        tree.heading("profit", text="سود")

        tree.column("id", width=60, anchor=tk.CENTER)
        tree.column("name", width=120, anchor=tk.CENTER)
        tree.column("production_date", width=120, anchor=tk.CENTER)
        tree.column("diseases", width=120, anchor=tk.CENTER)
        tree.column("compositions", width=120, anchor=tk.CENTER)
        tree.column("type", width=120, anchor=tk.CENTER)
        tree.column("total_cost", width=120, anchor=tk.CENTER)
        tree.column("retail_price", width=130, anchor=tk.CENTER)
        tree.column("profit", width=120, anchor=tk.CENTER)

        # Add scrollbar
        drugs_scrollbar = ttk.Scrollbar(self.drugs_tab, orient="vertical", command=tree.yview)
        tree.configure(yscrollcommand=drugs_scrollbar.set)

        # Pack treeview and scrollbar
        drugs_scrollbar.pack(side=tk.LEFT, fill="y")
        tree.pack(side=tk.RIGHT, fill="both", expand=True, padx=10, pady=5)

        # Apply alternating row colors
        tree.tag_configure('oddrow', background='#f3f4f6')
        tree.tag_configure('evenrow', background='#ffffff')

        # Summary frame
        summary_frame = ttk.Frame(self.drugs_tab)
        summary_frame.pack(fill="x", padx=10, pady=5)

        # Summary labels
        self.drugs_count_var = tk.StringVar(value="تعداد داروها: 0")
        self.drugs_total_cost_var = tk.StringVar(value="هزینه کل: 0 تومان")
        self.drugs_total_profit_var = tk.StringVar(value="سود کل: 0 تومان")

        # نمایش لیبل‌ها به صورت عمودی (زیر هم)
        ttk.Label(summary_frame, textvariable=self.drugs_count_var).pack(anchor=tk.E, padx=10, pady=2)
        ttk.Label(summary_frame, textvariable=self.drugs_total_cost_var).pack(anchor=tk.E, padx=10, pady=2)
        ttk.Label(summary_frame, textvariable=self.drugs_total_profit_var).pack(anchor=tk.E, padx=10, pady=2)

        # Load initial data
        self.filter_drugs_report()

    def filter_drugs_report(self):
        """Filter and load drugs report based on filters"""
        from_date = self.from_date.get().strip()
        to_date = self.to_date.get().strip()
        disease = self.disease_filter.get()
        # --- تبدیل تاریخ شمسی به میلادی برای کوئری ---
        if from_date:
            from_date = shamsi_to_gregorian(from_date) or ""
        if to_date:
            to_date = shamsi_to_gregorian(to_date) or ""
        base_query = """
            SELECT
                d.id,
                d.name,
                d.production_date,
                GROUP_CONCAT(ds.name, '، ') AS diseases,
                COUNT(DISTINCT dc.herb_id) AS composition_count,
                d.total_workshop_cost,
                d.retail_price,
                (d.retail_price - d.total_workshop_cost) AS profit,
                dt.name AS drug_type_name -- Select type name from DrugTypes
            FROM Drugs d
            LEFT JOIN DrugDiseases dd ON d.id = dd.drug_id
            LEFT JOIN Diseases ds ON dd.disease_id = ds.id
            LEFT JOIN DrugCompositions dc ON d.id = dc.drug_id
            LEFT JOIN DrugTypes dt ON d.drug_type_id = dt.type_id -- Join with DrugTypes
        """
        conditions = []
        params = []
        if from_date:
            conditions.append("d.production_date >= ?")
            params.append(from_date)
        if to_date:
            conditions.append("d.production_date <= ?")
            params.append(to_date)
        if disease and disease != "همه":
            conditions.append("ds.name = ?")
            params.append(disease)
        if conditions:
            base_query += " WHERE " + " AND ".join(conditions)

        #base_query += " GROUP BY d.id ORDER BY d.production_date DESC"
        # Group by all selected non-aggregated columns
        base_query += " GROUP BY d.id, d.name, d.production_date, d.total_workshop_cost, d.retail_price, dt.name"
        base_query += " ORDER BY d.production_date DESC"
        self.load_drugs_report(base_query, params)

    def load_drugs_report(self, query, params=[]):
        """Load drugs report based on query"""
        for item in self.drugs_tree.get_children():
            self.drugs_tree.delete(item)
        try:
            if params:
                drugs = self.cursor.execute(query, params).fetchall()
            else:
                drugs = self.cursor.execute(query).fetchall()
            total_cost = 0
            total_profit = 0
            for i, drug in enumerate(drugs):
                # Unpack the fetched data including drug_type_name
                drug_id, name, prod_date, diseases, comp_count, total_cost_val, retail_price, profit, drug_type_name = drug
                shamsi_date = gregorian_to_shamsi(prod_date) if prod_date else ""
                diseases_display = diseases if diseases else "بدون گروه دارویی"
                cost_display = f"{total_cost_val:,.0f}" if total_cost_val else "0"
                price_display = f"{retail_price:,.0f}" if retail_price else "0"
                profit_display = f"{profit:,.0f}" if profit else "0"
                drug_type_display = drug_type_name if drug_type_name else "نامشخص" # Handle NULL type
                tag = 'evenrow' if i % 2 == 0 else 'oddrow'
                self.drugs_tree.insert("", "end", values=(
                    profit_display,
                    price_display,
                    cost_display,
                    drug_type_display, # Use the fetched drug type name
                    comp_count,
                    diseases_display,
                    shamsi_date,  # تاریخ تولید (شمسی)
                    name,
                    drug_id
                ), tags=(tag,))
                total_cost += total_cost_val or 0
                total_profit += profit or 0
            self.drugs_count_var.set(f"تعداد داروها: {len(drugs)}")
            self.drugs_total_cost_var.set(f"هزینه کل: {total_cost:,.0f} تومان")
            self.drugs_total_profit_var.set(f"سود کل: {total_profit:,.0f} تومان")
        except Exception as e:
            messagebox.showerror("خطا", f"خطا در بارگیری گزارش داروها: {str(e)}")

    def setup_financial_tab(self):
        """Set up the financial report tab"""
        # Create cards for financial summary
        summary_frame = ttk.Frame(self.financial_tab, padding=10)
        summary_frame.pack(fill="x", padx=10, pady=10)

        # Create cards
        card_data = [
            ("ارزش کل موجودی", "total_inventory_var", "#4CAF50"),
            ("هزینه کل تولید", "total_production_cost_var", "#F44336"),
            ("درآمد تخمینی", "estimated_revenue_var", "#2196F3"),
            ("سود ناخالص", "gross_profit_var", "#FFC107")
        ]

        # Create StringVars for dynamic updating
        self.financial_vars = {}
        for var_name in [data[1] for data in card_data]:
            self.financial_vars[var_name] = tk.StringVar(value="0 تومان")

        # Create cards
        for i, (title, var_name, color) in enumerate(card_data):
            card_frame = ttk.Frame(summary_frame, padding=10)
            card_frame.grid(row=0, column=i, padx=10, pady=5, sticky="nsew")

            ttk.Label(
                card_frame,
                text=title,
                font=("Arial", 10, "bold")
            ).pack(anchor="center")

            ttk.Label(
                card_frame,
                textvariable=self.financial_vars[var_name],
                font=("Arial", 12, "bold")
            ).pack(anchor="center", pady=5)

            # Add colored indicator
            indicator = tk.Canvas(card_frame, width=50, height=5, highlightthickness=0)
            indicator.pack(anchor="center", pady=5)
            indicator.create_rectangle(0, 0, 50, 5, fill=color, outline="")

        # Configure grid
        summary_frame.columnconfigure(0, weight=1)
        summary_frame.columnconfigure(1, weight=1)
        summary_frame.columnconfigure(2, weight=1)
        summary_frame.columnconfigure(3, weight=1)

        # Monthly report frame
        monthly_frame = ttk.LabelFrame(self.financial_tab, text="گزارش ماهیانه",labelanchor="ne")
        monthly_frame.pack(fill="both", expand=True, padx=10, pady=5)

        # Monthly report treeview
        #columns = ("month", "drug_count", "cost", "revenue", "profit")
        columns = ("profit", "revenue", "cost", "drug_count", "month")
        tree = ttk.Treeview(monthly_frame, columns=columns, show="headings", height=10)
        self.monthly_tree = tree # Assign to instance variable
        self.report_treeviews[3] = tree # Store reference

        # Set up columns
        tree.heading("month", text="ماه")
        tree.heading("drug_count", text="تعداد داروهای تولید شده")
        tree.heading("cost", text="هزینه تولید")
        tree.heading("revenue", text="درآمد تخمینی")
        tree.heading("profit", text="سود")

        tree.column("month", width=100, anchor=tk.CENTER)
        tree.column("drug_count", width=150, anchor=tk.CENTER)
        tree.column("cost", width=150, anchor=tk.E)
        tree.column("revenue", width=150, anchor=tk.E)
        tree.column("profit", width=150, anchor=tk.E)

        # Add scrollbar
        monthly_scrollbar = ttk.Scrollbar(monthly_frame, orient="vertical", command=tree.yview)
        tree.configure(yscrollcommand=monthly_scrollbar.set)

        # Pack treeview and scrollbar
        monthly_scrollbar.pack(side=tk.LEFT, fill="y")
        tree.pack(side=tk.RIGHT, fill="both", expand=True, pady=5)

        # Apply alternating row colors
        tree.tag_configure('oddrow', background='#f3f4f6')
        tree.tag_configure('evenrow', background='#ffffff')

        # Year filter frame
        filter_frame = ttk.Frame(self.financial_tab)
        filter_frame.pack(fill="x", padx=10, pady=5)

        ttk.Label(filter_frame, text="سال").pack(side=tk.RIGHT, padx=5)

        # Get unique years from database
        years = self.get_available_years()

        self.year_filter = ttk.Combobox(
            filter_frame,
            values=years,
            width=10,
            state="readonly"
        )

        # Set default to current year or first available
        current_year = datetime.now().year
        if str(current_year) in years:
            self.year_filter.set(str(current_year))
        elif years:
            self.year_filter.current(0)

        self.year_filter.pack(side=tk.RIGHT, padx=5)
        self.year_filter.bind("<<ComboboxSelected>>", self.update_financial_report)

        # Load initial data
        self.update_financial_report()

    def get_available_years(self):
        """Get available years from production dates"""
        try:
            self.cursor.execute("SELECT DISTINCT substr(production_date, 1, 4) FROM Drugs ORDER BY 1 DESC")
            years = [row[0] for row in self.cursor.fetchall()]
            return years or [str(datetime.now().year)]
        except:
            return [str(datetime.now().year)]

    def update_financial_report(self, event=None):
        """Update financial report based on selected year"""
        year = self.year_filter.get()

        # Clear existing items
        for item in self.monthly_tree.get_children():
            self.monthly_tree.delete(item)

        try:
            # Get monthly data
            query = """
                SELECT
                    substr(production_date, 6, 2) as month,
                    COUNT(*) as drug_count,
                    SUM(total_workshop_cost) as total_cost,
                    SUM(retail_price) as total_revenue,
                    SUM(retail_price - total_workshop_cost) as total_profit
                FROM Drugs
                WHERE substr(production_date, 1, 4) = ?
                GROUP BY month
                ORDER BY month
            """

            monthly_data = self.cursor.execute(query, (year,)).fetchall()

            # Month names mapping
            month_names = {
                "01": "فروردین",
                "02": "اردیبهشت",
                "03": "خرداد",
                "04": "تیر",
                "05": "مرداد",
                "06": "شهریور",
                "07": "مهر",
                "08": "آبان",
                "09": "آذر",
                "10": "دی",
                "11": "بهمن",
                "12": "اسفند"
            }

            # Process results
            annual_cost = 0
            annual_revenue = 0
            annual_profit = 0

            for i, data in enumerate(monthly_data):
                month_num, count, cost, revenue, profit = data

                # Format display values
                month_name = month_names.get(month_num, month_num)
                count_display = f"{count}"
                cost_display = f"{cost:,.0f} تومان" if cost else "0 تومان"
                revenue_display = f"{revenue:,.0f} تومان" if revenue else "0 تومان"
                profit_display = f"{profit:,.0f} تومان" if profit else "0 تومان"

                # Add to treeview with alternating colors
                tag = 'evenrow' if i % 2 == 0 else 'oddrow'
                self.monthly_tree.insert("", "end", values=(
                    profit_display,
                    revenue_display,
                    cost_display,
                    count_display,
                    month_name

                ), tags=(tag,))

                # Update annual totals
                annual_cost += cost or 0
                annual_revenue += revenue or 0
                annual_profit += profit or 0

            # Get inventory value
            self.cursor.execute("SELECT SUM(unit_price * (current_weight / 1000.0)) FROM Herbs")
            inventory_value = self.cursor.fetchone()[0] or 0

            # Update financial summary
            self.financial_vars["total_inventory_var"].set(f"{inventory_value:,.0f} تومان")
            self.financial_vars["total_production_cost_var"].set(f"{annual_cost:,.0f} تومان")
            self.financial_vars["estimated_revenue_var"].set(f"{annual_revenue:,.0f} تومان")
            self.financial_vars["gross_profit_var"].set(f"{annual_profit:,.0f} تومان")

        except Exception as e:
            messagebox.showerror("خطا", f"خطا در بارگیری گزارش مالی: {str(e)}")

    def create_report_toolbar(self):
        """Create toolbar for reports"""
        toolbar = ttk.Frame(self.parent)
        toolbar.pack(fill="x", padx=10, pady=5)

        buttons = [
            ("ذخیره گزارش", self.save_report),
            ("چاپ", self.print_report),
            ("خروجی PDF", self.export_pdf),
            ("خروجی Excel", self.export_excel),
            ("بررسی موجودی", self.check_low_inventory),  # دکمه جدید
            ("بارگذاری مجدد", self.reload_reports)
        ]

        for text, command in buttons:
            ttk.Button(toolbar, text=text, command=command).pack(side="right", padx=5)

    def _get_report_data(self, report_type):
        """Helper function to get data and headers for the current report type."""
        treeview = self.report_treeviews.get(report_type)
        if not treeview:
            # Handle summary tab or invalid type
            if report_type == 0:
                 headers = ["عنوان", "مقدار"]
                 data = [
                    ["تعداد کل انواع گیاهان", self.total_herb_types_var.get()],
                    ["مجموع وزن کل گیاهان", self.total_herb_weight_var.get()],
                    ["ارزش کل موجودی انبار", self.total_inventory_value_var.get()],
                    ["تعداد کل داروهای تولید شده", self.total_drugs_produced_var.get()]
                 ]
                 return headers, data
            else:
                return [], [] # Return empty for invalid types

        headers = [treeview.heading(col)["text"] for col in treeview["columns"]]
        data = [treeview.item(item)['values'] for item in treeview.get_children()]
        return headers, data

    def generate_report(self):
        """Generate all reports"""
        try:
            # 1. Total Herb Types
            self.cursor.execute("SELECT COUNT(*) FROM Herbs")
            herb_count = self.cursor.fetchone()[0]
            self.total_herb_types_var.set(f"{herb_count:,}")

            # 2. Total Herb Weight
            self.cursor.execute("SELECT SUM(current_weight) FROM Herbs")
            total_weight_grams = self.cursor.fetchone()[0]
            if total_weight_grams is None: total_weight_grams = 0.0
            self.total_herb_weight_var.set(f"{total_weight_grams:,.0f}")

            # 3. Total Inventory Value
            self.cursor.execute("SELECT SUM(unit_price * (current_weight / 1000.0)) FROM Herbs")
            total_value = self.cursor.fetchone()[0]
            if total_value is None: total_value = 0.0
            self.total_inventory_value_var.set(f"{total_value:,.0f}")

            # 4. Total Drugs Produced
            self.cursor.execute("SELECT COUNT(*) FROM Drugs")
            drug_count = self.cursor.fetchone()[0]
            self.total_drugs_produced_var.set(f"{drug_count:,}")

            # 5. Latest activities
            self.load_latest_activities()

            # 6. Redraw chart
            self.draw_placeholder_chart()

            # 7. Refresh other reports if tabs exist
            self.filter_inventory_report()
            self.filter_drugs_report()
            self.update_financial_report()

        except Exception as e:
            messagebox.showerror("خطای گزارش", f"خطا در تولید گزارش: {str(e)}")

    def load_latest_activities(self):
        """Load latest activities (newest herbs and drugs)"""
        # Clear existing items
        for item in self.activity_tree.get_children():
            self.activity_tree.delete(item)

        try:
            # Get latest herbs
            self.cursor.execute(
                "SELECT purchase_date, 'گیاه', name, id FROM Herbs ORDER BY purchase_date DESC LIMIT 5"
            )
            latest_herbs = self.cursor.fetchall()

            # Get latest drugs
            self.cursor.execute(
                "SELECT production_date, 'دارو', name, id FROM Drugs ORDER BY production_date DESC LIMIT 5"
            )
            latest_drugs = self.cursor.fetchall()

            # Combine and sort
            activities = sorted(latest_herbs + latest_drugs, key=lambda x: x[0] if x[0] else "", reverse=True)

            # Display in treeview
            for i, activity in enumerate(activities[:10]):  # Show top 10
                date, act_type, name, id_val = activity

                # Convert date to Persian format
                if date:
                    try:
                        # Parse the Gregorian date
                        y, m, d = map(int, date.split('-'))
                        # Convert to Persian date
                        shamsi_date = jdatetime.date.fromgregorian(day=d, month=m, year=y).strftime("%Y/%m/%d")
                    except Exception:
                        shamsi_date = date  # Keep original if conversion fails
                else:
                    shamsi_date = ""

                # Add to treeview with alternating colors
                tag = 'evenrow' if i % 2 == 0 else 'oddrow'
                self.activity_tree.insert("", "end", values=(shamsi_date, act_type, name, id_val), tags=(tag,))

            # Apply alternating row colors
            self.activity_tree.tag_configure('oddrow', background='#f3f4f6')
            self.activity_tree.tag_configure('evenrow', background='#ffffff')

        except Exception as e:
            print(f"Error loading activities: {e}")

    def check_low_inventory(self):
        """Check for low inventory items and show warnings"""
        try:
            # Get threshold from settings
            self.cursor.execute("SELECT value FROM AppSettings WHERE key = 'low_inventory_threshold'")  
            result = self.cursor.fetchone()
            threshold = float(result[0]) if result else 100  # Default 100g

            # Find herbs with low inventory
            self.cursor.execute("""
                SELECT id, name, current_weight
                FROM Herbs
                WHERE current_weight < ?
                ORDER BY current_weight
            """, (threshold,))

            low_items = self.cursor.fetchall()

            if low_items:
                # Create warning window
                warning_window = tk.Toplevel(self.root)
                warning_window.title("هشدار موجودی کم")
                warning_window.geometry("500x400")

                # Add warning message
                ttk.Label(
                    warning_window,
                    text="موارد زیر دارای موجودی کمتر از حد مجاز هستند:",
                    font=("Tahoma", 11, "bold"),
                    foreground="red"
                ).pack(pady=10)

                # Create treeview for low items
                columns = ("id", "name", "weight")
                tree = ttk.Treeview(warning_window, columns=columns, show="headings")

                tree.heading("id", text="کد")
                tree.heading("name", text="نام گیاه")
                tree.heading("weight", text="موجودی (گرم)")

                tree.column("id", width=80, anchor="center")
                tree.column("name", width=200, anchor="center")
                tree.column("weight", width=120, anchor="center")

                # Add scrollbar
                scrollbar = ttk.Scrollbar(warning_window, orient="vertical", command=tree.yview)
                tree.configure(yscrollcommand=scrollbar.set)

                scrollbar.pack(side="right", fill="y")
                tree.pack(fill="both", expand=True, padx=10, pady=10)

                # Add items to tree
                for item in low_items:
                    tree.insert("", "end", values=(item[0], item[1], f"{item[2]:,.0f}"))

                # Close button
                ttk.Button(
                    warning_window,
                    text="بستن",
                    command=warning_window.destroy
                ).pack(pady=10)

        except Exception as e:
            messagebox.showerror("خطا", f"خطا در بررسی موجودی: {str(e)}")

    def save_report(self):
        """Save the current report"""
        # Get current tab
        current_tab = self.reports_notebook.index("current")
        tab_names = {
            0: "خلاصه_وضعیت",
            1: "موجودی_انبار",
            2: "داروهای_تولید_شده",
            3: "گزارش_مالی"
        }
        report_type = tab_names.get(current_tab, "گزارش")

        # Ask for file format
        formats = [
            ("Excel Files", "*.xlsx"),
            ("CSV Files", "*.csv"),
            ("Text Files", "*.txt")
        ]

        file_path = filedialog.asksaveasfilename(
            defaultextension=".xlsx",
            filetypes=formats,
            title="ذخیره گزارش",
            initialfile=f"{report_type}_{datetime.now().strftime('%Y%m%d_%H%M')}"
        )

        if not file_path:
            return  # User cancelled

        # Determine file type and save
        ext = file_path.split('.')[-1].lower()

        try:
            if ext == 'xlsx':
                self.export_excel(file_path) # Call the actual export function
            elif ext == 'pdf':
                self.export_pdf(file_path) # Call the actual export function
            elif ext == 'csv':
                self._save_as_csv(file_path, current_tab)
            elif ext == 'txt':
                self._save_as_text(file_path, current_tab)
            else:
                 messagebox.showwarning("فرمت نامعتبر", f"فرمت فایل '{ext}' پشتیبانی نمی‌شود.")
                 return

            messagebox.showinfo("موفق", f"گزارش با موفقیت در مسیر زیر ذخیره شد:\n{file_path}")

        except Exception as e:
            messagebox.showerror("خطا", f"خطا در ذخیره گزارش: {str(e)}")

    def _save_as_csv(self, file_path, report_type):
        """Save report as CSV"""
        headers, data = self._get_report_data(report_type)
        if not headers:
            messagebox.showerror("خطا", "داده‌ای برای ذخیره یافت نشد.")
            return

        # Write to CSV
        with open(file_path, 'w', newline='', encoding='utf-8-sig') as f:
            writer = csv.writer(f)
            writer.writerow(headers)
            writer.writerows(data)

    def _save_as_text(self, file_path, report_type):
        """Save report as plain text"""
        # Get report title based on type
        titles = {
            0: "گزارش خلاصه وضعیت",
            1: "گزارش موجودی انبار",
            2: "گزارش داروهای تولید شده",
            3: "گزارش مالی"
        }
        title = titles.get(report_type, "گزارش")

        headers, data = self._get_report_data(report_type)
        if not headers:
            messagebox.showerror("خطا", "داده‌ای برای ذخیره یافت نشد.")
            return

        # Prepare data
        lines = [
            title,
            f"تاریخ تولید گزارش: {datetime.now().strftime('%Y/%m/%d %H:%M')}",
            "-" * 80,
            ""
        ]

        # Add report data based on type
        if report_type == 0: # Summary
             lines.extend([f"{h}: {d[1]}" for h, d in zip(headers, data)])
        elif report_type == 3: # Financial (special handling)
             # Add financial summary
            lines.extend([
                "خلاصه مالی:",
                f"ارزش کل موجودی: {self.financial_vars['total_inventory_var'].get()}",
                f"هزینه کل تولید: {self.financial_vars['total_production_cost_var'].get()}",
                f"درآمد تخمینی: {self.financial_vars['estimated_revenue_var'].get()}",
                f"سود ناخالص: {self.financial_vars['gross_profit_var'].get()}",
                "",
                "گزارش ماهیانه:",
                "-" * 80
            ])
            # Add header and data rows for monthly report
            lines.append("\t".join(headers))
            lines.append("-" * 80)
            for row_data in data:
                lines.append("\t".join([str(v) for v in row_data]))
        else: # Inventory or Drugs
            # Add header
            lines.append("\t".join(headers))
            lines.append("-" * 80)
            # Add data rows
            for row_data in data:
                lines.append("\t".join([str(v) for v in row_data]))


        # Write to file
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write("\n".join(lines))

    def print_report(self):
        """Generate a temporary PDF and open it for printing."""
        if not REPORTLAB_AVAILABLE:
            messagebox.showerror("خطا", "کتابخانه reportlab برای چاپ مورد نیاز است.")
            return

        try:
            # Create a temporary PDF file
            temp_pdf_file = tempfile.NamedTemporaryFile(delete=False, suffix=".pdf")
            temp_pdf_path = temp_pdf_file.name
            temp_pdf_file.close() # Close the file handle so reportlab can write to it

            # Generate the PDF content into the temporary file
            self._generate_pdf_content(temp_pdf_path)

            # Open the PDF with the default system viewer/printer
            if sys.platform == "win32":
                os.startfile(temp_pdf_path, "print")
            elif sys.platform == "darwin": # macOS
                subprocess.call(["open", temp_pdf_path])
            else: # Linux
                subprocess.call(["xdg-open", temp_pdf_path])

            # Optionally, schedule deletion of the temp file after a delay
            # Or leave it for the OS to clean up in the temp directory

        except Exception as e:
            messagebox.showerror("خطا در چاپ", f"خطا در ایجاد یا باز کردن فایل PDF برای چاپ: {str(e)}")
            # Clean up temp file in case of error opening it
            if 'temp_pdf_path' in locals() and os.path.exists(temp_pdf_path):
                 try:
                     os.remove(temp_pdf_path)
                 except OSError:
                     pass # Ignore error if file is locked

    def _generate_pdf_content(self, file_path):
        """Internal helper to generate PDF content."""
        current_tab = self.reports_notebook.index("current")
        headers, data = self._get_report_data(current_tab)

        if not headers:
            raise ValueError("داده‌ای برای ایجاد PDF یافت نشد.")

        # Reverse data for RTL display in PDF table
        headers_rtl = headers[::-1]
        data_rtl = [row[::-1] for row in data]

        # Add headers to data for the table
        table_data = [headers_rtl] + data_rtl

        # Create PDF document
        doc = SimpleDocTemplate(file_path, pagesize=(11*inch, 8.5*inch)) # Landscape
        styles = getSampleStyleSheet()

        # --- Persian Style Definition ---
        # Use the registered Persian font if available, otherwise fallback
        persian_style = styles['Normal']
        persian_style.fontName = PERSIAN_FONT_NAME
        persian_style.fontSize = 10
        persian_style.alignment = 2 # Right Align (TA_RIGHT = 2)

        persian_style_body = styles['BodyText']
        persian_style_body.fontName = PERSIAN_FONT_NAME
        persian_style_body.fontSize = 10
        persian_style_body.alignment = 2 # Right Align

        persian_style_heading = styles['Heading1']
        persian_style_heading.fontName = PERSIAN_FONT_NAME
        persian_style_heading.fontSize = 14
        persian_style_heading.alignment = 1 # Center Align (TA_CENTER = 1)
        # --------------------------------

        story = []

        # Add title
        titles = {
            0: "گزارش خلاصه وضعیت", 1: "گزارش موجودی انبار",
            2: "گزارش داروهای تولید شده", 3: "گزارش مالی"
        }
        title_text = titles.get(current_tab, "گزارش")
        story.append(Paragraph(title_text, persian_style_heading))
        story.append(Spacer(1, 0.2*inch))

        # Create table
        table = Table(table_data, repeatRows=1) # Repeat headers on each page

        # Style the table
        style = TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey), # Header background
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke), # Header text color
            ('ALIGN', (0, 0), (-1, -1), 'RIGHT'), # Right align all cells
            ('FONTNAME', (0, 0), (-1, -1), PERSIAN_FONT_NAME), # Use Persian font
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12), # Header padding
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige), # Body background
            ('GRID', (0, 0), (-1, -1), 1, colors.black) # Grid lines
        ])
        table.setStyle(style)

        # Apply alternating row colors (requires iterating through data)
        row_num = len(table_data)
        for i in range(1, row_num):
            if i % 2 == 0:
                bc = colors.whitesmoke
            else:
                bc = colors.lightgrey
            ts = TableStyle([('BACKGROUND', (0, i), (-1, i), bc)])
            table.setStyle(ts)

        story.append(table)
        doc.build(story)


    def export_pdf(self, file_path=None):
        """Export the current report as PDF"""
        if not REPORTLAB_AVAILABLE:
            messagebox.showerror("خطا", "کتابخانه reportlab برای صدور PDF مورد نیاز است.")
            # Offer fallback to text
            if messagebox.askyesno("سوال", "آیا مایلید گزارش به صورت فایل متنی ذخیره شود؟"):
                 current_tab = self.reports_notebook.index("current")
                 txt_path = filedialog.asksaveasfilename(
                     defaultextension=".txt", filetypes=[("Text Files", "*.txt")],
                     title="ذخیره گزارش به صورت متن"
                 )
                 if txt_path:
                     self._save_as_text(txt_path, current_tab)
                     messagebox.showinfo("موفق", f"گزارش متنی ذخیره شد:\n{txt_path}")
            return

        if not file_path:
            file_path = filedialog.asksaveasfilename(
                defaultextension=".pdf",
                filetypes=[("PDF Files", "*.pdf")],
                title="ذخیره گزارش به صورت PDF"
            )
            if not file_path:
                return # User cancelled

        try:
            self._generate_pdf_content(file_path)
            # No success message here, handled by save_report
        except Exception as e:
            messagebox.showerror("خطا در صدور PDF", f"خطا در ایجاد فایل PDF: {str(e)}")
            raise # Re-raise for save_report to catch

    def export_excel(self, file_path=None):
        """Export the current report as Excel"""
        if not OPENPYXL_AVAILABLE:
            messagebox.showerror("خطا", "کتابخانه openpyxl برای صدور Excel مورد نیاز است.")
             # Offer fallback to CSV
            if messagebox.askyesno("سوال", "آیا مایلید گزارش به صورت فایل CSV ذخیره شود؟"):
                 current_tab = self.reports_notebook.index("current")
                 csv_path = filedialog.asksaveasfilename(
                     defaultextension=".csv", filetypes=[("CSV Files", "*.csv")],
                     title="ذخیره گزارش به صورت CSV"
                 )
                 if csv_path:
                     self._save_as_csv(csv_path, current_tab)
                     messagebox.showinfo("موفق", f"گزارش CSV ذخیره شد:\n{csv_path}")
            return

        if not file_path:
            file_path = filedialog.asksaveasfilename(
                defaultextension=".xlsx",
                filetypes=[("Excel Files", "*.xlsx")],
                title="ذخیره گزارش به صورت Excel"
            )
            if not file_path:
                return # User cancelled

        try:
            current_tab = self.reports_notebook.index("current")
            headers, data = self._get_report_data(current_tab)

            if not headers:
                raise ValueError("داده‌ای برای ایجاد Excel یافت نشد.")

            # Create workbook and sheet
            wb = openpyxl.Workbook()
            ws = wb.active

            # Set sheet direction to RTL
            ws.sheet_view.rightToLeft = True

            # Write headers
            ws.append(headers)

            # Apply header style
            header_font = Font(bold=True)
            for cell in ws[1]:
                cell.font = header_font
                cell.alignment = Alignment(horizontal='center')

            # Write data
            for row_data in data:
                ws.append(row_data)

            # Adjust column widths
            for col_idx, header in enumerate(headers, 1):
                column_letter = get_column_letter(col_idx)
                max_length = len(header)
                for row_idx in range(2, ws.max_row + 1):
                    cell_value = ws.cell(row=row_idx, column=col_idx).value
                    if cell_value:
                        # Basic length check, might need refinement for complex data
                        cell_len = len(str(cell_value))
                        if cell_len > max_length:
                            max_length = cell_len
                # Add some padding to the width
                adjusted_width = (max_length + 2) * 1.2
                ws.column_dimensions[column_letter].width = adjusted_width

            # Save workbook
            wb.save(file_path)
             # No success message here, handled by save_report

        except Exception as e:
            messagebox.showerror("خطا در صدور Excel", f"خطا در ایجاد فایل Excel: {str(e)}")
            raise # Re-raise for save_report to catch

    def reload_reports(self):
        """Reload all reports"""
        self.generate_report()

def shamsi_to_gregorian(shamsi_str):
    try:
        return jdatetime.datetime.strptime(shamsi_str, "%Y-%m-%d").togregorian().strftime("%Y-%m-%d")
    except Exception:
        return None

def gregorian_to_shamsi(gregorian_str):
    try:
        y, m, d = map(int, gregorian_str.split('-'))
        return jdatetime.date.fromgregorian(day=d, month=m, year=y).strftime("%Y-%m-%d")
    except Exception:
        return gregorian_str


def main():
    # Show login window first
    login_window = LoginWindow()
    current_user = login_window.run()

    if not current_user:
        return  # Exit if login failed

    # Continue with main application
    root = tk.Tk()
    root.title("برنامه مدیریت کارگاه")
    root.geometry("1400x650")
    #root.state('zoomed')  # Make window maximized
    # Pass the current_user dictionary directly
    app = HerbalFactoryApp(root, setup_database(), current_user)
    root.mainloop()
    # Ensure connection is closed if mainloop finishes
    try:
        conn.close()
    except NameError: # Handle case where conn might not be defined if setup failed
        pass


# -------------------- Run Application --------------------
if __name__ == "__main__":
    main()
