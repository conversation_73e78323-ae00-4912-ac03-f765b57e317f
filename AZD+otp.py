from config.api_config import api_config
import tkinter as tk
from tkinter import simpledialog,messagebox
import requests
import pandas as pd
from collections import defaultdict
from cryptography.fernet import Fernet
import random

# تولید یک کلید رمزگذاری
key = Fernet.generate_key()
cipher_suite = Fernet(key)
# رمز عبور پیش‌فرض (در محیط واقعی باید از فایل یا پایگاه داده استفاده کرد)
DEFAULT_PASSWORD = "123"
ENCRYPTED_PASSWORD = cipher_suite.encrypt(DEFAULT_PASSWORD.encode())

# تنظیمات API کاوه‌نگار
API_KEY = api_config.kavenegar
SENDER = "10004346"  # شماره فرستنده ثبت‌شده در کاوه‌نگار

# متغیرهای جهانی
otp_code = ""
phone_number = ""

def check_password(entered_password):
    try:
        decrypted_password = cipher_suite.decrypt(ENCRYPTED_PASSWORD).decode()
        return entered_password == decrypted_password
    except Exception:
        return False


def send_otp(phone_number, otp):
    try:
        url = f"https://api.kavenegar.com/v1/{API_KEY}/verify/lookup.json"
        params = {
            "receptor": phone_number,
            "token": otp,
            "template": "YourTemplateName"  # نام قالبی که در پنل کاوه‌نگار ساختید
        }
        response = requests.get(url, params=params)
        if response.status_code == 200:
            return True
        return False
    except Exception as e:
        print("Error sending OTP:", e)
        return False

def generate_otp():
    return str(random.randint(1000, 9999))  # کد ۴ رقمی تصادفی

def request_otp():
    global otp_code, phone_number
    phone_number = phone_entry.get().strip()
    
    # اعتبارسنجی شماره تلفن
    if not phone_number.isdigit() or len(phone_number) != 11:
        messagebox.showerror("خطا", "شماره تلفن معتبر نیست! (مثال: 09123456789)")
        return
    
    otp_code = generate_otp()
    if send_otp(phone_number, otp_code):
        messagebox.showinfo("موفق", f"کد OTP به شماره {phone_number} ارسال شد.")
    else:
        messagebox.showerror("خطا", "ارسال کد OTP با مشکل مواجه شد!")

def verify_otp():
    entered_otp = otp_entry.get().strip()
    if entered_otp == otp_code:
        messagebox.showinfo("موفق", "احراز هویت دو مرحله‌ای موفقیت‌آمیز بود!")
        # اینجا می‌توانید کاربر را به صفحه اصلی هدایت کنید
        login_window.destroy()  # بستن صفحه ورود
        open_main_app()         # باز کردن برنامه اصلی
    else:
        messagebox.showerror("خطا", "کد OTP نادرست است!")

# تابع برای باز کردن برنامه اصلی


# تابع برای صفحه ورود
def login():
    entered_password = password_entry.get()
    if check_password(entered_password):
        login_window.destroy()  # بستن صفحه ورود
        open_main_app()         # باز کردن برنامه اصلی
    else:
        messagebox.showerror("خطا", "رمز عبور اشتباه است!")


# لیست آدرس‌های معروف صرافی‌ها (برای تشخیص صرافی)
EXCHANGE_ADDRESSES = {
    "Binance": ["0x...", "0x..."],  # جایگزین کنید با آدرس‌های واقعی
    "Coinbase": ["0x...", "0x..."],
    "Kraken": ["0x...", "0x..."],
}

# تابع برای تشخیص صرافی
def detect_exchange(address):
    for exchange, addresses in EXCHANGE_ADDRESSES.items():
        if address in addresses:
            return exchange
    return "Unknown"

# تابع برای اضافه کردن دستی آدرس صرافی
def add_exchange_address():
    exchange_name = simpledialog.askstring("اضافه کردن صرافی", "نام صرافی را وارد کنید:")
    if not exchange_name:
        messagebox.showwarning("خطا", "نام صرافی وارد نشده است.")
        return
    exchange_address = simpledialog.askstring("اضافه کردن صرافی", "آدرس صرافی را وارد کنید:")
    if not exchange_address:
        messagebox.showwarning("خطا", "آدرس صرافی وارد نشده است.")
        return
       # اضافه کردن آدرس صرافی به لیست
    if exchange_name not in EXCHANGE_ADDRESSES:
        EXCHANGE_ADDRESSES[exchange_name] = []
    EXCHANGE_ADDRESSES[exchange_name].append(exchange_address)

    # نمایش پیام موفقیت
    messagebox.showinfo("موفق", f"آدرس صرافی {exchange_name} با موفقیت اضافه شد.")

# تابع برای دریافت تراکنش‌های توکن‌های ERC-20 (اتریوم)
def get_erc20_transactions(address):
    api_key = api_config.eth
    url = f"https://api.etherscan.io/api?module=account&action=tokentx&address={address}&startblock=0&endblock=********&sort=asc&apikey={api_key}"
    response = requests.get(url)
    data = response.json()

    transactions = []
    if data["status"] == "1":
        for tx in data["result"]:
            from_address = tx["from"]
            to_address = tx["to"]
            value = int(tx["value"]) / 10**int(tx["tokenDecimal"])  # تبدیل به مقدار قابل خواندن
            token = tx["tokenSymbol"]
            transactions.append({
                "from": from_address,
                "to": to_address,
                "value": value,
                "token": token
            })
    return transactions

# تابع برای دریافت تراکنش‌های توکن‌های BEP-20 (Binance Smart Chain)
def get_bep20_transactions(address):
    api_key = api_config.bsc
    url = f"https://api.bscscan.com/api?module=account&action=tokentx&address={address}&startblock=0&endblock=********&sort=asc&apikey={api_key}"
    response = requests.get(url)
    data = response.json()

    transactions = []
    if data["status"] == "1":
        for tx in data["result"]:
            from_address = tx["from"]
            to_address = tx["to"]
            value = int(tx["value"]) / 10**int(tx["tokenDecimal"])  # تبدیل به مقدار قابل خواندن
            token = tx["tokenSymbol"]
            transactions.append({
                "from": from_address,
                "to": to_address,
                "value": value,
                "token": token
            })
    return transactions

def get_bitcoin_transactions(address):
    url = f"https://blockchain.info/rawaddr/{address}"
    response = requests.get(url)
    data = response.json()

    transactions = []
    for tx in data["txs"]:
        for input_ in tx.get("inputs", []):
            from_address = input_.get("prev_out", {}).get("addr", "Unknown")
            value = input_.get("prev_out", {}).get("value", 0) / 1e8  # تبدیل به BTC
            token = "BTC"
            to_address = tx.get("out", [{}])[0].get("addr", "Unknown")
            transactions.append({
                "from": from_address,
                "to": to_address,
                "value": value,
                "token": token
            })
    return transactions

# تابع برای دریافت تراکنش‌ها (مثال برای اتریوم)

def get_ethereum_transactions(address):
    api_key = api_config.eth
    url = f"https://api.etherscan.io/api?module=account&action=txlist&address={address}&startblock=0&endblock=********&sort=asc&apikey={api_key}"
    response = requests.get(url)
    data = response.json()

    transactions = []
    if data["status"] == "1":
        for tx in data["result"]:
            from_address = tx["from"]
            to_address = tx["to"]
            value = int(tx["value"]) / 10**18  # تبدیل به ETH
            token = "ETH"
            transactions.append({
                "from": from_address,
                "to": to_address,
                "value": value,
                "token": token
            })
    return transactions

def get_binance_transactions(address):
    api_key = api_config.bsc
    url = f"https://api.bscscan.com/api?module=account&action=txlist&address={address}&startblock=0&endblock=********&sort=asc&apikey={api_key}"
    response = requests.get(url)
    data = response.json()

    transactions = []
    if data["status"] == "1":
        for tx in data["result"]:
            from_address = tx["from"]
            to_address = tx["to"]
            value = int(tx["value"]) / 10**18  # تبدیل به BNB
            token = "BNB"
            transactions.append({
                "from": from_address,
                "to": to_address,
                "value": value,
                "token": token
            })
    return transactions

def get_transactions():
    address = address_entry.get()
    blockchain = blockchain_var.get()
    transaction_type = transaction_type_var.get()

    if blockchain == "Bitcoin":
        transactions = get_bitcoin_transactions(address)
    elif blockchain == "Ethereum":
        if transaction_type == "Token":
            transactions = get_erc20_transactions(address)
        else:
            transactions = get_ethereum_transactions(address)
    elif blockchain == "Binance Smart Chain":
        if transaction_type == "Token":
            transactions = get_bep20_transactions(address)
        else:
            transactions = get_binance_transactions(address)
    else:
        messagebox.showerror("خطا", "بلاک‌چین انتخابی معتبر نیست.")
        return

    process_transactions(transactions)
# تابع اصلی برای دریافت تراکنش‌ها

def process_transactions(transactions):
    data = []
    seen_pairs = {}

    for tx in transactions:
        from_address = tx["from"]
        to_address = tx["to"]
        value = tx["value"]
        token = tx["token"]

        # تشخیص صرافی‌ها
        from_exchange = detect_exchange(from_address)
        to_exchange = detect_exchange(to_address)

        # شمارش تعداد تکرار
        pair = (from_address, to_address)
        if pair in seen_pairs:
            seen_pairs[pair] += 1
        else:
            seen_pairs[pair] = 1

        # افزودن به داده‌ها
        data.append([
            from_address,
            from_exchange,
            to_address,
            to_exchange,
            token,
            value,
            seen_pairs[pair]
        ])

    # نمایش داده‌ها در جدول
    display_data(data)

# تابع برای نمایش داده‌ها در جدول
def display_data(data):
    result_text.delete(1.0, tk.END)
    result_text.insert(tk.END, "آدرس مبدا | نوع صرافی مبدا | آدرس مقصد | نوع صرافی مقصد | توکن | مقدار ارز | تعداد تکرار\n")
    result_text.insert(tk.END, "-" * 100 + "\n")

    for row in data:
        result_text.insert(tk.END, " | ".join(map(str, row)) + "\n")

# تابع برای ذخیره داده‌ها به صورت اکسل
def save_to_excel():
    data = result_text.get(1.0, tk.END).split("\n")[2:]  # جدا کردن داده‌ها از جدول
    rows = [row.split(" | ") for row in data if row.strip()]
    df = pd.DataFrame(rows, columns=["آدرس مبدا", "نوع صرافی مبدا", "آدرس مقصد", "نوع صرافی مقصد", "توکن", "مقدار ارز", "تعداد تکرار"])
    df.to_excel("transactions.xlsx", index=False)
    messagebox.showinfo("موفق", "داده‌ها با موفقیت در فایل اکسل ذخیره شدند.")


# تابع برای باز کردن برنامه اصلی
def open_main_app():
    global root
    
        
    # ایجاد رابط کاربری
    root = tk.Tk()
    root.title("رهگیری آدرس کیف پول")
    root.geometry("1200x800")

    tk.Label(root, text="آدرس کیف پول:").pack(pady=10)
    address_entry = tk.Entry(root, width=50)
    address_entry.pack(pady=5)

    #tk.Button(root, text="اضافه کردن آدرس صرافی", command=add_exchange_address).pack(pady=5)
    add_exchange_button = tk.Button(root, text="اضافه کردن آدرس صرافی", command=add_exchange_address)
    add_exchange_button.pack(pady=5)

    tk.Label(root, text="انتخاب بلاک‌چین:").pack(pady=5)
    blockchain_var = tk.StringVar(root)
    blockchain_var.set("Bitcoin")  # مقدار پیش‌فرض
    blockchain_menu = tk.OptionMenu(root, blockchain_var, "Bitcoin", "Ethereum", "Binance Smart Chain")
    blockchain_menu.pack(pady=5)


    tk.Label(root, text="نوع تراکنش:").pack(pady=5)
    transaction_type_var = tk.StringVar(root)
    transaction_type_var.set("All")  # مقدار پیش‌فرض
    transaction_type_menu = tk.OptionMenu(root, transaction_type_var, "All", "Token")
    transaction_type_menu.pack(pady=5)




    tk.Button(root, text="دریافت تراکنش‌ها", command=get_transactions).pack(pady=10)
    tk.Button(root, text="ذخیره به اکسل", command=save_to_excel).pack(pady=5)

    result_text = tk.Text(root, height=30, width=150)
    result_text.pack(pady=10)

    root.mainloop()



# ایجاد صفحه ورود
login_window = tk.Tk()
login_window.title("ورود به برنامه")
login_window.geometry("500x300")

#tk.Label(login_window, text="رمز عبور را وارد کنید:").pack(pady=10)
#password_entry = tk.Entry(login_window, show="*", width=30)
#password_entry.pack(pady=5)

#tk.Button(login_window, text="ورود", command=login).pack(pady=10)
'''
tk.Label(login_window, text="شماره تلفن خود را وارد کنید:").pack(pady=10)
phone_entry = tk.Entry(login_window, width=30)
phone_entry.pack(pady=5)

tk.Button(login_window, text="دریافت کد OTP", command=request_otp).pack(pady=10)

tk.Label(login_window, text="کد OTP را وارد کنید:").pack(pady=10)
otp_entry = tk.Entry(login_window, show="*", width=30)
otp_entry.pack(pady=5)

tk.Button(login_window, text="تأیید کد OTP", command=verify_otp).pack(pady=10)
'''
# فیلد شماره تلفن
tk.Label(login_window, text="شماره تلفن:").pack()
phone_entry = tk.Entry(login_window)
phone_entry.pack()

# دکمه درخواست OTP
tk.Button(login_window, text="دریافت کد OTP", command=request_otp).pack(pady=10)

# فیلد وارد کردن OTP
tk.Label(login_window, text="کد OTP:").pack()
otp_entry = tk.Entry(login_window)
otp_entry.pack()

# دکمه تأیید OTP
tk.Button(login_window, text="تأیید کد", command=verify_otp).pack(pady=10)



login_window.mainloop()


