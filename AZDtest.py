from config.api_config import api_config
import tkinter as tk
import sys
import random
import requests
import pandas as pd
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                            QLabel, QLineEdit, QPushButton, QTextEdit, QComboBox, 
                            QMessageBox, QInputDialog)
from PyQt5.QtCore import Qt
from config import api_config
from cryptography.fernet import Fernet

# کلید رمزنگاری
key = Fernet.generate_key()
cipher_suite = Fernet(key)
DEFAULT_PASSWORD = "123"
ENCRYPTED_PASSWORD = cipher_suite.encrypt(DEFAULT_PASSWORD.encode())

# تنظیمات API کاوه‌نگار
API_KEY = api_config.kavenegar
SENDER = "10004346"  # شماره فرستنده ثبت‌شده در کاوه‌نگار


class LoginWindow(QWidget):
    def __init__(self):
        super().__init__()
        self.otp_code = ""
        self.phone_number = ""
        self.init_ui()
        
    def init_ui(self):
        self.setWindowTitle("ورود به سیستم")
        self.setGeometry(300, 300, 400, 300)
        
        layout = QVBoxLayout()
        
        # ورودی شماره تلفن
        self.phone_label = QLabel("شماره تلفن:")
        self.phone_entry = QLineEdit()
        self.phone_entry.setPlaceholderText("09123456789")
        
        # دکمه دریافت OTP
        self.get_otp_btn = QPushButton("دریافت کد تأیید")
        self.get_otp_btn.clicked.connect(self.request_otp)
        
        # ورودی کد OTP
        self.otp_label = QLabel("کد تأیید:")
        self.otp_entry = QLineEdit()
        
        # دکمه تأیید
        self.verify_btn = QPushButton("تأیید ورود")
        self.verify_btn.clicked.connect(self.verify_otp)
        
        layout.addWidget(self.phone_label)
        layout.addWidget(self.phone_entry)
        layout.addWidget(self.get_otp_btn)
        layout.addWidget(self.otp_label)
        layout.addWidget(self.otp_entry)
        layout.addWidget(self.verify_btn)
        
        self.setLayout(layout)
    
    def request_otp(self):
        self.phone_number = self.phone_entry.text()
        
        if not self.phone_number.isdigit() or len(self.phone_number) != 11:
            QMessageBox.warning(self, "خطا", "شماره تلفن معتبر نیست! (مثال: 09123456789)")
            return
        
        self.otp_code = str(random.randint(1000, 9999))
        if self.send_otp(self.phone_number, self.otp_code):
            QMessageBox.information(self, "موفق", f"کد OTP به شماره {self.phone_number} ارسال شد.")
        else:
            QMessageBox.critical(self, "خطا", "ارسال کد OTP با مشکل مواجه شد!")
    
    def send_otp(self, phone_number, otp):
        try:
            url = f"https://api.kavenegar.com/v1/{API_KEY}/verify/lookup.json"
            params = {
                "receptor": phone_number,
                "token": otp,
                "template": "YourTemplateName"
            }
            response = requests.get(url, params=params)
            return response.status_code == 200
        except Exception as e:
            print("Error sending OTP:", e)
            return False
    
    def verify_otp(self):
        entered_otp = self.otp_entry.text()
        if entered_otp == self.otp_code:
            self.main_app = MainApp()
            self.main_app.show()
            self.close()
        else:
            QMessageBox.critical(self, "خطا", "کد OTP نادرست است!")

class MainApp(QMainWindow):
    def __init__(self):
        super().__init__()
        self.exchange_addresses = {
            "Binance": ["0x...", "0x..."],
            "Coinbase": ["0x...", "0x..."],
            "Kraken": ["0x...", "0x..."]
        }
        self.init_ui()
        
    def init_ui(self):
        self.setWindowTitle("رهگیری آدرس کیف پول")
        self.setGeometry(100, 100, 1200, 800)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QVBoxLayout()
        
        # بخش ورودی اطلاعات
        input_layout = QHBoxLayout()
        
        # ورودی آدرس کیف پول
        self.address_label = QLabel("آدرس کیف پول:")
        self.address_entry = QLineEdit()
        
        # انتخاب بلاکچین
        self.blockchain_label = QLabel("بلاکچین:")
        self.blockchain_combo = QComboBox()
        self.blockchain_combo.addItems(["Bitcoin", "Ethereum", "Binance Smart Chain"])
        
        # انتخاب نوع تراکنش
        self.tx_type_label = QLabel("نوع تراکنش:")
        self.tx_type_combo = QComboBox()
        self.tx_type_combo.addItems(["All", "Token"])
        
        input_layout.addWidget(self.address_label)
        input_layout.addWidget(self.address_entry)
        input_layout.addWidget(self.blockchain_label)
        input_layout.addWidget(self.blockchain_combo)
        input_layout.addWidget(self.tx_type_label)
        input_layout.addWidget(self.tx_type_combo)
        
        # بخش دکمه‌ها
        button_layout = QHBoxLayout()
        
        self.get_tx_btn = QPushButton("دریافت تراکنش‌ها")
        self.get_tx_btn.clicked.connect(self.get_transactions)
        
        self.add_exchange_btn = QPushButton("اضافه کردن صرافی")
        self.add_exchange_btn.clicked.connect(self.add_exchange_address)
        
        self.save_btn = QPushButton("ذخیره به اکسل")
        self.save_btn.clicked.connect(self.save_to_excel)
        
        button_layout.addWidget(self.get_tx_btn)
        button_layout.addWidget(self.add_exchange_btn)
        button_layout.addWidget(self.save_btn)
        
        # نمایش نتایج
        self.result_text = QTextEdit()
        self.result_text.setReadOnly(True)
        
        main_layout.addLayout(input_layout)
        main_layout.addLayout(button_layout)
        main_layout.addWidget(self.result_text)
        
        central_widget.setLayout(main_layout)
    
    def detect_exchange(self, address):
        for exchange, addresses in self.exchange_addresses.items():
            if address in addresses:
                return exchange
        return "Unknown"
    
    def add_exchange_address(self):
        exchange_name, ok = QInputDialog.getText(self, "اضافه کردن صرافی", "نام صرافی را وارد کنید:")
        if not ok or not exchange_name:
            return
            
        exchange_address, ok = QInputDialog.getText(self, "اضافه کردن صرافی", "آدرس صرافی را وارد کنید:")
        if not ok or not exchange_address:
            return
            
        if exchange_name not in self.exchange_addresses:
            self.exchange_addresses[exchange_name] = []
        self.exchange_addresses[exchange_name].append(exchange_address)
        
        QMessageBox.information(self, "موفق", f"آدرس صرافی {exchange_name} با موفقیت اضافه شد.")
    
    def get_transactions(self):
        address = self.address_entry.text()
        blockchain = self.blockchain_combo.currentText()
        tx_type = self.tx_type_combo.currentText()
        
        if not address:
            QMessageBox.warning(self, "خطا", "لطفاً آدرس کیف پول را وارد کنید.")
            return
            
        if blockchain == "Bitcoin":
            transactions = self.get_bitcoin_transactions(address)
        elif blockchain == "Ethereum":
            if tx_type == "Token":
                transactions = self.get_erc20_transactions(address)
            else:
                transactions = self.get_ethereum_transactions(address)
        elif blockchain == "Binance Smart Chain":
            if tx_type == "Token":
                transactions = self.get_bep20_transactions(address)
            else:
                transactions = self.get_binance_transactions(address)
        
        self.process_transactions(transactions)
    
    def process_transactions(self, transactions):
        data = []
        seen_pairs = {}
        
        for tx in transactions:
            from_address = tx.get("from", "Unknown")
            to_address = tx.get("to", "Unknown")
            value = tx.get("value", 0)
            token = tx.get("token", "Unknown")
            
            from_exchange = self.detect_exchange(from_address)
            to_exchange = self.detect_exchange(to_address)
            
            pair = (from_address, to_address)
            seen_pairs[pair] = seen_pairs.get(pair, 0) + 1
            
            data.append([
                from_address,
                from_exchange,
                to_address,
                to_exchange,
                token,
                str(value),
                str(seen_pairs[pair])
            ])
        
        self.display_data(data)
    
    def display_data(self, data):
        self.result_text.clear()
        header = " | ".join(["آدرس مبدا", "صرافی مبدا", "آدرس مقصد", "صرافی مقصد", "توکن", "مقدار", "تعداد"])
        self.result_text.append(header)
        self.result_text.append("-" * len(header))
        
        for row in data:
            self.result_text.append(" | ".join(row))
    
    def save_to_excel(self):
        data = []
        text = self.result_text.toPlainText()
        lines = text.split("\n")[2:]  # Skip header
        
        for line in lines:
            if line.strip():
                data.append(line.split(" | "))
        
        if not data:
            QMessageBox.warning(self, "خطا", "داده‌ای برای ذخیره وجود ندارد.")
            return
            
        df = pd.DataFrame(data, columns=["آدرس مبدا", "صرافی مبدا", "آدرس مقصد", "صرافی مقصد", "توکن", "مقدار", "تعداد"])
        df.to_excel("transactions.xlsx", index=False)
        QMessageBox.information(self, "موفق", "داده‌ها با موفقیت در فایل اکسل ذخیره شدند.")
    
    # توابع دریافت تراکنش‌ها (همانند نسخه اصلی)
    def get_erc20_transactions(self, address):
        api_key = api_config.eth
        url = f"https://api.etherscan.io/api?module=account&action=tokentx&address={address}&startblock=0&endblock=********&sort=asc&apikey={api_key}"
        response = requests.get(url)
        data = response.json()

        transactions = []
        if data["status"] == "1":
            for tx in data["result"]:
                transactions.append({
                    "from": tx["from"],
                    "to": tx["to"],
                    "value": int(tx["value"]) / 10**int(tx["tokenDecimal"]),
                    "token": tx["tokenSymbol"]
                })
        return transactions
    
    def get_bep20_transactions(self, address):
        api_key = api_config.bsc
        url = f"https://api.bscscan.com/api?module=account&action=tokentx&address={address}&startblock=0&endblock=********&sort=asc&apikey={api_key}"
        response = requests.get(url)
        data = response.json()

        transactions = []
        if data["status"] == "1":
            for tx in data["result"]:
                transactions.append({
                    "from": tx["from"],
                    "to": tx["to"],
                    "value": int(tx["value"]) / 10**int(tx["tokenDecimal"]),
                    "token": tx["tokenSymbol"]
                })
        return transactions
    
    def get_bitcoin_transactions(self, address):
        url = f"https://blockchain.info/rawaddr/{address}"
        response = requests.get(url)
        data = response.json()

        transactions = []
        for tx in data["txs"]:
            for input_ in tx.get("inputs", []):
                transactions.append({
                    "from": input_.get("prev_out", {}).get("addr", "Unknown"),
                    "to": tx.get("out", [{}])[0].get("addr", "Unknown"),
                    "value": input_.get("prev_out", {}).get("value", 0) / 1e8,
                    "token": "BTC"
                })
        return transactions
    
    def get_ethereum_transactions(self, address):
        api_key = api_config.eth
        url = f"https://api.etherscan.io/api?module=account&action=txlist&address={address}&startblock=0&endblock=********&sort=asc&apikey={api_key}"
        response = requests.get(url)
        data = response.json()

        transactions = []
        if data["status"] == "1":
            for tx in data["result"]:
                transactions.append({
                    "from": tx["from"],
                    "to": tx["to"],
                    "value": int(tx["value"]) / 10**18,
                    "token": "ETH"
                })
        return transactions
    
    def get_binance_transactions(self, address):
        api_key = api_config.bsc
        url = f"https://api.bscscan.com/api?module=account&action=txlist&address={address}&startblock=0&endblock=********&sort=asc&apikey={api_key}"
        response = requests.get(url)
        data = response.json()

        transactions = []
        if data["status"] == "1":
            for tx in data["result"]:
                transactions.append({
                    "from": tx["from"],
                    "to": tx["to"],
                    "value": int(tx["value"]) / 10**18,
                    "token": "BNB"
                })
        return transactions

if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    # نمایش پنجره ورود اولیه
    login_window = LoginWindow()
    login_window.show()
    
    sys.exit(app.exec_())