import tkinter as tk
from tkinter import ttk, messagebox
import sqlite3
from datetime import datetime

# ایجاد دیتابیس و جداول
def init_db():
    conn = sqlite3.connect('pharmacy1.db')
    cursor = conn.cursor()
    
    # جدول مواد اولیه
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS materials (
        id INTEGER PRIMARY KEY,
        code TEXT UNIQUE,
        name TEXT,
        price REAL,
        inventory REAL,
        shelf TEXT
    )
    ''')
    
    # جدول داروها
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS drugs (
        id INTEGER PRIMARY KEY,
        code TEXT UNIQUE,
        name TEXT,
        date TEXT,
        total_price REAL,
        representative_price REAL,
        wholesale_price REAL,
        retail_price REAL
    )
    ''')
    
    # جدول فرمولاسیون داروها
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS formulations (
        id INTEGER PRIMARY KEY,
        drug_id INTEGER,
        material_id INTEGER,
        weight REAL,
        FOREIGN KEY(drug_id) REFERENCES drugs(id),
        FOREIGN KEY(material_id) REFERENCES materials(id)
    )
    ''')
    
    conn.commit()
    conn.close()

# کلاس اصلی برنامه
class PharmacyApp:
    def __init__(self, root):
        self.root = root
        self.root.title("نرم‌افزار کارگاه داروسازی طب سنتی")
        self.root.geometry("1000x700")
        
        # ایجاد تب‌ها
        self.notebook = ttk.Notebook(root)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # تب مدیریت داروها
        self.drug_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.drug_tab, text="مدیریت داروها")
        
        # تب مواد اولیه
        self.material_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.material_tab, text="مواد اولیه")
        
        # تب قیمت‌گذاری
        self.pricing_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.pricing_tab, text="محاسبه قیمت")
        
        # مقداردهی اولیه رابط کاربری
        self.init_drug_tab()
        self.init_material_tab()
        self.init_pricing_tab()
        #self.update_pricing_combos()
        # بارگذاری داده‌های اولیه
        self.load_materials()
        self.load_drugs()

    # بخش مدیریت داروها
    def init_drug_tab(self):
        # ورودی‌ها
        ttk.Label(self.drug_tab, text="کد دارو:").grid(row=0, column=0, padx=5, pady=5)
        self.drug_code = ttk.Entry(self.drug_tab)
        self.drug_code.grid(row=0, column=1, padx=5, pady=5)
        
        ttk.Label(self.drug_tab, text="نام دارو:").grid(row=1, column=0, padx=5, pady=5)
        self.drug_name = ttk.Entry(self.drug_tab)
        self.drug_name.grid(row=1, column=1, padx=5, pady=5)
        
        ttk.Label(self.drug_tab, text="تاریخ:").grid(row=2, column=0, padx=5, pady=5)
        self.drug_date = ttk.Entry(self.drug_tab)
        self.drug_date.insert(0, datetime.now().strftime("%Y.%m.%d"))
        self.drug_date.grid(row=2, column=1, padx=5, pady=5)
        
        # دکمه‌ها
        ttk.Button(self.drug_tab, text="ذخیره دارو", command=self.save_drug).grid(row=3, column=1, pady=10)
        
        # جدول نمایش داروها
        self.drug_tree = ttk.Treeview(self.drug_tab, columns=("code", "name", "date"), show="headings")
        self.drug_tree.heading("code", text="کد دارو")
        self.drug_tree.heading("name", text="نام دارو")
        self.drug_tree.heading("date", text="تاریخ")
        self.drug_tree.grid(row=4, column=0, columnspan=2, padx=10, pady=10, sticky="nsew")
        
        # اسکرول بار
        scrollbar = ttk.Scrollbar(self.drug_tab, orient="vertical", command=self.drug_tree.yview)
        scrollbar.grid(row=4, column=2, sticky="ns")
        self.drug_tree.configure(yscrollcommand=scrollbar.set)
        
        # انتخاب دارو
        self.drug_tree.bind("<<TreeviewSelect>>", self.on_drug_select)

    # بخش مواد اولیه
    def init_material_tab(self):
        # ورودی‌ها
        ttk.Label(self.material_tab, text="کد ماده:").grid(row=0, column=0, padx=5, pady=5)
        self.material_code = ttk.Entry(self.material_tab)
        self.material_code.grid(row=0, column=1, padx=5, pady=5)
        
        ttk.Label(self.material_tab, text="نام ماده:").grid(row=1, column=0, padx=5, pady=5)
        self.material_name = ttk.Entry(self.material_tab)
        self.material_name.grid(row=1, column=1, padx=5, pady=5)
        
        ttk.Label(self.material_tab, text="قیمت:").grid(row=2, column=0, padx=5, pady=5)
        self.material_price = ttk.Entry(self.material_tab)
        self.material_price.grid(row=2, column=1, padx=5, pady=5)
        
        ttk.Label(self.material_tab, text="موجودی:").grid(row=3, column=0, padx=5, pady=5)
        self.material_inventory = ttk.Entry(self.material_tab)
        self.material_inventory.grid(row=3, column=1, padx=5, pady=5)
        
        ttk.Label(self.material_tab, text="قفسه:").grid(row=4, column=0, padx=5, pady=5)
        self.material_shelf = ttk.Entry(self.material_tab)
        self.material_shelf.grid(row=4, column=1, padx=5, pady=5)
        
        # دکمه‌ها
        ttk.Button(self.material_tab, text="ذخیره ماده", command=self.save_material).grid(row=5, column=1, pady=10)
        
        # جدول نمایش مواد
        self.material_tree = ttk.Treeview(self.material_tab, columns=("code", "name", "price", "inventory", "shelf"), show="headings")
        self.material_tree.heading("code", text="کد")
        self.material_tree.heading("name", text="نام ماده")
        self.material_tree.heading("price", text="قیمت")
        self.material_tree.heading("inventory", text="موجودی")
        self.material_tree.heading("shelf", text="قفسه")
        self.material_tree.grid(row=6, column=0, columnspan=2, padx=10, pady=10, sticky="nsew")
        
        # اسکرول بار
        scrollbar = ttk.Scrollbar(self.material_tab, orient="vertical", command=self.material_tree.yview)
        scrollbar.grid(row=6, column=2, sticky="ns")
        self.material_tree.configure(yscrollcommand=scrollbar.set)
        
        # انتخاب ماده
        self.material_tree.bind("<<TreeviewSelect>>", self.on_material_select)

    # بخش قیمت‌گذاری
    def init_pricing_tab(self):
        # انتخاب دارو
        ttk.Label(self.pricing_tab, text="انتخاب دارو:").grid(row=0, column=0, padx=5, pady=5)
        self.pricing_drug = ttk.Combobox(self.pricing_tab)
        self.pricing_drug.grid(row=0, column=1, padx=5, pady=5)
        
        # دکمه محاسبه
        ttk.Button(self.pricing_tab, text="محاسبه قیمت", command=self.calculate_prices).grid(row=1, column=1, pady=10)
        
        # نتایج قیمت‌گذاری
        ttk.Label(self.pricing_tab, text="قیمت تمام شده:").grid(row=2, column=0, padx=5, pady=5)
        self.total_price = ttk.Label(self.pricing_tab, text="0")
        self.total_price.grid(row=2, column=1, padx=5, pady=5)
        
        ttk.Label(self.pricing_tab, text="قیمت نمایندگی:").grid(row=3, column=0, padx=5, pady=5)
        self.representative_price = ttk.Label(self.pricing_tab, text="0")
        self.representative_price.grid(row=3, column=1, padx=5, pady=5)
        
        ttk.Label(self.pricing_tab, text="قیمت عمده:").grid(row=4, column=0, padx=5, pady=5)
        self.wholesale_price = ttk.Label(self.pricing_tab, text="0")
        self.wholesale_price.grid(row=4, column=1, padx=5, pady=5)
        
        ttk.Label(self.pricing_tab, text="قیمت خرده:").grid(row=5, column=0, padx=5, pady=5)
        self.retail_price = ttk.Label(self.pricing_tab, text="0")
        self.retail_price.grid(row=5, column=1, padx=5, pady=5)
        
        # جدول مواد تشکیل‌دهنده
        self.formulation_tree = ttk.Treeview(self.pricing_tab, columns=("material", "weight"), show="headings")
        self.formulation_tree.heading("material", text="ماده")
        self.formulation_tree.heading("weight", text="وزن")
        self.formulation_tree.grid(row=6, column=0, columnspan=2, padx=10, pady=10, sticky="nsew")

    # بارگذاری مواد اولیه
    def load_materials(self):
        conn = sqlite3.connect('pharmacy.db')
        cursor = conn.cursor()
        cursor.execute("SELECT code, name, price, inventory, shelf FROM materials")
        rows = cursor.fetchall()
        
        for row in self.material_tree.get_children():
            self.material_tree.delete(row)
            
        for row in rows:
            self.material_tree.insert("", tk.END, values=row)
        
        conn.close()

    # بارگذاری داروها
    def load_drugs(self):
        conn = sqlite3.connect('pharmacy.db')
        cursor = conn.cursor()
        cursor.execute("SELECT code, name, date FROM drugs")
        rows = cursor.fetchall()
        
        for row in self.drug_tree.get_children():
            self.drug_tree.delete(row)
            
        for row in rows:
            self.drug_tree.insert("", tk.END, values=row)
            
        # برای Combobox
        self.pricing_drug['values'] = [row[1] for row in rows]
        
        conn.close()

    # ذخیره ماده جدید
    def save_material(self):
        code = self.material_code.get()
        name = self.material_name.get()
        price = float(self.material_price.get())
        inventory = float(self.material_inventory.get())
        shelf = self.material_shelf.get()
        
        conn = sqlite3.connect('pharmacy.db')
        cursor = conn.cursor()
        
        try:
            cursor.execute(
                "INSERT INTO materials (code, name, price, inventory, shelf) VALUES (?, ?, ?, ?, ?)",
                (code, name, price, inventory, shelf)
            )
            conn.commit()
            messagebox.showinfo("موفق", "ماده با موفقیت ذخیره شد")
            self.load_materials()
        except sqlite3.IntegrityError:
            messagebox.showerror("خطا", "کد ماده تکراری است")
        
        conn.close()

    # ذخیره داروی جدید
    def save_drug(self):
        code = self.drug_code.get()
        name = self.drug_name.get()
        date = self.drug_date.get()
        
        conn = sqlite3.connect('pharmacy.db')
        cursor = conn.cursor()
        
        try:
            cursor.execute(
                "INSERT INTO drugs (code, name, date) VALUES (?, ?, ?)",
                (code, name, date)
            )
            conn.commit()
            messagebox.showinfo("موفق", "دارو با موفقیت ذخیره شد")
            self.load_drugs()
        except sqlite3.IntegrityError:
            messagebox.showerror("خطا", "کد دارو تکراری است")
        
        conn.close()

    # محاسبه قیمت‌ها
    def calculate_prices(self):
        drug_name = self.pricing_drug.get()
        
        conn = sqlite3.connect('pharmacy.db')
        cursor = conn.cursor()
        
        # دریافت اطلاعات دارو
        cursor.execute("SELECT id FROM drugs WHERE name=?", (drug_name,))
        drug_id = cursor.fetchone()[0]
        
        # محاسبه قیمت مواد اولیه
        cursor.execute('''
        SELECT SUM(m.price * f.weight) 
        FROM formulations f 
        JOIN materials m ON f.material_id = m.id 
        WHERE f.drug_id = ?
        ''', (drug_id,))
        base_price = cursor.fetchone()[0] or 0
        
        # هزینه‌های ثابت (مثال)
        tablet_cost = 3000  # قیمت قرص زنی
        packaging_cost = 4000  # هزینه ظرف و برچسب
        overhead_cost = 3600  # هزینه سربار
        
        total_cost = base_price + tablet_cost + packaging_cost + overhead_cost
        
        # قیمت‌گذاری
        representative_price = total_cost * 1.3  # 30% سود
        wholesale_price = representative_price * 1.2  # 20% سود اضافه
        retail_price = representative_price * 1.4  # 40% سود اضافه
        
        # نمایش نتایج
        self.total_price.config(text=f"{total_cost:,.0f} تومان")
        self.representative_price.config(text=f"{representative_price:,.0f} تومان")
        self.wholesale_price.config(text=f"{wholesale_price:,.0f} تومان")
        self.retail_price.config(text=f"{retail_price:,.0f} تومان")
        
        # نمایش فرمولاسیون
        cursor.execute('''
        SELECT m.name, f.weight 
        FROM formulations f 
        JOIN materials m ON f.material_id = m.id 
        WHERE f.drug_id = ?
        ''', (drug_id,))
        formulations = cursor.fetchall()
        
        for row in self.formulation_tree.get_children():
            self.formulation_tree.delete(row)
            
        for material, weight in formulations:
            self.formulation_tree.insert("", tk.END, values=(material, weight))
        
        conn.close()

    # انتخاب دارو
    def on_drug_select(self, event):
        selected = self.drug_tree.focus()
        if selected:
            values = self.drug_tree.item(selected, 'values')
            self.drug_code.delete(0, tk.END)
            self.drug_code.insert(0, values[0])
            self.drug_name.delete(0, tk.END)
            self.drug_name.insert(0, values[1])
            self.drug_date.delete(0, tk.END)
            self.drug_date.insert(0, values[2])

    # انتخاب ماده
    def on_material_select(self, event):
        """وقتی یک ماده در جدول انتخاب می‌شود"""
        selected = self.material_tree.selection()
        if not selected:
            return

        # پر کردن فیلدها با مقادیر ماده انتخاب شده
        values = self.material_tree.item(selected[0])['values']
        self.clear_material_fields()
        
        self.material_code.insert(0, values[0])
        self.material_name.insert(0, values[1])
        self.material_price.insert(0, values[2])
        self.material_inventory.insert(0, values[3])
        self.material_shelf.insert(0, values[4] if values[4] else "")

# اجرای برنامه
if __name__ == "__main__":
    init_db()
    root = tk.Tk()
    app = PharmacyApp(root)
    root.mainloop()