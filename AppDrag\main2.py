import tkinter as tk
from tkinter import ttk, messagebox
import sqlite3
from datetime import datetime
from jdatetime import datetime as jdatetime

# ایجاد دیتابیس و جداول
def init_db():
    conn = sqlite3.connect('pharmacy1.db')
    cursor = conn.cursor()
    
    # جدول مواد اولیه
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS materials (
        id INTEGER PRIMARY KEY,
        code TEXT UNIQUE,
        name TEXT,
        price REAL,
        inventory REAL,
        shelf TEXT
    )
    ''')
    
    # جدول داروها
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS drugs (
        id INTEGER PRIMARY KEY,
        code TEXT UNIQUE,
        name TEXT,
        date TEXT,
        total_price REAL,
        representative_price REAL,
        wholesale_price REAL,
        retail_price REAL
    )
    ''')
    
    # جدول فرمولاسیون داروها
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS formulations (
        id INTEGER PRIMARY KEY,
        drug_id INTEGER,
        material_id INTEGER,
        weight REAL,
        FOREIGN KEY(drug_id) REFERENCES drugs(id),
        FOREIGN KEY(material_id) REFERENCES materials(id)
    )
    ''')
    
    conn.commit()
    conn.close()

# کلاس اصلی برنامه
class PharmacyApp:
    def __init__(self, root):
        self.root = root
        self.root.title("نرم‌افزار کارگاه داروسازی طب سنتی")
        self.root.geometry("1200x800")
        
        # ایجاد تب‌ها
        self.notebook = ttk.Notebook(root)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # تب مدیریت داروها
        self.drug_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.drug_tab, text="مدیریت داروها")
        
        # تب مواد اولیه
        self.material_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.material_tab, text="مواد اولیه")
        
        # تب فرمولاسیون
        self.formulation_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.formulation_tab, text="فرمولاسیون")
        
        # تب قیمت‌گذاری
        self.pricing_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.pricing_tab, text="محاسبه قیمت")
        
        # مقداردهی اولیه رابط کاربری
        self.init_drug_tab()
        self.init_material_tab()
        self.init_formulation_tab()
        self.init_pricing_tab()
        self.update_pricing_combos()
        # بارگذاری داده‌های اولیه
        self.load_materials()
        self.load_drugs()

    def gregorian_to_jalali(self, date_str):
        """تبدیل تاریخ میلادی به شمسی"""
        try:
            g_date = datetime.strptime(date_str, "%Y.%m.%d")
            j_date = jdatetime.fromgregorian(datetime=g_date)
            return j_date.strftime("%Y.%m.%d")
        except:
            return date_str

    def jalali_to_gregorian(self, date_str):
        """تبدیل تاریخ شمسی به میلادی"""
        try:
            j_date = jdatetime.strptime(date_str, "%Y.%m.%d")
            g_date = j_date.togregorian()
            return g_date.strftime("%Y.%m.%d")
        except:
            return date_str

    def init_drug_tab(self):
        """ایجاد تب مدیریت داروها"""
        # فریم ورود اطلاعات
        input_frame = ttk.LabelFrame(self.drug_tab, text="اطلاعات دارو")
        input_frame.pack(fill=tk.X, padx=5, pady=5)

        # ردیف اول
        ttk.Label(input_frame, text="کد:").grid(row=0, column=0, padx=5, pady=5)
        self.drug_code = ttk.Entry(input_frame, width=15)
        self.drug_code.grid(row=0, column=1, padx=5, pady=5)

        ttk.Label(input_frame, text="نام:").grid(row=0, column=2, padx=5, pady=5)
        self.drug_name = ttk.Entry(input_frame, width=40)
        self.drug_name.grid(row=0, column=3, padx=5, pady=5)

        ttk.Label(input_frame, text="تاریخ:").grid(row=0, column=4, padx=5, pady=5)
        self.drug_date = ttk.Entry(input_frame, width=15)
        self.drug_date.grid(row=0, column=5, padx=5, pady=5)

        # ردیف دوم - قیمت‌ها
        ttk.Label(input_frame, text="قیمت تمام شده:").grid(row=1, column=0, padx=5, pady=5)
        self.drug_total_price = ttk.Entry(input_frame, width=15)
        self.drug_total_price.grid(row=1, column=1, padx=5, pady=5)

        ttk.Label(input_frame, text="قیمت نماینده:").grid(row=1, column=2, padx=5, pady=5)
        self.drug_representative_price = ttk.Entry(input_frame, width=15)
        self.drug_representative_price.grid(row=1, column=3, padx=5, pady=5)

        ttk.Label(input_frame, text="قیمت عمده:").grid(row=1, column=4, padx=5, pady=5)
        self.drug_wholesale_price = ttk.Entry(input_frame, width=15)
        self.drug_wholesale_price.grid(row=1, column=5, padx=5, pady=5)

        ttk.Label(input_frame, text="قیمت خرده:").grid(row=2, column=0, padx=5, pady=5)
        self.drug_retail_price = ttk.Entry(input_frame, width=15)
        self.drug_retail_price.grid(row=2, column=1, padx=5, pady=5)

        # دکمه‌ها
        btn_frame = ttk.Frame(input_frame)
        btn_frame.grid(row=3, column=0, columnspan=6, pady=10)

        ttk.Button(btn_frame, text="افزودن", command=self.add_drug).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="ویرایش", command=self.edit_drug).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="حذف", command=self.delete_drug).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="پاک کردن", command=self.clear_drug_fields).pack(side=tk.LEFT, padx=5)

        # جدول نمایش داروها
        table_frame = ttk.Frame(self.drug_tab)
        table_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # ایجاد Treeview
        self.drug_tree = ttk.Treeview(table_frame,
            columns=("code", "name", "date", "total_price", "representative_price", "wholesale_price", "retail_price"),
            show="headings")

        # تنظیم عناوین ستون‌ها
        self.drug_tree.heading("code", text="کد")
        self.drug_tree.heading("name", text="نام")
        self.drug_tree.heading("date", text="تاریخ")
        self.drug_tree.heading("total_price", text="قیمت تمام شده")
        self.drug_tree.heading("representative_price", text="قیمت نماینده")
        self.drug_tree.heading("wholesale_price", text="قیمت عمده")
        self.drug_tree.heading("retail_price", text="قیمت خرده")

        # تنظیم عرض ستون‌ها
        self.drug_tree.column("code", width=80)
        self.drug_tree.column("name", width=200)
        self.drug_tree.column("date", width=100)
        self.drug_tree.column("total_price", width=120)
        self.drug_tree.column("representative_price", width=120)
        self.drug_tree.column("wholesale_price", width=120)
        self.drug_tree.column("retail_price", width=120)

        # اضافه کردن اسکرول‌بار
        scrollbar = ttk.Scrollbar(table_frame, orient="vertical", command=self.drug_tree.yview)
        self.drug_tree.configure(yscrollcommand=scrollbar.set)

        self.drug_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # اتصال رویداد انتخاب سطر
        self.drug_tree.bind('<<TreeviewSelect>>', self.on_drug_select)

    def init_material_tab(self):
        """ایجاد تب مواد اولیه"""
        # فریم ورود اطلاعات
        input_frame = ttk.LabelFrame(self.material_tab, text="اطلاعات ماده")
        input_frame.pack(fill=tk.X, padx=5, pady=5)

        # ردیف اول
        ttk.Label(input_frame, text="کد:").grid(row=0, column=0, padx=5, pady=5)
        self.material_code = ttk.Entry(input_frame, width=15)
        self.material_code.grid(row=0, column=1, padx=5, pady=5)

        ttk.Label(input_frame, text="نام:").grid(row=0, column=2, padx=5, pady=5)
        self.material_name = ttk.Entry(input_frame, width=40)
        self.material_name.grid(row=0, column=3, padx=5, pady=5)

        # ردیف دوم
        ttk.Label(input_frame, text="قیمت (تومان):").grid(row=1, column=0, padx=5, pady=5)
        self.material_price = ttk.Entry(input_frame, width=15)
        self.material_price.grid(row=1, column=1, padx=5, pady=5)

        ttk.Label(input_frame, text="موجودی (گرم):").grid(row=1, column=2, padx=5, pady=5)
        self.material_inventory = ttk.Entry(input_frame, width=15)
        self.material_inventory.grid(row=1, column=3, padx=5, pady=5, sticky='w')

        # ردیف سوم
        ttk.Label(input_frame, text="قفسه:").grid(row=2, column=0, padx=5, pady=5)
        self.material_shelf = ttk.Entry(input_frame, width=15)
        self.material_shelf.grid(row=2, column=1, padx=5, pady=5)

        # دکمه‌ها
        btn_frame = ttk.Frame(input_frame)
        btn_frame.grid(row=3, column=0, columnspan=4, pady=10)

        ttk.Button(btn_frame, text="افزودن", command=self.add_material).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="ویرایش", command=self.edit_material).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="حذف", command=self.delete_material).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="پاک کردن", command=self.clear_material_fields).pack(side=tk.LEFT, padx=5)

        # جدول نمایش مواد
        table_frame = ttk.Frame(self.material_tab)
        table_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # ایجاد Treeview با اسکرول‌بار
        self.material_tree = ttk.Treeview(table_frame, 
            columns=("code", "name", "price", "inventory", "shelf"),
            show="headings")

        # تنظیم عناوین ستون‌ها
        self.material_tree.heading("code", text="کد")
        self.material_tree.heading("name", text="نام")
        self.material_tree.heading("price", text="قیمت (تومان)")
        self.material_tree.heading("inventory", text="موجودی (گرم)")
        self.material_tree.heading("shelf", text="قفسه")

        # تنظیم عرض ستون‌ها
        self.material_tree.column("code", width=100)
        self.material_tree.column("name", width=200)
        self.material_tree.column("price", width=150)
        self.material_tree.column("inventory", width=150)
        self.material_tree.column("shelf", width=100)

        # اضافه کردن اسکرول‌بار
        scrollbar = ttk.Scrollbar(table_frame, orient="vertical", command=self.material_tree.yview)
        self.material_tree.configure(yscrollcommand=scrollbar.set)

        self.material_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # اتصال رویداد انتخاب سطر
        self.material_tree.bind('<<TreeviewSelect>>', self.on_material_select)

    def add_material(self):
        """افزودن ماده جدید"""
        # دریافت مقادیر
        code = self.material_code.get()
        name = self.material_name.get()
        price = self.material_price.get()
        inventory = self.material_inventory.get()
        shelf = self.material_shelf.get()

        # بررسی فیلدهای اجباری
        if not all([code, name, price, inventory]):
            messagebox.showerror("خطا", "لطفاً فیلدهای ضروری را پر کنید.")
            return

        # تبدیل مقادیر عددی
        try:
            price = float(price)
            inventory = float(inventory)
            if price < 0 or inventory < 0:
                raise ValueError
        except ValueError:
            messagebox.showerror("خطا", "لطفاً مقادیر عددی معتبر وارد کنید.")
            return

        conn = sqlite3.connect('pharmacy1.db')
        cursor = conn.cursor()

        try:
            cursor.execute("""
                INSERT INTO materials (code, name, price, inventory, shelf)
                VALUES (?, ?, ?, ?, ?)
            """, (code, name, price, inventory, shelf))
            
            conn.commit()
            self.load_materials()
            self.clear_material_fields()
            self.update_formulation_combos()
            messagebox.showinfo("موفقیت", "ماده با موفقیت اضافه شد.")
            
        except sqlite3.IntegrityError:
            messagebox.showerror("خطا", "کد وارد شده تکراری است.")
        except sqlite3.Error as e:
            messagebox.showerror("خطا", f"خطا در ذخیره‌سازی: {str(e)}")
        finally:
            conn.close()

    def edit_material(self):
        """ویرایش ماده انتخاب شده"""
        selected = self.material_tree.selection()
        if not selected:
            messagebox.showerror("خطا", "لطفاً یک ماده را انتخاب کنید.")
            return

        # دریافت مقادیر
        code = self.material_code.get()
        name = self.material_name.get()
        price = self.material_price.get()
        inventory = self.material_inventory.get()
        shelf = self.material_shelf.get()

        # بررسی فیلدهای اجباری
        if not all([code, name, price, inventory]):
            messagebox.showerror("خطا", "لطفاً فیلدهای ضروری را پر کنید.")
            return

        # تبدیل مقادیر عددی
        try:
            price = float(price)
            inventory = float(inventory)
            if price < 0 or inventory < 0:
                raise ValueError
        except ValueError:
            messagebox.showerror("خطا", "لطفاً مقادیر عددی معتبر وارد کنید.")
            return

        conn = sqlite3.connect('pharmacy1.db')
        cursor = conn.cursor()

        try:
            cursor.execute("""
                UPDATE materials 
                SET name=?, price=?, inventory=?, shelf=?
                WHERE code=?
            """, (name, price, inventory, shelf, code))
            
            conn.commit()
            self.load_materials()
            self.clear_material_fields()
            messagebox.showinfo("موفقیت", "ماده با موفقیت ویرایش شد.")
            
        except sqlite3.Error as e:
            messagebox.showerror("خطا", f"خطا در به‌روزرسانی: {str(e)}")
        finally:
            conn.close()

    def delete_material(self):
        """حذف ماده انتخاب شده"""
        selected = self.material_tree.selection()
        if not selected:
            messagebox.showerror("خطا", "لطفاً یک ماده را انتخاب کنید.")
            return

        if not messagebox.askyesno("تأیید حذف", "آیا از حذف این ماده اطمینان دارید؟"):
            return

        code = self.material_code.get()
        
        conn = sqlite3.connect('pharmacy1.db')
        cursor = conn.cursor()

        try:
            # بررسی استفاده در فرمولاسیون
            cursor.execute("""
                SELECT COUNT(*) FROM formulations f
                JOIN materials m ON f.material_id = m.id
                WHERE m.code = ?
            """, (code,))
            
            if cursor.fetchone()[0] > 0:
                messagebox.showerror("خطا", "این ماده در فرمولاسیون داروها استفاده شده است و قابل حذف نیست.")
                return

            cursor.execute("DELETE FROM materials WHERE code=?", (code,))
            conn.commit()
            
            self.load_materials()
            self.clear_material_fields()
            self.update_formulation_combos()
            messagebox.showinfo("موفقیت", "ماده با موفقیت حذف شد.")
            
        except sqlite3.Error as e:
            messagebox.showerror("خطا", f"خطا در حذف: {str(e)}")
        finally:
            conn.close()

    def clear_material_fields(self):
        """پاک کردن فیلدهای فرم مواد"""
        self.material_code.delete(0, tk.END)
        self.material_name.delete(0, tk.END)
        self.material_price.delete(0, tk.END)
        self.material_inventory.delete(0, tk.END)
        self.material_shelf.delete(0, tk.END)

    def on_material_select(self, event):
        """وقتی یک ماده در جدول انتخاب می‌شود"""
        selected = self.material_tree.selection()
        if not selected:
            return

        # پر کردن فیلدها با مقادیر ماده انتخاب شده
        values = self.material_tree.item(selected[0])['values']
        self.clear_material_fields()
        
        self.material_code.insert(0, values[0])
        self.material_name.insert(0, values[1])
        self.material_price.insert(0, values[2])
        self.material_inventory.insert(0, values[3])
        self.material_shelf.insert(0, values[4] if values[4] else "")

    def init_pricing_tab(self):
        """ایجاد تب محاسبه قیمت"""
        # فریم انتخاب دارو
        drug_frame = ttk.LabelFrame(self.pricing_tab, text="انتخاب دارو")
        drug_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(drug_frame, text="دارو:").grid(row=0, column=0, padx=5, pady=5)
        self.pricing_drug_combo = ttk.Combobox(drug_frame, width=40)
        self.pricing_drug_combo.grid(row=0, column=1, padx=5, pady=5)
        self.pricing_drug_combo.bind('<<ComboboxSelected>>', self.on_pricing_drug_select)

        # فریم نمایش قیمت‌ها
        price_frame = ttk.LabelFrame(self.pricing_tab, text="قیمت‌ها")
        price_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # قیمت تمام شده
        ttk.Label(price_frame, text="قیمت تمام شده:").grid(row=0, column=0, padx=5, pady=5)
        self.pricing_total_price = ttk.Label(price_frame, text="0 تومان")
        self.pricing_total_price.grid(row=0, column=1, padx=5, pady=5)

        # قیمت نماینده
        ttk.Label(price_frame, text="قیمت نماینده:").grid(row=1, column=0, padx=5, pady=5)
        self.pricing_representative_price = ttk.Label(price_frame, text="0 تومان")
        self.pricing_representative_price.grid(row=1, column=1, padx=5, pady=5)

        # قیمت عمده
        ttk.Label(price_frame, text="قیمت عمده:").grid(row=2, column=0, padx=5, pady=5)
        self.pricing_wholesale_price = ttk.Label(price_frame, text="0 تومان")
        self.pricing_wholesale_price.grid(row=2, column=1, padx=5, pady=5)

        # قیمت خرده
        ttk.Label(price_frame, text="قیمت خرده:").grid(row=3, column=0, padx=5, pady=5)
        self.pricing_retail_price = ttk.Label(price_frame, text="0 تومان")
        self.pricing_retail_price.grid(row=3, column=1, padx=5, pady=5)

        # فریم محاسبات
        calc_frame = ttk.LabelFrame(self.pricing_tab, text="محاسبات")
        calc_frame.pack(fill=tk.X, padx=5, pady=5)

        # درصد سود نماینده
        ttk.Label(calc_frame, text="درصد سود نماینده:").grid(row=0, column=0, padx=5, pady=5)
        self.representative_profit = ttk.Entry(calc_frame, width=10)
        self.representative_profit.grid(row=0, column=1, padx=5, pady=5)
        self.representative_profit.insert(0, "20")

        # درصد سود عمده
        ttk.Label(calc_frame, text="درصد سود عمده:").grid(row=0, column=2, padx=5, pady=5)
        self.wholesale_profit = ttk.Entry(calc_frame, width=10)
        self.wholesale_profit.grid(row=0, column=3, padx=5, pady=5)
        self.wholesale_profit.insert(0, "30")

        # درصد سود خرده
        ttk.Label(calc_frame, text="درصد سود خرده:").grid(row=0, column=4, padx=5, pady=5)
        self.retail_profit = ttk.Entry(calc_frame, width=10)
        self.retail_profit.grid(row=0, column=5, padx=5, pady=5)
        self.retail_profit.insert(0, "40")

        # دکمه محاسبه
        ttk.Button(calc_frame, text="محاسبه قیمت‌ها", command=self.calculate_prices).grid(row=1, column=0, columnspan=6, pady=10)

    def on_pricing_drug_select(self, event):
        """وقتی دارویی در تب محاسبه قیمت انتخاب می‌شود"""
        drug_name = self.pricing_drug_combo.get()
        if not drug_name:
            return

        conn = sqlite3.connect('pharmacy1.db')
        cursor = conn.cursor()

        try:
            cursor.execute("""
                SELECT total_price, representative_price, wholesale_price, retail_price
                FROM drugs WHERE name = ?
            """, (drug_name,))
            
            row = cursor.fetchone()
            if row:
                self.pricing_total_price.config(text=f"{row[0]:,.0f} تومان")
                self.pricing_representative_price.config(text=f"{row[1]:,.0f} تومان")
                self.pricing_wholesale_price.config(text=f"{row[2]:,.0f} تومان")
                self.pricing_retail_price.config(text=f"{row[3]:,.0f} تومان")
            
        except sqlite3.Error as e:
            messagebox.showerror("خطا", f"خطا در بازیابی اطلاعات: {str(e)}")
        finally:
            conn.close()

    def calculate_prices(self):
        """محاسبه قیمت‌ها بر اساس درصد سود"""
        drug_name = self.pricing_drug_combo.get()
        if not drug_name:
            messagebox.showerror("خطا", "لطفاً یک دارو را انتخاب کنید.")
            return

        try:
            # دریافت درصدهای سود
            rep_profit = float(self.representative_profit.get()) / 100
            whole_profit = float(self.wholesale_profit.get()) / 100
            retail_profit = float(self.retail_profit.get()) / 100

            conn = sqlite3.connect('pharmacy1.db')
            cursor = conn.cursor()

            cursor.execute("SELECT total_price FROM drugs WHERE name = ?", (drug_name,))
            total_price = cursor.fetchone()[0]

            # محاسبه قیمت‌ها
            representative_price = total_price * (1 + rep_profit)
            wholesale_price = representative_price * (1 + whole_profit)
            retail_price = wholesale_price * (1 + retail_profit)

            # به‌روزرسانی نمایش
            self.pricing_total_price.config(text=f"{total_price:,.0f} تومان")
            self.pricing_representative_price.config(text=f"{representative_price:,.0f} تومان")
            self.pricing_wholesale_price.config(text=f"{wholesale_price:,.0f} تومان")
            self.pricing_retail_price.config(text=f"{retail_price:,.0f} تومان")

            # ذخیره قیمت‌های جدید
            cursor.execute("""
                UPDATE drugs 
                SET representative_price=?, wholesale_price=?, retail_price=?
                WHERE name=?
            """, (representative_price, wholesale_price, retail_price, drug_name))
            
            conn.commit()
            self.load_drugs()
            
        except ValueError:
            messagebox.showerror("خطا", "لطفاً درصدها را به صورت عددی وارد کنید.")
        except sqlite3.Error as e:
            messagebox.showerror("خطا", f"خطا در به‌روزرسانی قیمت‌ها: {str(e)}")
        finally:
            conn.close()

    def update_pricing_combos(self):
        """به‌روزرسانی لیست کشویی داروها در تب محاسبه قیمت"""
        conn = sqlite3.connect('pharmacy1.db')
        cursor = conn.cursor()
        
        try:
            cursor.execute("SELECT name FROM drugs ORDER BY name")
            drugs = [row[0] for row in cursor.fetchall()]
            self.pricing_drug_combo['values'] = drugs
            
        except sqlite3.Error as e:
            messagebox.showerror("خطا", f"خطا در بازیابی لیست داروها: {str(e)}")
        finally:
            conn.close()

    def init_formulation_tab(self):
        """ایجاد تب فرمولاسیون"""
        # فریم انتخاب دارو
        drug_frame = ttk.LabelFrame(self.formulation_tab, text="انتخاب دارو")
        drug_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(drug_frame, text="دارو:").grid(row=0, column=0, padx=5, pady=5)
        self.formulation_drug_combo = ttk.Combobox(drug_frame, width=40)
        self.formulation_drug_combo.grid(row=0, column=1, padx=5, pady=5)
        self.formulation_drug_combo.bind('<<ComboboxSelected>>', self.on_formulation_drug_select)

        # فریم افزودن ماده
        material_frame = ttk.LabelFrame(self.formulation_tab, text="افزودن ماده")
        material_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(material_frame, text="ماده:").grid(row=0, column=0, padx=5, pady=5)
        self.formulation_material_combo = ttk.Combobox(material_frame, width=40)
        self.formulation_material_combo.grid(row=0, column=1, padx=5, pady=5)

        ttk.Label(material_frame, text="مقدار (گرم):").grid(row=1, column=0, padx=5, pady=5)
        self.formulation_weight = ttk.Entry(material_frame, width=15)
        self.formulation_weight.grid(row=1, column=1, padx=5, pady=5, sticky='w')

        ttk.Button(material_frame, text="افزودن ماده", command=self.add_formulation_material).grid(row=2, column=1, pady=10, sticky='w')

        # جدول نمایش مواد
        table_frame = ttk.LabelFrame(self.formulation_tab, text="مواد تشکیل‌دهنده")
        table_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # ایجاد فریم برای جدول و اسکرول‌بار
        tree_frame = ttk.Frame(table_frame)
        tree_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.formulation_tree = ttk.Treeview(tree_frame, columns=("material", "weight", "price"), show="headings")
        self.formulation_tree.heading("material", text="نام ماده")
        self.formulation_tree.heading("weight", text="مقدار (گرم)")
        self.formulation_tree.heading("price", text="قیمت (تومان)")
        
        # تنظیم عرض ستون‌ها
        self.formulation_tree.column("material", width=200)
        self.formulation_tree.column("weight", width=100)
        self.formulation_tree.column("price", width=150)
        
        # اضافه کردن اسکرول‌بار
        scrollbar = ttk.Scrollbar(tree_frame, orient="vertical", command=self.formulation_tree.yview)
        self.formulation_tree.configure(yscrollcommand=scrollbar.set)
        
        self.formulation_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # دکمه‌های عملیات
        btn_frame = ttk.Frame(table_frame)
        btn_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(btn_frame, text="حذف ماده", command=self.remove_formulation_material).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="ویرایش مقدار", command=self.edit_formulation_material).pack(side=tk.LEFT, padx=5)

        # نمایش جمع کل
        total_frame = ttk.LabelFrame(self.formulation_tab, text="جمع کل")
        total_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(total_frame, text="جمع وزن:").grid(row=0, column=0, padx=5, pady=5)
        self.total_weight_label = ttk.Label(total_frame, text="0 گرم")
        self.total_weight_label.grid(row=0, column=1, padx=5, pady=5)

        ttk.Label(total_frame, text="جمع قیمت مواد:").grid(row=0, column=2, padx=5, pady=5)
        self.total_price_label = ttk.Label(total_frame, text="0 تومان")
        self.total_price_label.grid(row=0, column=3, padx=5, pady=5)

        # به‌روزرسانی لیست‌های کشویی
        self.update_formulation_combos()

    def update_formulation_combos(self):
        """به‌روزرسانی لیست‌های کشویی داروها و مواد"""
        # به‌روزرسانی لیست داروها
        conn = sqlite3.connect('pharmacy1.db')
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM drugs ORDER BY name")
        drugs = [row[0] for row in cursor.fetchall()]
        self.formulation_drug_combo['values'] = drugs

        # به‌روزرسانی لیست مواد
        cursor.execute("SELECT name FROM materials ORDER BY name")
        materials = [row[0] for row in cursor.fetchall()]
        self.formulation_material_combo['values'] = materials
        
        conn.close()

    def on_formulation_drug_select(self, event):
        """وقتی دارویی انتخاب می‌شود"""
        drug_name = self.formulation_drug_combo.get()
        if not drug_name:
            return

        # پاک کردن جدول قبلی
        for item in self.formulation_tree.get_children():
            self.formulation_tree.delete(item)

        # بارگذاری فرمولاسیون دارو
        conn = sqlite3.connect('pharmacy1.db')
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT m.name, f.weight, m.price 
            FROM formulations f 
            JOIN drugs d ON f.drug_id = d.id 
            JOIN materials m ON f.material_id = m.id 
            WHERE d.name = ?
            ORDER BY m.name
        """, (drug_name,))
        
        rows = cursor.fetchall()
        total_weight = 0
        total_price = 0
        
        for name, weight, price in rows:
            material_price = weight * price
            self.formulation_tree.insert("", tk.END, values=(name, f"{weight:,.1f}", f"{material_price:,.0f}"))
            total_weight += weight
            total_price += material_price

        self.total_weight_label.config(text=f"{total_weight:,.1f} گرم")
        self.total_price_label.config(text=f"{total_price:,.0f} تومان")
        
        conn.close()

    def add_formulation_material(self):
        """افزودن ماده به فرمولاسیون"""
        drug_name = self.formulation_drug_combo.get()
        material_name = self.formulation_material_combo.get()
        weight = self.formulation_weight.get()

        if not all([drug_name, material_name, weight]):
            messagebox.showerror("خطا", "لطفاً تمام فیلدها را پر کنید.")
            return

        try:
            weight = float(weight)
            if weight <= 0:
                raise ValueError
        except ValueError:
            messagebox.showerror("خطا", "لطفاً مقدار معتبر وارد کنید.")
            return

        conn = sqlite3.connect('pharmacy1.db')
        cursor = conn.cursor()

        try:
            # دریافت ID دارو و ماده
            cursor.execute("SELECT id FROM drugs WHERE name=?", (drug_name,))
            drug_id = cursor.fetchone()[0]
            
            cursor.execute("SELECT id, price FROM materials WHERE name=?", (material_name,))
            material_id, price = cursor.fetchone()

            # بررسی تکراری نبودن ماده
            cursor.execute("""
                SELECT id FROM formulations 
                WHERE drug_id=? AND material_id=?
            """, (drug_id, material_id))
            
            if cursor.fetchone():
                if messagebox.askyesno("ماده تکراری", "این ماده قبلاً اضافه شده است. آیا می‌خواهید مقدار آن به‌روز شود؟"):
                    cursor.execute("""
                        UPDATE formulations 
                        SET weight=weight+? 
                        WHERE drug_id=? AND material_id=?
                    """, (weight, drug_id, material_id))
            else:
                cursor.execute("""
                    INSERT INTO formulations (drug_id, material_id, weight)
                    VALUES (?, ?, ?)
                """, (drug_id, material_id, weight))

            conn.commit()
            
            # به‌روزرسانی نمایش
            self.on_formulation_drug_select(None)
            self.formulation_weight.delete(0, tk.END)
            self.formulation_material_combo.set('')
            
            messagebox.showinfo("موفقیت", "ماده با موفقیت اضافه شد.")
            
        except sqlite3.Error as e:
            messagebox.showerror("خطا", f"خطا در ذخیره‌سازی: {str(e)}")
        finally:
            conn.close()

    def remove_formulation_material(self):
        """حذف ماده از فرمولاسیون"""
        selected = self.formulation_tree.selection()
        if not selected:
            messagebox.showerror("خطا", "لطفاً یک ماده را انتخاب کنید.")
            return

        if not messagebox.askyesno("تأیید حذف", "آیا از حذف این ماده اطمینان دارید؟"):
            return

        drug_name = self.formulation_drug_combo.get()
        material_name = self.formulation_tree.item(selected[0])['values'][0]

        conn = sqlite3.connect('pharmacy1.db')
        cursor = conn.cursor()

        try:
            cursor.execute("""
                DELETE FROM formulations 
                WHERE drug_id=(SELECT id FROM drugs WHERE name=?) 
                AND material_id=(SELECT id FROM materials WHERE name=?)
            """, (drug_name, material_name))
            
            conn.commit()
            self.on_formulation_drug_select(None)
            messagebox.showinfo("موفقیت", "ماده با موفقیت حذف شد.")
            
        except sqlite3.Error as e:
            messagebox.showerror("خطا", f"خطا در حذف: {str(e)}")
        finally:
            conn.close()

    def load_materials(self):
        conn = sqlite3.connect('pharmacy1.db')
        cursor = conn.cursor()
        cursor.execute("SELECT code, name, price, inventory, shelf FROM materials")
        rows = cursor.fetchall()
        
        for row in self.material_tree.get_children():
            self.material_tree.delete(row)
            
        for row in rows:
            self.material_tree.insert("", tk.END, values=row)
        
        conn.close()

    def edit_formulation_material(self):
        """ویرایش مقدار ماده در فرمولاسیون"""
        selected = self.formulation_tree.selection()
        if not selected:
            messagebox.showerror("خطا", "لطفاً یک ماده را انتخاب کنید.")
            return

        drug_name = self.formulation_drug_combo.get()
        material_name = self.formulation_tree.item(selected[0])['values'][0]
        current_weight = float(self.formulation_tree.item(selected[0])['values'][1].replace(',', ''))

        # ایجاد پنجره ویرایش
        edit_window = tk.Toplevel(self.root)
        edit_window.title("ویرایش مقدار")
        edit_window.geometry("300x150")
        
        ttk.Label(edit_window, text=f"ویرایش مقدار برای {material_name}").pack(pady=5)
        
        weight_var = tk.StringVar(value=str(current_weight))
        weight_entry = ttk.Entry(edit_window, textvariable=weight_var)
        weight_entry.pack(pady=5)
        weight_entry.select_range(0, tk.END)
        weight_entry.focus()

        def save_edit():
            try:
                new_weight = float(weight_var.get())
                if new_weight <= 0:
                    raise ValueError
                
                conn = sqlite3.connect('pharmacy1.db')
                cursor = conn.cursor()
                
                cursor.execute("""
                    UPDATE formulations 
                    SET weight=? 
                    WHERE drug_id=(SELECT id FROM drugs WHERE name=?)
                    AND material_id=(SELECT id FROM materials WHERE name=?)
                """, (new_weight, drug_name, material_name))
                
                conn.commit()
                conn.close()
                
                self.on_formulation_drug_select(None)
                edit_window.destroy()
                messagebox.showinfo("موفقیت", "مقدار با موفقیت به‌روز شد.")
                
            except ValueError:
                messagebox.showerror("خطا", "لطفاً مقدار معتبر وارد کنید.")
            except sqlite3.Error as e:
                messagebox.showerror("خطا", f"خطا در به‌روزرسانی: {str(e)}")

        ttk.Button(edit_window, text="ذخیره", command=save_edit).pack(pady=5)
        ttk.Button(edit_window, text="انصراف", command=edit_window.destroy).pack(pady=5)

    def load_drugs(self):
        """بارگذاری لیست داروها"""
        if not hasattr(self, 'drug_tree'):
            return
            
        conn = sqlite3.connect('pharmacy1.db')
        cursor = conn.cursor()
        
        try:
            cursor.execute("SELECT code, name, date, total_price, representative_price, wholesale_price, retail_price FROM drugs ORDER BY name")
            rows = cursor.fetchall()
            
            for item in self.drug_tree.get_children():
                self.drug_tree.delete(item)
                
            for row in rows:
                self.drug_tree.insert("", tk.END, values=row)
                
        except sqlite3.Error as e:
            messagebox.showerror("خطا", f"خطا در بارگذاری داروها: {str(e)}")
        finally:
            conn.close()

    def on_drug_select(self, event):
        """وقتی یک دارو در جدول انتخاب می‌شود"""
        selected = self.drug_tree.selection()
        if not selected:
            return

        # پر کردن فیلدها با مقادیر داروی انتخاب شده
        values = self.drug_tree.item(selected[0])['values']
        self.clear_drug_fields()
        
        self.drug_code.insert(0, values[0])
        self.drug_name.insert(0, values[1])
        self.drug_date.insert(0, values[2])
        self.drug_total_price.insert(0, values[3] if values[3] else "")
        self.drug_representative_price.insert(0, values[4] if values[4] else "")
        self.drug_wholesale_price.insert(0, values[5] if values[5] else "")
        self.drug_retail_price.insert(0, values[6] if values[6] else "")

    def add_drug(self):
        """افزودن داروی جدید"""
        # دریافت مقادیر
        code = self.drug_code.get()
        name = self.drug_name.get()
        date = self.drug_date.get()
        total_price = self.drug_total_price.get()
        representative_price = self.drug_representative_price.get()
        wholesale_price = self.drug_wholesale_price.get()
        retail_price = self.drug_retail_price.get()

        # بررسی فیلدهای اجباری
        if not all([code, name, date]):
            messagebox.showerror("خطا", "لطفاً فیلدهای اجباری را پر کنید.")
            return

        # تبدیل قیمت‌ها به عدد
        try:
            total_price = float(total_price) if total_price else 0
            representative_price = float(representative_price) if representative_price else 0
            wholesale_price = float(wholesale_price) if wholesale_price else 0
            retail_price = float(retail_price) if retail_price else 0
        except ValueError:
            messagebox.showerror("خطا", "لطفاً قیمت‌ها را به صورت عددی وارد کنید.")
            return

        # تبدیل تاریخ شمسی به میلادی
        g_date = self.jalali_to_gregorian(date)

        conn = sqlite3.connect('pharmacy1.db')
        cursor = conn.cursor()

        try:
            cursor.execute("""
                INSERT INTO drugs (code, name, date, total_price, representative_price, wholesale_price, retail_price)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (code, name, g_date, total_price, representative_price, wholesale_price, retail_price))
            
            conn.commit()
            self.load_drugs()
            self.clear_drug_fields()
            messagebox.showinfo("موفقیت", "دارو با موفقیت اضافه شد.")
            
        except sqlite3.IntegrityError:
            messagebox.showerror("خطا", "کد وارد شده تکراری است.")
        except sqlite3.Error as e:
            messagebox.showerror("خطا", f"خطا در ذخیره‌سازی: {str(e)}")
        finally:
            conn.close()

    def edit_drug(self):
        """ویرایش داروی انتخاب شده"""
        selected = self.drug_tree.selection()
        if not selected:
            messagebox.showerror("خطا", "لطفاً یک دارو را انتخاب کنید.")
            return

        # دریافت مقادیر
        code = self.drug_code.get()
        name = self.drug_name.get()
        date = self.drug_date.get()
        total_price = self.drug_total_price.get()
        representative_price = self.drug_representative_price.get()
        wholesale_price = self.drug_wholesale_price.get()
        retail_price = self.drug_retail_price.get()

        # بررسی فیلدهای اجباری
        if not all([code, name, date]):
            messagebox.showerror("خطا", "لطفاً فیلدهای اجباری را پر کنید.")
            return

        # تبدیل قیمت‌ها به عدد
        try:
            total_price = float(total_price) if total_price else 0
            representative_price = float(representative_price) if representative_price else 0
            wholesale_price = float(wholesale_price) if wholesale_price else 0
            retail_price = float(retail_price) if retail_price else 0
        except ValueError:
            messagebox.showerror("خطا", "لطفاً قیمت‌ها را به صورت عددی وارد کنید.")
            return

        # تبدیل تاریخ شمسی به میلادی
        g_date = self.jalali_to_gregorian(date)

        conn = sqlite3.connect('pharmacy1.db')
        cursor = conn.cursor()

        try:
            cursor.execute("""
                UPDATE drugs 
                SET name=?, date=?, total_price=?, representative_price=?, wholesale_price=?, retail_price=?
                WHERE code=?
            """, (name, g_date, total_price, representative_price, wholesale_price, retail_price, code))
            
            conn.commit()
            self.load_drugs()
            self.clear_drug_fields()
            messagebox.showinfo("موفقیت", "دارو با موفقیت ویرایش شد.")
            
        except sqlite3.Error as e:
            messagebox.showerror("خطا", f"خطا در به‌روزرسانی: {str(e)}")
        finally:
            conn.close()

    def delete_drug(self):
        """حذف داروی انتخاب شده"""
        selected = self.drug_tree.selection()
        if not selected:
            messagebox.showerror("خطا", "لطفاً یک دارو را انتخاب کنید.")
            return

        if not messagebox.askyesno("تأیید حذف", "آیا از حذف این دارو اطمینان دارید؟"):
            return

        code = self.drug_code.get()
        
        conn = sqlite3.connect('pharmacy1.db')
        cursor = conn.cursor()

        try:
            # بررسی استفاده در فرمولاسیون
            cursor.execute("""
                SELECT COUNT(*) FROM formulations f
                JOIN drugs d ON f.drug_id = d.id
                WHERE d.code = ?
            """, (code,))
            
            if cursor.fetchone()[0] > 0:
                messagebox.showerror("خطا", "این دارو در فرمولاسیون استفاده شده است و قابل حذف نیست.")
                return

            cursor.execute("DELETE FROM drugs WHERE code=?", (code,))
            conn.commit()
            
            self.load_drugs()
            self.clear_drug_fields()
            messagebox.showinfo("موفقیت", "دارو با موفقیت حذف شد.")
            
        except sqlite3.Error as e:
            messagebox.showerror("خطا", f"خطا در حذف: {str(e)}")
        finally:
            conn.close()

    def clear_drug_fields(self):
        """پاک کردن فیلدهای فرم دارو"""
        self.drug_code.delete(0, tk.END)
        self.drug_name.delete(0, tk.END)
        self.drug_date.delete(0, tk.END)
        self.drug_total_price.delete(0, tk.END)
        self.drug_representative_price.delete(0, tk.END)
        self.drug_wholesale_price.delete(0, tk.END)
        self.drug_retail_price.delete(0, tk.END)

# اجرای برنامه
if __name__ == "__main__":
    init_db()
    root = tk.Tk()
    app = PharmacyApp(root)
    root.mainloop() 