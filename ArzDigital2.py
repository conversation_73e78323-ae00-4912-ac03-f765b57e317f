from config.api_config import api_config
import requests
import json
from datetime import datetime
import pandas as pd
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from tkinter.scrolledtext import ScrolledText
import webbrowser
import time
import random
from collections import Counter
from threading import Thread
from tkinter import font as tkfont
from tkinter import ttk
import numpy as np
import networkx as nx
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import math
import scipy as sp     




class ExchangeDetector:
    def __init__(self):
        self.patterns = {
            "Binance": {
                "BTC": ["1", "3", "bc1"],
                "ETH": ["0x"],
                "TRC20": ["Txxxxxxxx"],  # الگوی خاص Binance
            },
            "KuCoin": {
                "TRC20": ["Txxxxxx"],  # الگوی خاص KuCoin
            },
            "Trust Wallet": {
                "BEP2": ["bnb1"],
            },
            "Ethereum Classic": {  
                "ETC": ["0x"],  
            },  
            "Litecoin": {  
                "LTC": ["Lxxxxxxxx"],  
            },  
            "Ripple": {  
                "XRP": ["rxxxxxxxxxxxxxxxxxxxxxxxxxx"],  
            },  
            "Cardano": {  
                "ADA": ["addr1qxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"],  
            },  
            "Polkadot": {  
                "DOT": ["***************************"],  
            } 
        }
    
    def detect_by_pattern(self, address, currency):
        """تشخیص بر اساس الگوی استاتیک آدرس"""
        for exchange, patterns in self.patterns.items():
            if currency in patterns:
                for prefix in patterns[currency]:
                    if address.startswith(prefix):
                        return exchange
        return "Unknown"
    
    def detect_by_api(self, address, currency):
        """تشخیص با استفاده از APIهای بلوکچین"""
        if currency == "TRC20":
            try:
                url = f"https://apilist.tronscan.org/api/account?address={address}"
                response = requests.get(url).json()
                if "exchange" in response.get("data", {}).get("tag", "").lower():
                    return response["data"]["tag"]
            except:
                pass
        return "Unknown"


class CryptoWalletTrackerApp:
    def __init__(self, root):
        self.root = root    
        self.setup_main_window()
        self.create_widgets() 
        self.set_dark_theme()
        self.create_menu()
          # در __init__، دکمه جستجو را به این متد وصل کنید:
        self.search_btn.config(command=self.track_wallet_threaded)
        self.current_transactions = None
        self.stop_search_flag = False  # فلگ کنترل توقف جستجو
        self.search_thread = None      # ذخیره ریسه جستجو
       

    def setup_main_window(self):
        self.root.geometry("1300x800")
        self.root.resizable(True, True)
        self.root.title("برنامه تحلیل و ردیابی رمز ارزها ")
        #self.main_frame = ttk.Frame(self.root, padding="10")
        #self.main_frame.pack(fill=tk.BOTH, expand=True)

    def create_widgets(self):
        # عنوان برنامه
      
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        title_label = ttk.Label(main_frame, 
                              text="ردیاب تراکنش‌های کیف پول ارز دیجیتال",
                              font=('Tahoma', 12, 'bold'))
        title_label.pack(pady=2)
        
        input_frame = ttk.Frame(main_frame)
        input_frame.pack(fill=tk.X, pady=10)
        
        ttk.Label(input_frame, text="آدرس کیف پول:").pack(side=tk.LEFT, padx=5)
        
        self.wallet_entry = ttk.Entry(input_frame, width=60)
        self.wallet_entry.pack(side=tk.LEFT, expand=True, fill=tk.X, padx=5)
        
        self.search_btn = ttk.Button(input_frame, text="جستجو", command=self.track_wallet)
        self.search_btn.pack(side=tk.LEFT, padx=5)
        
        source_frame = ttk.LabelFrame(main_frame, text="منابع اطلاعاتی", padding=10)
        source_frame.pack(fill=tk.X, pady=10)
        
        # لیست منابع و متغیرها
        sources = [
            ("blockchain(bsc,eth,poly)", "blockchain_var"),       
            ("covalent(All)", "covalent_var"),
            ("blockcypher", "blockcypher_var"),
            ("Arkham Intelligence", "arkham_var"),
            ("Tronscan", "tronscan_var"),
            ("solana", "solana_var"), 
            ("Etherscan (ETH)", "etherscan_var"), 
            ("BscScan (BSC)", "bscscan_var")
                
        ]
        
        # مقداردهی اولیه متغیرها
        for _, var_name in sources:
            setattr(self, var_name, tk.BooleanVar(value=True))
        
        # ایجاد چک‌باکس‌ها به صورت جدولی
        for i, (text, var_name) in enumerate(sources):
            row = i // 3  # تقسیم به دو ستون
            col = i % 3
            ttk.Checkbutton(
                source_frame,
                text=text,
                variable=getattr(self, var_name)
            ).grid(
                row=row,
                column=col,
                sticky="w",
                padx=10,
                pady=1,
                ipadx=5,
                ipady=1
            )
        
        # تنظیمات گرید برای ظاهر بهتر
        source_frame.grid_columnconfigure(0, weight=1, uniform="group1")
        source_frame.grid_columnconfigure(1, weight=1, uniform="group1")
        
 
        result_frame = ttk.LabelFrame(main_frame, text="نتایج تراکنش‌ها", padding=10)
        result_frame.pack(fill=tk.BOTH, expand=True)
        


       # تنظیم ارتفاع سطرها - این بخش باید قبل از ایجاد Treeview باشد
        style = ttk.Style()
        style.theme_use('clam')  # استفاده از تمی که از rowheight پشتیبانی می‌کند
        style.configure('Treeview', 
               rowheight=30,
               font=('Tahoma', 9),
               background="#ffffff",
               fieldbackground="#ffffff")
        #self.tree = ttk.Treeview(result_frame, columns=('Source', 'From', 'To', 'Token', 'Amount', 'Date'), show='headings')
        #self.tree = ttk.Treeview(result_frame, columns=('Source', 'From', 'To', 'Token', 'Amounts', 'Dates', 'Count'), show='headings')
      
        self.tree = ttk.Treeview(result_frame, columns=(
            'Source', 'From', 'From Exchange', 'To', 'To Exchange', 
            'Token', 'Amounts', 'Dates', 'Count'
            ), show='headings')
        #self.tree.heading('Count', text='تعداد تکرار')
        #self.tree.column('Count', width=80)
           # تنظیمات ستون‌ها
        self.tree.heading('Source', text='منبع')
        self.tree.heading('From', text='مبدا')
        self.tree.heading('From Exchange', text='صرافی مبدا')
        self.tree.heading('To', text='مقصد')
        self.tree.heading('To Exchange', text='صرافی مقصد')
        self.tree.heading('Token', text='توکن')
        self.tree.heading('Amounts', text='مقادیر')
        self.tree.heading('Dates', text='تاریخ‌ها')
        self.tree.heading('Count', text='تعداد تکرار')

        
    # تنظیم عرض ستون‌ها
        self.tree.column('Source', width=40)
        self.tree.column('From', width=275)
        self.tree.column('From Exchange', width=80)
        self.tree.column('To', width=275)
        self.tree.column('To Exchange', width=80)
        self.tree.column('Token', width=60)
        self.tree.column('Amounts', width=100)
        self.tree.column('Dates', width=150)
        self.tree.column('Count', width=40)
        # تنظیم تراز متن برای ستون‌های چندخطی
        self.tree.tag_configure('multiline', font=('Tahoma', 9))

        #for col, (text, width) in columns.items():
        #    self.tree.heading(col, text=text)
        #    self.tree.column(col, width=width)
        
        scrollbar = ttk.Scrollbar(result_frame, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscroll=scrollbar.set)
        
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        self.status_var = tk.StringVar()
        self.status_var.set("آماده")
        status_label = ttk.Label(main_frame, textvariable=self.status_var,font="bold")
        status_label.pack(side=tk.BOTTOM, fill=tk.X)

        # اضافه کردن دکمه نمایش آمار
        stats_frame = ttk.Frame(main_frame)
        stats_frame.pack(fill=tk.X, pady=5)
        # دکمه نمایش نمودار (پایین پنجره)
        self.show_chart_btn = ttk.Button(main_frame,
                                   text="نمایش نمودار",
                                   command=self.show_chart,
                                   state=tk.DISABLED)
        self.show_chart_btn.pack(side=tk.RIGHT, padx=5) 
    
        self.stop_btn = ttk.Button(main_frame, text="توقف جستجو", 
                                 command=self.stop_search,
                                 state=tk.DISABLED)
        self.stop_btn.pack(side=tk.RIGHT, padx=5)

        # دکمه نمایش گراف
        self.show_graph_btn = ttk.Button(
            main_frame,
            text="نمایش گراف پیشرفته",
            command=self.show_transaction_graph,
            state=tk.DISABLED
        )
        self.show_graph_btn.pack(side=tk.RIGHT, padx=5)
        #self.stats_btn = ttk.Button(stats_frame, text="نمایش آمار", command=self.show_stats_from_tree)
        #self.stats_btn.pack(side=tk.LEFT, padx=5)

        self.current_tooltip = None

    # استفاده از closure برای دسترسی به self
        def make_tooltip_handlers(self):
            def show_tooltip(event):
                if hasattr(self, 'current_tooltip') and self.current_tooltip:
                    self.current_tooltip.destroy()
                
                item = self.tree.identify_row(event.y)
                col = self.tree.identify_column(event.x)
                
                if item and col:
                    value = self.tree.item(item, 'values')[int(col[1:])-1]
                    
                    if "\n" in value:
                        self.current_tooltip = tk.Toplevel(self.root)
                        self.current_tooltip.wm_overrideredirect(True)
                        self.current_tooltip.wm_geometry(f"+{event.x_root+20}+{event.y_root+10}")
                        
                        label = ttk.Label(
                            self.current_tooltip,
                            text=value,
                            background="#2F4F4F",
                            foreground="white",
                            relief="solid",
                            padding=5,
                            font=('Tahoma', 9)
                        )
                        label.pack()

            def close_tooltip(event=None):
                if hasattr(self, 'current_tooltip') and self.current_tooltip:
                    self.current_tooltip.destroy()
                    self.current_tooltip = None

            return show_tooltip, close_tooltip

        show_tooltip, close_tooltip = make_tooltip_handlers(self)
        self.tree.bind("<Motion>", show_tooltip)
        self.tree.bind("<Leave>", close_tooltip)

    def show_chart(self):
       
        if not self.current_transactions:
            messagebox.showwarning("هشدار", "داده‌ای برای نمایش وجود ندارد")
            return
            
        # ایجاد پنجره جدید برای نمودار
        chart_window = tk.Toplevel(self.root)
        chart_window.title("نمودار تراکنش‌ها")
        chart_window.geometry("600x400")
        
        # ایجاد کانواس برای نمودار
        chart_canvas = tk.Canvas(chart_window, bg='#2e2e2e')
        chart_canvas.pack(fill=tk.BOTH, expand=True)
        
        # محاسبه مقادیر
        wallet_address = self.wallet_entry.get().strip()
        total_in = sum(float(tx['amount']) for tx in self.current_transactions 
                     if tx['to'] == wallet_address)
        total_out = sum(float(tx['amount']) for tx in self.current_transactions 
                      if tx['from'] == wallet_address)
        
        # متن راهنما
        chart_canvas.create_text(300, 30, text="نمودار ورودی و خروجی", 
                               fill="white", font=('B Nazanin', 14))
        
        # رسم نمودارها
        max_val = max(total_in, total_out, 1)  # جلوگیری از تقسیم بر صفر
        
        # نمودار ورودی (سبز)
        chart_canvas.create_rectangle(100, 100, 100 + (total_in/max_val)*400, 150, 
                                    fill='#2ecc71', outline='')
        chart_canvas.create_text(520, 125, text=f"{total_in:.4f}", fill="white")
        
        # نمودار خروجی (قرمز)
        chart_canvas.create_rectangle(100, 200, 100 + (total_out/max_val)*400, 250, 
                                    fill='#e74c3c', outline='')
        chart_canvas.create_text(520, 225, text=f"{total_out:.4f}", fill="white")
        
        # متن راهنما
        chart_canvas.create_text(150, 80, text="ورودی", fill="white", anchor='w')
        chart_canvas.create_text(150, 180, text="خروجی", fill="white", anchor='w')
        chart_canvas.create_text(300, 300, 
                               text=f"نتیجه نهایی: ورودی={total_in:.4f} | خروجی={total_out:.4f}",
                               fill="white")

    def animate_transactions(self, transactions):
        if not hasattr(self, 'animation_frame'):
            self.animation_frame = ttk.Frame(self.root)
            self.animation_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
    
    # پاک کردن کانواس قبلی (اگر وجود دارد)
        if hasattr(self, 'animation_canvas'):
            self.animation_canvas.destroy()
        
        self.animation_canvas = tk.Canvas(self.animation_frame, bg='#2e2e2e', highlightthickness=0)
        self.animation_canvas.pack(fill=tk.BOTH, expand=True)
    
        try:
            # محاسبه مقادیر با مدیریت خطا
            total_in = 0.0
            total_out = 0.0
            wallet_address = self.wallet_entry.get().strip()
            
            for tx in transactions:
                try:
                    amount = float(tx.get('amount', 0))
                    if tx.get('to') == wallet_address:
                        total_in += amount
                    elif tx.get('from') == wallet_address:
                        total_out += amount
                except (ValueError, TypeError):
                    continue
            
            # متن راهنما
            self.animation_canvas.create_text(100, 30, text="نمودار ورودی و خروجی", 
                                            fill="white", font=('B Nazanin', 14))
            
            # تابع به‌روزرسانی انیمیشن
            def update_animation(step):
                self.animation_canvas.delete('animation')
                
                # محاسبه مقادیر جاری
                current_in = min(step/10 * total_in, total_in)
                current_out = min(step/10 * total_out, total_out)
                
                # رسم نمودارها
                max_val = max(total_in, total_out, 1)  # جلوگیری از تقسیم بر صفر
                self.animation_canvas.create_rectangle(
                    50, 80, 50 + (current_in/max_val) * 300, 120,
                    fill='#2ecc71', outline='', tags='animation'
                )
                self.animation_canvas.create_text(
                    360, 100, text=f"{current_in:.4f}",
                    fill="white", tags='animation'
                )
                
                self.animation_canvas.create_rectangle(
                    50, 180, 50 + (current_out/max_val) * 300, 220,
                    fill='#e74c3c', outline='', tags='animation'
                )
                self.animation_canvas.create_text(
                    360, 200, text=f"{current_out:.4f}",
                    fill="white", tags='animation'
                )
                
                if step < 10:
                    self.root.after(150, update_animation, step+1)
                else:
                    # نمایش نتیجه نهایی
                    self.animation_canvas.create_text(
                        200, 250, 
                        text=f"ورودی کل: {total_in:.4f} | خروجی کل: {total_out:.4f}",
                        fill="white", font=('B Nazanin', 11), tags='animation'
                    )
            
            update_animation(0)
        
        except Exception as e:
            print(f"خطا در ایجاد انیمیشن: {str(e)}")
            self.animation_canvas.create_text(
                150, 50, 
                text="خطا در نمایش انیمیشن", 
                fill="red", font=('B Nazanin', 12)
            )
    
    def set_dark_theme(self):
        self.root.configure(bg='#2e2e2e')
        self.style = ttk.Style()
        self.style.theme_use('clam')
        
        self.style.configure('TFrame', background='#2e2e2e')
        self.style.configure('TLabel', background='#2e2e2e', foreground='white')
        self.style.configure('TButton', background='#3e3e3e', foreground='white')
        self.style.configure('TEntry', fieldbackground='#3e3e3e', foreground='white')
        self.style.configure('TCombobox', fieldbackground='#3e3e3e', foreground='white')
        self.style.map('TButton', background=[('active', '#4e4e4e')])
    
    def add_smart_tooltips(self, wallet_address):
        for item in self.tree.get_children():
            values = self.tree.item(item, 'values')
        
            if wallet_address in values[1]:  # مبدأ
                self.tree.tag_configure('from_tip', background='#2e2e2e')
                self.tree.item(item, tags=('from_tip',))
            
            elif wallet_address in values[3]:  # مقصد
                self.tree.tag_configure('to_tip', background='#2e2e2e')
                self.tree.item(item, tags=('to_tip',))

    # اتصال رویداد هاور
        self.tree.bind("<Motion>", lambda e: self.show_tooltip(e, wallet_address))

    def highlight_searched_address(self, wallet_address):
        self.tree.tag_configure('highlight_address', foreground='red')
        
        for item in self.tree.get_children():
            values = list(self.tree.item(item, 'values'))

            new_from = values[1]
            new_to = values[3]
        # بررسی ستون From (مبدا)
            if wallet_address in values[1]:  # اگر آدرس در مبدا وجود دارد
                new_from = new_from.replace(wallet_address, 
                                      f"{wallet_address}==>")
        # بررسی ستون To (مقصد)
            if wallet_address in values[3]:  # اگر آدرس در مقصد وجود دارد
               new_to = new_to.replace(wallet_address, 
                                  f"==>{wallet_address}")
        # اگر آدرس در هر یک از ستون‌ها وجود داشت، رکورد را آپدیت کن
            if new_from != values[1] or new_to != values[3]:
                self.tree.item(item, 
                         values=(values[0], new_from, values[2], new_to, *values[4:]),
                         tags=('highlight',))
        
    def create_menu(self):
        menubar = tk.Menu(self.root)
        
        file_menu = tk.Menu(menubar, tearoff=0)
        file_menu.add_command(label="ذخیره نتایج", command=self.save_results)
        file_menu.add_separator()
        file_menu.add_command(label="خروج", command=self.root.quit)
        menubar.add_cascade(label="فایل", menu=file_menu)
        
        help_menu = tk.Menu(menubar, tearoff=0)
        help_menu.add_command(label="راهنما", command=self.show_help)
        help_menu.add_command(label="درباره", command=self.show_about)
        menubar.add_cascade(label="کمک", menu=help_menu)
        
        self.root.config(menu=menubar)

    def show_transaction_graph(self):
        if not self.current_transactions:
            messagebox.showwarning("هشدار", "داده‌ای برای نمایش وجود ندارد")
            return
        
        # ایجاد پنجره جدید برای گراف
        graph_window = tk.Toplevel(self.root)
        graph_window.title("گراف تراکنش‌ها - تحلیل ارتباطات")
        graph_window.geometry("1200x900")
        
        # ایجاد فریم اصلی با چیدمان ساده‌تر
        main_frame = ttk.Frame(graph_window)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # فریم کنترل‌ها در پایین (قبل از گراف)
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill=tk.X, side=tk.BOTTOM, padx=10, pady=10)
        
        # فریم برای گراف (بالای کنترل‌ها)
        graph_frame = ttk.Frame(main_frame)
        graph_frame.pack(fill=tk.BOTH, expand=True)
        
        # گروه‌بندی تراکنش‌ها
        merged_edges = {}
        for tx in self.current_transactions:
            amount = float(tx.get('amount', 0))
            if amount < 0.1:  # نادیده گرفتن تراکنش‌های کوچک
                continue
                
            key = (tx['from'], tx['to'], tx.get('token', 'UNKNOWN'))
            if key not in merged_edges:
                merged_edges[key] = {
                    'amount': amount,
                    'count': 1,
                    'dates': [tx.get('date', '')]
                }
            else:
                merged_edges[key]['amount'] += amount
                merged_edges[key]['count'] += 1
                merged_edges[key]['dates'].append(tx.get('date', ''))

        # ایجاد گراف (کدهای قبلی بدون تغییر)
        G = nx.DiGraph()
        for (from_addr, to_addr, token), data in merged_edges.items():
        # کوتاه کردن آدرس‌ها
            display_from = f"{from_addr[:6]}...{from_addr[-4:]}" 
            display_to = f"{to_addr[:6]}...{to_addr[-4:]}"
            
            G.add_edge(display_from, display_to, 
                    weight=data['amount'],
                    count=data['count'],
                    token=token,
                    amount=data['amount'])
        
        # چیدمان بهینه
        pos = nx.kamada_kawai_layout(G)
        
        # تنظیم اندازه گره‌ها بر اساس تعداد ارتباطات
        node_sizes = [800 + 200 * G.degree(node) for node in G.nodes()]
        
        # رسم گراف
        plt.figure(figsize=(12, 10))
        nx.draw_networkx_nodes(G, pos, node_size=node_sizes, alpha=0.8)
        
        # رسم یال‌ها با عرض متناسب با مقدار
        edge_widths = [0.3 + math.log(1 + G.edges[edge]['amount']) for edge in G.edges()]
        nx.draw_networkx_edges(G, pos, width=edge_widths, arrowstyle='->', arrowsize=15)
        
        # نمایش مقادیر روی یال‌ها
        edge_labels = {edge: f"{G.edges[edge]['amount']:.2f}" for edge in G.edges()}
        nx.draw_networkx_edge_labels(G, pos, edge_labels=edge_labels, font_size=8)

        '''
        wallet_address = self.wallet_entry.get().strip()
        
        # جمع‌آوری آمار
        total_in = 0
        total_out = 0
        unique_addresses = set()
        
        # اضافه کردن گره‌ها و یال‌ها به گراف
        for tx in self.current_transactions:
            from_addr = tx['from']
            to_addr = tx['to']
            amount = float(tx.get('amount', 0))
            token = tx.get('token', 'UNKNOWN')
            
            # محاسبه ورودی و خروجی
            if to_addr == wallet_address:
                total_in += amount
            elif from_addr == wallet_address:
                total_out += amount
                
            unique_addresses.update([from_addr, to_addr])
            
            # کوتاه کردن آدرس‌ها برای نمایش
            display_from = f"{from_addr[:6]}...{from_addr[-4:]}" if len(from_addr) > 10 else from_addr
            display_to = f"{to_addr[:6]}...{to_addr[-4:]}" if len(to_addr) > 10 else to_addr
            
            # تعیین رنگ و شکل گره‌ها
            from_shape = 'd'  # الماس برای مبداها
            to_shape = 'o'   # دایره برای مقصدها
            
            from_color = '#FF6B6B' if from_addr == wallet_address else '#4ECDC4'
            to_color = '#45B7D1' if to_addr == wallet_address else '#FFA07A'
            
            # اضافه کردن گره‌ها با ویژگی‌های نمایشی
            G.add_node(display_from, 
                    color=from_color,
                    shape=from_shape,
                    full_address=from_addr,
                    is_wallet=from_addr == wallet_address)
            
            G.add_node(display_to,
                    color=to_color,
                    shape=to_shape,
                    full_address=to_addr,
                    is_wallet=to_addr == wallet_address)
            
            # اضافه کردن یال با اطلاعات تراکنش
            G.add_edge(display_from, display_to,
                    weight=amount,
                    token=token,
                    date=tx.get('date', ''),
                    amount=amount)

        # رسم گراف
        fig = plt.figure(figsize=(10, 7), dpi=100, facecolor='#f5f5f5')
        try:
            plt.style.use('seaborn-v0_8')
        except:
            plt.style.use('ggplot')
        
        pos = nx.spring_layout(G, k=0.2, iterations=50, seed=42)
        
        # تهیه رنگ‌ها و شکل‌ها
        node_colors = [G.nodes[node]['color'] for node in G.nodes()]
        node_shapes = [G.nodes[node]['shape'] for node in G.nodes()]
        node_sizes = [1000 if G.nodes[node]['is_wallet'] else 600 for node in G.nodes()]
        
        # رسم گره‌ها با شکل‌های مختلف
        for shape in set(node_shapes):
            node_list = [node for node in G.nodes() if G.nodes[node]['shape'] == shape]
            nx.draw_networkx_nodes(G, pos, nodelist=node_list,
                                node_shape=shape,
                                node_color=[G.nodes[node]['color'] for node in node_list],
                                node_size=[1000 if G.nodes[node]['is_wallet'] else 600 for node in node_list],
                                alpha=0.9)
        
        # رسم یال‌ها با متن مقدار
        edge_labels = {}
        for edge in G.edges():
            amount = G.edges[edge]['amount']
            token = G.edges[edge]['token']
            edge_labels[edge] = f"{amount:.2f} {token}"
        
        # رسم یال‌ها با عرض متناسب با مقدار تراکنش
        edge_widths = [0.5 + G.edges[edge]['weight'] * 0.01 for edge in G.edges()]
        
        nx.draw_networkx_edges(G, pos, arrowstyle='->', arrowsize=15,
                            width=edge_widths, edge_color='#888',
                            connectionstyle='arc3,rad=0.1')
        
        # نمایش مقادیر روی یال‌ها
        nx.draw_networkx_edge_labels(
            G, pos,
            edge_labels=edge_labels,
            font_color='#333',
            font_size=8,
            bbox=dict(alpha=0.7, facecolor='white', edgecolor='none', boxstyle='round,pad=0.2')
        )
       
        '''

        # برچسب‌های گره
        nx.draw_networkx_labels(G, pos, font_size=9, font_family='Tahoma',
                            font_weight='bold')
        
        # عنوان و توضیحات
        plt.title(f"گراف تراکنش‌های کیف پول {wallet_address[:10]}...", fontsize=14, pad=20)
        
        # اضافه کردن توضیحات به گراف
        legend_elements = [
            plt.Line2D([0], [0], marker='d', color='w', label='آدرس مبدا',
                    markerfacecolor='#4ECDC4', markersize=10),
            plt.Line2D([0], [0], marker='o', color='w', label='آدرس مقصد',
                    markerfacecolor='#FFA07A', markersize=10),
            plt.Line2D([0], [0], marker='d', color='w', label='کیف پول شما (مبدا)',
                    markerfacecolor='#FF6B6B', markersize=10),
            plt.Line2D([0], [0], marker='o', color='w', label='کیف پول شما (مقصد)',
                    markerfacecolor='#45B7D1', markersize=10)
        ]
        
        plt.legend(handles=legend_elements, loc='upper right', bbox_to_anchor=(1.15, 1))
        
        # متن آمار پایین گراف
        stats_text = (
            f"تعداد تراکنش‌ها: {len(self.current_transactions)}\n"
            f"تعداد آدرس‌های منحصر به فرد: {len(unique_addresses)}\n"
            f"مجموع ورودی: {total_in:.4f}\n"
            f"مجموع خروجی: {total_out:.4f}"
        )
        
        plt.figtext(0.5, 0.01, stats_text, ha='center', fontsize=10,
                bbox={'facecolor': '#f0f0f0', 'alpha': 0.7, 'pad': 5})
        
        # اضافه کردن گراف به رابط کاربری
        canvas = FigureCanvasTkAgg(fig, master=graph_frame)
        canvas.draw()
        
        # ایجاد اسکرول بار
        scroll_y = ttk.Scrollbar(graph_frame, orient=tk.VERTICAL)
        scroll_y.pack(side=tk.RIGHT, fill=tk.Y)
        
        # نمایش گراف با اسکرول
        canvas.get_tk_widget().pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        canvas.get_tk_widget().config(yscrollcommand=scroll_y.set)
        scroll_y.config(command=canvas.get_tk_widget().yview)
        
        # دکمه‌های کنترل با چیدمان ساده
        btn_frame = ttk.Frame(control_frame)
        btn_frame.pack(pady=5)
        
        ttk.Button(btn_frame, text="ذخیره تصویر", 
                command=lambda: self.save_graph_image(fig)).grid(row=0, column=0, padx=5)
        
        ttk.Button(btn_frame, text="نمایش آمار کامل",
                command=lambda: self.show_full_stats(wallet_address, total_in, total_out,
                                                    len(self.current_transactions),
                                                    len(unique_addresses))).grid(row=0, column=1, padx=5)
        
        ttk.Button(btn_frame, text="بستن",
                command=graph_window.destroy).grid(row=0, column=2, padx=5)
        
        # نوار وضعیت
        status_var = tk.StringVar()
        status_label = ttk.Label(control_frame, textvariable=status_var,
                            relief=tk.SUNKEN, anchor=tk.W)
        status_label.pack(fill=tk.X, pady=5)
       
        def on_hover(event):
            if event.inaxes == ax:
                for node in G.nodes():
                    if np.sqrt((pos[node][0]-event.xdata)**2 + (pos[node][1]-event.ydata)**2) < 0.03:
                        status_var.set(f"آدرس: {G.nodes[node]['full_address']}")
                        break
                else:
                    for edge in G.edges():
                        x = (pos[edge[0]][0] + pos[edge[1]][0]) / 2
                        y = (pos[edge[0]][1] + pos[edge[1]][1]) / 2
                        if np.sqrt((x-event.xdata)**2 + (y-event.ydata)**2) < 0.03:
                            edge_data = G.edges[edge]
                            status_var.set(
                                f"تراکنش: {edge_data['amount']:.4f} {edge_data['token']} | تاریخ: {edge_data['date']}"
                            )
                            break
                    else:
                        status_var.set("")
        
        ax = plt.gca()
        fig.canvas.mpl_connect('motion_notify_event', on_hover)
        
        # تنظیمات نهایی پنجره
        graph_window.update()
        canvas.get_tk_widget().config(scrollregion=canvas.get_tk_widget().bbox("all"))
    
       


    def save_graph_image(self, fig):
        file_path = filedialog.asksaveasfilename(
            defaultextension=".png",
            filetypes=[("PNG Files", "*.png"), ("JPEG Files", "*.jpg"), ("PDF Files", "*.pdf"), ("All Files", "*.*")],
            title="ذخیره تصویر گراف"
        )
        
        if file_path:
            try:
                fig.savefig(file_path, dpi=300, bbox_inches='tight', facecolor=fig.get_facecolor())
                messagebox.showinfo("موفق", "تصویر گراف با موفقیت ذخیره شد")
            except Exception as e:
                messagebox.showerror("خطا", f"خطا در ذخیره تصویر: {str(e)}")

    def show_full_stats(self, wallet_address, total_in, total_out, total_txs, unique_addresses):
        stats_window = tk.Toplevel(self.root)
        stats_window.title("آمار کامل تراکنش‌ها")
        stats_window.geometry("600x400")
        
        main_frame = ttk.Frame(stats_window, padding=20)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # عنوان
        ttk.Label(main_frame, text=f"آمار تراکنش‌های کیف پول {wallet_address[:10]}...",
                font=('Tahoma', 12, 'bold')).pack(pady=10)
        
        # ایجاد Treeview برای نمایش آمار
        tree = ttk.Treeview(main_frame, columns=('metric', 'value'), show='headings')
        tree.heading('metric', text='معیار')
        tree.heading('value', text='مقدار')
        tree.column('metric', width=200, anchor='e')
        tree.column('value', width=200, anchor='w')
        
        # اضافه کردن داده‌ها
        stats_data = [
            ('تعداد کل تراکنش‌ها', f"{total_txs}"),
            ('تعداد آدرس‌های منحصر به فرد', f"{unique_addresses}"),
            ('مجموع ورودی', f"{total_in:.8f}"),
            ('مجموع خروجی', f"{total_out:.8f}"),
            ('مانده خالص', f"{(total_in - total_out):.8f}"),
            ('میانگین ورودی', f"{(total_in/total_txs if total_txs > 0 else 0):.8f}"),
            ('میانگین خروجی', f"{(total_out/total_txs if total_txs > 0 else 0):.8f}")
        ]
        
        for item in stats_data:
            tree.insert('', tk.END, values=item)
        
        tree.pack(fill=tk.BOTH, expand=True, pady=10)
        
        # دکمه بستن
        ttk.Button(main_frame, text="بستن", command=stats_window.destroy).pack(pady=10)
    
    def track_wallet_threaded(self):
        Thread(target=self.track_wallet, daemon=True).start()
    
 
    def get_arkham_data(self, wallet_address):    
        self.status_var.set("در حال دریافت داده از Arkham...")
        self.root.update()
        
        all_transactions = []
        limit = 50
        offset = 0
        retry_count = 0
        max_retries = 3
        
        while True:
            try:
                # توقف تصادفی + افزایش تدریجی زمان انتظار
                delay = random.uniform(0.5, 1.5) + (retry_count * 0.5)
                time.sleep(delay)
                
                #api_key = api_config.eth
                #url = f"https://api.etherscan.io/api?module=account&action=txlist&address={wallet_address}&startblock=0&endblock=********&page={page}&offset={offset}&sort=asc&apikey={api_key}"
                url = f"https://api.arkhamintelligence.com/address/{wallet_address}/transactions"
                params = {
                    'limit': limit,
                    'offset': offset
                }
                
                # اضافه کردن هدرهای ضروری
                headers = {
                    'User-Agent': 'Mozilla/5.0',
                    'Accept': 'application/json',
                    'Authorization': 'Bearer YOUR_API_KEY'  # اگر نیاز به کلید دارد
                }
                
                response = requests.get(url, headers=headers, params=params, timeout=10)
                
                if response.status_code == 200:
                    data = response.json()
                    transactions = data.get('transactions', [])
                    
                    if not transactions:
                        break
                        
                    all_transactions.extend(transactions)
                    offset += limit
                    retry_count = 0  # ریست شمارشگر تلاش مجدد
                    
                    self.status_var.set(f"در حال دریافت داده از Arkham... ({len(all_transactions)} تراکنش)")
                    self.root.update()
                    
                elif response.status_code == 429:
                    retry_count += 1
                    if retry_count > max_retries:
                        messagebox.showwarning("اخطار", "محدودیت نرخ درخواست. لطفاً稍后再试")
                        break
                    continue
                    
                else:
                    messagebox.showerror("خطا", f"خطای سرور: کد {response.status_code}")
                    break
                    
            except requests.exceptions.RequestException as e:
                retry_count += 1
                if retry_count > max_retries:
                    messagebox.showerror("خطا", f"خطای ارتباط: {str(e)}")
                    return None
                continue
                
        return {'transactions': all_transactions} if all_transactions else None

    def get_covalent_data(self, wallet_address):
        self.status_var.set("در حال دریافت داده از Covalent...")
        self.root.update()

        all_transactions = []
        page = 0
        page_size = 1000  # تعداد تراکنش در هر صفحه
        max_retries = 3  # حداکثر تعداد تلاش برای هر درخواست
        retry_delay = 5  # تاخیر بین تلاش‌های مجدد
        
        while True:
            # توقف تصادفی بین درخواست‌ها برای جلوگیری از Rate Limit
            time.sleep(random.uniform(0.5, 1.5))
            
            #url = f"https://api.covalenthq.com/v1/multi-chain/address/{wallet_address}/transactions/"
            url = f"https://api.covalenthq.com/v1/1/address/{wallet_address}/transactions_v2/"
            params = {
                'key': api_config.COVALENT,
                'quote-currency': 'USD',
                'page-number': page,
                'page-size': page_size
            }
            
            try:
                response = requests.get(
                    url,
                    params=params,
                    headers={'User-Agent': 'Mozilla/5.0'},
                    timeout=30  # محدودیت زمانی برای درخواست
                )
                
                if response.status_code == 200:
                    data = response.json()
                    # بررسی ساختار پاسخ
                    if not data.get('data', {}).get('items'):
                        break
                        
                    transactions = data['data']['items']
                    all_transactions.extend(transactions)
                    page += 1

                    self.status_var.set(
                        f"در حال دریافت داده از Covalent... ({len(all_transactions)} تراکنش)"
                    )
                    self.root.update()

                    # بررسی پایان داده‌ها
                    if len(transactions) < page_size:
                        break

                   

                elif response.status_code == 429:
                    retry_after = int(response.headers.get('Retry-After', 5))
                    time.sleep(retry_after)
                    retry_count += 1
                    if retry_count > max_retries:
                        messagebox.showwarning("اخطار", "محدودیت نرخ درخواست. لطفاً稍后再试")
                        break
                    continue

                else:
                    messagebox.showerror(
                        "خطا",
                        f"خطا در دریافت داده از Covalent: کد {response.status_code}\n{response.text[:200]}"
                    )
                    return None
                    
            
            except Exception as e:
                messagebox.showerror("خطا", f"خطا در اتصال به Covalent: {str(e)}")
                return None

        return {'data': {'items': all_transactions}} if all_transactions else None
    
   
    def get_blockcypher_data(self, wallet_address, coin='btc'):
        self.status_var.set("در حال دریافت داده از BlockCypher...")
        self.root.update()

        base_url = f"https://api.blockcypher.com/v1/{coin}/main/addrs/{wallet_address}/full"
        
        try:
            time.sleep(random.uniform(0.3, 0.7))
            response = requests.get(base_url, headers={'User-Agent': 'Mozilla/5.0'}, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                if not data.get('txs'):
                    messagebox.showinfo("اطلاع", "هیچ تراکنشی یافت نشد")
                    return {'data': {'items': []}}  # بازگرداندن ساختار خالی اما معتبر

                return {
                'data': {
                    'items': data['txs'],
                    'coin_symbol': coin.upper()  # اضافه کردن اطلاعات ارز
                }
                        }
                
            else:
                messagebox.showerror("خطا", f"خطای HTTP {response.status_code}")
                return {'data': {'items': []}}  # بازگرداندن ساختار خالی اما معتبر
                #transactions = data.get('txs', [])
                #transactions = data['txs']

                
        except Exception as e:
            messagebox.showerror("خطا", f"خطای ارتباطی: {str(e)}")
            return {'data': {'items': []}}  # بازگرداندن ساختار خالی اما معتبر

    #def get_blockchain_data(self, wallet_address):
    def get_blockchain_data(self, wallet_address, blockchain='bsc'):
        self.status_var.set(f"در حال دریافت داده از {blockchain.upper()}...")
        self.root.update()

        # تنظیمات API برای هر بلاک‌چین
        apis = {
            'bsc': {
                'url': 'https://api.bscscan.com/api',
                'params': {
                    'module': 'account',
                    'action': 'txlist',
                    'startblock': 0,
                    'endblock': ********,
                    'sort': 'asc'
                },
                'api_key': api_config.bsc,  # جایگزین کنید
                'decimals': 18  # تبدیل از Wei
            },
            'eth': {
                'url': 'https://api.etherscan.io/api',
                'params': {
                    'module': 'account',
                    'action': 'txlist',
                    'startblock': 0,
                    'endblock': ********,
                    'sort': 'asc'
                },
                'api_key': api_config.eth,  # جایگزین کنید
                'decimals': 18  # تبدیل از Wei
            },
            'tron': {
                'url': 'https://apilist.tronscan.org/api/transaction',
                'params': {
                    'limit': 50,
                    'start': 0,
                    'sort': '-timestamp'
                },
                'api_key': None,
                'decimals': 6  # تبدیل از SUN
            },
            'polygon': {
                'url': 'https://api.polygonscan.com/api',
                'params': {
                    'module': 'account',
                    'action': 'txlist',
                    'startblock': 0,
                    'endblock': ********,
                    'sort': 'asc'
                },
                'api_key': api_config.polygon,  # جایگزین کنید
                'decimals': 18  # تبدیل از Wei
            }
        }

        if blockchain not in apis:
            messagebox.showerror("خطا", f"بلاک‌چین {blockchain} پشتیبانی نمی‌شود")
            return None

        config = apis[blockchain]
        all_transactions = []
        page = 1
        offset = 10000 if blockchain in ['bsc', 'eth', 'polygon'] else 50

        while True:
            try:
                # توقف تصادفی برای جلوگیری از Rate Limit
                time.sleep(random.uniform(0.3, 0.7))
                
                # تنظیم پارامترهای درخواست
                params = config['params'].copy()
                params.update({
                    'address': wallet_address,
                    'page': page,
                    'offset': offset
                })
                if config['api_key']:
                    params['apikey'] = config['api_key']

                # ارسال درخواست
                response = requests.get(
                    config['url'],
                    params=params,
                    headers={'User-Agent': 'Mozilla/5.0'}
                )

                if response.status_code == 200:
                    data = response.json()
                    
                    # پردازش پاسخ بر اساس بلاک‌چین
                    if blockchain in ['bsc', 'eth', 'polygon']:
                        transactions = data.get('result', [])
                        if not transactions or isinstance(transactions, str):
                            break
                    elif blockchain == 'tron':
                        transactions = data.get('data', [])
                        if not transactions:
                            break

                    all_transactions.extend(transactions)
                    page += 1

                    # نمایش پیشرفت
                    self.status_var.set(
                        f"در حال دریافت داده از {blockchain.upper()}... ({len(all_transactions)} تراکنش)"
                    )
                    self.root.update()
                else:
                    messagebox.showwarning(
                        "اخطار",
                        f"پاسخ غیرمنتظره از {blockchain.upper()}: کد {response.status_code}"
                    )
                    break

            except Exception as e:
                messagebox.showerror(
                    "خطا",
                    f"خطا در دریافت داده از {blockchain.upper()}: {str(e)}"
                )
                return None

        return {'transactions': all_transactions, 'blockchain': blockchain} if all_transactions else None

    def get_tronscan_data(self, wallet_address):
        self.status_var.set("در حال دریافت داده از Tronscan...")
        self.root.update()
    
        all_transactions = []
        limit = 50
        start = 0
    
        while True:
            time.sleep(0.5)
            url = f"https://apilist.tronscan.org/api/transaction?address={wallet_address}&limit={limit}&start={start}&sort=-timestamp"
            try:
                response = requests.get(url, headers={'User-Agent': 'Mozilla/5.0'})
                if response.status_code == 200:
                    data = response.json()
                    transactions = data.get('data', [])
                    if not transactions:
                        break
                
                    all_transactions.extend(transactions)
                    start += limit
                
                # نمایش پیشرفت در وضعیت برنامه
                    self.status_var.set(f"در حال دریافت داده از Tronscan... ({len(all_transactions)} تراکنش)")
                    self.root.update()
                else:
                    break
            except Exception as e:
                messagebox.showerror("خطا", f"خطا در دریافت داده از Tronscan: {str(e)}")
                return None
    
        return {'data': all_transactions} if all_transactions else None
    
    def get_etherscan_data(self, wallet_address):
    
        self.status_var.set("در حال دریافت داده از Etherscan...")
        self.root.update()
    
        all_transactions = []
        page = 1
        offset = 10000  # حداکثر تراکنش در هر صفحه
    
        while True:
        # توقف تصادفی بین درخواست‌ها
            time.sleep(random.uniform(0.3, 0.7))
        
            api_key = api_config.eth
            url = f"https://api.etherscan.io/api?module=account&action=txlist&address={wallet_address}&startblock=0&endblock=********&page={page}&offset={offset}&sort=asc&apikey={api_key}"
            try:
                response = requests.get(url, headers={'User-Agent': 'Mozilla/5.0'})
                if response.status_code == 200:
                    data = response.json()
                    transactions = data.get('result', [])
                    if not transactions:
                        break
                
                    all_transactions.extend(transactions)
                    page += 1
                
                    self.status_var.set(f"در حال دریافت داده از Etherscan... ({len(all_transactions)} تراکنش)")
                    self.root.update()
                else:
                    break
            except Exception as e:
                messagebox.showerror("خطا", f"خطا در دریافت داده از Etherscan: {str(e)}")
                return None
    
        return {'result': all_transactions} if all_transactions else None
    
    def get_bscscan_data(self, wallet_address):
        self.status_var.set("در حال دریافت داده از BscScan...")
        self.root.update()

        all_transactions = []
        api_key = api_config.bsc # جایگزین کنید با API Key واقعی
        page = 1
        offset = 10000  # حداکثر تراکنش در هر درخواست
        
        while True:
            # توقف تصادفی بین درخواست‌ها برای جلوگیری از Rate Limit
            time.sleep(random.uniform(0.3, 0.7))
            
            url = f"https://api.bscscan.com/api?module=account&action=txlist&address={wallet_address}" \
                f"&startblock=0&endblock=********&page={page}&offset={offset}&sort=asc&apikey={api_key}"
            
            try:
                response = requests.get(url, headers={'User-Agent': 'Mozilla/5.0'})
                if response.status_code == 200:
                    data = response.json()
                    transactions = data.get('result', [])
                    
                    if not transactions or isinstance(transactions, str):
                        break
                    
                    all_transactions.extend(transactions)
                    page += 1
                    
                    # نمایش پیشرفت
                    self.status_var.set(f"در حال دریافت داده از BscScan... ({len(all_transactions)} تراکنش)")
                    self.root.update()
                else:
                    messagebox.showwarning("اخطار", f"پاسخ غیرمنتظره از BscScan: کد {response.status_code}")
                    break
                    
            except Exception as e:
                messagebox.showerror("خطا", f"خطا در دریافت داده از BscScan: {str(e)}")
                return None

        return {'result': all_transactions} if all_transactions else None

    def get_solana_data(self, wallet_address):
        if not hasattr(api_config, 'solana'):
            messagebox.showerror("خطا", "کلید API سولانا تنظیم نشده")
            return {'data': {'items': []}}

        API_KEY = api_config.solana
        headers = {
            'User-Agent': 'Mozilla/5.0',
            'Authorization': f'Bearer {API_KEY}'
        }
        
        all_transactions = []
        offset = 0
        limit = 50
        max_transactions = 1000
        retry_count = 0
        max_retries = 3

        while not self.stop_search_flag and retry_count < max_retries:
            try:
                time.sleep(random.uniform(0.5, 1.5))  # توقف تصادفی
                
                url = f"https://api.solscan.io/account/transactions?address={wallet_address}&offset={offset}&limit={limit}"
                response = requests.get(url, headers=headers, timeout=15)
                
                if self.stop_search_flag:
                    break

                if response.status_code == 200:
                    data = response.json()
                    transactions = data.get('data', [])
                    
                    if not transactions:
                        break
                        
                    all_transactions.extend(transactions)
                    offset += len(transactions)
                    
                    # به‌روزرسانی UI
                    self.root.after(0, lambda: self.status_var.set(
                        f"سولانا: دریافت {len(all_transactions)} تراکنش"
                    ))
                    
                    if len(all_transactions) >= max_transactions:
                        break

                elif response.status_code == 403:
                    retry_after = int(response.headers.get('Retry-After', 15))
                    self.root.after(0, lambda: self.status_var.set(
                        f"محدودیت موقت - توقف برای {retry_after} ثانیه"
                    ))
                    time.sleep(retry_after)
                    retry_count += 1
                    continue
                    
                else:
                    self.root.after(0, lambda: messagebox.showwarning(
                        "اخطار",
                        f"خطای سرور: کد {response.status_code}"
                    ))
                    break
                    
            except requests.exceptions.Timeout:
                retry_count += 1
                if retry_count < max_retries:
                    continue
            except Exception as e:
                if not self.stop_search_flag:
                    self.root.after(0, lambda: messagebox.showerror(
                        "خطا",
                        f"خطای غیرمنتظره: {str(e)}"
                    ))
                break
        
        return {'data': {'items': all_transactions}}

    def parse_transactions(self, data, source):
        transactions = []
        if not data:
            return transactions

        
        #source = source.lower() if source else ""

        try:
            if source == "covalent":
                transactions = self._parse_covalent_data(data)  
            elif source == "blockcypher":
                coin_symbol = 'BTC'  # یا از منبع دیگری دریافت کنید
                transactions = self._parse_blockcypher_data(data, coin_symbol)    
            elif source == "blockchain":
                transactions = self._parse_blockchain_data(data)
            elif source == "solana":
                transactions = self._parse_solana_data(data)
                #data = self.get_solana_data(self.wallet_address)
                #if data and not self.stop_search_flag:
                    #transactions = self._parse_solana_data(data)
                    #self.display_transactions(transactions)
            elif source == "arkham":
                transactions = self._parse_arkham_data(data)
            elif source == "tronscan":
                transactions = self._parse_tronscan_data(data)
            elif source == "etherscan":
                transactions = self._parse_etherscan_data(data)
            elif source == "bscscan":
                transactions = self._parse_bscscan_data(data)
            else:
                print(f"Warning: Unknown data source '{source}'")
        except Exception as e:
            print(f"Error parsing {source} transactions: {str(e)}")
            return []

        return transactions

    def _parse_covalent_data(self, data):
        """Parse Covalent API response"""
        transactions = []
        for tx in data.get('data', {}).get('items', []):
            transactions.append({
                'source': 'Covalent',
                'from': tx.get('from_address'),
                'to': tx.get('to_address'),
                'token': tx.get('token_symbol', 'UNKNOWN'),
                'amount': float(tx.get('value', 0)),
                'date': tx.get('block_signed_at'),
                'tx_hash': tx.get('tx_hash'),
                'chain': tx.get('chain_name', 'UNKNOWN').upper()
            })
        return transactions
 
    def _parse_blockcypher_data(self, data, coin_symbol):
        transactions = []
        if not data or not isinstance(data, dict) or 'data' not in data or 'items' not in data['data']:
            print("خطا: ساختار داده‌های BlockCypher نامعتبر است")
            return transactions
        try:
            for tx in data['data']['items']:
                try:
                    if not tx or not isinstance(tx, dict):
                        continue

                    # مقدار پیش‌فرض برای فیلدها
                    tx_inputs = tx.get('inputs', [{}])
                    tx_outputs = tx.get('outputs', [{}])
                    
                    from_address = tx_inputs[0].get('addresses', ['UNKNOWN'])[0] if tx_inputs else 'UNKNOWN'
                    to_address = tx_outputs[0].get('addresses', ['UNKNOWN'])[0] if tx_outputs else 'UNKNOWN'
       
                 # محاسبه مقدار با مدیریت خطاها
                    amount = 0.0
                    if 'outputs' in tx:
                        try:
                            amount = sum(out.get('value', 0) for out in tx['outputs']) / (10**8)
                        except (TypeError, ValueError):    
                            amount = 0.0
                    transactions.append({
                    'source': 'BlockCypher',
                    'from': tx['inputs'][0]['addresses'][0] if tx['inputs'] else 'UNKNOWN',
                    'to': tx['outputs'][0]['addresses'][0] if tx['outputs'] else 'UNKNOWN',
                    'token': coin_symbol,
                    'amount': sum(out['value'] for out in tx['outputs']) / (10**8),
                    'date': tx['confirmed'],
                    'tx_hash': tx['hash'],
                    'chain': coin_symbol.upper()
                    })
                except Exception as e:
                    print(f"خطا در پردازش تراکنش: {str(e)}")
                    continue 
                   
        except KeyError as e:
            print(f"خطای KeyError در پردازش داده‌ها: {str(e)}")
        except Exception as e:
            print(f"خطای غیرمنتظره در پردازش داده‌ها: {str(e)}")
  
        return transactions
   
    def _parse_blockchain_data(self, data):
        transactions = []
        blockchain = data.get('blockchain', '').lower()
        raw_data = data.get('transactions', []) or data.get('data', [])

        if blockchain == 'tron':
            return self._parse_tronscan_data(data)
        
        for tx in raw_data:
            try:
                transaction = {
                    'source': f'{blockchain.upper()}Scan',
                    'from': tx.get('from'),
                    'to': tx.get('to'),
                    'token': tx.get('tokenSymbol', blockchain.upper()),
                    'amount': self.safe_divide(tx.get('value'), self._get_decimals(blockchain)),
                    'date': self._timestamp_to_datetime(tx.get('timeStamp')),
                    'tx_hash': tx.get('hash'),
                    'status': tx.get('txreceipt_status', '1') == '1'  # 1 = موفق
                }
                
                # پردازش ویژه برای شبکه‌های خاص
                if blockchain == 'eth':
                    transaction['gas_used'] = tx.get('gasUsed')
                elif blockchain == 'bsc':
                    transaction['is_error'] = tx.get('isError') == '0'
                
                transactions.append(transaction)
            except Exception as e:
                print(f"Error parsing transaction: {str(e)}")
                continue

        return transactions

    def _parse_arkham_data(self, data):
        transactions = []
        for tx in data.get('transactions', []) or data.get('data', []):
            try:
                transactions.append({
                    'source': 'Arkham',
                    'from': tx.get('from'),
                    'to': tx.get('to'),
                    'token': tx.get('token', 'UNKNOWN'),
                    'amount': float(tx.get('amount', 0)),
                    'date': self._timestamp_to_datetime(tx.get('timestamp')),
                    'tx_hash': tx.get('txHash'),
                    'chain': tx.get('chain', 'UNKNOWN').upper()
                })
            except Exception as e:
                print(f"Error parsing Arkham transaction: {str(e)}")
                continue
        return transactions

    def _parse_tronscan_data(self, data):
        transactions = []
        for tx in data.get('data', []):
            try:
                transactions.append({
                    'source': 'Tronscan',
                    'from': tx.get('ownerAddress'),
                    'to': tx.get('toAddress'),
                    'token': tx.get('tokenType', 'TRX'),
                    'amount': self.safe_divide(tx.get('amount'), 10**6),
                    'date': self._timestamp_to_datetime(tx.get('timestamp')/1000),
                    'tx_hash': tx.get('hash'),
                    'status': tx.get('confirmed', True)
                })
            except Exception as e:
                print(f"Error parsing Tron transaction: {str(e)}")
                continue
        return transactions

    def _parse_etherscan_data(self, data):
        if isinstance(data, str):
            try:
                data = json.loads(data)
            except json.JSONDecodeError:
                data = {}
        
        transactions = []
        for tx in data.get('result', []):
            try:
                transactions.append({
                    'source': 'Etherscan',
                    'from': tx.get('from'),
                    'to': tx.get('to'),
                    'token': tx.get('tokenSymbol', 'ETH'),
                    'amount': self.safe_divide(tx.get('value'), 10**18),
                    'date': self._timestamp_to_datetime(tx.get('timeStamp')),
                    'tx_hash': tx.get('hash'),
                    'gas_used': tx.get('gasUsed')
                })
            except Exception as e:
                print(f"Error parsing Ethereum transaction: {str(e)}")
                continue
        return transactions

    def _parse_solana_data(self, data):
        transactions = []
        
        if not data or not isinstance(data, dict) or not data.get('data'):
            return transactions
            
        for tx in data['data'].get('items', []):
            try:
                transactions.append({
                    'source': 'Solscan',
                    'tx_hash': tx.get('txHash', 'N/A'),
                    'from': tx.get('from', 'N/A'),
                    'to': tx.get('to', 'N/A'),
                    'amount': float(tx.get('lamports', 0)) / 10**9,  # تبدیل از lamports به SOL
                    'token': 'SOL',
                    'date': self._timestamp_to_datetime(tx.get('timestamp', 0)),
                    'chain': 'SOLANA'
                })
            except Exception as e:
                print(f"خطا در پردازش تراکنش سولانا: {str(e)}")
                continue
                
        return transactions

    def _parse_bscscan_data(self, data):
        """Parse BscScan API response"""
        transactions = []
        for tx in data.get('result', []):
            transactions.append({
                'source': 'BscScan',
                'from': tx.get('from'),
                'to': tx.get('to'),
                'token': tx.get('tokenSymbol', 'BNB'),
                'amount': self.safe_divide(tx.get('value'), 1e18),
                'date': self._timestamp_to_datetime(tx.get('timeStamp')),
                'tx_hash': tx.get('hash')
            })
        return transactions

    def _get_decimals(self, blockchain):
        """Get decimal places for different blockchains"""
        decimals_map = {
            'eth': 18,
            'bsc': 18,
            'polygon': 18,
            'tron': 6,
            'arbitrum': 18,
            'optimism': 18
        }
        return decimals_map.get(blockchain.lower(), 18)  # پیش‌فرض 18 رقم اعشار
    
    def _timestamp_to_datetime(self, timestamp):
        """Convert various timestamp formats to datetime string"""
        try:
            if isinstance(timestamp, str):
                timestamp = int(timestamp)
            return datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')
        except:
            return "Unknown"

   
    def parse_transactions(self, data, source):
        transactions = []
        
        if not data:
            return transactions
        if source == "covalent":
            for tx in data.get('data', {}).get('items', []):
                transactions.append({
                    'source': 'Covalent',
                    'from': tx.get('from_address'),
                    'to': tx.get('to_address'),
                    'token': tx.get('token_symbol', 'UNKNOWN'),
                    'amount': float(tx.get('value', 0)),
                    'date': tx.get('block_signed_at')
                    #'chain': tx.get('chain_name', 'UNKNOWN').upper(),  
                })
        
        elif source == "blockchain":
            blockchain = data.get('blockchain', '')
            raw_data = data.get('transactions', []) or data.get('data', [])
            if blockchain == 'tron':
                for tx in data.get('data', []):
                    transactions.append({
                        'source': 'Tronscan',
                        'from': tx.get('ownerAddress'),
                        'to': tx.get('toAddress'),
                        'token': tx.get('tokenType', 'TRX'),
                        'amount': self.safe_divide(tx.get('amount'), 10**6),  # تبدیل از SUN
                        'date': datetime.fromtimestamp(tx.get('timestamp')/1000).strftime('%Y-%m-%d %H:%M:%S')
                    })
            elif blockchain in ['bsc', 'eth', 'polygon']:
                for tx in raw_data:
                    transactions.append({
                            'source': f'{blockchain.upper()}Scan',
                            'from': tx.get('from'),
                            'to': tx.get('to'),
                            'token': tx.get('tokenSymbol', blockchain.upper()),
                            'amount': self.safe_divide(tx.get('value'), 10**18),  # تبدیل از Wei
                            'date': datetime.fromtimestamp(int(tx.get('timeStamp'))).strftime('%Y-%m-%d %H:%M:%S')
                    })         
            
        elif source == "arkham":
            for tx in  data.get('transactions', []) or data.get('data', []):
                transactions.append({
                        'source': 'Arkham',
                        'from': tx.get('from'),
                        'to': tx.get('to'),
                        'token': tx.get('token'),
                        'amount': tx.get('amount'),
                        'date': datetime.fromtimestamp(tx.get('timestamp')).strftime('%Y-%m-%d %H:%M:%S')
                    })

        elif source == "tronscan":
            for tx in data.get('data', []):
                    transactions.append({
                        'source': 'Tronscan',
                        'from': tx.get('ownerAddress'),
                        'to': tx.get('toAddress'),
                        'token': tx.get('tokenType', 'TRX'),
                        'amount': self.safe_divide(tx.get('amount'), 10**6),
                        'date': datetime.fromtimestamp(tx.get('timestamp')/1000).strftime('%Y-%m-%d %H:%M:%S')
                    })
        
        elif source == "etherscan":
            if isinstance(data, str):
                try:
                    data = json.loads(data)
                except json.JSONDecodeError:
                    data = {}
                
            for tx in data.get('result', []):
                transactions.append({
                        'source': 'Etherscan',
                        'from': tx.get('from'),
                        'to': tx.get('to'),
                        'token': 'ETH',
                        'amount': self.safe_divide(tx.get('value'), 10**18),
                        'date': datetime.fromtimestamp(int(tx.get('timeStamp'))).strftime('%Y-%m-%d %H:%M:%S')
                    })
        
        elif source == "bscscan":
            for tx in data.get('result', []):
                transactions.append({
                        'source': 'BscScan',
                        'from': tx.get('from'),
                        'to': tx.get('to'),
                        'token': 'BNB',  # یا توکن‌های دیگر با پردازش اضافه
                        'amount': self.safe_divide(tx.get('value'), 1e18),  # تبدیل از Wei
                        'date': datetime.fromtimestamp(int(tx.get('timeStamp'))).strftime('%Y-%m-%d %H:%M:%S')
                    })

        return transactions
    

    @staticmethod
    def safe_divide(value, divisor, default=0):
        try:
            return float(value) / divisor
        except (ValueError, TypeError, ZeroDivisionError):
            return default
     
    def track_wallet(self):
        wallet_address = self.wallet_entry.get().strip()
        if not wallet_address:
            messagebox.showwarning("هشدار", "لطفاً آدرس کیف پول را وارد کنید")
            return

        self.search_btn.config(state=tk.DISABLED)
        self.show_chart_btn.config(state=tk.DISABLED)  # غیرفعال کردن دکمه نمودار
        self.status_var.set("در حال جستجو...")
        self.root.update()

    # پاک کردن نتایج قبلی
        for item in self.tree.get_children():
            self.tree.delete(item)

        all_transactions = []
        detector = ExchangeDetector()

        try:
            if self.solana_var.get():
                solana_data = self.get_solana_data(wallet_address)
                if solana_data and not self.stop_search_flag:
                    transactions = self.parse_transactions(solana_data, "solana")
                    all_transactions.extend(transactions)
           
            if self.covalent_var.get():
                covalent_data = self.get_covalent_data(wallet_address)
                if covalent_data:
                    all_transactions.extend(self.parse_transactions(covalent_data, "covalent"))

            if self.blockcypher_var.get():
                blockcypher_data = self.get_blockcypher_data(wallet_address)
                if blockcypher_data:
                    all_transactions.extend(self.parse_transactions(blockcypher_data, "blockcypher"))

            if self.blockchain_var.get():
                blockchain_data = self.get_blockchain_data(wallet_address)
                if blockchain_data:
                    all_transactions.extend(self.parse_transactions(blockchain_data, "blockchain"))

            if self.arkham_var.get():
                arkham_data = self.get_arkham_data(wallet_address)
                if arkham_data:
                    all_transactions.extend(self.parse_transactions(arkham_data, "arkham"))

            if self.tronscan_var.get():
                tronscan_data = self.get_tronscan_data(wallet_address)
                #if tronscan_data:
                all_transactions.extend(self.parse_transactions(tronscan_data, "tronscan"))

            if self.etherscan_var.get():
                etherscan_data = self.get_etherscan_data(wallet_address)
                if etherscan_data:
                    all_transactions.extend(self.parse_transactions(etherscan_data, "etherscan"))
            
            if self.bscscan_var.get():  # نیاز به اضافه کردن چک باکس در UI
                bscscan_data = self.get_bscscan_data(wallet_address)
                if bscscan_data:
                    all_transactions.extend(self.parse_transactions(bscscan_data, "bscscan"))

            if not all_transactions:
                messagebox.showinfo("اطلاع", "هیچ تراکنشی یافت نشد")
                self.status_var.set("آماده - هیچ تراکنشی یافت نشد")
                self.current_transactions = None  # تنظیم تراکنش‌ها به None
                return
            
            # مرحله 1: تشخیص صرافی‌ها برای تمام تراکنش‌ها
            for tx in all_transactions:
                tx['from_exchange'] = detector.detect_by_pattern(tx['from'], tx['token']) or "Unknown"
                tx['to_exchange'] = detector.detect_by_pattern(tx['to'], tx['token']) or "Unknown"
            
                if tx['from_exchange'] == "Unknown":
                    tx['from_exchange'] = detector.detect_by_api(tx['from'], tx['token'])
                if tx['to_exchange'] == "Unknown":
                    tx['to_exchange'] = detector.detect_by_api(tx['to'], tx['token'])
             

            # مرحله 2: گروه‌بندی تراکنش‌ها
            transaction_groups = {}
            for tx in all_transactions:
                key = tx.get('from', ''), tx.get('to', ''), tx.get('token', '')
                if key not in transaction_groups:
                    transaction_groups[key] = {
                        'source': tx.get('source', ''),
                        'from_exchange': tx.get('from_exchange', ''),
                        'to_exchange': tx.get('to_exchange', ''),
                        'amounts': [],
                        'dates': [],
                        'count': 0
                    }
                transaction_groups[key]['amounts'].append(f"{tx.get('amount', 0):.8f}")
                transaction_groups[key]['dates'].append(tx.get('date', ''))
                transaction_groups[key]['count'] += 1
               
            
            # نمایش در Treeview
            for key, group in transaction_groups.items():
                from_addr, to_addr, token = key
                self.tree.insert('', tk.END, values=(
                group['source'],
                from_addr,
                group['from_exchange'],
                to_addr,
                group['to_exchange'],
                token,
                "\n".join(group['amounts']),
                "\n".join(group['dates']),
                group['count']
                ))
            
            
            # ذخیره تراکنش‌ها برای نمایش نمودار
            self.current_transactions = all_transactions
            self.show_chart_btn.config(state=tk.NORMAL)  # فعال کردن دکمه نمودار

        # هایلایت آدرس
            self.highlight_searched_address(wallet_address)
            self.show_graph_btn.config(state=tk.NORMAL)  # فعال کردن دکمه گراف
            #self.status_var.set(f"آماده - {len(all_transactions)} تراکنش یافت شد")
            #self.search_btn.config(state=tk.NORMAL)
        except Exception as e:
            messagebox.showerror("خطا", f"خطا در پردازش تراکنش‌ها: {str(e)}")
            self.current_transactions = None
        finally:
            self.status_var.set(f"آماده - {len(all_transactions)} تراکنش یافت شد")
            self.search_btn.config(state=tk.NORMAL)
     
    def start_search(self):
        self.stop_search_flag = False
        self.search_btn.config(state=tk.DISABLED)
        self.stop_btn.config(state=tk.NORMAL)
        
        # ایجاد ریسه جدید برای جستجو
        self.search_thread = Thread(target=self._search_wallet_thread)
        self.search_thread.start()

    def stop_search(self):
        """توقف جستجو"""
        self.stop_search_flag = True
        if self.search_thread and self.search_thread.is_alive():
            self.search_thread.join(timeout=1)
        self.update_ui_after_stop()

    def _search_wallet_thread(self):
        """تابع اصلی جستجو در ریسه جداگانه"""
        try:
            wallet_address = self.wallet_entry.get().strip()
            if not wallet_address:
                return

            # فرآیند جستجو (مثال)
            for source in self.get_active_sources():
                if self.stop_search_flag:
                    break
                    
                self.fetch_and_process(source, wallet_address)
                
        except Exception as e:
            messagebox.showerror("خطا", str(e))
        finally:
            self.root.after(0, self.update_ui_after_stop)

    def update_ui_after_stop(self):
        """به روزرسانی UI پس از توقف"""
        self.search_btn.config(state=tk.NORMAL)
        self.stop_btn.config(state=tk.DISABLED)
        self.status_var.set("جستجو متوقف شد" if self.stop_search_flag else "جستجو کامل شد")

    def save_results(self):
        if not self.tree.get_children():
            messagebox.showwarning("هشدار", "هیچ داده‌ای برای ذخیره وجود ندارد")
            return
        
        file_path = filedialog.asksaveasfilename(
            defaultextension=".csv",
            filetypes=[("Excel Files", "*.xlsx"), ("CSV Files", "*.csv"), ("All Files", "*.*")],
            title="ذخیره نتایج به عنوان"
        )
        
        if not file_path:
            return
        
        try:
            data = []
            # جمع‌آوری داده‌های منحصر به فرد از Treeview
            #unique_data = []
            #seen = set()
            for item in self.tree.get_children():
                values = self.tree.item(item, 'values')
                data.append({
                    'منبع': values[0],
                    'مبدا': values[1],
                    'صرافی مبدا': values[2],  # ستون جدید
                    'مقصد': values[3],
                    'صرافی مقصد': values[4],  # ستون جدید
                    'توکن': values[5],
                    'مقادیر': values[6].replace("\n", " | "),
                    'تاریخ‌ها': values[7].replace("\n", " | "),
                    'تعداد': values[8]
                })
            
            df = pd.DataFrame(data)
            
            # محاسبه تعداد تکرار هر ترکیب مبدا/مقصد
            #df['تعداد تکرار'] = df.groupby(['مبدا', 'مقصد'])['مبدا'].transform('count')
            # مرتب‌سازی بر اساس تعداد تکرار (نزولی)
            #df = df.sort_values(by='تعداد تکرار', ascending=False)
            if file_path.endswith('.xlsx'):
                df.to_excel(file_path, index=False, engine='openpyxl')
            else:  # برای فایل‌های CSV
                df.to_csv(file_path, index=False, encoding='utf-8-sig')
            #df.to_csv(file_path, index=False, encoding='utf-8-sig')
            messagebox.showinfo("موفق", "نتایج با موفقیت ذخیره شد")
        except Exception as e:
            messagebox.showerror("خطا", f"خطا در ذخیره فایل: {str(e)}")
    
    def show_help(self):
        help_text = """...متن راهنما"""
        messagebox.showinfo("راهنما", help_text)
    
    def show_about(self):
        about_text = """...متن درباره برنامه"""
        messagebox.showinfo("درباره برنامه", about_text)

if __name__ == "__main__":
    root = tk.Tk()
    app = CryptoWalletTrackerApp(root)
    root.mainloop()