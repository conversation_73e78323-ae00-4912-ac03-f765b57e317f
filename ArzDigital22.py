from config.api_config import api_config
import requests
import json
from datetime import datetime
import pandas as pd
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from tkinter.scrolledtext import ScrolledText
import webbrowser
import time
import random
from collections import Counter
from threading import Thread
from tkinter import font as tkfont
from tkinter import ttk
import numpy as np
import networkx as nx
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import math
import scipy as sp   
from matplotlib.backends.backend_tkagg import NavigationToolbar2Tk  




class ExchangeDetector:
    def __init__(self):
        self.patterns = {
            "Binance": {
                "BTC": ["1", "3", "bc1"],
                "ETH": ["0x"],
                "TRC20": ["Txxxxxxxx"],  # الگوی خاص Binance
            },
            "KuCoin": {
                "TRC20": ["Txxxxxx"],  # الگوی خاص KuCoin
            },
            "Trust Wallet": {
                "BEP2": ["bnb1"],
            },
            "Ethereum Classic": {  
                "ETC": ["0x"],  
            },  
            "Litecoin": {  
                "LTC": ["Lxxxxxxxx"],  
            },  
            "Ripple": {  
                "XRP": ["rxxxxxxxxxxxxxxxxxxxxxxxxxx"],  
            },  
            "Cardano": {  
                "ADA": ["addr1qxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"],  
            },  
            "Polkadot": {  
                "DOT": ["***************************"],  
            } 
        }
        
        # اضافه کردن الگوهای کیف پول
        self.wallet_patterns = {
            "MetaMask": {
                "ETH": ["0x"],
                "BSC": ["0x"],
                "MATIC": ["0x"]
            },
            "Trust Wallet": {
                "BTC": ["1", "3", "bc1"],
                "ETH": ["0x"],
                "BSC": ["0x"],
                "TRX": ["T"],
                "BNB": ["bnb1"]
            },
            "Phantom": {
                "SOL": ["1", "2", "3", "4", "5", "6", "7", "8", "9"]
            },
            "TronLink": {
                "TRX": ["T"]
            },
            "Exodus": {
                "BTC": ["1", "3", "bc1"],
                "ETH": ["0x"],
                "LTC": ["L", "M", "3"],
                "XRP": ["r"]
            },
            "Atomic Wallet": {
                "BTC": ["1", "3", "bc1"],
                "ETH": ["0x"],
                "LTC": ["L", "M", "3"]
            }
        }
    
    def detect_wallet_type(self, address, currency):
        """تشخیص نوع کیف پول بر اساس الگوی آدرس"""
        for wallet, patterns in self.wallet_patterns.items():
            if currency in patterns:
                for prefix in patterns[currency]:
                    if address.startswith(prefix):
                        return wallet
        return "Unknown Wallet"
    
    def detect_by_pattern(self, address, currency):
        """تشخیص بر اساس الگوی استاتیک آدرس"""
        # ابتدا بررسی می‌کنیم که آیا آدرس متعلق به یک صرافی است
        for exchange, patterns in self.patterns.items():
            if currency in patterns:
                for prefix in patterns[currency]:
                    if address.startswith(prefix):
                        return exchange
        
        # اگر صرافی نبود، بررسی می‌کنیم که آیا متعلق به یک کیف پول است
        return self.detect_wallet_type(address, currency)
    
    def detect_by_api(self, address, currency):
        """تشخیص با استفاده از APIهای بلوکچین"""
        if currency == "TRC20":
            try:
                url = f"https://apilist.tronscan.org/api/account?address={address}"
                response = requests.get(url).json()
                if "exchange" in response.get("data", {}).get("tag", "").lower():
                    return response["data"]["tag"]
                elif "wallet" in response.get("data", {}).get("tag", "").lower():
                    return response["data"]["tag"]
            except:
                pass
        return "Unknown"


class CryptoWalletTrackerApp:
    def __init__(self, root):
        self.root = root    
        self.setup_main_window()
        self.create_widgets() 
        self.set_dark_theme()
        self.create_menu()
        self.exchange_detector = ExchangeDetector()  # ایجاد نمونه از کلاس تشخیص
        self.search_btn.config(command=self.track_wallet_threaded)
        self.current_transactions = None
        self.stop_search_flag = False
        self.search_thread = None

    def setup_main_window(self):
        self.root.geometry("1300x800")
        self.root.resizable(True, True)
        self.root.title("برنامه تحلیل و ردیابی رمز ارزها ")
        #self.main_frame = ttk.Frame(self.root, padding="10")
        #self.main_frame.pack(fill=tk.BOTH, expand=True)

    def create_widgets(self):
        # عنوان برنامه
      
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        title_label = ttk.Label(main_frame, 
                              text="ردیاب تراکنش‌های کیف پول ارز دیجیتال",
                              font=('Tahoma', 12, 'bold'))
        title_label.pack(pady=2)
        
        input_frame = ttk.Frame(main_frame)
        input_frame.pack(fill=tk.X, pady=10)
        
        ttk.Label(input_frame, text="آدرس کیف پول:").pack(side=tk.LEFT, padx=5)
        
        self.wallet_entry = ttk.Entry(input_frame, width=60)
        self.wallet_entry.pack(side=tk.LEFT, expand=True, fill=tk.X, padx=5)
        
        self.search_btn = ttk.Button(input_frame, text="جستجو", command=self.track_wallet)
        self.search_btn.pack(side=tk.LEFT, padx=5)
        
        source_frame = ttk.LabelFrame(main_frame, text="منابع اطلاعاتی", padding=10)
        source_frame.pack(fill=tk.X, pady=10)
        
        # لیست منابع و متغیرها
        sources = [
            ("blockchain(bsc,eth,poly)", "blockchain_var"),       
            ("covalent(All)", "covalent_var"),
            ("blockcypher", "blockcypher_var"),
            ("Arkham Intelligence", "arkham_var"),
            ("Tronscan", "tronscan_var"),
            ("solana", "solana_var"), 
            ("Etherscan (ETH)", "etherscan_var"), 
            ("BscScan (BSC)", "bscscan_var")
                
        ]
        
        # مقداردهی اولیه متغیرها
        for _, var_name in sources:
            setattr(self, var_name, tk.BooleanVar(value=True))
        
        # ایجاد چک‌باکس‌ها به صورت جدولی
        for i, (text, var_name) in enumerate(sources):
            row = i // 3  # تقسیم به دو ستون
            col = i % 3
            ttk.Checkbutton(
                source_frame,
                text=text,
                variable=getattr(self, var_name)
            ).grid(
                row=row,
                column=col,
                sticky="w",
                padx=10,
                pady=1,
                ipadx=5,
                ipady=1
            )
        
        # تنظیمات گرید برای ظاهر بهتر
        source_frame.grid_columnconfigure(0, weight=1, uniform="group1")
        source_frame.grid_columnconfigure(1, weight=1, uniform="group1")
        
 
        result_frame = ttk.LabelFrame(main_frame, text="نتایج تراکنش‌ها", padding=10)
        result_frame.pack(fill=tk.BOTH, expand=True)
        


       # تنظیم ارتفاع سطرها - این بخش باید قبل از ایجاد Treeview باشد
        style = ttk.Style()
        style.theme_use('clam')  # استفاده از تمی که از rowheight پشتیبانی می‌کند
        style.configure('Treeview', 
               rowheight=30,
               font=('Tahoma', 9),
               background="#ffffff",
               fieldbackground="#ffffff")
        #self.tree = ttk.Treeview(result_frame, columns=('Source', 'From', 'To', 'Token', 'Amount', 'Date'), show='headings')
        #self.tree = ttk.Treeview(result_frame, columns=('Source', 'From', 'To', 'Token', 'Amounts', 'Dates', 'Count'), show='headings')
      
        self.tree = ttk.Treeview(result_frame, columns=(
            'Source', 'From', 'From Type', 'From Exchange', 'To', 'To Type', 'To Exchange', 
            'Token', 'Amounts', 'Dates', 'Count'
            ), show='headings')
        #self.tree.heading('Count', text='تعداد تکرار')
        #self.tree.column('Count', width=80)
           # تنظیمات ستون‌ها
        self.tree.heading('Source', text='منبع')
        self.tree.heading('From', text='مبدا')
        self.tree.heading('From Type', text='نوع مبدا')
        self.tree.heading('From Exchange', text='صرافی مبدا')
        self.tree.heading('To', text='مقصد')
        self.tree.heading('To Type', text='نوع مقصد')
        self.tree.heading('To Exchange', text='صرافی مقصد')
        self.tree.heading('Token', text='توکن')
        self.tree.heading('Amounts', text='مقادیر')
        self.tree.heading('Dates', text='تاریخ‌ها')
        self.tree.heading('Count', text='تعداد تکرار')

        
    # تنظیم عرض ستون‌ها
        self.tree.column('Source', width=100)
        self.tree.column('From', width=200)
        self.tree.column('From Type', width=100)
        self.tree.column('From Exchange', width=100)
        self.tree.column('To', width=200)
        self.tree.column('To Type', width=100)
        self.tree.column('To Exchange', width=100)
        self.tree.column('Token', width=100)
        self.tree.column('Amounts', width=150)
        self.tree.column('Dates', width=150)
        self.tree.column('Count', width=80)
        # تنظیم تراز متن برای ستون‌های چندخطی
        self.tree.tag_configure('multiline', font=('Tahoma', 9))

        #for col, (text, width) in columns.items():
        #    self.tree.heading(col, text=text)
        #    self.tree.column(col, width=width)
        
        scrollbar = ttk.Scrollbar(result_frame, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscroll=scrollbar.set)
        
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        self.status_var = tk.StringVar()
        self.status_var.set("آماده")
        status_label = ttk.Label(main_frame, textvariable=self.status_var,font="bold")
        status_label.pack(side=tk.BOTTOM, fill=tk.X)

        # اضافه کردن دکمه نمایش آمار
        stats_frame = ttk.Frame(main_frame)
        stats_frame.pack(fill=tk.X, pady=5)
        # دکمه نمایش نمودار (پایین پنجره)
        self.show_chart_btn = ttk.Button(main_frame,
                                   text="نمایش نمودار",
                                   command=self.show_chart,
                                   state=tk.DISABLED)
        self.show_chart_btn.pack(side=tk.RIGHT, padx=5) 
    
        self.stop_btn = ttk.Button(main_frame, text="توقف جستجو", 
                                 command=self.stop_search,
                                 state=tk.DISABLED)
        self.stop_btn.pack(side=tk.RIGHT, padx=5)

        # دکمه نمایش گراف
        self.show_graph_btn = ttk.Button(
            main_frame,
            text="نمایش گراف پیشرفته",
            command=self.show_transaction_graph,
            state=tk.DISABLED
        )
        self.show_graph_btn.pack(side=tk.RIGHT, padx=5)
        #self.stats_btn = ttk.Button(stats_frame, text="نمایش آمار", command=self.show_stats_from_tree)
        #self.stats_btn.pack(side=tk.LEFT, padx=5)

        self.current_tooltip = None

    # استفاده از closure برای دسترسی به self
        def make_tooltip_handlers(self):
            def show_tooltip(event):
                if hasattr(self, 'current_tooltip') and self.current_tooltip:
                    self.current_tooltip.destroy()
                
                item = self.tree.identify_row(event.y)
                col = self.tree.identify_column(event.x)
                
                if item and col:
                    value = self.tree.item(item, 'values')[int(col[1:])-1]
                    
                    if "\n" in value:
                        self.current_tooltip = tk.Toplevel(self.root)
                        self.current_tooltip.wm_overrideredirect(True)
                        self.current_tooltip.wm_geometry(f"+{event.x_root+20}+{event.y_root+10}")
                        
                        label = ttk.Label(
                            self.current_tooltip,
                            text=value,
                            background="#2F4F4F",
                            foreground="white",
                            relief="solid",
                            padding=5,
                            font=('Tahoma', 9)
                        )
                        label.pack()

            def close_tooltip(event=None):
                if hasattr(self, 'current_tooltip') and self.current_tooltip:
                    self.current_tooltip.destroy()
                    self.current_tooltip = None

            return show_tooltip, close_tooltip

        show_tooltip, close_tooltip = make_tooltip_handlers(self)
        self.tree.bind("<Motion>", show_tooltip)
        self.tree.bind("<Leave>", close_tooltip)

    def show_chart(self):
        if not self.current_transactions:
            messagebox.showwarning("هشدار", "داده‌ای برای نمایش وجود ندارد")
            return
            
        chart_window = tk.Toplevel(self.root)
        chart_window.title("نمودار تراکنش‌ها")
        chart_window.geometry("800x500")  # افزایش اندازه پنجره
        
        chart_canvas = tk.Canvas(chart_window, bg='#2e2e2e')
        chart_canvas.pack(fill=tk.BOTH, expand=True)
        
        wallet_address = self.wallet_entry.get().strip()
        total_in = sum(float(tx['amount']) for tx in self.current_transactions 
                    if tx['to'] == wallet_address)
        total_out = sum(float(tx['amount']) for tx in self.current_transactions 
                    if tx['from'] == wallet_address)
        
        # تنظیمات نمایش
        chart_height = 400
        chart_width = 600
        padding = 100
        min_bar_width = 30  # حداقل عرض بار
        
        # محاسبه مقیاس
        max_val = max(total_in, total_out, 1e-8)  # حداقل مقدار بسیار کوچک برای جلوگیری از تقسیم بر صفر
        
        # استفاده از تابع لگاریتمی برای نمایش بهتر مقادیر کوچک
        def scale_value(val):
            return (math.log10(val + 1e-8) / math.log10(max_val + 1e-8)) * (chart_width - 2*padding)
        
        in_width = padding + max(scale_value(total_in), min_bar_width)
        out_width = padding + max(scale_value(total_out), min_bar_width)
        
        # متن عنوان
        chart_canvas.create_text(chart_width/2, 30, 
                            text=f"نمودار تراکنش‌های کیف پول {wallet_address[:8]}...", 
                            fill="white", font=('B Nazanin', 14, 'bold'))
        
        # نمودار ورودی (سبز)
        chart_canvas.create_rectangle(
            padding, 100, 
            in_width, 150, 
            fill='#2ecc71', outline='', width=2
        )
        chart_canvas.create_text(
            chart_width - 50, 125, 
            text=f"{total_in:.8f}",  # نمایش 8 رقم اعشار
            fill="white",
            font=('B Nazanin', 10)
        )
        
        # نمودار خروجی (قرمز)
        chart_canvas.create_rectangle(
            padding, 200, 
            out_width, 250, 
            fill='#e74c3c', outline='', width=2
        )
        chart_canvas.create_text(
            chart_width - 50, 225, 
            text=f"{total_out:.8f}",  # نمایش 8 رقم اعشار
            fill="white",
            font=('B Nazanin', 10)
        )
        
        # راهنما
        chart_canvas.create_text(
            padding + 10, 80, 
            text="ورودی (دریافتی)", 
            fill="white", 
            anchor='w',
            font=('B Nazanin', 11)
        )
        chart_canvas.create_text(
            padding + 10, 180, 
            text="خروجی (ارسال شده)", 
            fill="white", 
            anchor='w',
            font=('B Nazanin', 11)
        )
        
        # نتیجه نهایی
        net = total_in - total_out
        net_color = '#2ecc71' if net >= 0 else '#e74c3c'
        chart_canvas.create_text(
            chart_width/2, 300, 
            text=f"نتیجه نهایی: {net:.8f}",
            fill=net_color,
            font=('B Nazanin', 12, 'bold')
        )
        
        # نمایش مقیاس
        chart_canvas.create_text(
            padding, 350,
            text=f"0",
            fill="white",
            anchor='w'
        )
        chart_canvas.create_text(
            chart_width - padding, 350,
            text=f"{max_val:.4f}",
            fill="white",
            anchor='e'
        )

    def animate_transactions(self, transactions):
        if not hasattr(self, 'animation_frame'):
            self.animation_frame = ttk.Frame(self.root)
            self.animation_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
    
    # پاک کردن کانواس قبلی (اگر وجود دارد)
        if hasattr(self, 'animation_canvas'):
            self.animation_canvas.destroy()
        
        self.animation_canvas = tk.Canvas(self.animation_frame, bg='#2e2e2e', highlightthickness=0)
        self.animation_canvas.pack(fill=tk.BOTH, expand=True)
    
        try:
            # محاسبه مقادیر با مدیریت خطا
            total_in = 0.0
            total_out = 0.0
            wallet_address = self.wallet_entry.get().strip()
            
            for tx in transactions:
                try:
                    amount = float(tx.get('amount', 0))
                    if tx.get('to') == wallet_address:
                        total_in += amount
                    elif tx.get('from') == wallet_address:
                        total_out += amount
                except (ValueError, TypeError):
                    continue
            
            # متن راهنما
            self.animation_canvas.create_text(100, 30, text="نمودار ورودی و خروجی", 
                                            fill="white", font=('B Nazanin', 14))
            
            # تابع به‌روزرسانی انیمیشن
            def update_animation(step):
                self.animation_canvas.delete('animation')
                
                # محاسبه مقادیر جاری
                current_in = min(step/10 * total_in, total_in)
                current_out = min(step/10 * total_out, total_out)
                
                # رسم نمودارها
                max_val = max(total_in, total_out, 1)  # جلوگیری از تقسیم بر صفر
                self.animation_canvas.create_rectangle(
                    50, 80, 50 + (current_in/max_val) * 300, 120,
                    fill='#2ecc71', outline='', tags='animation'
                )
                self.animation_canvas.create_text(
                    360, 100, text=f"{current_in:.4f}",
                    fill="white", tags='animation'
                )
                
                self.animation_canvas.create_rectangle(
                    50, 180, 50 + (current_out/max_val) * 300, 220,
                    fill='#e74c3c', outline='', tags='animation'
                )
                self.animation_canvas.create_text(
                    360, 200, text=f"{current_out:.4f}",
                    fill="white", tags='animation'
                )
                
                if step < 10:
                    self.root.after(150, update_animation, step+1)
                else:
                    # نمایش نتیجه نهایی
                    self.animation_canvas.create_text(
                        200, 250, 
                        text=f"ورودی کل: {total_in:.4f} | خروجی کل: {total_out:.4f}",
                        fill="white", font=('B Nazanin', 11), tags='animation'
                    )
            
            update_animation(0)
        
        except Exception as e:
            print(f"خطا در ایجاد انیمیشن: {str(e)}")
            self.animation_canvas.create_text(
                150, 50, 
                text="خطا در نمایش انیمیشن", 
                fill="red", font=('B Nazanin', 12)
            )
    
    def set_dark_theme(self):
        self.root.configure(bg='#2e2e2e')
        self.style = ttk.Style()
        self.style.theme_use('clam')
        
        self.style.configure('TFrame', background='#2e2e2e')
        self.style.configure('TLabel', background='#2e2e2e', foreground='white')
        self.style.configure('TButton', background='#3e3e3e', foreground='white')
        self.style.configure('TEntry', fieldbackground='#3e3e3e', foreground='white')
        self.style.configure('TCombobox', fieldbackground='#3e3e3e', foreground='white')
        self.style.map('TButton', background=[('active', '#4e4e4e')])
    
    def add_smart_tooltips(self, wallet_address):
        for item in self.tree.get_children():
            values = self.tree.item(item, 'values')
        
            if wallet_address in values[1]:  # مبدأ
                self.tree.tag_configure('from_tip', background='#2e2e2e')
                self.tree.item(item, tags=('from_tip',))
            
            elif wallet_address in values[3]:  # مقصد
                self.tree.tag_configure('to_tip', background='#2e2e2e')
                self.tree.item(item, tags=('to_tip',))

    # اتصال رویداد هاور
        self.tree.bind("<Motion>", lambda e: self.show_tooltip(e, wallet_address))

    def highlight_searched_address(self, wallet_address):
        self.tree.tag_configure('highlight_address', foreground='red')
        
        for item in self.tree.get_children():
            values = list(self.tree.item(item, 'values'))

            new_from = values[1]
            new_to = values[3]
        # بررسی ستون From (مبدا)
            if wallet_address in values[1]:  # اگر آدرس در مبدا وجود دارد
                new_from = new_from.replace(wallet_address, 
                                      f"{wallet_address}==>")
        # بررسی ستون To (مقصد)
            if wallet_address in values[3]:  # اگر آدرس در مقصد وجود دارد
               new_to = new_to.replace(wallet_address, 
                                  f"==>{wallet_address}")
        # اگر آدرس در هر یک از ستون‌ها وجود داشت، رکورد را آپدیت کن
            if new_from != values[1] or new_to != values[3]:
                self.tree.item(item, 
                         values=(values[0], new_from, values[2], new_to, *values[4:]),
                         tags=('highlight',))

    def show_transaction_graph(self):
    # بررسی وجود داده
        if not self.current_transactions:
            messagebox.showwarning("هشدار", "داده‌ای برای نمایش وجود ندارد")
            return

        # --- تنظیمات اولیه پنجره ---
        graph_window = tk.Toplevel(self.root)
        graph_window.title("گراف تراکنش‌ها - تحلیل ارتباطات")
        graph_window.geometry("1400x1000")
        
        # ایجاد فریم‌های اصلی
        main_frame = ttk.Frame(graph_window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        graph_frame = ttk.Frame(main_frame)
        graph_frame.pack(fill=tk.BOTH, expand=True)
        
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill=tk.X, pady=(0, 10))

        # --- پردازش داده‌ها ---
        wallet_address = self.wallet_entry.get().strip()
        threshold = 0.0001  # حداقل مقدار تراکنش برای نمایش (کاهش یافته برای نمایش مقادیر کوچک)
        
        # محاسبه آمار کلی
        total_in = sum(float(tx.get('amount', 0)) for tx in self.current_transactions if tx['to'] == wallet_address)
        total_out = sum(float(tx.get('amount', 0)) for tx in self.current_transactions if tx['from'] == wallet_address)
        unique_addresses = {tx['from'] for tx in self.current_transactions}.union(
                        {tx['to'] for tx in self.current_transactions})

        # --- ایجاد گراف شبکه‌ای ---
        G = nx.DiGraph()
        
        # گروه‌بندی و فیلتر تراکنش‌ها
        merged_edges = {}
        for tx in self.current_transactions:
            amount = float(tx.get('amount', 0))
            if amount < threshold:
                continue
                
            key = (tx['from'], tx['to'], tx.get('token', 'UNKNOWN'))
            if key in merged_edges:
                merged_edges[key]['amount'] += amount
                merged_edges[key]['count'] += 1
                merged_edges[key]['dates'].append(tx.get('date', ''))
            else:
                merged_edges[key] = {
                    'amount': amount,
                    'count': 1,
                    'dates': [tx.get('date', '')],
                    'token': tx.get('token', 'UNKNOWN')
                }

        # اضافه کردن گره‌ها و یال‌ها به گراف
        for (from_addr, to_addr, _), data in merged_edges.items():
            # کوتاه کردن آدرس‌ها برای نمایش
            display_from = f"{from_addr[:6]}...{from_addr[-4:]}" 
            display_to = f"{to_addr[:6]}...{to_addr[-4:]}"
            
            # مشخص کردن نوع گره
            from_type = 'wallet' if from_addr == wallet_address else 'source'
            to_type = 'wallet' if to_addr == wallet_address else 'destination'
            
            # اضافه کردن گره‌ها با ویژگی‌های نمایشی
            G.add_node(display_from, 
                    full_address=from_addr,
                    node_type=from_type,
                    size=800 if from_type == 'wallet' else 500)
            
            G.add_node(display_to,
                    full_address=to_addr,
                    node_type=to_type,
                    size=800 if to_type == 'wallet' else 500)
            
            # اضافه کردن یال با اطلاعات تراکنش
            G.add_edge(display_from, display_to,
                    amount=data['amount'],
                    count=data['count'],
                    token=data['token'],
                    dates=data['dates'])

        # --- رسم گراف ---
        try:
            plt.style.use('seaborn-v0_8')
        except:
            plt.style.use('ggplot')
        fig = plt.figure(figsize=(12, 9), dpi=100, facecolor='#f8f9fa')
        
        
        # تعیین چیدمان گره‌ها
        pos = nx.spring_layout(G, k=0.25, iterations=100, seed=42)
        
        # تعیین ویژگی‌های نمایشی
        node_colors = {
            'wallet': '#e63946',  # قرمز برای کیف پول کاربر
            'source': '#457b9d',  # آبی برای مبداها
            'destination': '#2a9d8f'  # سبز برای مقصدها
        }
        
        # --- بهبود نمایش برای مقادیر کوچک ---
        # محاسبه حداکثر مقدار برای نرمالایز کردن
        max_amount = max(data['amount'] for data in merged_edges.values()) if merged_edges else 1
        
        # تابع برای محاسبه هوشمند عرض یال‌ها
        def calculate_edge_width(amount, max_amount):
            if max_amount <= 0:
                return 0.5
            # استفاده از ترکیب مقیاس لگاریتمی و خطی
            log_scale = math.log10(amount + 1) / math.log10(max_amount + 1)
            linear_scale = amount / max_amount
            # ترکیب دو مقیاس با وزن بیشتر برای مقادیر کوچک
            return 0.5 + (log_scale * 0.7 + linear_scale * 0.3) * 10
        
        edge_widths = [calculate_edge_width(G.edges[e]['amount'], max_amount) for e in G.edges()]
        min_edge_width = 0.8  # حداقل عرض برای یال‌ها
        edge_widths = [max(w, min_edge_width) for w in edge_widths]
        
        edge_colors = []
        for u, v in G.edges():
            if G.nodes[v]['node_type'] == 'wallet':  # ورودی به کیف پول
                edge_colors.append('#2a9d8f')
            elif G.nodes[u]['node_type'] == 'wallet':  # خروجی از کیف پول
                edge_colors.append('#e63946')
            else:  # تراکنش بین سایر آدرس‌ها
                edge_colors.append('#a8dadc')

        # رسم گره‌ها
        for node_type, color in node_colors.items():
            nodes = [n for n in G.nodes() if G.nodes[n]['node_type'] == node_type]
            sizes = [G.nodes[n]['size'] for n in nodes]
            nx.draw_networkx_nodes(
                G, pos, nodelist=nodes,
                node_color=color,
                node_size=sizes,
                alpha=0.9,
                edgecolors='#333',
                linewidths=0.5
            )

        # رسم یال‌ها با عرض‌های بهینه‌شده
        nx.draw_networkx_edges(
            G, pos,
            edge_color=edge_colors,
            width=edge_widths,
            arrowstyle='->',
            arrowsize=15,
            connectionstyle='arc3,rad=0.1',
            alpha=0.8
        )

        # بهبود نمایش مقادیر روی یال‌ها برای مقادیر کوچک
        edge_labels = {}
        for u, v in G.edges():
            amount = G.edges[(u, v)]['amount']
            token = G.edges[(u, v)]['token']
            
            # فرمت هوشمند برای نمایش مقادیر
            if amount < 0.0001:
                label = f"{amount:.8f} {token}"  # 8 رقم اعشار برای مقادیر بسیار کوچک
            elif amount < 0.01:
                label = f"{amount:.6f} {token}"  # 6 رقم اعشار برای مقادیر کوچک
            else:
                label = f"{amount:.4f} {token}"  # 4 رقم اعشار برای مقادیر معمولی
                
            edge_labels[(u, v)] = label

        nx.draw_networkx_edge_labels(
            G, pos,
            edge_labels=edge_labels,
            font_size=8,
            font_color='#333',
            bbox=dict(alpha=0.8, facecolor='white', edgecolor='none', boxstyle='round,pad=0.3')
        )

        # نمایش برچسب گره‌ها
        nx.draw_networkx_labels(
            G, pos,
            font_size=9,
            font_family='Tahoma',
            font_weight='bold'
        )

        # --- اضافه کردن عناصر گرافیکی ---
        plt.title(
            f"گراف تراکنش‌های کیف پول {wallet_address[:8]}...{wallet_address[-4:]}",
            fontsize=14,
            pad=20,
            fontweight='bold'
        )
        
        # ایجاد لیست راهنما
        legend_elements = [
            plt.Line2D([0], [0], marker='o', color='w', label='کیف پول شما',
                    markerfacecolor='#e63946', markersize=10),
            plt.Line2D([0], [0], marker='o', color='w', label='آدرس مبدا',
                    markerfacecolor='#457b9d', markersize=10),
            plt.Line2D([0], [0], marker='o', color='w', label='آدرس مقصد',
                    markerfacecolor='#2a9d8f', markersize=10),
            plt.Line2D([0], [0], color='#2a9d8f', lw=2, label='ورودی به کیف پول'),
            plt.Line2D([0], [0], color='#e63946', lw=2, label='خروجی از کیف پول')
        ]
        
        plt.legend(
            handles=legend_elements,
            loc='upper right',
            bbox_to_anchor=(1.25, 1),
            frameon=True,
            framealpha=0.9
        )

        # نمایش آمار پایین گراف
        stats_text = (
            f"تعداد تراکنش‌ها: {len(self.current_transactions):,}\n"
            f"تعداد آدرس‌های منحصر به فرد: {len(unique_addresses):,}\n"
            f"مجموع ورودی: {total_in:.8f}\n"
            f"مجموع خروجی: {total_out:.8f}\n"
            f"حداقل مقدار نمایش داده شده: {threshold:.8f}"
        )
        
        plt.figtext(
            0.5, 0.02,
            stats_text,
            ha='center',
            fontsize=10,
            bbox=dict(facecolor='#f0f0f0', alpha=0.7, pad=5)
        )

        # --- اضافه کردن گراف به رابط کاربری ---
        canvas = FigureCanvasTkAgg(fig, master=graph_frame)
        canvas.draw()
        
        # ایجاد اسکرول بار
        scroll_y = ttk.Scrollbar(graph_frame, orient=tk.VERTICAL)
        scroll_y.pack(side=tk.RIGHT, fill=tk.Y)
        canvas.get_tk_widget().pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        canvas.get_tk_widget().config(yscrollcommand=scroll_y.set)
        scroll_y.config(command=canvas.get_tk_widget().yview)

        # اضافه کردن نوار ابزار برای زوم و حرکت
        toolbar = NavigationToolbar2Tk(canvas, graph_frame)
        toolbar.update()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

        # --- ایجاد کنترل‌های پایین پنجره ---
        btn_frame = ttk.Frame(control_frame)
        btn_frame.pack(pady=5)
        
        # اضافه کردن اسلایدر برای تنظیم حداقل مقدار نمایش
        threshold_frame = ttk.Frame(control_frame)
        threshold_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(threshold_frame, text="حداقل مقدار نمایش:").pack(side=tk.LEFT)
        
        threshold_slider = ttk.Scale(
            threshold_frame,
            from_=-8,
            to=0,
            value=math.log10(threshold) if threshold > 0 else -8,
            command=lambda v: self.update_graph_threshold(10**float(v))
        )
        threshold_slider.pack(side=tk.LEFT, expand=True, fill=tk.X, padx=10)
        
        threshold_value = ttk.Label(threshold_frame, text=f"{threshold:.8f}")
        threshold_value.pack(side=tk.LEFT)
        
        # دکمه‌های کنترل
        ttk.Button(
            btn_frame,
            text="ذخیره تصویر",
            command=lambda: self.save_graph_image(fig)
        ).grid(row=0, column=0, padx=5)
        
        ttk.Button(
            btn_frame,
            text="نمایش آمار کامل",
            command=lambda: self.show_full_stats(
                wallet_address,
                total_in,
                total_out,
                len(self.current_transactions),
                len(unique_addresses)
            )
        ).grid(row=0, column=1, padx=5)
        
        ttk.Button(
            btn_frame,
            text="بستن",
            command=graph_window.destroy
        ).grid(row=0, column=2, padx=5)

        # --- نوار وضعیت برای نمایش اطلاعات روی هاور ---
        status_var = tk.StringVar()
        status_label = ttk.Label(
            control_frame,
            textvariable=status_var,
            relief=tk.SUNKEN,
            anchor=tk.W,
            padding=(5, 2)
        )
        status_label.pack(fill=tk.X)

        # --- عملکرد هاور موس ---
        def on_hover(event):
            if event.inaxes:
                # بررسی هاور روی گره‌ها
                for node in G.nodes():
                    node_pos = pos[node]
                    distance = np.sqrt((node_pos[0]-event.xdata)**2 + (node_pos[1]-event.ydata)**2)
                    if distance < 0.03:
                        status_var.set(
                            f"آدرس: {G.nodes[node]['full_address']} | "
                            f"نوع: {'کیف پول شما' if G.nodes[node]['node_type'] == 'wallet' else 'آدرس خارجی'}"
                        )
                        return
                
                # بررسی هاور روی یال‌ها
                for u, v in G.edges():
                    edge_pos = [(pos[u][0] + pos[v][0])/2, (pos[u][1] + pos[v][1])/2]
                    distance = np.sqrt((edge_pos[0]-event.xdata)**2 + (edge_pos[1]-event.ydata)**2)
                    if distance < 0.03:
                        edge_data = G.edges[(u, v)]
                        status_var.set(
                            f"تراکنش: {edge_data['amount']:.8f} {edge_data['token']} | "
                            f"تعداد: {edge_data['count']} | "
                            f"اولین تاریخ: {edge_data['dates'][0]}"
                        )
                        return
                
                status_var.set("")

        fig.canvas.mpl_connect('motion_notify_event', on_hover)

        # --- تنظیمات نهایی پنجره ---
        graph_window.update()
        canvas.get_tk_widget().config(scrollregion=canvas.get_tk_widget().bbox("all"))

           
    def create_menu(self):
        menubar = tk.Menu(self.root)
        
        file_menu = tk.Menu(menubar, tearoff=0)
        file_menu.add_command(label="ذخیره نتایج", command=self.save_results)
        file_menu.add_separator()
        file_menu.add_command(label="خروج", command=self.root.quit)
        menubar.add_cascade(label="فایل", menu=file_menu)
        
        help_menu = tk.Menu(menubar, tearoff=0)
        help_menu.add_command(label="راهنما", command=self.show_help)
        help_menu.add_command(label="درباره", command=self.show_about)
        menubar.add_cascade(label="کمک", menu=help_menu)
        
        self.root.config(menu=menubar)
    '''
    def show_transaction_graph(self):    
        # بررسی وجود داده
        if not self.current_transactions:
            messagebox.showwarning("هشدار", "داده‌ای برای نمایش وجود ندارد")
            return

        # --- تنظیمات اولیه پنجره ---
        graph_window = tk.Toplevel(self.root)
        graph_window.title("گراف تراکنش‌ها - تحلیل ارتباطات")
        graph_window.geometry("1400x1000")
        
        # ایجاد فریم‌های اصلی
        main_frame = ttk.Frame(graph_window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        graph_frame = ttk.Frame(main_frame)
        graph_frame.pack(fill=tk.BOTH, expand=True)
        
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill=tk.X, pady=(0, 10))

        # --- پردازش داده‌ها ---
        wallet_address = self.wallet_entry.get().strip()
        threshold = 0.001  # حداقل مقدار تراکنش برای نمایش
        
        # محاسبه آمار کلی
        total_in = sum(float(tx.get('amount', 0)) for tx in self.current_transactions if tx['to'] == wallet_address)
        total_out = sum(float(tx.get('amount', 0)) for tx in self.current_transactions if tx['from'] == wallet_address)
        unique_addresses = {tx['from'] for tx in self.current_transactions}.union(
                        {tx['to'] for tx in self.current_transactions})

        # --- ایجاد گراف شبکه‌ای ---
        G = nx.DiGraph()
        
        # گروه‌بندی و فیلتر تراکنش‌ها
        merged_edges = {}
        for tx in self.current_transactions:
            amount = float(tx.get('amount', 0))
            if amount < threshold:
                continue
                
            key = (tx['from'], tx['to'], tx.get('token', 'UNKNOWN'))
            if key in merged_edges:
                merged_edges[key]['amount'] += amount
                merged_edges[key]['count'] += 1
                merged_edges[key]['dates'].append(tx.get('date', ''))
            else:
                merged_edges[key] = {
                    'amount': amount,
                    'count': 1,
                    'dates': [tx.get('date', '')],
                    'token': tx.get('token', 'UNKNOWN')
                }

        # اضافه کردن گره‌ها و یال‌ها به گراف
        for (from_addr, to_addr, _), data in merged_edges.items():
            # کوتاه کردن آدرس‌ها برای نمایش
            display_from = f"{from_addr[:6]}...{from_addr[-4:]}" 
            display_to = f"{to_addr[:6]}...{to_addr[-4:]}"
            
            # مشخص کردن نوع گره
            from_type = 'wallet' if from_addr == wallet_address else 'source'
            to_type = 'wallet' if to_addr == wallet_address else 'destination'
            
            # اضافه کردن گره‌ها با ویژگی‌های نمایشی
            G.add_node(display_from, 
                    full_address=from_addr,
                    node_type=from_type,
                    size=800 if from_type == 'wallet' else 500)
            
            G.add_node(display_to,
                    full_address=to_addr,
                    node_type=to_type,
                    size=800 if to_type == 'wallet' else 500)
            
            # اضافه کردن یال با اطلاعات تراکنش
            G.add_edge(display_from, display_to,
                    amount=data['amount'],
                    count=data['count'],
                    token=data['token'],
                    dates=data['dates'])

        
        
        # --- رسم گراف ---
        #plt.style.use('seaborn')
        fig = plt.figure(figsize=(12, 9), dpi=100, facecolor='#f8f9fa')
        try:
            plt.style.use('seaborn-v0_8')
        except:
            plt.style.use('ggplot')
        # تعیین چیدمان گره‌ها
        pos = nx.spring_layout(G, k=0.25, iterations=100, seed=42)
        
        # تعیین ویژگی‌های نمایشی
        node_colors = {
            'wallet': '#e63946',  # قرمز برای کیف پول کاربر
            'source': '#457b9d',  # آبی برای مبداها
            'destination': '#2a9d8f'  # سبز برای مقصدها
        }
        
        edge_colors = []
        for u, v in G.edges():
            if G.nodes[v]['node_type'] == 'wallet':  # ورودی به کیف پول
                edge_colors.append('#2a9d8f')
            elif G.nodes[u]['node_type'] == 'wallet':  # خروجی از کیف پول
                edge_colors.append('#e63946')
            else:  # تراکنش بین سایر آدرس‌ها
                edge_colors.append('#a8dadc')

        # رسم گره‌ها
        for node_type, color in node_colors.items():
            nodes = [n for n in G.nodes() if G.nodes[n]['node_type'] == node_type]
            sizes = [G.nodes[n]['size'] for n in nodes]
            nx.draw_networkx_nodes(
                G, pos, nodelist=nodes,
                node_color=color,
                node_size=sizes,
                alpha=0.9,
                edgecolors='#333',
                linewidths=0.5
            )

        # رسم یال‌ها
        nx.draw_networkx_edges(
            G, pos,
            edge_color=edge_colors,
            width=[0.3 + math.log(1 + G.edges[e]['amount']/10) for e in G.edges()],
            arrowstyle='->',
            arrowsize=12,
            connectionstyle='arc3,rad=0.1',
            alpha=0.7
        )

        # نمایش مقادیر روی یال‌ها
        edge_labels = {
            (u, v): f"{G.edges[(u, v)]['amount']:.2f} {G.edges[(u, v)]['token']}"
            for u, v in G.edges()
        }
        nx.draw_networkx_edge_labels(
            G, pos,
            edge_labels=edge_labels,
            font_size=8,
            font_color='#333',
            bbox=dict(alpha=0.7, facecolor='white', edgecolor='none', boxstyle='round,pad=0.3')
        )

        # نمایش برچسب گره‌ها
        nx.draw_networkx_labels(
            G, pos,
            font_size=9,
            font_family='Tahoma',
            font_weight='bold'
        )

        # --- اضافه کردن عناصر گرافیکی ---
        plt.title(
            f"گراف تراکنش‌های کیف پول {wallet_address[:8]}...{wallet_address[-4:]}",
            fontsize=14,
            pad=20,
            fontweight='bold'
        )
        
        # ایجاد لیست راهنما
        legend_elements = [
            plt.Line2D([0], [0], marker='o', color='w', label='کیف پول شما',
                    markerfacecolor='#e63946', markersize=10),
            plt.Line2D([0], [0], marker='o', color='w', label='آدرس مبدا',
                    markerfacecolor='#457b9d', markersize=10),
            plt.Line2D([0], [0], marker='o', color='w', label='آدرس مقصد',
                    markerfacecolor='#2a9d8f', markersize=10),
            plt.Line2D([0], [0], color='#2a9d8f', lw=2, label='ورودی به کیف پول'),
            plt.Line2D([0], [0], color='#e63946', lw=2, label='خروجی از کیف پول')
        ]
        
        plt.legend(
            handles=legend_elements,
            loc='upper right',
            bbox_to_anchor=(1.25, 1),
            frameon=True,
            framealpha=0.9
        )

        # نمایش آمار پایین گراف
        stats_text = (
            f"تعداد تراکنش‌ها: {len(self.current_transactions):,}\n"
            f"تعداد آدرس‌های منحصر به فرد: {len(unique_addresses):,}\n"
            f"مجموع ورودی: {total_in:.4f}\n"
            f"مجموع خروجی: {total_out:.4f}"
        )
        
        plt.figtext(
            0.5, 0.02,
            stats_text,
            ha='center',
            fontsize=10,
            bbox=dict(facecolor='#f0f0f0', alpha=0.7, pad=5)
        )

        # --- اضافه کردن گراف به رابط کاربری ---
        canvas = FigureCanvasTkAgg(fig, master=graph_frame)
        canvas.draw()
        
        # ایجاد اسکرول بار
        scroll_y = ttk.Scrollbar(graph_frame, orient=tk.VERTICAL)
        scroll_y.pack(side=tk.RIGHT, fill=tk.Y)
        canvas.get_tk_widget().pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        canvas.get_tk_widget().config(yscrollcommand=scroll_y.set)
        scroll_y.config(command=canvas.get_tk_widget().yview)

        # اضافه کردن نوار ابزار برای زوم و حرکت
        toolbar = NavigationToolbar2Tk(canvas, graph_frame)
        toolbar.update()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

        # --- ایجاد کنترل‌های پایین پنجره ---
        btn_frame = ttk.Frame(control_frame)
        btn_frame.pack(pady=5)
        
        ttk.Button(
            btn_frame,
            text="ذخیره تصویر",
            command=lambda: self.save_graph_image(fig)
        ).grid(row=0, column=0, padx=5)
        
        ttk.Button(
            btn_frame,
            text="نمایش آمار کامل",
            command=lambda: self.show_full_stats(
                wallet_address,
                total_in,
                total_out,
                len(self.current_transactions),
                len(unique_addresses)
            )
        ).grid(row=0, column=1, padx=5)
        
        ttk.Button(
            btn_frame,
            text="بستن",
            command=graph_window.destroy
        ).grid(row=0, column=2, padx=5)

        # --- نوار وضعیت برای نمایش اطلاعات روی هاور ---
        status_var = tk.StringVar()
        status_label = ttk.Label(
            control_frame,
            textvariable=status_var,
            relief=tk.SUNKEN,
            anchor=tk.W,
            padding=(5, 2)
        )
        status_label.pack(fill=tk.X)

        # --- عملکرد هاور موس ---
        def on_hover(event):
            if event.inaxes:
                # بررسی هاور روی گره‌ها
                for node in G.nodes():
                    node_pos = pos[node]
                    distance = np.sqrt((node_pos[0]-event.xdata)**2 + (node_pos[1]-event.ydata)**2)
                    if distance < 0.03:
                        status_var.set(
                            f"آدرس: {G.nodes[node]['full_address']} | "
                            f"نوع: {'کیف پول شما' if G.nodes[node]['node_type'] == 'wallet' else 'آدرس خارجی'}"
                        )
                        return
                
                # بررسی هاور روی یال‌ها
                for u, v in G.edges():
                    edge_pos = [(pos[u][0] + pos[v][0])/2, (pos[u][1] + pos[v][1])/2]
                    distance = np.sqrt((edge_pos[0]-event.xdata)**2 + (edge_pos[1]-event.ydata)**2)
                    if distance < 0.03:
                        edge_data = G.edges[(u, v)]
                        status_var.set(
                            f"تراکنش: {edge_data['amount']:.4f} {edge_data['token']} | "
                            f"تعداد: {edge_data['count']} | "
                            f"اولین تاریخ: {edge_data['dates'][0]}"
                        )
                        return
                
                status_var.set("")

        fig.canvas.mpl_connect('motion_notify_event', on_hover)

        # --- تنظیمات نهایی پنجره ---
        graph_window.update()
        canvas.get_tk_widget().config(scrollregion=canvas.get_tk_widget().bbox("all"))
    '''
    def save_graph_image(self, fig):
        file_path = filedialog.asksaveasfilename(
            defaultextension=".png",
            filetypes=[("PNG Files", "*.png"), ("JPEG Files", "*.jpg"), ("PDF Files", "*.pdf"), ("All Files", "*.*")],
            title="ذخیره تصویر گراف"
        )
        
        if file_path:
            try:
                fig.savefig(file_path, dpi=300, bbox_inches='tight', facecolor=fig.get_facecolor())
                messagebox.showinfo("موفق", "تصویر گراف با موفقیت ذخیره شد")
            except Exception as e:
                messagebox.showerror("خطا", f"خطا در ذخیره تصویر: {str(e)}")

    def show_full_stats(self, wallet_address, total_in, total_out, total_txs, unique_addresses):
        stats_window = tk.Toplevel(self.root)
        stats_window.title("آمار کامل تراکنش‌ها")
        stats_window.geometry("650x450")  # افزایش اندازه پنجره
        
        main_frame = ttk.Frame(stats_window, padding=20)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # عنوان
        ttk.Label(main_frame, 
                text=f"آمار تراکنش‌های کیف پول {wallet_address[:10]}...",
                font=('B Nazanin', 14, 'bold')).pack(pady=10)
        
        # ایجاد Treeview با استایل بهتر
        style = ttk.Style()
        style.configure("Treeview.Heading", font=('B Nazanin', 11, 'bold'))
        style.configure("Treeview", font=('B Nazanin', 10), rowheight=25)
        
        tree = ttk.Treeview(main_frame, columns=('metric', 'value'), show='headings')
        tree.heading('metric', text='معیار')
        tree.heading('value', text='مقدار')
        tree.column('metric', width=250, anchor='e')
        tree.column('value', width=300, anchor='w')
        
        # تابع برای نمایش هوشمند اعداد
        def format_value(value, is_currency=True):
            if value == 0:
                return "0"
                
            abs_value = abs(value)
            if is_currency:
                if abs_value < 0.000001:  # برای مقادیر بسیار کوچک
                    return f"{value:.12f}".rstrip('0').rstrip('.')
                elif abs_value < 0.01:
                    return f"{value:.8f}".rstrip('0').rstrip('.')
                elif abs_value < 1:
                    return f"{value:.6f}".rstrip('0').rstrip('.')
                else:
                    return f"{value:.4f}".rstrip('0').rstrip('.')
            else:
                return f"{value:,}"  # برای اعداد غیر ارزی
        
        # محاسبه مقادیر
        net_balance = total_in - total_out
        avg_in = total_in / total_txs if total_txs > 0 else 0
        avg_out = total_out / total_txs if total_txs > 0 else 0
        
        # اضافه کردن داده‌ها با فرمت مناسب
        stats_data = [
            ('تعداد کل تراکنش‌ها', format_value(total_txs, False)),
            ('تعداد آدرس‌های منحصر به فرد', format_value(unique_addresses, False)),
            ('مجموع ورودی', format_value(total_in)),
            ('مجموع خروجی', format_value(total_out)),
            ('مانده خالص', format_value(net_balance)),
            ('میانگین ورودی', format_value(avg_in)),
            ('میانگین خروجی', format_value(avg_out)),
            ('نسبت ورودی به خروجی', 
            f"{format_value(total_in/total_out if total_out > 0 else float('inf'))}:1" 
            if total_txs > 0 else "N/A"),
            ('بیشترین تراکنش ورودی', format_value(max(
                [float(tx['amount']) for tx in self.current_transactions 
                if tx['to'] == wallet_address], default=0))),
            ('بیشترین تراکنش خروجی', format_value(max(
                [float(tx['amount']) for tx in self.current_transactions 
                if tx['from'] == wallet_address], default=0)))
        ]
        
        for item in stats_data:
            tree.insert('', tk.END, values=item)
    
        # اضافه کردن اسکرول بار
        scrollbar = ttk.Scrollbar(main_frame, orient=tk.VERTICAL, command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)
        tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # دکمه بستن با استایل بهتر
        btn_frame = ttk.Frame(main_frame)
        btn_frame.pack(pady=10)
        ttk.Button(btn_frame, text="بستن", 
                command=stats_window.destroy,
                style='Accent.TButton').pack(padx=10, ipadx=20)
        
        # تنظیم تم بهتر
        style.configure('Accent.TButton', font=('B Nazanin', 10, 'bold'))
    
    def track_wallet_threaded(self):
        Thread(target=self.track_wallet, daemon=True).start()
    
 
    def get_arkham_data(self, wallet_address):    
        self.status_var.set("در حال دریافت داده از Arkham...")
        self.root.update()
        
        all_transactions = []
        limit = 50
        offset = 0
        retry_count = 0
        max_retries = 3
        
        while True:
            try:
                # توقف تصادفی + افزایش تدریجی زمان انتظار
                delay = random.uniform(0.5, 1.5) + (retry_count * 0.5)
                time.sleep(delay)
                
                #api_key = api_config.eth
                #url = f"https://api.etherscan.io/api?module=account&action=txlist&address={wallet_address}&startblock=0&endblock=********&page={page}&offset={offset}&sort=asc&apikey={api_key}"
                url = f"https://api.arkhamintelligence.com/address/{wallet_address}/transactions"
                params = {
                    'limit': limit,
                    'offset': offset
                }
                
                # اضافه کردن هدرهای ضروری
                headers = {
                    'User-Agent': 'Mozilla/5.0',
                    'Accept': 'application/json',
                    'Authorization': 'Bearer YOUR_API_KEY'  # اگر نیاز به کلید دارد
                }
                
                response = requests.get(url, headers=headers, params=params, timeout=10)
                
                if response.status_code == 200:
                    data = response.json()
                    transactions = data.get('transactions', [])
                    
                    if not transactions:
                        break
                        
                    all_transactions.extend(transactions)
                    offset += limit
                    retry_count = 0  # ریست شمارشگر تلاش مجدد
                    
                    self.status_var.set(f"در حال دریافت داده از Arkham... ({len(all_transactions)} تراکنش)")
                    self.root.update()
                    
                elif response.status_code == 429:
                    retry_count += 1
                    if retry_count > max_retries:
                        messagebox.showwarning("اخطار", "محدودیت نرخ درخواست. لطفاً稍后再试")
                        break
                    continue
                    
                else:
                    messagebox.showerror("خطا", f"خطای سرور: کد {response.status_code}")
                    break
                    
            except requests.exceptions.RequestException as e:
                retry_count += 1
                if retry_count > max_retries:
                    messagebox.showerror("خطا", f"خطای ارتباط: {str(e)}")
                    return None
                continue
                
        return {'transactions': all_transactions} if all_transactions else None

    def get_covalent_data(self, wallet_address):
        self.status_var.set("در حال دریافت داده از Covalent...")
        self.root.update()

        all_transactions = []
        page = 0
        page_size = 1000  # تعداد تراکنش در هر صفحه
        max_retries = 3  # حداکثر تعداد تلاش برای هر درخواست
        retry_delay = 5  # تاخیر بین تلاش‌های مجدد
        
        while True:
            # توقف تصادفی بین درخواست‌ها برای جلوگیری از Rate Limit
            time.sleep(random.uniform(0.5, 1.5))
            
            #url = f"https://api.covalenthq.com/v1/multi-chain/address/{wallet_address}/transactions/"
            url = f"https://api.covalenthq.com/v1/1/address/{wallet_address}/transactions_v2/"
            params = {
                'key': api_config.COVALENT,
                'quote-currency': 'USD',
                'page-number': page,
                'page-size': page_size
            }
            
            try:
                response = requests.get(
                    url,
                    params=params,
                    headers={'User-Agent': 'Mozilla/5.0'},
                    timeout=30  # محدودیت زمانی برای درخواست
                )
                
                if response.status_code == 200:
                    data = response.json()
                    # بررسی ساختار پاسخ
                    if not data.get('data', {}).get('items'):
                        break
                        
                    transactions = data['data']['items']
                    all_transactions.extend(transactions)
                    page += 1

                    self.status_var.set(
                        f"در حال دریافت داده از Covalent... ({len(all_transactions)} تراکنش)"
                    )
                    self.root.update()

                    # بررسی پایان داده‌ها
                    if len(transactions) < page_size:
                        break

                   

                elif response.status_code == 429:
                    retry_after = int(response.headers.get('Retry-After', 5))
                    time.sleep(retry_after)
                    retry_count += 1
                    if retry_count > max_retries:
                        messagebox.showwarning("اخطار", "محدودیت نرخ درخواست. لطفاً稍后再试")
                        break
                    continue

                else:
                    messagebox.showerror(
                        "خطا",
                        f"خطا در دریافت داده از Covalent: کد {response.status_code}\n{response.text[:200]}"
                    )
                    return None
                    
            
            except Exception as e:
                messagebox.showerror("خطا", f"خطا در اتصال به Covalent: {str(e)}")
                return None

        return {'data': {'items': all_transactions}} if all_transactions else None
    
   
    def get_blockcypher_data(self, wallet_address, coin='btc'):
        self.status_var.set("در حال دریافت داده از BlockCypher...")
        self.root.update()

        base_url = f"https://api.blockcypher.com/v1/{coin}/main/addrs/{wallet_address}/full"
        
        try:
            time.sleep(random.uniform(0.3, 0.7))
            response = requests.get(base_url, headers={'User-Agent': 'Mozilla/5.0'}, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                if not data.get('txs'):
                    messagebox.showinfo("اطلاع", "هیچ تراکنشی یافت نشد")
                    return {'data': {'items': []}}  # بازگرداندن ساختار خالی اما معتبر

                return {
                'data': {
                    'items': data['txs'],
                    'coin_symbol': coin.upper()  # اضافه کردن اطلاعات ارز
                }
                        }
                
            else:
                messagebox.showerror("خطا", f"خطای HTTP {response.status_code}")
                return {'data': {'items': []}}  # بازگرداندن ساختار خالی اما معتبر
                #transactions = data.get('txs', [])
                #transactions = data['txs']

                
        except Exception as e:
            messagebox.showerror("خطا", f"خطای ارتباطی: {str(e)}")
            return {'data': {'items': []}}  # بازگرداندن ساختار خالی اما معتبر

    #def get_blockchain_data(self, wallet_address):
    def get_blockchain_data(self, wallet_address, blockchain='bsc'):
        self.status_var.set(f"در حال دریافت داده از {blockchain.upper()}...")
        self.root.update()

        # تنظیمات API برای هر بلاک‌چین
        apis = {
            'bsc': {
                'url': 'https://api.bscscan.com/api',
                'params': {
                    'module': 'account',
                    'action': 'txlist',
                    'startblock': 0,
                    'endblock': ********,
                    'sort': 'asc'
                },
                'api_key': api_config.bsc,  # جایگزین کنید
                'decimals': 18  # تبدیل از Wei
            },
            'eth': {
                'url': 'https://api.etherscan.io/api',
                'params': {
                    'module': 'account',
                    'action': 'txlist',
                    'startblock': 0,
                    'endblock': ********,
                    'sort': 'asc'
                },
                'api_key': api_config.eth,  # جایگزین کنید
                'decimals': 18  # تبدیل از Wei
            },
            'tron': {
                'url': 'https://apilist.tronscan.org/api/transaction',
                'params': {
                    'limit': 50,
                    'start': 0,
                    'sort': '-timestamp'
                },
                'api_key': None,
                'decimals': 6  # تبدیل از SUN
            },
            'polygon': {
                'url': 'https://api.polygonscan.com/api',
                'params': {
                    'module': 'account',
                    'action': 'txlist',
                    'startblock': 0,
                    'endblock': ********,
                    'sort': 'asc'
                },
                'api_key': api_config.polygon,  # جایگزین کنید
                'decimals': 18  # تبدیل از Wei
            }
        }

        if blockchain not in apis:
            messagebox.showerror("خطا", f"بلاک‌چین {blockchain} پشتیبانی نمی‌شود")
            return None

        config = apis[blockchain]
        all_transactions = []
        page = 1
        offset = 10000 if blockchain in ['bsc', 'eth', 'polygon'] else 50

        while True:
            try:
                # توقف تصادفی برای جلوگیری از Rate Limit
                time.sleep(random.uniform(0.3, 0.7))
                
                # تنظیم پارامترهای درخواست
                params = config['params'].copy()
                params.update({
                    'address': wallet_address,
                    'page': page,
                    'offset': offset
                })
                if config['api_key']:
                    params['apikey'] = config['api_key']

                # ارسال درخواست
                response = requests.get(
                    config['url'],
                    params=params,
                    headers={'User-Agent': 'Mozilla/5.0'}
                )

                if response.status_code == 200:
                    data = response.json()
                    
                    # پردازش پاسخ بر اساس بلاک‌چین
                    if blockchain in ['bsc', 'eth', 'polygon']:
                        transactions = data.get('result', [])
                        if not transactions or isinstance(transactions, str):
                            break
                    elif blockchain == 'tron':
                        transactions = data.get('data', [])
                        if not transactions:
                            break

                    all_transactions.extend(transactions)
                    page += 1

                    # نمایش پیشرفت
                    self.status_var.set(
                        f"در حال دریافت داده از {blockchain.upper()}... ({len(all_transactions)} تراکنش)"
                    )
                    self.root.update()
                else:
                    messagebox.showwarning(
                        "اخطار",
                        f"پاسخ غیرمنتظره از {blockchain.upper()}: کد {response.status_code}"
                    )
                    break

            except Exception as e:
                messagebox.showerror(
                    "خطا",
                    f"خطا در دریافت داده از {blockchain.upper()}: {str(e)}"
                )
                return None

        return {'transactions': all_transactions, 'blockchain': blockchain} if all_transactions else None

    def get_tronscan_data(self, wallet_address):
        self.status_var.set("در حال دریافت داده از Tronscan...")
        self.root.update()
    
        all_transactions = []
        limit = 50
        start = 0
    
        while True:
            time.sleep(0.5)
            url = f"https://apilist.tronscan.org/api/transaction?address={wallet_address}&limit={limit}&start={start}&sort=-timestamp"
            try:
                response = requests.get(url, headers={'User-Agent': 'Mozilla/5.0'})
                if response.status_code == 200:
                    data = response.json()
                    transactions = data.get('data', [])
                    if not transactions:
                        break
                
                    all_transactions.extend(transactions)
                    start += limit
                
                # نمایش پیشرفت در وضعیت برنامه
                    self.status_var.set(f"در حال دریافت داده از Tronscan... ({len(all_transactions)} تراکنش)")
                    self.root.update()
                else:
                    break
            except Exception as e:
                messagebox.showerror("خطا", f"خطا در دریافت داده از Tronscan: {str(e)}")
                return None
    
        return {'data': all_transactions} if all_transactions else None
    
    def get_etherscan_data(self, wallet_address):
    
        self.status_var.set("در حال دریافت داده از Etherscan...")
        self.root.update()
    
        all_transactions = []
        page = 1
        offset = 10000  # حداکثر تراکنش در هر صفحه
    
        while True:
        # توقف تصادفی بین درخواست‌ها
            time.sleep(random.uniform(0.3, 0.7))
        
            api_key = api_config.eth
            url = f"https://api.etherscan.io/api?module=account&action=txlist&address={wallet_address}&startblock=0&endblock=********&page={page}&offset={offset}&sort=asc&apikey={api_key}"
            try:
                response = requests.get(url, headers={'User-Agent': 'Mozilla/5.0'})
                if response.status_code == 200:
                    data = response.json()
                    transactions = data.get('result', [])
                    if not transactions:
                        break
                
                    all_transactions.extend(transactions)
                    page += 1
                
                    self.status_var.set(f"در حال دریافت داده از Etherscan... ({len(all_transactions)} تراکنش)")
                    self.root.update()
                else:
                    break
            except Exception as e:
                messagebox.showerror("خطا", f"خطا در دریافت داده از Etherscan: {str(e)}")
                return None
    
        return {'result': all_transactions} if all_transactions else None
    
    def get_bscscan_data(self, wallet_address):
        self.status_var.set("در حال دریافت داده از BscScan...")
        self.root.update()

        all_transactions = []
        api_key = api_config.bsc # جایگزین کنید با API Key واقعی
        page = 1
        offset = 10000  # حداکثر تراکنش در هر درخواست
        
        while True:
            # توقف تصادفی بین درخواست‌ها برای جلوگیری از Rate Limit
            time.sleep(random.uniform(0.3, 0.7))
            
            url = f"https://api.bscscan.com/api?module=account&action=txlist&address={wallet_address}" \
                f"&startblock=0&endblock=********&page={page}&offset={offset}&sort=asc&apikey={api_key}"
            
            try:
                response = requests.get(url, headers={'User-Agent': 'Mozilla/5.0'})
                if response.status_code == 200:
                    data = response.json()
                    transactions = data.get('result', [])
                    
                    if not transactions or isinstance(transactions, str):
                        break
                    
                    all_transactions.extend(transactions)
                    page += 1
                    
                    # نمایش پیشرفت
                    self.status_var.set(f"در حال دریافت داده از BscScan... ({len(all_transactions)} تراکنش)")
                    self.root.update()
                else:
                    messagebox.showwarning("اخطار", f"پاسخ غیرمنتظره از BscScan: کد {response.status_code}")
                    break
                    
            except Exception as e:
                messagebox.showerror("خطا", f"خطا در دریافت داده از BscScan: {str(e)}")
                return None

        return {'result': all_transactions} if all_transactions else None

    def get_solana_data(self, wallet_address):
        if not hasattr(api_config, 'solana'):
            messagebox.showerror("خطا", "کلید API سولانا تنظیم نشده")
            return {'data': {'items': []}}

        API_KEY = api_config.solana
        headers = {
            'User-Agent': 'Mozilla/5.0',
            'Authorization': f'Bearer {API_KEY}'
        }
        
        all_transactions = []
        offset = 0
        limit = 50
        max_transactions = 1000
        retry_count = 0
        max_retries = 3

        while not self.stop_search_flag and retry_count < max_retries:
            try:
                time.sleep(random.uniform(0.5, 1.5))  # توقف تصادفی
                
                url = f"https://api.solscan.io/account/transactions?address={wallet_address}&offset={offset}&limit={limit}"
                response = requests.get(url, headers=headers, timeout=15)
                
                if self.stop_search_flag:
                    break

                if response.status_code == 200:
                    data = response.json()
                    transactions = data.get('data', [])
                    
                    if not transactions:
                        break
                        
                    all_transactions.extend(transactions)
                    offset += len(transactions)
                    
                    # به‌روزرسانی UI
                    self.root.after(0, lambda: self.status_var.set(
                        f"سولانا: دریافت {len(all_transactions)} تراکنش"
                    ))
                    
                    if len(all_transactions) >= max_transactions:
                        break

                elif response.status_code == 403:
                    retry_after = int(response.headers.get('Retry-After', 15))
                    self.root.after(0, lambda: self.status_var.set(
                        f"محدودیت موقت - توقف برای {retry_after} ثانیه"
                    ))
                    time.sleep(retry_after)
                    retry_count += 1
                    continue
                    
                else:
                    self.root.after(0, lambda: messagebox.showwarning(
                        "اخطار",
                        f"خطای سرور: کد {response.status_code}"
                    ))
                    break
                    
            except requests.exceptions.Timeout:
                retry_count += 1
                if retry_count < max_retries:
                    continue
            except Exception as e:
                if not self.stop_search_flag:
                    self.root.after(0, lambda: messagebox.showerror(
                        "خطا",
                        f"خطای غیرمنتظره: {str(e)}"
                    ))
                break
        
        return {'data': {'items': all_transactions}}

    def parse_transactions(self, data, source):
        """پردازش تراکنش‌ها و تشخیص نوع آدرس‌ها"""
        transactions = []
        
        # پردازش داده‌های Tronscan
        if source == "tronscan":
            if isinstance(data, dict) and 'data' in data:
                for tx in data['data']:
                    try:
                        from_addr = tx.get('ownerAddress', '')
                        to_addr = tx.get('toAddress', '')
                        amount = float(tx.get('amount', 0)) / 1e6  # تبدیل از SUN به TRX
                        date = datetime.fromtimestamp(tx.get('timestamp', 0)/1000).strftime('%Y-%m-%d %H:%M:%S')
                        
                        # تشخیص نوع آدرس‌ها
                        from_type = self.exchange_detector.detect_by_pattern(from_addr, 'TRX')
                        to_type = self.exchange_detector.detect_by_pattern(to_addr, 'TRX')
                        
                        transaction = {
                            'source': 'Tronscan',
                            'from': from_addr,
                            'from_type': from_type,
                            'from_exchange': from_type if 'Exchange' in from_type else '',
                            'to': to_addr,
                            'to_type': to_type,
                            'to_exchange': to_type if 'Exchange' in to_type else '',
                            'token': tx.get('tokenType', 'TRX'),
                            'amount': amount,
                            'date': date,
                            'count': 1
                        }
                        transactions.append(transaction)
                    except Exception as e:
                        print(f"خطا در پردازش تراکنش Tronscan: {str(e)}")
                        continue
            return transactions
            
        # پردازش داده‌های سایر منابع
        if isinstance(data, list):
            for tx in data:
                try:
                    from_addr = tx.get('from', '')
                    to_addr = tx.get('to', '')
                    token = tx.get('token', 'ETH')
                    
                    from_type = self.exchange_detector.detect_by_pattern(from_addr, token)
                    to_type = self.exchange_detector.detect_by_pattern(to_addr, token)
                    
                    transaction = {
                        'source': source,
                        'from': from_addr,
                        'from_type': from_type,
                        'from_exchange': from_type if 'Exchange' in from_type else '',
                        'to': to_addr,
                        'to_type': to_type,
                        'to_exchange': to_type if 'Exchange' in to_type else '',
                        'token': token,
                        'amount': float(tx.get('amount', 0)),
                        'date': tx.get('date', ''),
                        'count': 1
                    }
                    transactions.append(transaction)
                except Exception as e:
                    print(f"خطا در پردازش تراکنش {source}: {str(e)}")
                    continue
                    
        return transactions

    def track_wallet(self):
        wallet_address = self.wallet_entry.get().strip()
        if not wallet_address:
            messagebox.showwarning("هشدار", "لطفاً آدرس کیف پول را وارد کنید")
            return

        self.search_btn.config(state=tk.DISABLED)
        self.show_chart_btn.config(state=tk.DISABLED)
        self.status_var.set("در حال جستجو...")
        self.root.update()

        # پاک کردن نتایج قبلی
        for item in self.tree.get_children():
            self.tree.delete(item)

        all_transactions = []
        detector = ExchangeDetector()

        try:
            if self.tronscan_var.get():
                tronscan_data = self.get_tronscan_data(wallet_address)
                if tronscan_data:
                    transactions = self.parse_transactions(tronscan_data, "tronscan")
                    all_transactions.extend(transactions)

            if self.solana_var.get():
                solana_data = self.get_solana_data(wallet_address)
                if solana_data and not self.stop_search_flag:
                    transactions = self.parse_transactions(solana_data, "solana")
                    all_transactions.extend(transactions)
           
            if self.covalent_var.get():
                covalent_data = self.get_covalent_data(wallet_address)
                if covalent_data:
                    all_transactions.extend(self.parse_transactions(covalent_data, "covalent"))

            if self.blockcypher_var.get():
                blockcypher_data = self.get_blockcypher_data(wallet_address)
                if blockcypher_data:
                    all_transactions.extend(self.parse_transactions(blockcypher_data, "blockcypher"))

            if self.blockchain_var.get():
                blockchain_data = self.get_blockchain_data(wallet_address)
                if blockchain_data:
                    all_transactions.extend(self.parse_transactions(blockchain_data, "blockchain"))

            if self.arkham_var.get():
                arkham_data = self.get_arkham_data(wallet_address)
                if arkham_data:
                    all_transactions.extend(self.parse_transactions(arkham_data, "arkham"))

            if self.etherscan_var.get():
                etherscan_data = self.get_etherscan_data(wallet_address)
                if etherscan_data:
                    all_transactions.extend(self.parse_transactions(etherscan_data, "etherscan"))
            
            if self.bscscan_var.get():  # نیاز به اضافه کردن چک باکس در UI
                bscscan_data = self.get_bscscan_data(wallet_address)
                if bscscan_data:
                    all_transactions.extend(self.parse_transactions(bscscan_data, "bscscan"))

            if not all_transactions:
                messagebox.showinfo("اطلاع", "هیچ تراکنشی یافت نشد")
                self.status_var.set("آماده - هیچ تراکنشی یافت نشد")
                self.current_transactions = None
                return
            
            # گروه‌بندی تراکنش‌ها
            transaction_groups = {}
            for tx in all_transactions:
                key = (tx['from'], tx['to'], tx['token'])
                if key not in transaction_groups:
                    transaction_groups[key] = {
                        'source': tx['source'],
                        'from_type': tx['from_type'],
                        'from_exchange': tx['from_exchange'],
                        'to_type': tx['to_type'],
                        'to_exchange': tx['to_exchange'],
                        'amounts': [],
                        'dates': [],
                        'count': 0
                    }
                transaction_groups[key]['amounts'].append(f"{tx['amount']:.8f}")
                transaction_groups[key]['dates'].append(tx['date'])
                transaction_groups[key]['count'] += 1
            
            # نمایش در Treeview
            for key, group in transaction_groups.items():
                from_addr, to_addr, token = key
                self.tree.insert('', tk.END, values=(
                    group['source'],
                    from_addr,
                    group['from_type'],
                    group['from_exchange'],
                    to_addr,
                    group['to_type'],
                    group['to_exchange'],
                    token,
                    "\n".join(group['amounts']),
                    "\n".join(group['dates']),
                    group['count']
                ))
            
            self.current_transactions = all_transactions
            self.show_chart_btn.config(state=tk.NORMAL)
            self.show_graph_btn.config(state=tk.NORMAL)
            self.highlight_searched_address(wallet_address)
            
        except Exception as e:
            messagebox.showerror("خطا", f"خطا در پردازش تراکنش‌ها: {str(e)}")
            self.current_transactions = None
        finally:
            self.status_var.set(f"آماده - {len(all_transactions)} تراکنش یافت شد")
            self.search_btn.config(state=tk.NORMAL)

    def start_search(self):
        self.stop_search_flag = False
        self.search_btn.config(state=tk.DISABLED)
        self.stop_btn.config(state=tk.NORMAL)
        
        # ایجاد ریسه جدید برای جستجو
        self.search_thread = Thread(target=self._search_wallet_thread)
        self.search_thread.start()

    def stop_search(self):
        """توقف جستجو"""
        self.stop_search_flag = True
        if self.search_thread and self.search_thread.is_alive():
            self.search_thread.join(timeout=1)
        self.update_ui_after_stop()

    def _search_wallet_thread(self):
        """تابع اصلی جستجو در ریسه جداگانه"""
        try:
            wallet_address = self.wallet_entry.get().strip()
            if not wallet_address:
                return

            # فرآیند جستجو (مثال)
            for source in self.get_active_sources():
                if self.stop_search_flag:
                    break
                    
                self.fetch_and_process(source, wallet_address)
                
        except Exception as e:
            messagebox.showerror("خطا", str(e))
        finally:
            self.root.after(0, self.update_ui_after_stop)

    def update_ui_after_stop(self):
        """به روزرسانی UI پس از توقف"""
        self.search_btn.config(state=tk.NORMAL)
        self.stop_btn.config(state=tk.DISABLED)
        self.status_var.set("جستجو متوقف شد" if self.stop_search_flag else "جستجو کامل شد")

    def save_results(self):
        if not self.tree.get_children():
            messagebox.showwarning("هشدار", "هیچ داده‌ای برای ذخیره وجود ندارد")
            return
        
        file_path = filedialog.asksaveasfilename(
            defaultextension=".csv",
            filetypes=[("Excel Files", "*.xlsx"), ("CSV Files", "*.csv"), ("All Files", "*.*")],
            title="ذخیره نتایج به عنوان"
        )
        
        if not file_path:
            return
        
        try:
            data = []
            # جمع‌آوری داده‌های منحصر به فرد از Treeview
            #unique_data = []
            #seen = set()
            for item in self.tree.get_children():
                values = self.tree.item(item, 'values')
                data.append({
                    'منبع': values[0],
                    'مبدا': values[1],
                    'صرافی مبدا': values[2],  # ستون جدید
                    'مقصد': values[3],
                    'صرافی مقصد': values[4],  # ستون جدید
                    'توکن': values[5],
                    'مقادیر': values[6].replace("\n", " | "),
                    'تاریخ‌ها': values[7].replace("\n", " | "),
                    'تعداد': values[8]
                })
            
            df = pd.DataFrame(data)
            
            # محاسبه تعداد تکرار هر ترکیب مبدا/مقصد
            #df['تعداد تکرار'] = df.groupby(['مبدا', 'مقصد'])['مبدا'].transform('count')
            # مرتب‌سازی بر اساس تعداد تکرار (نزولی)
            #df = df.sort_values(by='تعداد تکرار', ascending=False)
            if file_path.endswith('.xlsx'):
                df.to_excel(file_path, index=False, engine='openpyxl')
            else:  # برای فایل‌های CSV
                df.to_csv(file_path, index=False, encoding='utf-8-sig')
            #df.to_csv(file_path, index=False, encoding='utf-8-sig')
            messagebox.showinfo("موفق", "نتایج با موفقیت ذخیره شد")
        except Exception as e:
            messagebox.showerror("خطا", f"خطا در ذخیره فایل: {str(e)}")
    
    def show_help(self):
        help_text = """...متن راهنما"""
        messagebox.showinfo("راهنما", help_text)
    
    def show_about(self):
        about_text = """...متن درباره برنامه"""
        messagebox.showinfo("درباره برنامه", about_text)

if __name__ == "__main__":
    root = tk.Tk()
    app = CryptoWalletTrackerApp(root)
    root.mainloop()