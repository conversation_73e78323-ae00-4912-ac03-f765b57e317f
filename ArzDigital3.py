from config.api_config import api_config
import requests
import json
from datetime import datetime
import pandas as pd
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from tkinter.scrolledtext import ScrolledText
import webbrowser
import time
import random
from collections import Counter
from threading import Thread
from tkinter import font as tkfont
import numpy as np

  

class ExchangeDetector:
    def __init__(self):
        # 1. پایگاه داده آدرس‌های شناخته شده
        self.known_addresses = self._load_known_addresses()
        self.known_addresses = {
            'Binance': [
                '******************************************',  # آدرس واقعی بایننس
                '******************************************'
            ],
            'Coinbase': [
                '******************************************'
            ]
        }
        self.patterns = {
            "Binance": {
                "BTC": ["1", "3", "bc1"],
                "ETH": ["0x"],
                "TRC20": ["Txxxxxxxx"],  # الگوی خاص Binance
            },
            "KuCoin": {
                "TRC20": ["Txxxxxx"],  # الگوی خاص KuCoin
            },
            "Trust Wallet": {
                "BEP2": ["bnb1"],
            }
        }
        # 3. APIهای مورد نیاز
        self.apis = {
            'TRON': 'https://apilist.tronscan.org/api/account',
            'ETH': 'https://api.etherscan.io/api'
        }
    
    def detect_exchange(self, address, currency=None):
        for exchange, addresses in self.known_addresses.items():
            if address in addresses:
                return {
                    'exchange': exchange,
                    'confidence': 100,  # 100% اطمینان برای آدرس‌های شناخته شده
                    'type': 'hot_wallet'
                }
        
        # سپس بررسی الگوها
        if address.startswith('0x') and len(address) == 42:
            return {
                'exchange': 'Possible_Exchange',
                'confidence': 60,  # 60% اطمینان برای آدرس‌های اتریومی
                'type': 'pattern_match'
            }
        
        # در غیر این صورت
        return {
            'exchange': 'Unknown',
            'confidence': 0,
            'type': 'unknown'
        }

    def _load_known_addresses(self):
        """بارگذاری آدرس‌های شناخته شده از فایل/دیتابیس"""
        try:
            with open('exchange_addresses.json', 'r') as f:
                return json.load(f)
        except:
            return {
                'Binance': {
                    'hot_wallets': [
                        '******************************************',
                        '******************************************'
                    ],
                    'cold_wallets': [
                        '******************************************',
                        '******************************************'
                    ]
                },
                'Coinbase': {
                    'hot_wallets': [
                        '******************************************',
                        '******************************************'
                    ],
                    'cold_wallets': [
                        '******************************************',
                        '******************************************'
                    ]
                }
            }

    def _check_known_addresses(self, address):
        """بررسی آدرس در لیست آدرس‌های شناخته شده"""
        for exchange, data in self.known_addresses.items():
            if address in data['hot_wallets']:
                return {'exchange': exchange, 'type': 'hot_wallet'}
            if address in data['cold_wallets']:
                return {'exchange': exchange, 'type': 'cold_wallet'}
        return None

    def _check_address_pattern(self, address, currency=None):
        """تشخیص بر اساس الگوی آدرس"""
        for exchange, patterns in self.patterns.items():
            if currency and currency in patterns:
                for pattern in patterns[currency]:
                    if re.match(pattern, address):
                        return {'exchange': exchange}
            
            # بررسی عمومی بدون در نظر گرفتن ارز
            for _, currency_patterns in patterns.items():
                if isinstance(currency_patterns, list):
                    for pattern in currency_patterns:
                        if re.match(pattern, address):
                            return {'exchange': exchange}
        return None

    def _check_behavior(self, address, currency=None):
        """تحلیل رفتاری آدرس"""
        # نیاز به پیاده‌سازی اتصال به بلاکچین
        tx_count = self._get_transaction_count(address, currency)
        if tx_count > 1000:
            return {'exchange': 'Likely Exchange', 'confidence': 80}
        return None

    def _check_via_external_apis(self, address, currency=None):
        """بررسی از طریق APIهای خارجی"""
        if currency == 'TRX':
            data = self._check_tronscan_api(address)
            if data and data.get('is_exchange'):
                return {
                    'exchange': data.get('exchange_name', 'Unknown Exchange'),
                    'confidence': 85
                }
        return None

    def _check_tronscan_api(self, address):
        """استفاده از Tronscan API"""
        try:
            url = f"{self.apis['TRON']}?address={address}"
            response = requests.get(url)
            if response.status_code == 200:
                data = response.json()
                return {
                    'is_exchange': data.get('data', {}).get('isExchange', False),
                    'exchange_name': data.get('data', {}).get('exchangeName')
                }
        except:
            return None

    def detect_by_pattern(self, address, currency):
        """تشخیص بر اساس الگوی استاتیک آدرس"""
        exchange_patterns = {
        "Binance": {
            "BTC": [r'^(1|3)[a-km-zA-HJ-NP-Z1-9]{25,34}$', r'^bc1[a-z0-9]{25,90}$'],
            "ETH": [r'^0x[a-fA-F0-9]{40}$'],
            "TRX": [r'^T[a-zA-Z0-9]{33}$']
        },
        "Coinbase": {
            "BTC": [r'^3[a-km-zA-HJ-NP-Z1-9]{33,34}$'],
            "ETH": [r'^0x[a-fA-F0-9]{40}$']
        }
            }
        for exchange, patterns in self.patterns.items():
            if currency in patterns:
                for prefix in patterns[currency]:
                    if address.startswith(prefix):
                        return exchange
        return "Unknown"
    
    def detect_by_api(self, address, currency):
        """تشخیص با استفاده از APIهای بلوکچین"""
        if currency == "TRC20":
            try:
                url = f"https://apilist.tronscan.org/api/account?address={address}"
                response = requests.get(url).json()
                if "exchange" in response.get("data", {}).get("tag", "").lower():
                    return response["data"]["tag"]
            except:
                pass
        return "Unknown"


class CryptoWalletTrackerApp:
    def __init__(self, root):
        self.root = root    
        self.setup_main_window()
        self.create_widgets() 
        self.set_dark_theme()
        self.create_menu()
          # در __init__، دکمه جستجو را به این متد وصل کنید:
        self.search_btn.config(command=self.track_wallet_threaded)
        self.exchange_detector = ExchangeDetector()
        self.current_transactions = None
        

    def setup_main_window(self):
        self.root.geometry("1300x800")
        self.root.resizable(True, True)
        self.root.title("برنامه تحلیل و ردیابی رمز ارزها ")
        #self.main_frame = ttk.Frame(self.root, padding="10")
        #self.main_frame.pack(fill=tk.BOTH, expand=True)


    def create_widgets(self):
        # عنوان برنامه
      
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        title_label = ttk.Label(main_frame, 
                              text="ردیاب تراکنش‌های کیف پول ارز دیجیتال",
                              font=('Tahoma', 12, 'bold'))
        title_label.pack(pady=2)
        
        input_frame = ttk.Frame(main_frame)
        input_frame.pack(fill=tk.X, pady=10)
        
        ttk.Label(input_frame, text="آدرس کیف پول:").pack(side=tk.LEFT, padx=5)
        
        self.wallet_entry = ttk.Entry(input_frame, width=60)
        self.wallet_entry.pack(side=tk.LEFT, expand=True, fill=tk.X, padx=5)
        
        self.search_btn = ttk.Button(input_frame, text="جستجو", command=self.track_wallet)
        self.search_btn.pack(side=tk.LEFT, padx=5)
        
        source_frame = ttk.LabelFrame(main_frame, text="منابع اطلاعاتی", padding=10)
        source_frame.pack(fill=tk.X, pady=10)
        
        # لیست منابع و متغیرها
        sources = [
            ("blockchain(bsc,eth,poly)", "blockchain_var"),       
            ("covalent(All)", "covalent_var"),
            ("Arkham Intelligence", "arkham_var"),
            ("Tronscan", "tronscan_var"),
            ("Etherscan (ETH)", "etherscan_var"), 
            ("BscScan (BSC)", "bscscan_var")
                
        ]
        
        # مقداردهی اولیه متغیرها
        for _, var_name in sources:
            setattr(self, var_name, tk.BooleanVar(value=True))
        
        # ایجاد چک‌باکس‌ها به صورت جدولی
        for i, (text, var_name) in enumerate(sources):
            row = i // 2  # تقسیم به دو ستون
            col = i % 2
            ttk.Checkbutton(
                source_frame,
                text=text,
                variable=getattr(self, var_name)
            ).grid(
                row=row,
                column=col,
                sticky="w",
                padx=10,
                pady=1,
                ipadx=5,
                ipady=1
            )
        
        # تنظیمات گرید برای ظاهر بهتر
        source_frame.grid_columnconfigure(0, weight=1, uniform="group1")
        source_frame.grid_columnconfigure(1, weight=1, uniform="group1")
       
        result_frame = ttk.LabelFrame(main_frame, text="نتایج تراکنش‌ها", padding=10)
        result_frame.pack(fill=tk.BOTH, expand=True)
    

       # تنظیم ارتفاع سطرها - این بخش باید قبل از ایجاد Treeview باشد
        style = ttk.Style()
        style.theme_use('clam')  # استفاده از تمی که از rowheight پشتیبانی می‌کند
        style.configure('Treeview', 
               rowheight=30,
               font=('Tahoma', 9),
               background="#ffffff",
               fieldbackground="#ffffff")
        #self.tree = ttk.Treeview(result_frame, columns=('Source', 'From', 'To', 'Token', 'Amount', 'Date'), show='headings')
        #self.tree = ttk.Treeview(result_frame, columns=('Source', 'From', 'To', 'Token', 'Amounts', 'Dates', 'Count'), show='headings')
            
        self.tree = ttk.Treeview(result_frame, columns=(
            "Source", "From", "From_Exchange", "From_confidence", 
            "To", "To_Exchange", "To_confidence", "Token", "Amounts", "Dates","Count"
            ), show='headings')
       

        #self.tree.heading('Count', text='تعداد تکرار')
        #self.tree.column('Count', width=80)
           # تنظیمات ستون‌ها
        self.tree.heading('Source', text='منبع')
        self.tree.heading('From', text='مبدا')
        self.tree.heading('From_Exchange', text='صرافی مبدا')
        self.tree.heading("From_confidence", text="اطمینان (%)")
        self.tree.heading('To', text='مقصد')
        self.tree.heading('To_Exchange', text='صرافی مقصد')
        self.tree.heading("To_confidence", text="اطمینان (%)")
        self.tree.heading('Token', text='توکن')
        self.tree.heading('Amounts', text='مقادیر')
        self.tree.heading('Dates', text='تاریخ‌ها')
        self.tree.heading('Count', text='تعداد تکرار')

        
    # تنظیم عرض ستون‌ها
        self.tree.column('Source', width=30)
        self.tree.column('From', width=250)
        self.tree.column('From_Exchange', width=70)
        self.tree.column("From_confidence", width=70)
        self.tree.column('To', width=250)
        self.tree.column('To_Exchange', width=70)
        self.tree.column("To_confidence", width=70)
        self.tree.column('Token', width=70)
        self.tree.column('Amounts', width=80)
        self.tree.column('Dates', width=150)
        self.tree.column('Count', width=30)
        # تنظیم تراز متن برای ستون‌های چندخطی
        self.tree.tag_configure('multiline', font=('Tahoma', 9))

        
        scrollbar = ttk.Scrollbar(result_frame, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscroll=scrollbar.set)
        
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        self.status_var = tk.StringVar()
        self.status_var.set("آماده")
        status_label = ttk.Label(main_frame, textvariable=self.status_var, font=('Tahoma', 10, 'bold'))
        status_label.pack(side=tk.BOTTOM, fill=tk.X)

        # دکمه نمایش نمودار (پایین پنجره)
        self.show_chart_btn = ttk.Button(main_frame,
                                   text="نمایش نمودار",
                                   command=self.show_chart,
                                   state=tk.DISABLED)
        self.show_chart_btn.pack(side=tk.BOTTOM, pady=10)

   
        # اضافه کردن دکمه نمایش آمار
        stats_frame = ttk.Frame(main_frame)
        stats_frame.pack(fill=tk.X, pady=5)
        #self.stats_btn = ttk.Button(stats_frame, text="نمایش آمار", command=self.show_stats_from_tree)
        #self.stats_btn.pack(side=tk.LEFT, padx=5)

        self.current_tooltip = None

    # استفاده از closure برای دسترسی به self
        def make_tooltip_handlers(self):
            def show_tooltip(event):
                if hasattr(self, 'current_tooltip') and self.current_tooltip:
                    self.current_tooltip.destroy()
                
                item = self.tree.identify_row(event.y)
                col = self.tree.identify_column(event.x)
                
                if item and col:
                    value = self.tree.item(item, 'values')[int(col[1:])-1]
                    
                    if "\n" in value:
                        self.current_tooltip = tk.Toplevel(self.root)
                        self.current_tooltip.wm_overrideredirect(True)
                        self.current_tooltip.wm_geometry(f"+{event.x_root+20}+{event.y_root+10}")
                        
                        label = ttk.Label(
                            self.current_tooltip,
                            text=value,
                            background="#2F4F4F",
                            foreground="white",
                            relief="solid",
                            padding=5,
                            font=('Tahoma', 9)
                        )
                        label.pack()

            def close_tooltip(event=None):
                if hasattr(self, 'current_tooltip') and self.current_tooltip:
                    self.current_tooltip.destroy()
                    self.current_tooltip = None

            return show_tooltip, close_tooltip

        show_tooltip, close_tooltip = make_tooltip_handlers(self)
        self.tree.bind("<Motion>", show_tooltip)
        self.tree.bind("<Leave>", close_tooltip)

    def show_chart(self):
       
        if not self.current_transactions:
            messagebox.showwarning("هشدار", "داده‌ای برای نمایش وجود ندارد")
            return
            
        # ایجاد پنجره جدید برای نمودار
        chart_window = tk.Toplevel(self.root)
        chart_window.title("نمودار تراکنش‌ها")
        chart_window.geometry("600x400")
        
        # ایجاد کانواس برای نمودار
        chart_canvas = tk.Canvas(chart_window, bg='#2e2e2e')
        chart_canvas.pack(fill=tk.BOTH, expand=True)
        
        # محاسبه مقادیر
        wallet_address = self.wallet_entry.get().strip()
        total_in = sum(float(tx['amount']) for tx in self.current_transactions 
                     if tx['to'] == wallet_address)
        total_out = sum(float(tx['amount']) for tx in self.current_transactions 
                      if tx['from'] == wallet_address)
        
        # متن راهنما
        chart_canvas.create_text(300, 30, text="نمودار ورودی و خروجی", 
                               fill="white", font=('B Nazanin', 14))
        
        # رسم نمودارها
        max_val = max(total_in, total_out, 1)  # جلوگیری از تقسیم بر صفر
        
        # نمودار ورودی (سبز)
        chart_canvas.create_rectangle(100, 100, 100 + (total_in/max_val)*400, 150, 
                                    fill='#2ecc71', outline='')
        chart_canvas.create_text(520, 125, text=f"{total_in:.4f}", fill="white")
        
        # نمودار خروجی (قرمز)
        chart_canvas.create_rectangle(100, 200, 100 + (total_out/max_val)*400, 250, 
                                    fill='#e74c3c', outline='')
        chart_canvas.create_text(520, 225, text=f"{total_out:.4f}", fill="white")
        
        # متن راهنما
        chart_canvas.create_text(150, 80, text="ورودی", fill="white", anchor='w')
        chart_canvas.create_text(150, 180, text="خروجی", fill="white", anchor='w')
        chart_canvas.create_text(300, 300, 
                               text=f"نتیجه نهایی: ورودی={total_in:.4f} | خروجی={total_out:.4f}",
                               fill="white")

    def animate_transactions(self, transactions):
        if not hasattr(self, 'animation_frame'):
            self.animation_frame = ttk.Frame(self.root)
            self.animation_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
    
    # پاک کردن کانواس قبلی (اگر وجود دارد)
        if hasattr(self, 'animation_canvas'):
            self.animation_canvas.destroy()
        
        self.animation_canvas = tk.Canvas(self.animation_frame, bg='#2e2e2e', highlightthickness=0)
        self.animation_canvas.pack(fill=tk.BOTH, expand=True)
    
        try:
            # محاسبه مقادیر با مدیریت خطا
            total_in = 0.0
            total_out = 0.0
            wallet_address = self.wallet_entry.get().strip()
            
            for tx in transactions:
                try:
                    amount = float(tx.get('amount', 0))
                    if tx.get('to') == wallet_address:
                        total_in += amount
                    elif tx.get('from') == wallet_address:
                        total_out += amount
                except (ValueError, TypeError):
                    continue
            
            # متن راهنما
            self.animation_canvas.create_text(100, 30, text="نمودار ورودی و خروجی", 
                                            fill="white", font=('B Nazanin', 14))
            
            # تابع به‌روزرسانی انیمیشن
            def update_animation(step):
                self.animation_canvas.delete('animation')
                
                # محاسبه مقادیر جاری
                current_in = min(step/10 * total_in, total_in)
                current_out = min(step/10 * total_out, total_out)
                
                # رسم نمودارها
                max_val = max(total_in, total_out, 1)  # جلوگیری از تقسیم بر صفر
                self.animation_canvas.create_rectangle(
                    50, 80, 50 + (current_in/max_val) * 300, 120,
                    fill='#2ecc71', outline='', tags='animation'
                )
                self.animation_canvas.create_text(
                    360, 100, text=f"{current_in:.4f}",
                    fill="white", tags='animation'
                )
                
                self.animation_canvas.create_rectangle(
                    50, 180, 50 + (current_out/max_val) * 300, 220,
                    fill='#e74c3c', outline='', tags='animation'
                )
                self.animation_canvas.create_text(
                    360, 200, text=f"{current_out:.4f}",
                    fill="white", tags='animation'
                )
                
                if step < 10:
                    self.root.after(150, update_animation, step+1)
                else:
                    # نمایش نتیجه نهایی
                    self.animation_canvas.create_text(
                        200, 250, 
                        text=f"ورودی کل: {total_in:.4f} | خروجی کل: {total_out:.4f}",
                        fill="white", font=('B Nazanin', 11), tags='animation'
                    )
            
            update_animation(0)
        
        except Exception as e:
            print(f"خطا در ایجاد انیمیشن: {str(e)}")
            self.animation_canvas.create_text(
                150, 50, 
                text="خطا در نمایش انیمیشن", 
                fill="red", font=('B Nazanin', 12)
            )
    
    def set_dark_theme(self):
        self.root.configure(bg='#2e2e2e')
        self.style = ttk.Style()
        self.style.theme_use('clam')
        
        self.style.configure('TFrame', background='#2e2e2e')
        self.style.configure('TLabel', background='#2e2e2e', foreground='white')
        self.style.configure('TButton', background='#3e3e3e', foreground='white')
        self.style.configure('TEntry', fieldbackground='#3e3e3e', foreground='white')
        self.style.configure('TCombobox', fieldbackground='#3e3e3e', foreground='white')
        self.style.map('TButton', background=[('active', '#4e4e4e')])
    
    def add_smart_tooltips(self, wallet_address):
        for item in self.tree.get_children():
            values = self.tree.item(item, 'values')
        
            if wallet_address in values[1]:  # مبدأ
                self.tree.tag_configure('from_tip', background='#2e2e2e')
                self.tree.item(item, tags=('from_tip',))
            
            elif wallet_address in values[3]:  # مقصد
                self.tree.tag_configure('to_tip', background='#2e2e2e')
                self.tree.item(item, tags=('to_tip',))

    # اتصال رویداد هاور
        self.tree.bind("<Motion>", lambda e: self.show_tooltip(e, wallet_address))

    def highlight_searched_address(self, wallet_address):
        self.tree.tag_configure('highlight_address', foreground='red')
        
        for item in self.tree.get_children():
            values = list(self.tree.item(item, 'values'))

            new_from = values[1]
            new_to = values[3]
        # بررسی ستون From (مبدا)
            if wallet_address in values[1]:  # اگر آدرس در مبدا وجود دارد
                new_from = new_from.replace(wallet_address, 
                                      f"🔴{wallet_address}🔴")
        # بررسی ستون To (مقصد)
            if wallet_address in values[3]:  # اگر آدرس در مقصد وجود دارد
               new_to = new_to.replace(wallet_address, 
                                  f"🔴{wallet_address}🔴")
        # اگر آدرس در هر یک از ستون‌ها وجود داشت، رکورد را آپدیت کن
            if new_from != values[1] or new_to != values[3]:
                self.tree.item(item, 
                         values=(values[0], new_from, values[2], new_to, *values[4:]),
                         tags=('highlight',))
        
    def create_menu(self):
        menubar = tk.Menu(self.root)
        
        file_menu = tk.Menu(menubar, tearoff=0)
        file_menu.add_command(label="ذخیره نتایج", command=self.save_results)
        file_menu.add_separator()
        file_menu.add_command(label="خروج", command=self.root.quit)
        menubar.add_cascade(label="فایل", menu=file_menu)
        
        help_menu = tk.Menu(menubar, tearoff=0)
        help_menu.add_command(label="راهنما", command=self.show_help)
        help_menu.add_command(label="درباره", command=self.show_about)
        menubar.add_cascade(label="کمک", menu=help_menu)
        
        self.root.config(menu=menubar)
    
    def track_wallet_threaded(self):
        Thread(target=self.track_wallet, daemon=True).start()
    
    """
    def get_arkham_data(self, wallet_address):
        self.status_var.set("در حال دریافت داده از Arkham...")
        self.root.update()
        
        
        all_transactions = []
        limit = 50
        offset = 0
    
        while True:
        # توقف تصادفی بین درخواست‌ها
            time.sleep(random.uniform(0.3, 0.7))
        
            url = f"https://api.arkhamintelligence.com/address/{wallet_address}/transactions?limit={limit}&offset={offset}"
            try:
                response = requests.get(url, headers={'User-Agent': 'Mozilla/5.0'})
                if response.status_code == 200:
                    data = response.json()
                    transactions = data.get('transactions', [])
                    if not transactions:
                        break
                
                    all_transactions.extend(transactions)
                    offset += limit
                
                    self.status_var.set(f"در حال دریافت داده از Arkham... ({len(all_transactions)} تراکنش)")
                    self.root.update()
                else:
                    break
            except Exception as e:
                messagebox.showerror("خطا", f"خطا در دریافت داده از Arkham: {str(e)}")
                return None
    
        return {'transactions': all_transactions} if all_transactions else None
    """
    
    def get_arkham_data(self, wallet_address):    
        self.status_var.set("در حال دریافت داده از Arkham...")
        self.root.update()
        
        all_transactions = []
        limit = 50
        offset = 0
        retry_count = 0
        max_retries = 3
        
        while True:
            try:
                # توقف تصادفی + افزایش تدریجی زمان انتظار
                delay = random.uniform(0.5, 1.5) + (retry_count * 0.5)
                time.sleep(delay)
                
                #api_key = api_config.eth
                #url = f"https://api.etherscan.io/api?module=account&action=txlist&address={wallet_address}&startblock=0&endblock=********&page={page}&offset={offset}&sort=asc&apikey={api_key}"
                url = f"https://api.arkhamintelligence.com/address/{wallet_address}/transactions"
                params = {
                    'limit': limit,
                    'offset': offset
                }
                
                # اضافه کردن هدرهای ضروری
                headers = {
                    'User-Agent': 'Mozilla/5.0',
                    'Accept': 'application/json',
                    'Authorization': 'Bearer YOUR_API_KEY'  # اگر نیاز به کلید دارد
                }
                
                response = requests.get(url, headers=headers, params=params, timeout=10)
                
                if response.status_code == 200:
                    data = response.json()
                    transactions = data.get('transactions', [])
                    
                    if not transactions:
                        break
                        
                    all_transactions.extend(transactions)
                    offset += limit
                    retry_count = 0  # ریست شمارشگر تلاش مجدد
                    
                    self.status_var.set(f"در حال دریافت داده از Arkham... ({len(all_transactions)} تراکنش)")
                    self.root.update()
                    
                elif response.status_code == 429:
                    retry_count += 1
                    if retry_count > max_retries:
                        messagebox.showwarning("اخطار", "محدودیت نرخ درخواست. لطفاً稍后再试")
                        break
                    continue
                    
                else:
                    messagebox.showerror("خطا", f"خطای سرور: کد {response.status_code}")
                    break
                    
            except requests.exceptions.RequestException as e:
                retry_count += 1
                if retry_count > max_retries:
                    messagebox.showerror("خطا", f"خطای ارتباط: {str(e)}")
                    return None
                continue
                
        return {'transactions': all_transactions} if all_transactions else None

    def get_covalent_data(self, wallet_address):
        self.status_var.set("در حال دریافت داده از Covalent...")
        self.root.update()

        all_transactions = []
        page = 0
        page_size = 1000  # تعداد تراکنش در هر صفحه
        max_retries = 3  # حداکثر تعداد تلاش برای هر درخواست
        retry_delay = 5  # تاخیر بین تلاش‌های مجدد
        
        while True:
            # توقف تصادفی بین درخواست‌ها برای جلوگیری از Rate Limit
            time.sleep(random.uniform(0.5, 1.5))
            
            #url = f"https://api.covalenthq.com/v1/multi-chain/address/{wallet_address}/transactions/"
            url = f"https://api.covalenthq.com/v1/1/address/{wallet_address}/transactions_v2/"
            params = {
                'key': api_config.COVALENT,
                'quote-currency': 'USD',
                'page-number': page,
                'page-size': page_size
            }
            
            try:
                response = requests.get(
                    url,
                    params=params,
                    headers={'User-Agent': 'Mozilla/5.0'},
                    timeout=30  # محدودیت زمانی برای درخواست
                )
                """
                if response.status_code == 200:
                    data = response.json()
                    # بررسی ساختار پاسخ
                    if not data.get('data') or not isinstance(data['data'], dict):
                        messagebox.showwarning("اخطار", "ساختار پاسخ Covalent نامعتبر است")
                        break
                        
                    transactions = data['data'].get('items', [])
                    pagination = data['data'].get('pagination', {})
                    
                    if not transactions:
                        break
                    
                    all_transactions.extend(transactions)
                    page += 1
                    
                    # نمایش پیشرفت
                    self.status_var.set(
                        f"در حال دریافت داده از Covalent... ({len(all_transactions)} تراکنش)"
                    )
                    self.root.update()
                    
                    # بررسی پایان صفحه‌بندی
                    if not pagination.get('has_more', True):
                        break

                elif response.status_code == 429:
                    # مدیریت Rate Limit
                    retry_after = int(response.headers.get('Retry-After', retry_delay))
                    time.sleep(retry_after + 2)
                    continue
                else:
                    messagebox.showwarning(
                        "اخطار",
                        f"پاسخ غیرمنتظره از Covalent: کد {response.status_code}\n{response.text[:200]}"
                    )
                    break
                    
            except requests.exceptions.RequestException as e:
                max_retries -= 1
                if max_retries > 0:
                    time.sleep(retry_delay)
                    continue
                messagebox.showerror(
                    "خطا در ارتباط",
                    f"خطا در اتصال به Covalent پس از ۳ تلاش: {str(e)}"
                )
                return None
            except json.JSONDecodeError:
                messagebox.showerror("خطا", "پاسخ دریافتی از Covalent معتبر نیست (JSON نامعتبر)")
                return None
            except Exception as e:
                messagebox.showerror(
                    "خطای ناشناخته",
                    f"خطا در پردازش داده Covalent: {str(e)}"
                )
                return None
                   """
                # بررسی وضعیت پاسخ
                if response.status_code == 200:
                    data = response.json()
                    
                    # بررسی ساختار پاسخ
                    if not data.get('data', {}).get('items'):
                        break
                        
                    transactions = data['data']['items']
                    all_transactions.extend(transactions)
                    page += 1
                    
                    self.status_var.set(
                        f"در حال دریافت داده از Covalent... ({len(all_transactions)} تراکنش)"
                    )
                    self.root.update()
                    
                    # بررسی پایان داده‌ها
                    if len(transactions) < page_size:
                        break
                        
                elif response.status_code == 429:
                    retry_after = int(response.headers.get('Retry-After', 5))
                    time.sleep(retry_after)
                    retry_count += 1
                    if retry_count > max_retries:
                        messagebox.showwarning("اخطار", "محدودیت نرخ درخواست. لطفاً稍后再试")
                        break
                    continue
                    
                else:
                    messagebox.showerror(
                        "خطا",
                        f"خطا در دریافت داده از Covalent: کد {response.status_code}\n{response.text[:200]}"
                    )
                    return None
                    
            except Exception as e:
                messagebox.showerror("خطا", f"خطا در اتصال به Covalent: {str(e)}")
                return None

        return {'data': {'items': all_transactions}} if all_transactions else None 
        
    #def get_blockchain_data(self, wallet_address):
    def get_blockchain_data(self, wallet_address, blockchain='bsc'):
        self.status_var.set(f"در حال دریافت داده از {blockchain.upper()}...")
        self.root.update()

        # تنظیمات API برای هر بلاک‌چین
        apis = {
            'bsc': {
                'url': 'https://api.bscscan.com/api',
                'params': {
                    'module': 'account',
                    'action': 'txlist',
                    'startblock': 0,
                    'endblock': ********,
                    'sort': 'asc'
                },
                'api_key': api_config.bsc,  # جایگزین کنید
                'decimals': 18  # تبدیل از Wei
            },
            'eth': {
                'url': 'https://api.etherscan.io/api',
                'params': {
                    'module': 'account',
                    'action': 'txlist',
                    'startblock': 0,
                    'endblock': ********,
                    'sort': 'asc'
                },
                'api_key': api_config.eth,  # جایگزین کنید
                'decimals': 18  # تبدیل از Wei
            },
            'tron': {
                'url': 'https://apilist.tronscan.org/api/transaction',
                'params': {
                    'limit': 50,
                    'start': 0,
                    'sort': '-timestamp'
                },
                'api_key': None,
                'decimals': 6  # تبدیل از SUN
            },
            'polygon': {
                'url': 'https://api.polygonscan.com/api',
                'params': {
                    'module': 'account',
                    'action': 'txlist',
                    'startblock': 0,
                    'endblock': ********,
                    'sort': 'asc'
                },
                'api_key': api_config.polygon,  # جایگزین کنید
                'decimals': 18  # تبدیل از Wei
            }
        }

        if blockchain not in apis:
            messagebox.showerror("خطا", f"بلاک‌چین {blockchain} پشتیبانی نمی‌شود")
            return None

        config = apis[blockchain]
        all_transactions = []
        page = 1
        offset = 10000 if blockchain in ['bsc', 'eth', 'polygon'] else 50

        while True:
            try:
                # توقف تصادفی برای جلوگیری از Rate Limit
                time.sleep(random.uniform(0.3, 0.7))
                
                # تنظیم پارامترهای درخواست
                params = config['params'].copy()
                params.update({
                    'address': wallet_address,
                    'page': page,
                    'offset': offset
                })
                if config['api_key']:
                    params['apikey'] = config['api_key']

                # ارسال درخواست
                response = requests.get(
                    config['url'],
                    params=params,
                    headers={'User-Agent': 'Mozilla/5.0'}
                )

                if response.status_code == 200:
                    data = response.json()
                    
                    # پردازش پاسخ بر اساس بلاک‌چین
                    if blockchain in ['bsc', 'eth', 'polygon']:
                        transactions = data.get('result', [])
                        if not transactions or isinstance(transactions, str):
                            break
                    elif blockchain == 'tron':
                        transactions = data.get('data', [])
                        if not transactions:
                            break

                    all_transactions.extend(transactions)
                    page += 1

                    # نمایش پیشرفت
                    self.status_var.set(
                        f"در حال دریافت داده از {blockchain.upper()}... ({len(all_transactions)} تراکنش)"
                    )
                    self.root.update()
                else:
                    messagebox.showwarning(
                        "اخطار",
                        f"پاسخ غیرمنتظره از {blockchain.upper()}: کد {response.status_code}"
                    )
                    break

            except Exception as e:
                messagebox.showerror(
                    "خطا",
                    f"خطا در دریافت داده از {blockchain.upper()}: {str(e)}"
                )
                return None

        return {'transactions': all_transactions, 'blockchain': blockchain} if all_transactions else None

    def get_tronscan_data(self, wallet_address):
        """Get ALL transaction data from Tronscan with pagination"""
        self.status_var.set("در حال دریافت داده از Tronscan...")
        self.root.update()
    
        all_transactions = []
        limit = 50
        start = 0
    
        while True:
            time.sleep(0.5)
            url = f"https://apilist.tronscan.org/api/transaction?address={wallet_address}&limit={limit}&start={start}&sort=-timestamp"
            try:
                response = requests.get(url, headers={'User-Agent': 'Mozilla/5.0'})
                if response.status_code == 200:
                    data = response.json()
                    transactions = data.get('data', [])
                    if not transactions:
                        break
                
                    all_transactions.extend(transactions)
                    start += limit
                
                # نمایش پیشرفت در وضعیت برنامه
                    self.status_var.set(f"در حال دریافت داده از Tronscan... ({len(all_transactions)} تراکنش)")
                    self.root.update()
                else:
                    break
            except Exception as e:
                messagebox.showerror("خطا", f"خطا در دریافت داده از Tronscan: {str(e)}")
                return None
    
        return {'data': all_transactions} if all_transactions else None
    
    def get_etherscan_data(self, wallet_address):
    
        self.status_var.set("در حال دریافت داده از Etherscan...")
        self.root.update()
    
        all_transactions = []
        page = 1
        offset = 10000  # حداکثر تراکنش در هر صفحه
    
        while True:
        # توقف تصادفی بین درخواست‌ها
            time.sleep(random.uniform(0.3, 0.7))
        
            api_key = api_config.eth
            url = f"https://api.etherscan.io/api?module=account&action=txlist&address={wallet_address}&startblock=0&endblock=********&page={page}&offset={offset}&sort=asc&apikey={api_key}"
            try:
                response = requests.get(url, headers={'User-Agent': 'Mozilla/5.0'})
                if response.status_code == 200:
                    data = response.json()
                    transactions = data.get('result', [])
                    if not transactions:
                        break
                
                    all_transactions.extend(transactions)
                    page += 1
                
                    self.status_var.set(f"در حال دریافت داده از Etherscan... ({len(all_transactions)} تراکنش)")
                    self.root.update()
                else:
                    break
            except Exception as e:
                messagebox.showerror("خطا", f"خطا در دریافت داده از Etherscan: {str(e)}")
                return None
    
        return {'result': all_transactions} if all_transactions else None
    
    def get_bscscan_data(self, wallet_address):
        self.status_var.set("در حال دریافت داده از BscScan...")
        self.root.update()

        all_transactions = []
        api_key = api_config.bsc # جایگزین کنید با API Key واقعی
        page = 1
        offset = 10000  # حداکثر تراکنش در هر درخواست
        
        while True:
            # توقف تصادفی بین درخواست‌ها برای جلوگیری از Rate Limit
            time.sleep(random.uniform(0.3, 0.7))
            
            url = f"https://api.bscscan.com/api?module=account&action=txlist&address={wallet_address}" \
                f"&startblock=0&endblock=********&page={page}&offset={offset}&sort=asc&apikey={api_key}"
            
            try:
                response = requests.get(url, headers={'User-Agent': 'Mozilla/5.0'})
                if response.status_code == 200:
                    data = response.json()
                    transactions = data.get('result', [])
                    
                    if not transactions or isinstance(transactions, str):
                        break
                    
                    all_transactions.extend(transactions)
                    page += 1
                    
                    # نمایش پیشرفت
                    self.status_var.set(f"در حال دریافت داده از BscScan... ({len(all_transactions)} تراکنش)")
                    self.root.update()
                else:
                    messagebox.showwarning("اخطار", f"پاسخ غیرمنتظره از BscScan: کد {response.status_code}")
                    break
                    
            except Exception as e:
                messagebox.showerror("خطا", f"خطا در دریافت داده از BscScan: {str(e)}")
                return None

        return {'result': all_transactions} if all_transactions else None
 
    def parse_transactions(self, data, source):
        if not data:
            return []

        transactions = []
        source = source.lower() if source else ""

        try:
            raw_transactions = []
            if source == "covalent":
                transactions = self._parse_covalent(data)
            elif source == "blockchain":
                transactions = self._parse_blockchain_data(data)
            elif source == "arkham":
                transactions = self._parse_arkham_data(data)
            elif source == "tronscan" :
                transactions = self._parse_tronscan_data(data)
            elif source == "etherscan":
                transactions = self._parse_etherscan_data(data)
            elif source == "bscscan":
                transactions = self._parse_bscscan_data(data)
            else:
                print(f"Warning: Unknown data source '{source}'")

                return []

            for tx in raw_transactions:
                parsed = {
                    'source': tx.get('source', source),
                    'from': tx.get('from'),
                    'to': tx.get('to'),
                    'token': tx.get('token', 'UNKNOWN'),
                    'amount': float(tx.get('amount', 0)),
                    'date': tx.get('date'),
                    'tx_hash': tx.get('tx_hash'),
                    'chain': tx.get('chain', 'UNKNOWN')
                }
                parsed = self.analyze_transaction(parsed)  # تحلیل پیشرفته
                transactions.append(parsed)

        except Exception as e:
            print(f"Error parsing {source} transactions: {str(e)}")
            return []

        return transactions

    def _parse_covalent(self, data):
        """Parse Covalent API response"""
        transactions = []
        for tx in data.get('data', {}).get('items', []):
            transactions.append({
                'source': 'Covalent',
                'from': tx.get('from_address'),
                'to': tx.get('to_address'),
                'token': tx.get('token_symbol', 'UNKNOWN'),
                'amount': float(tx.get('value', 0)),
                'date': tx.get('block_signed_at'),
                'tx_hash': tx.get('tx_hash'),
                'chain': tx.get('chain_name', 'UNKNOWN').upper()
            })
        return transactions

    def _parse_blockchain_data(self, data):
        transactions = []
        blockchain = data.get('blockchain', '').lower()
        raw_data = data.get('transactions', []) or data.get('data', [])

        if blockchain == 'tron':
            return self._parse_tronscan_data(data)
        
        for tx in raw_data:
            try:
                transaction = {
                    'source': f'{blockchain.upper()}Scan',
                    'from': tx.get('from'),
                    'to': tx.get('to'),
                    'token': tx.get('tokenSymbol', blockchain.upper()),
                    'amount': self.safe_divide(tx.get('value'), self._get_decimals(blockchain)),
                    'date': self._timestamp_to_datetime(tx.get('timeStamp')),
                    'tx_hash': tx.get('hash'),
                    'status': tx.get('txreceipt_status', '1') == '1'  # 1 = موفق
                }
                
                # پردازش ویژه برای شبکه‌های خاص
                if blockchain == 'eth':
                    transaction['gas_used'] = tx.get('gasUsed')
                elif blockchain == 'bsc':
                    transaction['is_error'] = tx.get('isError') == '0'
                
                transactions.append(transaction)
            except Exception as e:
                print(f"Error parsing transaction: {str(e)}")
                continue

        return transactions

    def _parse_arkham_data(self, data):
        transactions = []
        for tx in data.get('transactions', []) or data.get('data', []):
            try:
                transactions.append({
                    'source': 'Arkham',
                    'from': tx.get('from'),
                    'to': tx.get('to'),
                    'token': tx.get('token', 'UNKNOWN'),
                    'amount': float(tx.get('amount', 0)),
                    'date': self._timestamp_to_datetime(tx.get('timestamp')),
                    'tx_hash': tx.get('txHash'),
                    'chain': tx.get('chain', 'UNKNOWN').upper()
                })
            except Exception as e:
                print(f"Error parsing Arkham transaction: {str(e)}")
                continue
        return transactions

    def _parse_tronscan_data(self, data):
        transactions = []
        for tx in data.get('data', []):
            try:
                transactions.append({
                    'source': 'Tronscan',
                    'from': tx.get('ownerAddress'),
                    'to': tx.get('toAddress'),
                    'token': tx.get('tokenType', 'TRX'),
                    'amount': self.safe_divide(tx.get('amount'), 10**6),
                    'date': self._timestamp_to_datetime(tx.get('timestamp')/1000),
                    'tx_hash': tx.get('hash'),
                    'status': tx.get('confirmed', True)
                })
            except Exception as e:
                print(f"Error parsing Tron transaction: {str(e)}")
                continue
        return transactions

    def _parse_etherscan_data(self, data):
        if isinstance(data, str):
            try:
                data = json.loads(data)
            except json.JSONDecodeError:
                data = {}
        
        transactions = []
        for tx in data.get('result', []):
            try:
                transactions.append({
                    'source': 'Etherscan',
                    'from': tx.get('from'),
                    'to': tx.get('to'),
                    'token': tx.get('tokenSymbol', 'ETH'),
                    'amount': self.safe_divide(tx.get('value'), 10**18),
                    'date': self._timestamp_to_datetime(tx.get('timeStamp')),
                    'tx_hash': tx.get('hash'),
                    'gas_used': tx.get('gasUsed')
                })
            except Exception as e:
                print(f"Error parsing Ethereum transaction: {str(e)}")
                continue
        return transactions

    def _get_decimals(self, blockchain):
        """Get decimal places for different blockchains"""
        decimals_map = {
            'eth': 18,
            'bsc': 18,
            'polygon': 18,
            'tron': 6,
            'arbitrum': 18,
            'optimism': 18
        }
        return decimals_map.get(blockchain.lower(), 18)  # پیش‌فرض 18 رقم اعشار

    def _parse_bscscan_data(self, data):
        """Parse BscScan API response"""
        transactions = []
        for tx in data.get('result', []):
            transactions.append({
                'source': 'BscScan',
                'from': tx.get('from'),
                'to': tx.get('to'),
                'token': tx.get('tokenSymbol', 'BNB'),
                'amount': self.safe_divide(tx.get('value'), 1e18),
                'date': self._timestamp_to_datetime(tx.get('timeStamp')),
                'tx_hash': tx.get('hash')
            })
        return transactions

    def _timestamp_to_datetime(self, timestamp):
        """Convert various timestamp formats to datetime string"""
        try:
            if isinstance(timestamp, str):
                timestamp = int(timestamp)
            return datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')
        except:
            return "Unknown"

    def analyze_transaction(self, tx):
        
    # تشخیص صرافی‌ها
        from_result = self.exchange_detector.detect_exchange(tx['from'], tx.get('token'))
        to_result = self.exchange_detector.detect_exchange(tx['to'], tx.get('token'))
        tx.update({
        'from_exchange': from_result.get('exchange', 'Unknown'),
        'from_confidence': from_result.get('confidence', 0),  # این خط حتماً باید وجود داشته باشد
        'to_exchange': to_result.get('exchange', 'Unknown'),
        'to_confidence': to_result.get('confidence', 0),     # این خط حتماً باید وجود داشته باشد
    })
        return tx
        


    @staticmethod
    def safe_divide(value, divisor, default=0):
        """Safely divide two numbers with error handling"""
        try:
            return float(value) / divisor
        except (ValueError, TypeError, ZeroDivisionError):
            return default
     
    def track_wallet(self):
        wallet_address = self.wallet_entry.get().strip()
        if not wallet_address:
            messagebox.showwarning("هشدار", "لطفاً آدرس کیف پول را وارد کنید")
            return

        self.search_btn.config(state=tk.DISABLED)
        self.show_chart_btn.config(state=tk.DISABLED)  # غیرفعال کردن دکمه نمودار
        self.status_var.set("در حال جستجو...")
        self.root.update()

    # پاک کردن نتایج قبلی
        for item in self.tree.get_children():
            self.tree.delete(item)

        all_transactions = []
        detector = ExchangeDetector()

        try:
            if self.covalent_var.get():
                covalent_data = self.get_covalent_data(wallet_address)
                if covalent_data:
                    all_transactions.extend(self.parse_transactions(covalent_data, "covalent"))


            if self.blockchain_var.get():
                blockchain_data = self.get_blockchain_data(wallet_address)
                if blockchain_data:
                    all_transactions.extend(self.parse_transactions(blockchain_data, "blockchain"))

            if self.arkham_var.get():
                arkham_data = self.get_arkham_data(wallet_address)
                if arkham_data:
                    all_transactions.extend(self.parse_transactions(arkham_data, "arkham"))

            if self.tronscan_var.get():
                tronscan_data = self.get_tronscan_data(wallet_address)
                if tronscan_data:
                    all_transactions.extend(self.parse_transactions(tronscan_data, "tronscan"))

            if self.etherscan_var.get():
                etherscan_data = self.get_etherscan_data(wallet_address)
                if etherscan_data:
                    all_transactions.extend(self.parse_transactions(etherscan_data, "etherscan"))
            
            if self.bscscan_var.get():  # نیاز به اضافه کردن چک باکس در UI
                bscscan_data = self.get_bscscan_data(wallet_address)
                if bscscan_data:
                    all_transactions.extend(self.parse_transactions(bscscan_data, "bscscan"))

            if not all_transactions:
                messagebox.showinfo("اطلاع", "هیچ تراکنشی یافت نشد")
                self.status_var.set("آماده - هیچ تراکنشی یافت نشد")
                self.current_transactions = None  # تنظیم تراکنش‌ها به None
                return

        # پردازش تراکنش‌ها
            transaction_groups = {}
            for tx in all_transactions:
            # تشخیص صرافی‌ها
                tx['from_exchange'] = detector.detect_by_pattern(tx['from'], tx['token']) or "Unknown"
                tx['to_exchange'] = detector.detect_by_pattern(tx['to'], tx['token']) or "Unknown"
            
                if tx['from_exchange'] == "Unknown":
                    tx['from_exchange'] = detector.detect_by_api(tx['from'], tx['token'])
                if tx['to_exchange'] == "Unknown":
                    tx['to_exchange'] = detector.detect_by_api(tx['to'], tx['token'])
                
                key = (tx['from'], tx['to'], tx['token'])
                if key not in transaction_groups:
                    transaction_groups[key] = {
                            'source': tx['source'],
                            'from_exchange': tx['from_exchange'],
                            'from_confidence': tx['from_confidence'],  # این خط باید وجود داشته باشد
                            'to_exchange': tx['to_exchange'],
                            'to_confidence': tx['to_confidence'],      # این خط باید وجود داشته باشد
                            'amounts': [],
                            'dates': [],
                            'count': 0
                                        }
                    
                """
            # گروه‌بندی تراکنش‌ها
                key = (tx['from'], tx['to'], tx['token'])
                if key not in transaction_groups:
                    transaction_groups[key] = {
                    'source': tx['source'],
                    'from_exchange': tx['from_exchange'],
                    'from_confidence': tx['from_confidence'],
                    'to_exchange': tx['to_exchange'],
                    'to_confidence': tx['to_confidence'],
                    'amounts': [],
                    'dates': [],
                    'count': 0
                    }
                """
                transaction_groups[key]['amounts'].append(str(tx['amount']))
                transaction_groups[key]['dates'].append(tx['date'])
                transaction_groups[key]['count'] += 1

        # نمایش در Treeview
            for key, group in transaction_groups.items():
                from_addr, to_addr, token = key
                self.tree.insert('', tk.END, values=(
                group['source'],
                from_addr,
                group['from_exchange'],
                group['from_confidence'],
                to_addr,
                group['to_exchange'],
                group['to_confidence'],
                token,
                "\n".join(group['amounts']),
                "\n".join(group['dates']),
                group['count']
                ))

        # هایلایت و انیمیشن
            
            # ذخیره تراکنش‌ها برای نمایش نمودار
            self.current_transactions = all_transactions
            self.show_chart_btn.config(state=tk.NORMAL)  # فعال کردن دکمه نمودار

        # هایلایت آدرس
            self.highlight_searched_address(wallet_address)
        except Exception as e:
            messagebox.showerror("خطا", f"خطا در پردازش تراکنش‌ها: {str(e)}")
            self.current_transactions = None
        finally:
            self.status_var.set(f"آماده - {len(all_transactions)} تراکنش یافت شد")
            self.search_btn.config(state=tk.NORMAL)
        

    def calculate_risk_score(self, tx):
        score = 0
        
        # امتیاز بر اساس صرافی مبدا
        if tx['from_exchange'] != 'Unknown':
            score += 30
        
        # امتیاز بر اساس صرافی مقصد
        if tx['to_exchange'] != 'Unknown':
            score += 20
        
        # امتیاز بر اساس حجم تراکنش
        try:
            amount = float(max(tx['amounts'], key=float))
            if amount > 10:  # برای مثال 10 واحد از توکن
                score += min(50, amount * 2)
        except:
            pass
        
        return min(100, score)  # حداکثر امتیاز 100


    def save_results(self):
        if not self.tree.get_children():
            messagebox.showwarning("هشدار", "هیچ داده‌ای برای ذخیره وجود ندارد")
            return
        
        file_path = filedialog.asksaveasfilename(
            defaultextension=".csv",
            filetypes=[("Excel Files", "*.xlsx"), ("CSV Files", "*.csv"), ("All Files", "*.*")],
            title="ذخیره نتایج به عنوان"
        )
        
        if not file_path:
            return
        
        try:
            data = []
            # جمع‌آوری داده‌های منحصر به فرد از Treeview
            #unique_data = []
            #seen = set()
            for item in self.tree.get_children():
                values = self.tree.item(item, 'values')
                data.append({
                    'منبع': values[0],
                    'مبدا': values[1],
                    'صرافی مبدا': values[2],  # ستون جدید
                    'مقصد': values[3],
                    'صرافی مقصد': values[4],  # ستون جدید
                    'توکن': values[5],
                    'مقادیر': values[6].replace("\n", " | "),
                    'تاریخ‌ها': values[7].replace("\n", " | "),
                    'تعداد': values[8]
                })
            
            df = pd.DataFrame(data)
            
            # محاسبه تعداد تکرار هر ترکیب مبدا/مقصد
            #df['تعداد تکرار'] = df.groupby(['مبدا', 'مقصد'])['مبدا'].transform('count')
            # مرتب‌سازی بر اساس تعداد تکرار (نزولی)
            #df = df.sort_values(by='تعداد تکرار', ascending=False)
            if file_path.endswith('.xlsx'):
                df.to_excel(file_path, index=False, engine='openpyxl')
            else:  # برای فایل‌های CSV
                df.to_csv(file_path, index=False, encoding='utf-8-sig')
            #df.to_csv(file_path, index=False, encoding='utf-8-sig')
            messagebox.showinfo("موفق", "نتایج با موفقیت ذخیره شد")
        except Exception as e:
            messagebox.showerror("خطا", f"خطا در ذخیره فایل: {str(e)}")
    
    def show_help(self):
        help_text = """...متن راهنما"""
        messagebox.showinfo("راهنما", help_text)
    
    def show_about(self):
        about_text = """...متن درباره برنامه"""
        messagebox.showinfo("درباره برنامه", about_text)

if __name__ == "__main__":
    root = tk.Tk()
    app = CryptoWalletTrackerApp(root)
    root.mainloop()
   