from web3 import Web3
import requests
import json
import tkinter as tk
from tkinter import ttk, messagebox
import time
import random

class CryptoWalletTrackerApp:
    def __init__(self, root):
        self.root = root    
        self.setup_main_window()
        self.create_widgets() 
        self.set_dark_theme()

    def setup_main_window(self):
        self.root.geometry("1300x800")
        self.root.resizable(True, True)
        self.root.title("برنامه تحلیل و ردیابی رمز ارزها ")
     

    def create_widgets(self):
        # عنوان برنامه
      
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        title_label = ttk.Label(main_frame, 
                              text="ردیاب تراکنش‌های کیف پول ارز دیجیتال",
                              font=('Tahoma', 12, 'bold'))
        title_label.pack(pady=2)
        
        input_frame = ttk.Frame(main_frame)
        input_frame.pack(fill=tk.X, pady=10)
        
        ttk.Label(input_frame, text="آدرس کیف پول:").pack(side=tk.LEFT, padx=5)
        
        self.wallet_entry = ttk.Entry(input_frame, width=60)
        self.wallet_entry.pack(side=tk.LEFT, expand=True, fill=tk.X, padx=5)
        
        self.search_btn = ttk.Button(input_frame, text="جستجو", command=self.track_wallet)
        self.search_btn.pack(side=tk.LEFT, padx=5)
        
        source_frame = ttk.LabelFrame(main_frame, text="منابع اطلاعاتی", padding=10)
        source_frame.pack(fill=tk.X, pady=10)
        
        # لیست منابع و متغیرها
        sources = [
            ("blockcypher", "blockcypher_var"),         
            ("solana", "solana_var"), 
            
            ("BscScan (BSC)", "bscscan_var")
                
        ]
        
        # مقداردهی اولیه متغیرها
        for _, var_name in sources:
            setattr(self, var_name, tk.BooleanVar(value=True))
        
        # ایجاد چک‌باکس‌ها به صورت جدولی
        for i, (text, var_name) in enumerate(sources):
            row = i // 2  # تقسیم به دو ستون
            col = i % 2
            ttk.Checkbutton(
                source_frame,
                text=text,
                variable=getattr(self, var_name)
            ).grid(
                row=row,
                column=col,
                sticky="w",
                padx=10,
                pady=1,
                ipadx=5,
                ipady=1
            )
        
        # تنظیمات گرید برای ظاهر بهتر
        source_frame.grid_columnconfigure(0, weight=1, uniform="group1")
        source_frame.grid_columnconfigure(1, weight=1, uniform="group1")
        
        result_frame = ttk.LabelFrame(main_frame, text="نتایج تراکنش‌ها", padding=10)
        result_frame.pack(fill=tk.BOTH, expand=True)
        
       # تنظیم ارتفاع سطرها - این بخش باید قبل از ایجاد Treeview باشد
        style = ttk.Style()
        style.theme_use('clam')  # استفاده از تمی که از rowheight پشتیبانی می‌کند
        style.configure('Treeview', 
               rowheight=30,
               font=('Tahoma', 9),
               background="#ffffff",
               fieldbackground="#ffffff")
        #self.tree = ttk.Treeview(result_frame, columns=('Source', 'From', 'To', 'Token', 'Amount', 'Date'), show='headings')
        #self.tree = ttk.Treeview(result_frame, columns=('Source', 'From', 'To', 'Token', 'Amounts', 'Dates', 'Count'), show='headings')
      
        self.tree = ttk.Treeview(result_frame, columns=(
            'Source', 'From', 'From Exchange', 'To', 'To Exchange', 
            'Token', 'Amounts', 'Dates', 'Count'
            ), show='headings')
        #self.tree.heading('Count', text='تعداد تکرار')
        #self.tree.column('Count', width=80)
           # تنظیمات ستون‌ها
        self.tree.heading('Source', text='منبع')
        self.tree.heading('From', text='مبدا')
        self.tree.heading('From Exchange', text='صرافی مبدا')
        self.tree.heading('To', text='مقصد')
        self.tree.heading('To Exchange', text='صرافی مقصد')
        self.tree.heading('Token', text='توکن')
        self.tree.heading('Amounts', text='مقادیر')
        self.tree.heading('Dates', text='تاریخ‌ها')
        self.tree.heading('Count', text='تعداد تکرار')

        
    # تنظیم عرض ستون‌ها
        self.tree.column('Source', width=50)
        self.tree.column('From', width=250)
        self.tree.column('From Exchange', width=100)
        self.tree.column('To', width=250)
        self.tree.column('To Exchange', width=100)
        self.tree.column('Token', width=80)
        self.tree.column('Amounts', width=100)
        self.tree.column('Dates', width=150)
        self.tree.column('Count', width=50)
        # تنظیم تراز متن برای ستون‌های چندخطی
        self.tree.tag_configure('multiline', font=('Tahoma', 9))

        
        scrollbar = ttk.Scrollbar(result_frame, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscroll=scrollbar.set)
        
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        self.status_var = tk.StringVar()
        self.status_var.set("آماده")
        status_label = ttk.Label(main_frame, textvariable=self.status_var,font="bold")
        status_label.pack(side=tk.BOTTOM, fill=tk.X)
   
    def set_dark_theme(self):
        self.root.configure(bg='#2e2e2e')
        self.style = ttk.Style()
        self.style.theme_use('clam')
        
        self.style.configure('TFrame', background='#2e2e2e')
        self.style.configure('TLabel', background='#2e2e2e', foreground='white')
        self.style.configure('TButton', background='#3e3e3e', foreground='white')
        self.style.configure('TEntry', fieldbackground='#3e3e3e', foreground='white')
        self.style.configure('TCombobox', fieldbackground='#3e3e3e', foreground='white')
        self.style.map('TButton', background=[('active', '#4e4e4e')])
   
    def get_blockcypher_data(self, wallet_address, coin='btc'):
        self.status_var.set("در حال دریافت داده از BlockCypher...")
        self.root.update()

        base_url = f"https://api.blockcypher.com/v1/{coin}/main/addrs/{wallet_address}/full"
        
        try:
            time.sleep(random.uniform(0.3, 0.7))
            response = requests.get(base_url, headers={'User-Agent': 'Mozilla/5.0'}, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                if not data.get('txs'):
                    messagebox.showinfo("اطلاع", "هیچ تراکنشی یافت نشد")
                    return {'data': {'items': []}}  # بازگرداندن ساختار خالی اما معتبر

                return {
                'data': {
                    'items': data['txs'],
                    'coin_symbol': coin.upper()  # اضافه کردن اطلاعات ارز
                }
                        }
                
            else:
                messagebox.showerror("خطا", f"خطای HTTP {response.status_code}")
                return {'data': {'items': []}}  # بازگرداندن ساختار خالی اما معتبر
                #transactions = data.get('txs', [])
                #transactions = data['txs']

                
        except Exception as e:
            messagebox.showerror("خطا", f"خطای ارتباطی: {str(e)}")
            return {'data': {'items': []}}  # بازگرداندن ساختار خالی اما معتبر

    def get_solana_data(self, wallet_address):
        all_transactions = []
        offset = 0
        limit = 50  # تعداد تراکنش در هر درخواست
        
        while True:
            time.sleep(random.uniform(0.3, 0.7))
            url = f"https://api.solscan.io/account/transactions?address={wallet_address}&offset={offset}&limit={limit}"
            
            try:
                response = requests.get(url, headers={'User-Agent': 'Mozilla/5.0'}, timeout=10)
                
                if response.status_code == 200:
                    data = response.json()
                    transactions = data.get('data', [])
                    
                    if not transactions:
                        break
                        
                    all_transactions.extend(transactions)
                    offset += len(transactions)
                    
                    # برای جلوگیری از دریافت تمام تاریخچه (در صورت نیاز می‌توانید محدودیت تنظیم کنید)
                    if len(all_transactions) >= 1000:  # مثال: حداکثر 1000 تراکنش
                        break
                else:
                    messagebox.showwarning("اخطار", f"محدودیت درخواست - کد وضعیت: {response.status_code}")
                    break
                    
            except Exception as e:
                messagebox.showerror("خطا", f"خطا در اتصال به Solscan: {str(e)}")
                return None

        return {'data': {'items': all_transactions}}

    def parse_transactions(self, data, source):
        transactions = []
        if not data:
            return transactions

        try:
            if source == "covalent":
                transactions = self._parse_covalent_data(data)  
            elif source == "solana":
                transactions = self._parse_solana_data(data)
            elif source == "blockcypher":
                coin_symbol = 'BTC'  # یا از منبع دیگری دریافت کنید
                transactions = self._parse_blockcypher_data(data, coin_symbol) 
                #transactions = self._parse_blockcypher_data(data)  
            
            else:
                print(f"Warning: Unknown data source '{source}'")
        except Exception as e:
            print(f"Error parsing {source} transactions: {str(e)}")
            return []

        return transactions

    def _parse_solana_data(self, data):
        transactions = []
        
        if not data or not isinstance(data, dict) or not data.get('data'):
            return transactions
            
        for tx in data['data'].get('items', []):
            try:
                transactions.append({
                    'source': 'Solscan',
                    'tx_hash': tx.get('txHash', 'N/A'),
                    'from': tx.get('from', 'N/A'),
                    'to': tx.get('to', 'N/A'),
                    'amount': float(tx.get('lamports', 0)) / 10**9,  # تبدیل از lamports به SOL
                    'token': 'SOL',
                    'date': self._timestamp_to_datetime(tx.get('timestamp', 0)),
                    'chain': 'SOLANA'
                })
            except Exception as e:
                print(f"خطا در پردازش تراکنش سولانا: {str(e)}")
                continue
                
        return transactions

    def _parse_blockcypher_data(self, data, coin_symbol):
        transactions = []
        if not data or not isinstance(data, dict) or 'data' not in data or 'items' not in data['data']:
            print("خطا: ساختار داده‌های BlockCypher نامعتبر است")
            return transactions
        try:
            for tx in data['data']['items']:
                try:
                    if not tx or not isinstance(tx, dict):
                        continue

                    # مقدار پیش‌فرض برای فیلدها
                    tx_inputs = tx.get('inputs', [{}])
                    tx_outputs = tx.get('outputs', [{}])
                    
                    from_address = tx_inputs[0].get('addresses', ['UNKNOWN'])[0] if tx_inputs else 'UNKNOWN'
                    to_address = tx_outputs[0].get('addresses', ['UNKNOWN'])[0] if tx_outputs else 'UNKNOWN'
       
                 # محاسبه مقدار با مدیریت خطاها
                    amount = 0.0
                    if 'outputs' in tx:
                        try:
                            amount = sum(out.get('value', 0) for out in tx['outputs']) / (10**8)
                        except (TypeError, ValueError):    
                            amount = 0.0
                    transactions.append({
                    'source': 'BlockCypher',
                    'from': tx['inputs'][0]['addresses'][0] if tx['inputs'] else 'UNKNOWN',
                    'to': tx['outputs'][0]['addresses'][0] if tx['outputs'] else 'UNKNOWN',
                    'token': coin_symbol,
                    'amount': sum(out['value'] for out in tx['outputs']) / (10**8),
                    'date': tx['confirmed'],
                    'tx_hash': tx['hash'],
                    'chain': coin_symbol.upper()
                    })
                except Exception as e:
                    print(f"خطا در پردازش تراکنش: {str(e)}")
                    continue 
                   
        except KeyError as e:
            print(f"خطای KeyError در پردازش داده‌ها: {str(e)}")
        except Exception as e:
            print(f"خطای غیرمنتظره در پردازش داده‌ها: {str(e)}")
  
        #return transactions
    
    def _get_decimals(self, blockchain):
        """Get decimal places for different blockchains"""
        decimals_map = {
            'eth': 18,
            'bsc': 18,
            'polygon': 18,
            'tron': 6,
            'arbitrum': 18,
            'optimism': 18
        }
        return decimals_map.get(blockchain.lower(), 18)  # پیش‌فرض 18 رقم اعشار

    def _timestamp_to_datetime(self, timestamp):
        """Convert various timestamp formats to datetime string"""
        try:
            if isinstance(timestamp, str):
                timestamp = int(timestamp)
            return datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')
        except:
            return "Unknown"

    @staticmethod
    def safe_divide(value, divisor, default=0):
        """Safely divide two numbers with error handling"""
        try:
            return float(value) / divisor
        except (ValueError, TypeError, ZeroDivisionError):
            return default
     
    def track_wallet(self):
        wallet_address = self.wallet_entry.get().strip()
        if not wallet_address:
            messagebox.showwarning("هشدار", "لطفاً آدرس کیف پول را وارد کنید")
            return

        self.search_btn.config(state=tk.DISABLED)
        #self.show_chart_btn.config(state=tk.DISABLED)  # غیرفعال کردن دکمه نمودار
        self.status_var.set("در حال جستجو...")
        self.root.update()

    # پاک کردن نتایج قبلی
        for item in self.tree.get_children():
            self.tree.delete(item)

        all_transactions = []
        #detector = ExchangeDetector()

        try:
            
            if self.solana_var.get():
                solana_data = self.get_solana_data(wallet_address)
                if solana_data:
                    parsed = self.parse_transactions(solana_data, "solana")
                    if parsed:  # فقط اگر داده معتبر بود اضافه شود
                        all_transactions.extend(parsed)
            
            if self.blockcypher_var.get():
                blockcypher_data = self.get_blockcypher_data(wallet_address, 'btc')
                if blockcypher_data:
                    parsed = self.parse_transactions(blockcypher_data, "blockcypher")
                    if parsed:  # فقط اگر داده معتبر بود اضافه شود
                        all_transactions.extend(parsed)

            if not all_transactions:
                messagebox.showinfo("اطلاع", "هیچ تراکنشی یافت نشد")
                self.status_var.set("آماده - هیچ تراکنشی یافت نشد")
                self.current_transactions = None  # تنظیم تراکنش‌ها به None
                return

            
            transaction_groups = {}
            for tx in all_transactions:
                key = tx.get('from', ''), tx.get('to', ''), tx.get('token', '')
                if key not in transaction_groups:
                    transaction_groups[key] = {
                        'source': tx.get('source', ''),
                        'from_exchange': tx.get('from_exchange', ''),
                        'to_exchange': tx.get('to_exchange', ''),
                        'amounts': [],
                        'dates': [],
                        'count': 0
                    }
                transaction_groups[key]['amounts'].append(f"{tx.get('amount', 0):.8f}")
                transaction_groups[key]['dates'].append(tx.get('date', ''))
                transaction_groups[key]['count'] += 1

# حالا فقط یک حلقه برای نمایش داریم
            for key, group in transaction_groups.items():
                from_addr, to_addr, token = key
                self.tree.insert('', tk.END, values=(
                    group['source'],
                    from_addr,
                    group['from_exchange'],
                    to_addr,
                    group['to_exchange'],
                    token,
                    "\n".join(group['amounts']),
                    "\n".join(group['dates']),
                    group['count']
                ))

        # نمایش در Treeview
            #print("tree tree tree")
            '''
            for tx in all_transactions:
                self.tree.insert('', tk.END, values=(
                tx.get('source', ''),
                tx.get('from', ''),
                '',  # From Exchange
                tx.get('to', ''),
                '',  # To Exchange
                tx.get('token', ''),
                f"{tx.get('amount', 0):.8f}",
                tx.get('date', ''),
                ''   # Count
                        ))
            '''            
            """
            for key, group in all_transactions :
                from_addr, to_addr, token = key
                self.tree.insert('', tk.END, values=(
                group['source'],
                from_addr,
                group['from_exchange'],
                to_addr,
                group['to_exchange'],
                token,
                "\n".join(group['amounts']),
                "\n".join(group['dates']),
                group['count']
                ))
            """
        # هایلایت و انیمیشن
            
            # ذخیره تراکنش‌ها برای نمایش نمودار
            self.current_transactions = all_transactions
            #self.show_chart_btn.config(state=tk.NORMAL)  # فعال کردن دکمه نمودار

        # هایلایت آدرس
            #self.highlight_searched_address(wallet_address)
            #self.status_var.set(f"آماده - {len(all_transactions)} تراکنش یافت شد")
            #self.search_btn.config(state=tk.NORMAL)
        except Exception as e:
            messagebox.showerror("خطا", f"خطا در پردازش تراکنش‌ها: {str(e)}")
            self.current_transactions = None
        finally:
            self.status_var.set(f"آماده - {len(all_transactions)} تراکنش یافت شد")
            self.search_btn.config(state=tk.NORMAL)
        

if __name__ == "__main__":
    root = tk.Tk()
    app = CryptoWalletTrackerApp(root)
    root.mainloop()



'''
def get_blockcypher_data(wallet_address, coin='btc'):
    base_url = f"https://api.blockcypher.com/v1/{coin}/main/addrs/{wallet_address}/full"
    
    try:
        time.sleep(random.uniform(0.3, 0.7))
        response = requests.get(base_url, 
                             headers={'User-Agent': 'Mozilla/5.0'}, 
                             timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            if not data.get('txs'):  # اگر تراکنشی وجود نداشت
                messagebox.showinfo("اطلاع", "هیچ تراکنشی یافت نشد")
                return None
                
            transactions = data['txs']
            parsed_transactions = []
            
            for tx in transactions:
                try:
                    parsed_transactions.append({
                        'source': 'BlockCypher',
                        'from': tx['inputs'][0]['addresses'][0] if tx.get('inputs') else 'N/A',
                        'to': tx['outputs'][0]['addresses'][0] if tx.get('outputs') else 'N/A',
                        'token': coin.upper(),
                        'amount': f"{sum(out['value'] for out in tx.get('outputs', [])) / 10**8:.8f}",
                        'date': tx.get('confirmed', 'N/A'),
                        'tx_hash': tx.get('hash', 'N/A')
                    })
                except Exception as e:
                    print(f"خطا در پردازش تراکنش: {str(e)}")
                    continue
            
            return {'data': {'items': parsed_transactions}}
            
        else:
            messagebox.showerror("خطا", f"خطای API: {response.status_code}")
            return None
            
    except requests.exceptions.RequestException as e:
        messagebox.showerror("خطای ارتباطی", f"خطا در اتصال به سرور: {str(e)}")
        return None
    except Exception as e:
        messagebox.showerror("خطای ناشناخته", f"خطای غیرمنتظره: {str(e)}")
        return None
'''

"""
def display_transactions(transactions_data):
    if not transactions_data or not transactions_data.get('data'):
        messagebox.showwarning("هشدار", "داده‌ای برای نمایش وجود ندارد")
        return
    
    transactions = transactions_data['data']['items']
    
    # پاک کردن داده‌های قبلی
    for item in tree.get_children():
        tree.delete(item)
    
    # اضافه کردن تراکنش‌های جدید
    for tx in transactions:
        tree.insert('', 'end', values=(
            tx['source'],
            tx['from'],
            '',  # From Exchange
            tx['to'],
            '',  # To Exchange
            tx['token'],
            tx['amount'],
            tx['date'],
            ''   # Count
        ))
"""


'''
# ایجاد پنجره اصلی
root = tk.Tk()
root.geometry("1300x600")
root.title("برنامه تحلیل و ردیابی رمز ارزها")

# ایجاد Treeview
columns = ('Source', 'From', 'From Exchange', 'To', 'To Exchange', 
           'Token', 'Amounts', 'Dates', 'Count')
tree = ttk.Treeview(root, columns=columns, show='headings')

# تنظیم هدینگ‌ها
headings = {
    'Source': 'منبع',
    'From': 'مبدا',
    'From Exchange': 'صرافی مبدا',
    'To': 'مقصد',
    'To Exchange': 'صرافی مقصد',
    'Token': 'توکن',
    'Amounts': 'مقادیر',
    'Dates': 'تاریخ‌ها',
    'Count': 'تعداد تکرار'
}

for col, text in headings.items():
    tree.heading(col, text=text)
    tree.column(col, width=100 if col in ['Source', 'Count'] else 150)

tree.pack(fill=tk.BOTH, expand=True)

# اسکرول بار
scrollbar = ttk.Scrollbar(root, orient=tk.VERTICAL, command=tree.yview)
tree.configure(yscroll=scrollbar.set)
scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

# تست با آدرس ساتوشی
#get_blockcypher_data("**********************************", 'btc')

# روش صحیح فراخوانی:
data = get_blockcypher_data("**********************************", 'btc')
if data:  # فقط اگر داده معتبر بود نمایش دهد
    display_transactions(data)


root.mainloop()'
'''