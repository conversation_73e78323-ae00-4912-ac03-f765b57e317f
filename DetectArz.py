import tkinter as tk
from tkinter import ttk, messagebox
import re
import json
import os
from web3 import Web3
import requests

class ExchangeDatabase:
    def __init__(self):
        self.db_path = "exchange_addresses.json"
        self.exchange_data = self._load_database()
        
        # الگوهای خاص آدرس صرافی‌ها
        self.patterns = {
            'Binance': [
                r'^bnb1[a-z0-9]{39}$',
                r'^0x[a-f0-9]{40}$',
                r'^1[a-km-zA-HJ-NP-Z1-9]{25,34}$'
            ],
            'Coinbase': [
                r'^3[a-km-zA-HJ-NP-Z1-9]{33,34}$',
                r'^bc1[a-z0-9]{39,59}$'
            ]
        }
    
    def _load_database(self):
        if os.path.exists(self.db_path):
            with open(self.db_path, 'r') as f:
                return json.load(f)
        return {
            'Binance': {
            'hot_wallets': [
                '******************************************',
                '******************************************'
            ],
            'cold_wallets': [
                '******************************************',
                '******************************************'
            ],
            'deposit_addresses': []
            },
            'Coinbase': {
            'hot_wallets': [
                '******************************************',
                '******************************************'
            ],
            'cold_wallets': [
                '******************************************',
                '******************************************'
            ],
            'deposit_addresses': []
            }
            # سایر صرافی‌ها
        }
    
    def update_database(self):
        # خودکارسازی آپدیت از منابع معتبر
        sources = [
            'https://etherscan.io/accounts/label/exchange',
            'https://www.walletexplorer.com'
        ]
        # پیاده‌سازی مکانیسم آپدیت

class BehaviorAnalyzer:
    def __init__(self, web3_provider):
        self.w3 = Web3(Web3.HTTPProvider(web3_provider))
        self.api_keys = {
            'etherscan': 'YOUR_ETHERSCAN_API',
            'blockcypher': 'YOUR_BLOCKCYPHER_API'
        }
    
    def analyze_behavior(self, address):
        stats = {
            'tx_count': self._get_transaction_count(address),
            'tx_frequency': self._get_transaction_frequency(address),
            'balance_changes': self._get_balance_changes(address),
            'neighbors': self._get_neighbor_addresses(address)
        }
        
        return self._evaluate_metrics(stats)
    
    def _evaluate_metrics(self, stats):
        # معیارهای تشخیص صرافی
        exchange_likelihood = 0
        
        if stats['tx_count'] > 1000:
            exchange_likelihood += 30
            
        if stats['tx_frequency'] > 10:  # تراکنش در ساعت
            exchange_likelihood += 25
            
        if len(stats['neighbors']) > 50:
            exchange_likelihood += 20
            
        if self._has_mixing_pattern(stats['balance_changes']):
            exchange_likelihood += 25
            
        return exchange_likelihood >= 70  # آستانه تشخیص
    
    def _get_transaction_count(self, address):
        # استفاده از APIهای مختلف
        pass

class ExchangeDetector:
    def __init__(self):
        self.db = ExchangeDatabase()
        self.analyzer = BehaviorAnalyzer('YOUR_INFURA_URL')
        self.cache = {}  # کش برای بهبود عملکرد
    
    def detect_exchange(self, address):
        # مرحله 1: بررسی کش
        if address in self.cache:
            return self.cache[address]
            
        # مرحله 2: بررسی مستقیم در دیتابیس
        direct_match = self._check_direct_match(address)
        if direct_match:
            self.cache[address] = direct_match
            return direct_match
            
        # مرحله 3: بررسی الگوی آدرس
        pattern_match = self._check_patterns(address)
        if pattern_match:
            self.cache[address] = pattern_match
            return pattern_match
            
        # مرحله 4: تحلیل رفتاری
        behavior_result = self.analyzer.analyze_behavior(address)
        if behavior_result:
            result = "Likely exchange address (Behavioral Analysis)"
            self.cache[address] = result
            return result
            
        return "Not identified as exchange address"
    
    def _check_direct_match(self, address):
        for exchange, data in self.db.exchange_data.items():
            if address in data['hot_wallets']:
                return f"{exchange} Hot Wallet"
            if address in data['cold_wallets']:
                return f"{exchange} Cold Wallet"
            if address in data['deposit_addresses']:
                return f"{exchange} Deposit Address"
        return None
    
    def _check_patterns(self, address):
        for exchange, patterns in self.db.patterns.items():
            for pattern in patterns:
                if re.match(pattern, address):
                    return f"Possible {exchange} address (Pattern Match)"
        return None

class TokenAnalyzer:
    ERC20_ABI = [...]  # ABI استاندارد ERC-20
    
    def __init__(self, provider_url):
        self.w3 = Web3(Web3.HTTPProvider(provider_url))
    
    def analyze_token(self, contract_address):
        contract = self.w3.eth.contract(
            address=self.w3.to_checksum_address(contract_address),
            abi=self.ERC20_ABI
        )
        
        try:
            return {
                'name': contract.functions.name().call(),
                'symbol': contract.functions.symbol().call(),
                'decimals': contract.functions.decimals().call(),
                'total_supply': contract.functions.totalSupply().call(),
                'standard': self._detect_token_standard(contract),
                'holders': self._get_top_holders(contract_address),
                'security': self._check_security(contract)
            }
        except Exception as e:
            return {'error': str(e)}
    
    def _detect_token_standard(self, contract):
        # بررسی استانداردهای مختلف
        if self._supports_interface(contract, '0x80ac58cd'):  # ERC-721
            return 'ERC-721'
        elif self._supports_interface(contract, '0xd9b67a26'):  # ERC-1155
            return 'ERC-1155'
        else:
            return 'ERC-20'
    
    def _check_security(self, contract):
        # بررسی موارد امنیتی
        checks = {
            'pausable': 'pause' in dir(contract.functions),
            'mintable': 'mint' in dir(contract.functions),
            'blacklist': 'blacklist' in dir(contract.functions)
        }
        return checks

class CryptoDetectorApp:
    def __init__(self, root):
        self.root = root
        self.root.title("تشخیص نوع ارز دیجیتال")
        self.root.geometry("700x500")
        # تنظیم فونت فارسی
        self.font = ("Tahoma", 12)
        
       
        self.exchange_detector = ExchangeDetector()
        self.token_analyzer = TokenAnalyzer('YOUR_INFURA_URL')
        
       

        self.create_widgets()
        self.setup_crypto_patterns()
    
    def setup_crypto_patterns(self):
        """الگوهای آدرس ارزهای مختلف"""
        self.crypto_patterns = {
            'BTC': {
                'name': 'بیت‌کوین',
                'patterns': [
                    r'^[13][a-km-zA-HJ-NP-Z1-9]{25,34}$',
                    r'^bc1[a-z0-9]{25,90}$'
                ]
            },
            'ETH': {
                'name': 'اتریوم',
                'patterns': [
                    r'^0x[a-fA-F0-9]{40}$'
                ],
                'is_erc20': False  # خود اتریوم یک توکن نیست
            },
            'XRP': {
                'name': 'ریپل',
                'patterns': [
                    r'^r[1-9a-km-zA-HJ-NP-Z]{24,34}$'
                ]
            },
            'LTC': {
                'name': 'لایت‌کوین',
                'patterns': [
                    r'^[LM3][a-km-zA-HJ-NP-Z1-9]{26,33}$',
                    r'^ltc1[a-z0-9]{39}$'
                ]
            },
            'BNB': {
                'name': 'بایننس کوین',
                'patterns': [
                    r'^bnb[a-z0-9]{39}$'
                ]
            },
            'TRX': {
                'name': 'ترون',
                'patterns': [
                    r'^T[a-zA-Z0-9]{33}$'
                ]
            },
            'SHIB': {
                 'name': 'Shiba Inu',
                 'patterns': [
                        r'^0x[a-fA-F0-9]{40}$',  # آدرس استاندارد اتریوم (ERC-20)
                ]
                },
            'DOGE': {
                'name': 'Dogecoin',
                'patterns': [
                    r'^D{1}[5-9A-HJ-NP-U]{1}[1-9A-HJ-NP-Za-km-z]{32}$',  # آدرس DOGE (Legacy)
                    r'^D{1}[5-9A-HJ-NP-U]{1}[1-9A-HJ-NP-Za-km-z]{33}$',  # برخی آدرس‌های طولانی‌تر
                    
                ]
            },
            'ADA': {
                'name': 'Cardano',
                'patterns': [
                    r'^addr1[0-9a-z]{58}$',  # آدرس کاردانو (Shelley)
                    r'^Ae2[0-9a-zA-Z]{50,}$',  # آدرس قدیمی (Byron)
                    
                ]
            },                    
            'USDT': {
                'name': 'تتر',
                'patterns': [
                    r'^0x[a-fA-F0-9]{40}$'
                ],
                'contract_address': [
                    '0xdAC17F958D2ee523a2206206994597C13D831ec7',  # USDT (ERC-20)
                    '0xTR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t'  # USDT (TRC-20)
                ],
                'is_erc20': True
            },
            'SOL': {
                'name': 'سولانا',
                'patterns': [
                    r'^[1-9A-HJ-NP-Za-km-z]{32,44}$'  # آدرس سولانا (Base58)
                ]
            },
            'DOT': {
                'name': 'پولکادات',
                'patterns': [
                    r'^1[a-z0-9]{47}$',  # آدرس پولکادات (SS58)
                    r'^[1-9A-HJ-NP-Za-km-z]{47}$'
                ]
            },
            'MATIC': {
                'name': 'پالیگان',
                'patterns': [
                    r'^0x[a-fA-F0-9]{40}$'  # آدرس اتریوم (ERC-20)
                ]
            },
            'AVAX': {
                'name': 'آوالانچ',
                'patterns': [
                    r'^X-[a-zA-Z0-9]{39}$',  # آدرس آوالانچ (X-Chain)
                    r'^0x[a-fA-F0-9]{40}$'  # آدرس C-Chain (مشابه اتریوم)
                ]
            },
            'LINK': {
                'name': 'چین لینک',
                'patterns': [
                    r'^0x[a-fA-F0-9]{40}$'  # آدرس اتریوم (ERC-20)
                ]
            },
            'XLM': {
                'name': 'استلار',
                'patterns': [
                    r'^G[A-Z0-9]{55}$'  # آدرس استلار
                ]
            },
            'ATOM': {
                'name': 'کازماس',
                'patterns': [
                    r'^cosmos1[a-z0-9]{38}$'  # آدرس کازماس (Bech32)
                ]
            },
            'UNI': {
                'name': 'یونی سواپ',
                'patterns': [
                    r'^0x[a-fA-F0-9]{40}$'  # آدرس اتریوم (ERC-20)
                ]
            },
            'ICP': {
                'name': 'اینترنت کامپیوتر',
                'patterns': [
                    r'^[a-f0-9]{64}$'  # آدرس ICP (Hex)
                ]
            },
            'PEPE': {
                'name': 'Pepe',
                'patterns': [
                    r'^0x[a-fA-F0-9]{40}$'
                ],
                'contract_address': [
                    '0x6982508145454Ce325dDbE47a25d4ec3d2311933'  # آدرس قرارداد PEPE
                ],
                'is_erc20': True
            },
        }
    
    def create_widgets(self):
        """ایجاد عناصر رابط کاربری"""
        # فریم اصلی
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # عنوان برنامه
        title_label = ttk.Label(
            main_frame,
            text="تشخیص نوع ارز دیجیتال از آدرس کیف پول",
            font=("Tahoma", 14, "bold")
        )
        title_label.pack(pady=10)
        
        # ورودی آدرس
        address_frame = ttk.Frame(main_frame)
        address_frame.pack(fill=tk.X, pady=10)
        
        ttk.Label(
            address_frame,
            text="آدرس کیف پول:",
            font=self.font
        ).pack(side=tk.LEFT)
        
        self.address_entry = ttk.Entry(
            address_frame,
            font=self.font,
            width=40
        )
        self.address_entry.pack(side=tk.LEFT, padx=10)
        self.address_entry.focus()
        
        # دکمه تشخیص
        detect_button = ttk.Button(
            main_frame,
            text="تشخیص نوع ارز",
            command=self.detect_crypto,
            style="Accent.TButton"
        )
        detect_button.pack(pady=20)
        
        # نتیجه تشخیص
        self.result_label = ttk.Label(
            main_frame,
            text="",
            font=self.font,
            foreground="green"
        )
        self.result_label.pack()
        
        # دکمه تشخیص صرافی
        exchange_button = ttk.Button(
            main_frame,
            text="تشخیص صرافی",
            command=self.detect_exchange,
            style="Accent.TButton"
        )
        exchange_button.pack(pady=10)

        # لیبل نتیجه تشخیص صرافی
        self.exchange_result_label = ttk.Label(
            main_frame,
            text="",
            font=self.font,
            foreground="blue"
        )
        self.exchange_result_label.pack()

        # نمونه آدرس‌ها
        samples_frame = ttk.LabelFrame(
            main_frame,
            text="نمونه آدرس‌ها برای تست",
            padding=10
        )
        samples_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        samples = [
            ("BTC", "**********************************"),
            ("ETH", "******************************************"),
            ("USDT (TRC20)", "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t")
        ]
        
        for symbol, address in samples:
            sample_frame = ttk.Frame(samples_frame)
            sample_frame.pack(fill=tk.X, pady=2)
            
            ttk.Label(
                sample_frame,
                text=f"{symbol}:",
                width=30,
                font=self.font
            ).pack(side=tk.LEFT)
            
            ttk.Label(
                sample_frame,
                text=address,
                font=("Tahoma", 10),
                foreground="red",
                cursor="hand2"
            ).pack(side=tk.LEFT)
            
            # امکان کلیک روی آدرس نمونه برای کپی به ورودی
            ttk.Label(
                sample_frame,
                text="[کپی]",
                font=("Tahoma", 9),
                foreground="blue",
                cursor="hand2"
            ).pack(side=tk.LEFT)
            self.setup_sample_click(sample_frame, address)
    
    def detect_exchange(self):
        address = self.address_entry.get().strip()
        
        if not address:
            messagebox.showwarning("خطا", "لطفاً آدرس کیف پول را وارد کنید")
            return
        
        result = self.exchange_detector.detect_exchange(address)
        
        # نمایش نتیجه
        if "Not identified" in result:
            self.exchange_result_label.config(
                text="این آدرس متعلق به صرافی شناخته شده نیست",
                foreground="red"
            )
        else:
            self.exchange_result_label.config(
                text=f"نتیجه تشخیص: {result}",
                foreground="blue"
            )
            
            # نمایش جزئیات بیشتر برای آدرس‌های صرافی
            if "Binance" in result or "Coinbase" in result:
                self.show_exchange_details(result.split()[0], address)

    def show_exchange_details(self, exchange_name, address):
        detail_window = tk.Toplevel(self.root)
        detail_window.title(f"جزئیات {exchange_name}")
        detail_window.geometry("500x300")
        
        # اطلاعات پایه
        ttk.Label(
            detail_window,
            text=f"صرافی: {exchange_name}",
            font=("Tahoma", 14, "bold")
        ).pack(pady=10)
        
        ttk.Label(
            detail_window,
            text=f"آدرس: {address}",
            font=self.font
        ).pack()
        
        # نوع کیف (اگر در دیتابیس وجود دارد)
        wallet_type = self.get_wallet_type(exchange_name, address)
        if wallet_type:
            ttk.Label(
                detail_window,
                text=f"نوع کیف: {wallet_type}",
                font=self.font
            ).pack()
        
        # اطلاعات امنیتی
        ttk.Label(
            detail_window,
            text="\nملاحظات امنیتی:",
            font=("Tahoma", 12, "bold")
        ).pack()
        
        security_info = {
            'Binance': "معمولاً برای تراکنش‌های روزمره استفاده می‌شود",
            'Coinbase': "معمولاً برای ذخیره‌سازی بلندمدت استفاده می‌شود"
        }
        
        ttk.Label(
            detail_window,
            text=security_info.get(exchange_name, "اطلاعات امنیتی موجود نیست"),
            font=self.font
        ).pack()
        
        # دکمه بستن
        ttk.Button(
            detail_window,
            text="بستن",
            command=detail_window.destroy
        ).pack(pady=20)

    def get_wallet_type(self, exchange_name, address):
        exchange_data = self.exchange_detector.db.exchange_data.get(exchange_name, {})
        
        if address in exchange_data.get('hot_wallets', []):
            return "کیف گرم (برای تراکنش‌های روزمره)"
        elif address in exchange_data.get('cold_wallets', []):
            return "کیف سرد (برای ذخیره‌سازی امن)"
        return None

    def setup_sample_click(self, frame, address):
        """تنظیم کلیک روی نمونه آدرس"""
        for child in frame.winfo_children():
            child.bind("<Button-1>", lambda e, addr=address: self.copy_sample(addr))
    
    def copy_sample(self, address):
        """کپی آدرس نمونه به فیلد ورودی"""
        self.address_entry.delete(0, tk.END)
        self.address_entry.insert(0, address)
        self.address_entry.focus()
    
    def detect_crypto(self):
        """تشخیص نوع ارز از آدرس وارد شده"""
        address = self.address_entry.get().strip()
        
        if not address:
            messagebox.showwarning("خطا", "لطفاً آدرس کیف پول را وارد کنید")
            return
        
        result = self.analyze_address(address)
        
        if result:
            self.result_label.config(
                text=f"نوع ارز: {result['name']} ({result['symbol']})",
                foreground="green"
            )
        else:
            self.result_label.config(
                text="نوع ارز تشخیص داده نشد!",
                foreground="red"
            )
    
    def analyze_address(self, address):
        for symbol, data in self.crypto_patterns.items():
            if data.get('is_erc20', False):  # اگر توکن ERC-20 است
                if address.lower() in [ca.lower() for ca in data.get('contract_address', [])]:
                    return symbol, data['name']
    
    # اگر قرارداد نبود، فقط ساختار آدرس را چک کنید
        for symbol, data in self.crypto_patterns.items():
            for pattern in data['patterns']:
                if re.match(pattern, address, re.IGNORECASE):
                    return {
                        'symbol': symbol,
                        'name': data['name'],
                        'address': address
                    }
        # بررسی آدرس‌های اتریوم (ممکن است توکن باشد)
        
        
        return None

if __name__ == "__main__":
    root = tk.Tk()
    
    # تنظیم تم تاریک (اختیاری)
    style = ttk.Style()
    style.theme_use('clam')
    style.configure("Accent.TButton", foreground='white', background='#0078d7')
    
    app = CryptoDetectorApp(root)
    root.mainloop()