import tkinter as tk
from tkinter import filedialog, messagebox, ttk 
import pandas as pd
from tkinter.font import Font
from openpyxl import Workbook  
import numpy as np
import jdatetime
import matplotlib.pyplot as plt
from matplotlib.backends.backend_pdf import PdfPages
import openpyxl
from openpyxl.formatting.rule import ColorScaleRule
import openpyxl.utils
from bidi.algorithm import get_display
from arabic_reshaper import reshape

class CallAnalyzerApp:
    def __init__(self, root):
        self.root = root
        self.df_calls = None
        self.df_serials = None
        self.summary_data = None
        self.tower_data = None
        self.night_sleep_data = None
        self.setup_ui()
        
    def setup_ui(self):
        self.root.title("نرم افزار تحلیل تماس‌های پیشرفته")
        self.root.configure(bg="#f0f0f0")
        
        # تنظیم اندازه و موقعیت پنجره
        window_width, window_height = 1400, 750
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        x_position = (screen_width // 2) - (window_width // 2)
        y_position = (screen_height // 2) - (window_height // 2)
        self.root.geometry(f"{window_width}x{window_height}+{x_position}+{y_position}")
        
        # تنظیم استایل‌ها
        self.configure_styles()
        self.create_menu()
        
        # ایجاد هدر
        header_frame = ttk.Frame(self.root, style="Header.TFrame")
        header_frame.pack(fill=tk.X, padx=5, pady=5)
        
        header_label = ttk.Label(
            header_frame, 
            text="نرم افزار تحلیل پیشرفته تماس‌ها", 
            style="Header.TLabel",
            font=("Tahoma", 16, "bold")
        )
        header_label.pack(pady=10)
        
        # ایجاد فریم‌های اصلی
        self.button_frame = ttk.Frame(self.root, style="ButtonFrame.TFrame")
        self.button_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=10, pady=10)
        
        self.tree_frame = ttk.Frame(self.root, style="TreeFrame.TFrame")
        self.tree_frame.pack(expand=True, fill='both', padx=10, pady=10)
        
        # اضافه کردن قابلیت جستجو
        self.add_search_filter()
        
        # ایجاد Treeview
        self.tree = ttk.Treeview(
            self.tree_frame,
            style="Custom.Treeview",
            selectmode="extended"
        )
        self.tree.pack(side=tk.LEFT,expand=True, fill='both')
        
        # اسکرول بار
        scrollbar = ttk.Scrollbar(
            self.tree_frame, 
            orient="vertical", 
            command=self.tree.yview
        )
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        # نوار وضعیت
        self.status_var = tk.StringVar()
        self.status_var.set("آماده به کار")
        status_bar = ttk.Label(
            self.root, 
            textvariable=self.status_var,
            relief=tk.SUNKEN,
            anchor=tk.W,
            style="Status.TLabel"
        )
        status_bar.pack(fill=tk.X, side=tk.BOTTOM, padx=5, pady=5)
        
        # ایجاد دکمه‌ها
        self.create_buttons()
        
    def configure_styles(self):
        style = ttk.Style()
        
        # استایل‌های اصلی
        style.configure("TFrame", background="#f0f0f0")
        style.configure("Header.TFrame", background="#4b6cb7")
        style.configure("Header.TLabel", background="#4b6cb7", foreground="white")
        style.configure("ButtonFrame.TFrame", background="#e0e0e0")
        style.configure("TreeFrame.TFrame", background="#ffffff")
        style.configure("Status.TLabel", background="#e0e0e0", font=("Tahoma", 9))
        
        # استایل دکمه‌ها
        style.configure(
            "TButton",
            padding=6,
            relief=tk.FLAT,
            background="#4b6cb7",
            foreground="red",
            font=("Tahoma", 10)
        )
        style.map(
            "TButton",
            background=[("active", "#3a5a9a")],
            foreground=[("active", "white")]
        )
        
        # استایل Treeview
        style.configure(
            "Custom.Treeview",
            font=("Tahoma", 10),
            rowheight=25,
            fieldbackground="#ffffff"
        )
        style.configure(
            "Custom.Treeview.Heading",
            font=("Tahoma", 10, "bold"),
            background="#4b6cb7",
            foreground="red",
            anchor='center'
        )
        style.map(
            "Custom.Treeview",
            background=[("selected", "#3a5a9a")],
            foreground=[("selected", "white")]
        )
        
    def create_menu(self):
        menubar = tk.Menu(self.root)
        
        # منوی فایل
        file_menu = tk.Menu(menubar, tearoff=0)
        file_menu.add_command(label="بارگذاری فایل تماس", command=lambda: self.load_file("calls"))
        file_menu.add_command(label="بارگذاری فایل سریال", command=lambda: self.load_file("serials"))
        file_menu.add_separator()
        file_menu.add_command(label="خروج", command=self.exit_app)
        menubar.add_cascade(label="فایل", menu=file_menu)
        
        # منوی گزارش
        report_menu = tk.Menu(menubar, tearoff=0)
        report_menu.add_command(label="ذخیره نتایج", command=self.save_to_excel)
        report_menu.add_command(label="ایجاد گزارش PDF", command=self.generate_report)
        menubar.add_cascade(label="گزارش", menu=report_menu)
        
        # منوی راهنما
        help_menu = tk.Menu(menubar, tearoff=0)
        help_menu.add_command(label="راهنمای استفاده", command=self.show_help)
        help_menu.add_command(label="درباره برنامه", command=self.show_about)
        menubar.add_cascade(label="راهنما", menu=help_menu)
        
        self.root.config(menu=menubar)
    
    def show_help(self):
        help_text = """راهنمای استفاده از برنامه:
1. ابتدا فایل تماس یا سریال را بارگذاری کنید
2. از بخش تحلیل‌ها، گزارش مورد نظر را انتخاب کنید
3. نتایج را می‌توانید ذخیره یا چاپ کنید"""
        messagebox.showinfo("راهنما", help_text)

    def show_about(self):
        about_text = "برنامه تحلیل تماس‌ها\nنسخه 2.0\nتوسعه داده شده با  و "
        messagebox.showinfo("پلیس ", about_text)

    def add_search_filter(self):
        search_frame = ttk.Frame(self.tree_frame)
        search_frame.pack(fill=tk.X, pady=5)
        
        search_label = ttk.Label(search_frame, text=" جستجو(نیازی به وارد کردن صفر نیست) ")
        search_label.pack(side=tk.RIGHT, padx=5)
        
        self.search_entry = ttk.Entry(search_frame)
        self.search_entry.pack(side=tk.RIGHT, expand=True, fill=tk.X, padx=5)
        self.search_entry.bind('<KeyRelease>', self.filter_data)
        
        reset_btn = ttk.Button(search_frame, text="پاک کردن", command=self.reset_search)
        reset_btn.pack(side=tk.LEFT)
    
    def filter_data(self, event):
        query = self.search_entry.get().strip().lower()  # حذف فاصله‌های اضافه و تبدیل به حروف کوچک
    
        if not query:
            self.reset_search_highlighting()
            return
    
        for item in self.tree.get_children():
            values = self.tree.item(item)['values']
            if self.is_match(query, values):
                self.tree.item(item, tags=('found',))
                self.tree.item(item, open=True)
            else:
                self.tree.item(item, tags=('hidden',))
    
        self.tree.tag_configure('found', background='#b3e6b3')
        self.tree.tag_configure('hidden', background='#ffcccc')

    def is_match(self, query, values):
    
        for value in values:
            str_value = str(value).strip().lower()

            # حذف صفرهای ابتدایی برای مقایسه بهتر شماره‌ها
            normalized_query = query.lstrip('0')
            normalized_value = str_value.lstrip('0')
        # تطابق دقیق
            if query == str_value or normalized_query == normalized_value:
                return True
            
        # تطابق جزئی
            if query in str_value or normalized_query in normalized_value:
                return True
            
        # تطابق اعداد (حذف جداکننده‌ها)
        if str(value).replace(',', '').replace(' ', '') == query:
            return True
            
        return False

    def reset_search_highlighting(self):
    
        for item in self.tree.get_children():
            self.tree.item(item, tags=('evenrow',) if int(self.tree.index(item))%2==0 else ('oddrow',))
            self.tree.item(item, open=True)


    def reset_search(self):
        self.search_entry.delete(0, tk.END)
        self.filter_data(None)
        
    def create_buttons(self):
        button_width = 20
        
        buttons = [
            ("بارگذاری فایل تماس", lambda: self.load_file("calls"), "#228B22"),
            ("بارگذاری فایل سریال", lambda: self.load_file("serials"), "#2F4F4F"),
            ("تحلیل تماس‌ها", self.analyze_calls, "#2196F3"),
            ("استخراج سریال گوشی", self.extract_phone_serials, "#DC143C"),
            ("تحلیل دکل مخابراتی", self.analyze_tower, "#BDB76B"),
            ("تحلیل تماس‌های شبانه", self.analyze_night_sleep, "#A52A2A"),
            ("ذخیره نتایج", self.save_to_excel, "#607D8B"),
            #("ایجاد گزارش PDF", self.generate_report, "#9C27B0"),
            ("خروج", self.exit_app, "#F44336")
        ]
        
        for text, command, color in buttons:
            button = tk.Button(
                self.button_frame, 
                text=text,
                command=command,
                width=button_width,
                bg=color,
                fg="white",
                bd=0,
                font=("Tahoma", 10, "bold"),
                padx=5,
                pady=5,
                activebackground=color,
                activeforeground="white"
            )
            button.pack(pady=5, ipady=3)
            button.bind("<Enter>", lambda e, b=button: b.config(bg=self.darken_color(b['bg'])))
            button.bind("<Leave>", lambda e, b=button, c=color: b.config(bg=c))
    
    def darken_color(self, color):
        rgb = tuple(int(color.lstrip('#')[i:i+2], 16) for i in (0, 2, 4))
        darkened = tuple(max(0, int(c * 0.8)) for c in rgb)
        return '#%02x%02x%02x' % darkened
    
    def load_file(self, file_type):
        self.status_var.set(f"در حال بارگذاری فایل {file_type}...")
        self.root.update()
        
        try:
            file_path = filedialog.askopenfilename(
                title=f"انتخاب فایل اکسل {file_type}",
                filetypes=[("فایل‌های اکسل", "*.xlsx;*.xls"), ("همه فایل‌ها", "*.*")]
            )
            
            if not file_path:
                self.status_var.set(f"بارگذاری فایل {file_type} لغو شد")
                return
                
            data = pd.read_excel(file_path)
            
            if len(data) == 0:
                raise ValueError("فایل انتخاب شده خالی است")
                
            if file_type == "calls":
                required_cols = ['شماره مبدا', 'شماره مقصد ', 'نوع تماس']
                self.df_calls = data
            else:
                required_cols = ['شماره تلفن', 'سریال گوشی']
                self.df_serials = data
                
            missing_cols = [col for col in required_cols if col not in data.columns]
            if missing_cols:
                raise ValueError(f"ستون‌های ضروری وجود ندارند: {', '.join(missing_cols)}")
                
            self.status_var.set(f"فایل {file_type} با {len(data)} رکورد بارگذاری شد")
            messagebox.showinfo(
                "موفقیت",
                f"فایل {file_type} با موفقیت بارگذاری شد!\nتعداد رکوردها: {len(data)}",
                parent=self.root
            )
            
        except PermissionError:
            self.status_var.set("خطا در دسترسی به فایل")
            messagebox.showerror(
                "خطا",
                "دسترسی به فایل امکان‌پذیر نیست",
                parent=self.root
            )
        except Exception as e:
            self.status_var.set(f"خطا در بارگذاری فایل {file_type}")
            messagebox.showerror(
                "خطا",
                f"خطا در بارگذاری فایل:\n{str(e)}",
                parent=self.root
            )
    
    def analyze_calls(self):
        if self.df_calls is None:
            messagebox.showwarning(
                "هشدار",
                "لطفاً ابتدا فایل تماس را بارگذاری کنید",
                parent=self.root
            )
            self.status_var.set("آماده به کار")
            return
            
        self.status_var.set("در حال تحلیل تماس‌ها...")
        self.root.update()
        
        try:
            required_columns = ['شماره مبدا', 'شماره مقصد ', 'نوع تماس']
            if not all(col in self.df_calls.columns for col in required_columns):
                raise ValueError("فایل ورودی فاقد ستون‌های مورد نیاز است")

            call_summary = self.df_calls.groupby(
                ['شماره مبدا', 'شماره مقصد ', 'نوع تماس']
            ).size().unstack(fill_value=0).reset_index()

            call_summary.rename(columns={
                'پیام کوتاه': 'تعداد پیام',
                'تماس صوتی': 'تعداد تماس'
            }, inplace=True)
            
            total_sms = call_summary['تعداد پیام'].sum()
            total_voice = call_summary['تعداد تماس'].sum()
            
            if total_sms > 0:
                call_summary['درصد پیام'] = (call_summary['تعداد پیام'] / total_sms * 100).round(2)
            if total_voice > 0:
                call_summary['درصد تماس'] = (call_summary['تعداد تماس'] / total_voice * 100).round(2)
            
            call_summary = call_summary.sort_values(by='تعداد تماس', ascending=False)
            
            self.summary_data = call_summary
            self.display_results(call_summary)
            self.status_var.set(f"تحلیل تماس‌ها انجام شد - {len(call_summary)} نتیجه")
            
        except Exception as e:
            self.status_var.set("خطا در تحلیل داده‌ها")
            messagebox.showerror(
                "خطای تحلیل",
                f"خطا در تحلیل داده‌ها:\n{str(e)}",
                parent=self.root
            )
    
    def extract_phone_serials(self):
        if self.df_serials is None:
            messagebox.showwarning(
                "هشدار",
                "لطفاً ابتدا فایل سریال را بارگذاری کنید",
                parent=self.root
            )
            self.status_var.set("آماده به کار")
            return
            
        self.status_var.set("در حال استخراج سریال گوشی‌ها...")
        self.root.update()
    
        try:
            required_columns = ['شماره تلفن', 'سریال گوشی', ' اولین تماس', ' آخرین تماس', 'مجموع تماس ها', 'مدل گوشی']
            if not all(col in self.df_serials.columns for col in required_columns):
                raise ValueError("فایل ورودی فاقد ستون‌های مورد نیاز برای استخراج سریال است")

            self.phone_serials_data = self.df_serials[required_columns].drop_duplicates()
            self.display_results(self.phone_serials_data)
            self.status_var.set(f"استخراج انجام شد - {len(self.phone_serials_data)} نتیجه")
            
        except Exception as e:
            self.status_var.set("خطا در استخراج داده‌ها")
            messagebox.showerror(
                "خطای استخراج",
                f"خطا در استخراج سریال گوشی‌ها:\n{str(e)}",
                parent=self.root
            )
    
    def analyze_tower(self):  
        if self.df_calls is None:
            messagebox.showwarning(
                "هشدار",
                "لطفاً ابتدا فایل تماس را بارگذاری کنید",
                parent=self.root
            )
            self.status_var.set("آماده به کار")
            return
            
        self.status_var.set("در حال تحلیل دکل‌های مخابراتی...")
        self.root.update()
        
        try:
            required_columns = ['lac', 'cell', 'تاریخ ']
            missing_cols = [col for col in required_columns if col not in self.df_calls.columns]
            if missing_cols:
                raise ValueError(f"ستون‌های زیر در فایل وجود ندارند: {', '.join(missing_cols)}")
            
            call_counts = self.df_calls.groupby(['lac', 'cell']).agg(
                تعداد_تماس=('تاریخ ', 'size'),
                آخرین_تماس=('تاریخ ', 'max')
            ).reset_index()
            
            call_counts = call_counts.sort_values(by='تعداد_تماس', ascending=False)
            
            self.tower_data = call_counts
            self.display_results(call_counts)
            self.status_var.set(f"تحلیل دکل‌ها انجام شد - {len(call_counts)} نتیجه")
            
        except Exception as e:
            self.status_var.set("خطا در تحلیل دکل‌ها")
            messagebox.showerror(
                "خطای تحلیل",
                f"خطا در تحلیل داده‌های دکل‌ها:\n{str(e)}",
                parent=self.root
            )
    
    def analyze_night_sleep(self):
        if self.df_calls is None:
            messagebox.showwarning(
                "هشدار",
                "لطفاً ابتدا فایل تماس را بارگذاری کنید",
                parent=self.root
            )
            self.status_var.set("آماده به کار")
            return
            
        self.status_var.set("در حال تحلیل تماس‌های شبانه...")
        self.root.update()
    
        try:
            required_columns = ['lac', 'cell', 'تاریخ ', 'ساعت ']
            missing_cols = [col for col in required_columns if col not in self.df_calls.columns]
            if missing_cols:
                raise ValueError(f"ستون‌های زیر در فایل وجود ندارند: {', '.join(missing_cols)}")
        
            night_calls = self.df_calls[
                (self.df_calls['ساعت '].astype(str) >= '00:00') & 
                (self.df_calls['ساعت '].astype(str) < '07:00')
            ]
        
            if night_calls.empty:
                messagebox.showinfo(
                    "اطلاعیه",
                    "هیچ تماسی در بازه زمانی شب (00:00 تا 07:00) یافت نشد",
                    parent=self.root
                )
                self.status_var.set("تماس شبانه یافت نشد")
                return
        
            night_stats = night_calls.groupby(['lac', 'cell']).agg(
                تعداد_تماس=('تاریخ ', 'size'),
                آخرین_تماس=('تاریخ ', 'max'),
                اولین_ساعت_تماس=('ساعت ', 'min'),
                آخرین_ساعت_تماس=('ساعت ', 'max'),
                تمام_ساعت_ها=('ساعت ', lambda x: ', '.join(sorted(x.astype(str))[:5]) + '...' if len(x) > 5 else ', '.join(sorted(x.astype(str))))
            ).reset_index()
        
            night_stats = night_stats.sort_values(by='تعداد_تماس', ascending=False)
        
            self.night_sleep_data = night_stats
            self.display_results(night_stats)
            self.status_var.set(f"تحلیل تماس‌های شبانه انجام شد - {len(night_stats)} نتیجه")
        
        except Exception as e:
            self.status_var.set("خطا در تحلیل تماس‌های شبانه")
            messagebox.showerror(
                "خطای تحلیل",
                f"خطا در تحلیل تماس‌های شبانه:\n{str(e)}",
                parent=self.root
            )
    
    def display_results(self, data):
        # پاک کردن نتایج قبلی
        for item in self.tree.get_children():
            self.tree.delete(item)
    
    # تنظیم ستون‌ها
        self.tree["columns"] = list(data.columns)
    
    # محاسبه عرض بهینه برای هر ستون
        font = tk.font.Font(family="Tahoma", size=10)
        col_widths = {}
    
    # محاسبه عرض بر اساس عنوان ستون‌ها
        for col in data.columns:
            col_widths[col] = font.measure(str(col)) + 20  # اضافه کردن padding
    
    # محاسبه عرض بر اساس محتوای ستون‌ها
        for _, row in data.iterrows():
            for col in data.columns:
                cell_width = font.measure(str(row[col])) + 20
                if cell_width > col_widths[col]:
                    col_widths[col] = cell_width
    
    # تنظیم حداقل و حداکثر عرض
        min_width = 80
        max_width = 300
    
    # پیکربندی ستون‌ها
        for col in data.columns:
            width = min(max(col_widths[col], min_width), max_width)
            self.tree.heading(col, text=col, anchor='center')
            if any(str(x).replace('.','',1).isdigit() for x in data[col]):
                self.tree.column(col, anchor='e', width=width, stretch=False)
            else:
                self.tree.column(col, anchor='w', width=width, stretch=False)
    
    # اضافه کردن داده‌ها
        for i, row in enumerate(data.itertuples(index=False)):
            tags = ('evenrow',) if i % 2 == 0 else ('oddrow',)
            self.tree.insert("", "end", values=row, tags=tags)
    
    # پیکربندی رنگ‌ها
        self.tree.tag_configure('evenrow', background='#ffffff')
        self.tree.tag_configure('oddrow', background='#f5f5f5')
    
    def save_to_excel(self):
        if not any([self.summary_data is not None, 
                   hasattr(self, 'phone_serials_data'), 
                   hasattr(self, 'tower_data'),
                   hasattr(self, 'night_sleep_data')]):
            messagebox.showwarning("هشدار", "هیچ داده‌ای برای ذخیره وجود ندارد")
            return
        
        output_path = filedialog.asksaveasfilename(
            title="ذخیره نتایج",
            defaultextension=".xlsx",
            filetypes=[("فایل اکسل", "*.xlsx"), ("همه فایل‌ها", "*.*")],
            initialfile="نتایج_تحلیل.xlsx"
        )
        
        if not output_path:
            return
        
        try:
            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                sheets = {
                    'تحلیل تماس': self.summary_data,
                    'سریال گوشی': getattr(self, 'phone_serials_data', None),
                    'تحلیل دکل‌ها': getattr(self, 'tower_data', None),
                    'تماس‌های شبانه': getattr(self, 'night_sleep_data', None)
                }
                
                for sheet_name, data in sheets.items():
                    if data is not None:
                        data.to_excel(writer, sheet_name=sheet_name, index=False)
                        worksheet = writer.sheets[sheet_name]
                        self.auto_fit_columns(worksheet, data)
                        
                        if sheet_name == 'تحلیل تماس' and 'درصد تماس' in data.columns:
                            self.add_conditional_formatting(worksheet, data)
            
            messagebox.showinfo("موفقیت", "نتایج با موفقیت ذخیره شدند", parent=self.root)
            self.status_var.set(f"نتایج در {output_path} ذخیره شد")
        except Exception as e:
            messagebox.showerror("خطا", f"خطا در ذخیره فایل:\n{str(e)}", parent=self.root)
            self.status_var.set("خطا در ذخیره نتایج")
    
    def auto_fit_columns(self, worksheet, dataframe):
        for column in worksheet.columns:
            max_length = 0
            column_letter = column[0].column_letter
            
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            
            adjusted_width = (max_length + 2) * 1.2
            worksheet.column_dimensions[column_letter].width = adjusted_width
    
    def add_conditional_formatting(self, worksheet, data):
        if 'درصد تماس' in data.columns:
            col_letter = openpyxl.utils.get_column_letter(data.columns.get_loc('درصد تماس') + 1)
            color_scale_rule = ColorScaleRule(
                start_type='percentile', start_value=0, start_color='FF0000',
                mid_type='percentile', mid_value=50, mid_color='FFFF00',
                end_type='percentile', end_value=100, end_color='00FF00'
            )
            worksheet.conditional_formatting.add(f"{col_letter}2:{col_letter}{len(data)+1}", color_scale_rule)
    
    def generate_report(self):
        if not any([self.summary_data is not None, 
                   hasattr(self, 'phone_serials_data'), 
                   hasattr(self, 'tower_data'),
                   hasattr(self, 'night_sleep_data')]):
            messagebox.showwarning("هشدار", "هیچ داده‌ای برای ایجاد گزارش وجود ندارد")
            return
        
        try:
            report_path = filedialog.asksaveasfilename(
                title="ذخیره گزارش",
                defaultextension=".pdf",
                filetypes=[("فایل PDF", "*.pdf")],
                initialfile="گزارش_تحلیل.pdf"
            )
            
            if not report_path:
                return
            
            with PdfPages(report_path) as pdf:
                plt.rcParams['font.family'] = 'Tahoma'
                plt.rcParams['axes.unicode_minus'] = False
                #plt.rcParams['text.direction'] = 'rtl'
                

                # گزارش تحلیل تماس‌ها
                if self.summary_data is not None:
                    fig, ax = plt.subplots(figsize=(10, 6))
                    self.summary_data.head(10).plot.bar(x='شماره مبدا', y='تعداد تماس', ax=ax)
                    ax.set_title(self.format_text('tekrar'))
                    ax.set_xlabel(self.format_text('شماره مبدا'))
                    ax.set_ylabel(self.format_text('تعداد تماس'))
                    plt.xticks(rotation=45, ha='right')
                    plt.tight_layout()
                    pdf.savefig(fig)
                    plt.close()
                
                # گزارش تماس‌های شبانه
                if hasattr(self, 'night_sleep_data') and self.night_sleep_data is not None:
                    fig, ax = plt.subplots(figsize=(10, 6))
                    self.night_sleep_data.head(10).plot.bar(x='lac', y='تعداد_تماس', ax=ax)
                    ax.set_title('10 دکل با بیشترین تماس شبانه')
                    plt.xticks(rotation=45, ha='left')  # تغییر ha به 'left'
                    plt.tight_layout()
                    pdf.savefig(fig)
                    plt.close()
                # گزارش دکل‌های مخابراتی
                # گزارش دکل‌های مخابراتی
                if hasattr(self, 'tower_data') and self.tower_data is not None:
                    fig, ax = plt.subplots(figsize=(10, 6))
                    self.tower_data.head(10).plot.bar(x='lac', y='تعداد_تماس', ax=ax)
                    ax.set_title('10 دکل پرترافیک')
                    plt.xticks(rotation=45, ha='left')  # تغییر ha به 'left'
                    plt.tight_layout()
                    pdf.savefig(fig)
                    plt.close()
                
            messagebox.showinfo("موفقیت", "گزارش با موفقیت ایجاد شد", parent=self.root)
            self.status_var.set(f"گزارش در {report_path} ذخیره شد")
        except ImportError:
            messagebox.showerror("خطا", "برای ایجاد گزارش نیاز به نصب matplotlib است", parent=self.root)
        except Exception as e:
            messagebox.showerror("خطا", f"خطا در ایجاد گزارش:\n{str(e)}", parent=self.root)
            self.status_var.set("خطا در ایجاد گزارش")
    
    
    def format_text(text):
        return get_display(reshape(text))

    def exit_app(self):
        self.root.quit()

if __name__ == "__main__":
    root = tk.Tk()
    try:
        root.iconbitmap("phone_analysis.ico")
    except:
        pass
    app = CallAnalyzerApp(root)
    root.mainloop()