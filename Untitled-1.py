import tkinter as tk
from tkinter import ttk

root = tk.Tk()
root.title("تنظیم اندازه LabelFrame")

# ایجاد فریم با اندازه ثابت
frame = ttk.LabelFrame(root, text="تنظیمات کاربر", padding=10)
frame.pack(padx=10, pady=10)
frame.config(width=600, height=400)  # تنظیم اندازه بعد از ایجاد فریم

# اضافه کردن محتوا به فریم (بدون تأثیر روی اندازه فریم)
label = ttk.Label(frame, text="نام کاربری:")
label.pack(pady=20)

entry = ttk.Entry(frame)
entry.pack()

root.mainloop()