import os
from dotenv import load_dotenv

class APIConfig:
    def __init__(self):
        # بارگذاری کلیدها از فایل .env
        load_dotenv()
        
        # کلیدهای پیش‌فرض (برای توسعه)
        self.DEFAULT_KEYS = {
            'bsc': 'your_bsc_key_here',
            'eth': 'your_eth_key_here',
            'polygon': 'your_polygon_key_here',
            'ARKHAM': 'your_arkham_key_here',
            'COVALENT': 'your_covalent_key_here',
            'coingecko':"your_coingecko_key_here",
            'solana': "your_solana_key_here",
            'blockcypher': 'your_blockcypher_key_here',
            'kavenegar': 'your_kavenegar_key_here'

        }
        
        # دریافت کلیدها از محیط یا استفاده از پیش‌فرض
        self.bsc = os.getenv('bsc_API_KEY') or self.DEFAULT_KEYS['bsc']
        self.eth = os.getenv('eth_API_KEY') or self.DEFAULT_KEYS['eth']
        self.polygon = os.getenv('polygon_API_KEY') or self.DEFAULT_KEYS['polygon']
        self.ARKHAM = os.getenv('ARKHAM_API_KEY') or self.DEFAULT_KEYS['ARKHAM']
        self.COVALENT = os.getenv('COVALENT_API_KEY') or self.DEFAULT_KEYS['COVALENT']
        self.coingecko = os.getenv('coingecko_API_KEY') or self.DEFAULT_KEYS['coingecko']
        self.blockcypher = os.getenv('blockcypher_API_KEY') or self.DEFAULT_KEYS['blockcypher']
        self.solana = os.getenv('solana_API_KEY') or self.DEFAULT_KEYS['solana']
        self.kavenegar = os.getenv('kavenegar_API_KEY') or self.DEFAULT_KEYS['kavenegar']



# ایجاد نمونه全局
api_config = APIConfig()