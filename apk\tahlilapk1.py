import os
import re
import subprocess
import zipfile
import customtkinter as ctk
from tkinter import filedialog, messagebox, scrolledtext

class APKAnalyzerApp:
    def __init__(self, master):
        self.master = master
        master.title("APK Analyzer - تحلیلگر فایل‌های APK")
        master.geometry("800x600")
        
        # تنظیم تم برنامه
        ctk.set_appearance_mode("dark")
        ctk.set_default_color_theme("blue")
        
        # ایجاد ویجت‌ها
        self.create_widgets()
    
    def create_widgets(self):
        # فریم اصلی
        self.main_frame = ctk.CTkFrame(self.master)
        self.main_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # عنوان برنامه
        self.title_label = ctk.CTkLabel(
            self.main_frame, 
            text="برنامه تحلیل فایل APK",
            font=("Arial", 16, "bold")
        )
        self.title_label.pack(pady=10)
        
        # دکمه انتخاب فایل
        self.select_button = ctk.CTkButton(
            self.main_frame,
            text="انتخاب فایل APK",
            command=self.select_apk_file
        )
        self.select_button.pack(pady=10)
        
        # نمایش مسیر فایل انتخاب شده
        self.file_path_label = ctk.CTkLabel(
            self.main_frame,
            text="هیچ فایلی انتخاب نشده است",
            wraplength=700
        )
        self.file_path_label.pack(pady=5)
        
        # دکمه تحلیل فایل
        self.analyze_button = ctk.CTkButton(
            self.main_frame,
            text="تحلیل فایل",
            command=self.analyze_apk,
            state="disabled"
        )
        self.analyze_button.pack(pady=10)
        
        # ناحیه نمایش نتایج
        self.result_label = ctk.CTkLabel(
            self.main_frame,
            text="نتایج تحلیل:",
            font=("Arial", 14)
        )
        self.result_label.pack(pady=5)
        
        self.result_text = scrolledtext.ScrolledText(
            self.main_frame,
            wrap="word",
            width=90,
            height=20,
            font=("Arial", 10)
        )
        self.result_text.pack(pady=10, padx=10, fill="both", expand=True)
        
        # وضعیت برنامه
        self.status_label = ctk.CTkLabel(
            self.main_frame,
            text="آماده",
            font=("Arial", 10)
        )
        self.status_label.pack(pady=5)
    
    def select_apk_file(self):
        file_path = filedialog.askopenfilename(
            title="فایل APK را انتخاب کنید",
            filetypes=[("APK Files", "*.apk")]
        )
        
        if file_path:
            self.file_path = file_path
            self.file_path_label.configure(text=file_path)
            self.analyze_button.configure(state="normal")
            self.status_label.configure(text="فایل انتخاب شد. برای تحلیل کلیک کنید.")
    
    def analyze_apk(self):
        if not hasattr(self, 'file_path'):
            messagebox.showerror("خطا", "لطفاً ابتدا یک فایل APK انتخاب کنید.")
            return
        
        self.status_label.configure(text="در حال تحلیل فایل...")
        self.master.update()
        
        try:
            # استخراج محتوای APK
            extracted_urls = self.extract_urls_from_apk(self.file_path)
            
            # نمایش نتایج
            self.result_text.delete(1.0, "end")
            
            if extracted_urls:
                self.result_text.insert("end", "لینک‌های یافت شده در فایل APK:\n\n")
                for url in extracted_urls:
                    self.result_text.insert("end", f"- {url}\n")
                
                self.result_text.insert("end", "\n⚠️ هشدار: این فایل ممکن است حاوی کد مخرب باشد!")
                messagebox.showwarning(
                    "هشدار امنیتی", 
                    "لینک‌های مشکوک در فایل APK یافت شد. نصب این فایل ممکن است خطرناک باشد!"
                )
            else:
                self.result_text.insert("end", "هیچ لینک مشکوکی در فایل یافت نشد.\n\n")
                self.result_text.insert("end", "توجه: عدم وجود لینک‌های مشکوک به معنی ایمن بودن فایل نیست.")
            
            self.status_label.configure(text="تحلیل کامل شد.")
        
        except Exception as e:
            messagebox.showerror("خطا", f"خطا در تحلیل فایل: {str(e)}")
            self.status_label.configure(text="خطا در تحلیل فایل")
    
    def extract_urls_from_apk(self, apk_path):
        # ایجاد یک دایرکتوری موقت برای استخراج فایل APK
        temp_dir = "temp_apk_extract"
        os.makedirs(temp_dir, exist_ok=True)
        
        # استخراج فایل APK
        with zipfile.ZipFile(apk_path, 'r') as zip_ref:
            zip_ref.extractall(temp_dir)
        
        # جستجوی لینک‌ها در فایل‌های استخراج شده
        urls = set()
        url_pattern = re.compile(
            r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+'
        )
        
        for root, _, files in os.walk(temp_dir):
            for file in files:
                file_path = os.path.join(root, file)
                try:
                    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                        content = f.read()
                        found_urls = url_pattern.findall(content)
                        urls.update(found_urls)
                except:
                    continue
        
        # حذف دایرکتوری موقت
        # (در محیط واقعی بهتر است از shutil.rmtree استفاده شود)
        
        return sorted(urls)

if __name__ == "__main__":
    root = ctk.CTk()
    app = APKAnalyzerApp(root)
    root.mainloop()