import json
from datetime import datetime
import pandas as pd
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from tkinter.scrolledtext import ScrolledText
import webbrowser
import time
import random
from collections import Counter
import requests



class CryptoWalletTrackerApp:
    def __init__(self, root):
        self.root = root
        self.root.title("ردیاب تراکنش‌های کیف پول ارز دیجیتال")
        self.root.geometry("1200x900")
        self.root.resizable(True, True)
        
        self.set_dark_theme()
        self.create_widgets()
        self.create_menu()
    
    def set_dark_theme(self):
        self.root.configure(bg='#2e2e2e')
        self.style = ttk.Style()
        self.style.theme_use('clam')
        
        self.style.configure('TFrame', background='#2e2e2e')
        self.style.configure('TLabel', background='#2e2e2e', foreground='white')
        self.style.configure('TButton', background='#3e3e3e', foreground='white')
        self.style.configure('TEntry', fieldbackground='#3e3e3e', foreground='white')
        self.style.configure('TCombobox', fieldbackground='#3e3e3e', foreground='white')
        self.style.map('TButton', background=[('active', '#4e4e4e')])
    
    def create_widgets(self):
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        title_label = ttk.Label(main_frame, 
                              text="ردیاب تراکنش‌های کیف پول ارز دیجیتال",
                              font=('Tahoma', 14, 'bold'))
        title_label.pack(pady=10)
        
        input_frame = ttk.Frame(main_frame)
        input_frame.pack(fill=tk.X, pady=10)
        
        ttk.Label(input_frame, text="آدرس کیف پول:").pack(side=tk.LEFT, padx=5)
        
        self.wallet_entry = ttk.Entry(input_frame, width=60)
        self.wallet_entry.pack(side=tk.LEFT, expand=True, fill=tk.X, padx=5)
        
        self.search_btn = ttk.Button(input_frame, text="جستجو", command=self.track_wallet)
        self.search_btn.pack(side=tk.LEFT, padx=5)
        
        source_frame = ttk.LabelFrame(main_frame, text="منابع اطلاعاتی", padding=10)
        source_frame.pack(fill=tk.X, pady=10)
        
        self.arkham_var = tk.BooleanVar(value=True)
        self.tronscan_var = tk.BooleanVar(value=True)
        self.etherscan_var = tk.BooleanVar(value=True)
        
        ttk.Checkbutton(source_frame, text="Arkham Intelligence", variable=self.arkham_var).pack(anchor=tk.W)
        ttk.Checkbutton(source_frame, text="Tronscan", variable=self.tronscan_var).pack(anchor=tk.W)
        ttk.Checkbutton(source_frame, text="Etherscan (ETH)", variable=self.etherscan_var).pack(anchor=tk.W)
        
        result_frame = ttk.LabelFrame(main_frame, text="نتایج تراکنش‌ها", padding=10)
        result_frame.pack(fill=tk.BOTH, expand=True)
        


        # تنظیم ارتفاع سطرها برای نمایش چندخطی
        style = ttk.Style()
        style.configure('Treeview', rowheight=30)  # ارتفاع پیش‌فرض سطرها
    
        #self.tree = ttk.Treeview(result_frame, columns=('Source', 'From', 'To', 'Token', 'Amount', 'Date'), show='headings')
        self.tree = ttk.Treeview(result_frame, columns=('Source', 'From', 'To', 'Token', 'Amounts', 'Dates', 'Count'), show='headings')
        #self.tree.heading('Count', text='تعداد تکرار')
        #self.tree.column('Count', width=80)
           # تنظیمات ستون‌ها
        self.tree.heading('Source', text='منبع')
        self.tree.heading('From', text='مبدا')
        self.tree.heading('To', text='مقصد')
        self.tree.heading('Token', text='توکن')
        self.tree.heading('Amounts', text='مقادیر')
        self.tree.heading('Dates', text='تاریخ‌ها')
        self.tree.heading('Count', text='تعداد تکرار')
        
    # تنظیم عرض ستون‌ها
        self.tree.column('Source', width=50)
        self.tree.column('From', width=250)
        self.tree.column('To', width=250)
        self.tree.column('Token', width=80)
        self.tree.column('Amounts', width=120)
        self.tree.column('Dates', width=150)
        self.tree.column('Count', width=80)
        # تنظیم تراز متن برای ستون‌های چندخطی
        self.tree.tag_configure('multiline', font=('Tahoma', 9))
        """
        columns = {
            'Source': ('منبع', 100),
            'From': ('مبدا', 220),
            'To': ('مقصد', 220),
            'Token': ('توکن', 80),
            'Amount': ('مقدار', 100),
            'Date': ('تاریخ', 120)
        }
        """
        #for col, (text, width) in columns.items():
        #    self.tree.heading(col, text=text)
        #    self.tree.column(col, width=width)
        
        scrollbar = ttk.Scrollbar(result_frame, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscroll=scrollbar.set)
        
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        self.status_var = tk.StringVar()
        self.status_var.set("آماده")
        status_label = ttk.Label(main_frame, textvariable=self.status_var)
        status_label.pack(side=tk.BOTTOM, fill=tk.X)

        # اضافه کردن دکمه نمایش آمار
        stats_frame = ttk.Frame(main_frame)
        stats_frame.pack(fill=tk.X, pady=5)
    
        self.stats_btn = ttk.Button(stats_frame, text="نمایش آمار", command=self.show_stats_from_tree)
        self.stats_btn.pack(side=tk.LEFT, padx=5)

        self.current_tooltip = None

        def show_tooltip(event):
        # بستن tooltip قبلی اگر وجود دارد
            if self.current_tooltip:
                self.current_tooltip.destroy()
                self.current_tooltip = None

            item = self.tree.identify_row(event.y)
            col = self.tree.identify_column(event.x)
        
            if item and col:
                value = self.tree.item(item, 'values')[int(col[1:])-1]
            
                if "\n" in value:
                # ایجاد tooltip جدید
                    self.current_tooltip = tk.Toplevel(self.root)
                    self.current_tooltip.wm_overrideredirect(True)
                    self.current_tooltip.wm_geometry(f"+{event.x_root+20}+{event.y_root+10}")
                
                    label = ttk.Label(
                        self.current_tooltip, 
                        text=value, 
                        background="#2F4F4F", 
                        foreground="white",
                        relief="solid", 
                        padding=5,
                        font=('Tahoma', 9)
                    )
                    label.pack()
                
                # بستن tooltip وقتی ماوس از روی Treeview خارج شد
                    self.tree.bind("<Leave>", lambda e: self.close_tooltip())

        def close_tooltip(self):
            if self.current_tooltip:
                self.current_tooltip.destroy()
                self.current_tooltip = None

        self.tree.bind("<Motion>", show_tooltip)
        self.tree.bind("<Leave>", lambda e: self.close_tooltip())

    def create_menu(self):
        menubar = tk.Menu(self.root)
        
        file_menu = tk.Menu(menubar, tearoff=0)
        file_menu.add_command(label="ذخیره نتایج", command=self.save_results)
        file_menu.add_separator()
        file_menu.add_command(label="خروج", command=self.root.quit)
        menubar.add_cascade(label="فایل", menu=file_menu)
        
        help_menu = tk.Menu(menubar, tearoff=0)
        help_menu.add_command(label="راهنما", command=self.show_help)
        help_menu.add_command(label="درباره", command=self.show_about)
        menubar.add_cascade(label="کمک", menu=help_menu)
        
        self.root.config(menu=menubar)
    
    def get_arkham_data(self, wallet_address):
        """Get transaction data from Arkham Intelligence"""
        self.status_var.set("در حال دریافت داده از Arkham...")
        self.root.update()
        
        
        all_transactions = []
        limit = 50
        offset = 0
    
        while True:
        # توقف تصادفی بین درخواست‌ها
            time.sleep(random.uniform(0.3, 0.7))
        
            url = f"https://api.arkhamintelligence.com/address/{wallet_address}/transactions?limit={limit}&offset={offset}"
            try:
                response = requests.get(url, headers={'User-Agent': 'Mozilla/5.0'})
                if response.status_code == 200:
                    data = response.json()
                    transactions = data.get('transactions', [])
                    if not transactions:
                        break
                
                    all_transactions.extend(transactions)
                    offset += limit
                
                    self.status_var.set(f"در حال دریافت داده از Arkham... ({len(all_transactions)} تراکنش)")
                    self.root.update()
                else:
                    break
            except Exception as e:
                messagebox.showerror("خطا", f"خطا در دریافت داده از Arkham: {str(e)}")
                return None
    
        return {'transactions': all_transactions} if all_transactions else None

    def get_etherscan_data(self, wallet_address):
    
        self.status_var.set("در حال دریافت داده از Etherscan...")
        self.root.update()
    
        all_transactions = []
        page = 1
        offset = 10000  # حداکثر تراکنش در هر صفحه
    
        while True:
        # توقف تصادفی بین درخواست‌ها
            time.sleep(random.uniform(0.3, 0.7))
        
            api_key = "G86T8PZ5I5ZNWMSRN64DJYI6145XDS3PXE"
            url = f"https://api.etherscan.io/api?module=account&action=txlist&address={wallet_address}&startblock=0&endblock=********&page={page}&offset={offset}&sort=asc&apikey={api_key}"
            try:
                response = requests.get(url, headers={'User-Agent': 'Mozilla/5.0'})
                if response.status_code == 200:
                    data = response.json()
                    transactions = data.get('result', [])
                    if not transactions:
                        break
                
                    all_transactions.extend(transactions)
                    page += 1
                
                    self.status_var.set(f"در حال دریافت داده از Etherscan... ({len(all_transactions)} تراکنش)")
                    self.root.update()
                else:
                    break
            except Exception as e:
                messagebox.showerror("خطا", f"خطا در دریافت داده از Etherscan: {str(e)}")
                return None
    
        return {'result': all_transactions} if all_transactions else None
    
    def get_tronscan_data(self, wallet_address):
        """Get ALL transaction data from Tronscan with pagination"""
        self.status_var.set("در حال دریافت داده از Tronscan...")
        self.root.update()
    
        all_transactions = []
        limit = 50
        start = 0
    
        while True:
            time.sleep(0.5)
            url = f"https://apilist.tronscan.org/api/transaction?address={wallet_address}&limit={limit}&start={start}&sort=-timestamp"
            try:
                response = requests.get(url, headers={'User-Agent': 'Mozilla/5.0'})
                if response.status_code == 200:
                    data = response.json()
                    transactions = data.get('data', [])
                    if not transactions:
                        break
                
                    all_transactions.extend(transactions)
                    start += limit
                
                # نمایش پیشرفت در وضعیت برنامه
                    self.status_var.set(f"در حال دریافت داده از Tronscan... ({len(all_transactions)} تراکنش)")
                    self.root.update()
                else:
                    break
            except Exception as e:
                messagebox.showerror("خطا", f"خطا در دریافت داده از Tronscan: {str(e)}")
                return None
    
        return {'data': all_transactions} if all_transactions else None
    
    def get_etherscan_data(self, wallet_address):
        """Get transaction data from Etherscan"""
        self.status_var.set("در حال دریافت داده از Etherscan...")
        self.root.update()
        
        api_key = "G86T8PZ5I5ZNWMSRN64DJYI6145XDS3PXE"
        url = f"https://api.etherscan.io/api?module=account&action=txlist&address={wallet_address}&startblock=0&endblock=********&sort=asc&apikey={api_key}"
        try:
            response = requests.get(url, headers={'User-Agent': 'Mozilla/5.0'})
            return response.json() if response.status_code == 200 else None
        except Exception as e:
            messagebox.showerror("خطا", f"خطا در دریافت داده از Etherscan: {str(e)}")
            return None
    
    def parse_transactions(self, data, source):
        """Parse transaction data from different sources"""
        transactions = []
        
        if not data:
            return transactions
            
        if source == "arkham":
            for tx in data.get('transactions', []):
                transactions.append({
                    'source': 'Arkham',
                    'from': tx.get('from'),
                    'to': tx.get('to'),
                    'token': tx.get('token'),
                    'amount': tx.get('amount'),
                    'date': datetime.fromtimestamp(tx.get('timestamp')).strftime('%Y-%m-%d %H:%M:%S')
                })
        
        elif source == "tronscan":
            for tx in data.get('data', []):
                transactions.append({
                    'source': 'Tronscan',
                    'from': tx.get('ownerAddress'),
                    'to': tx.get('toAddress'),
                    'token': tx.get('tokenType', 'TRX'),
                    'amount': self.safe_divide(tx.get('amount'), 10**6),
                    'date': datetime.fromtimestamp(tx.get('timestamp')/1000).strftime('%Y-%m-%d %H:%M:%S')
                })
        
        elif source == "etherscan":
            if isinstance(data, str):
                try:
                    data = json.loads(data)
                except json.JSONDecodeError:
                    data = {}
            
            for tx in data.get('result', []):
                transactions.append({
                    'source': 'Etherscan',
                    'from': tx.get('from'),
                    'to': tx.get('to'),
                    'token': 'ETH',
                    'amount': self.safe_divide(tx.get('value'), 10**18),
                    'date': datetime.fromtimestamp(int(tx.get('timeStamp'))).strftime('%Y-%m-%d %H:%M:%S')
                })
        
        return transactions
    
    @staticmethod
    def safe_divide(value, divisor, default=0):
        """Safely divide two numbers with error handling"""
        try:
            return float(value) / divisor
        except (ValueError, TypeError, ZeroDivisionError):
            return default
     
    def track_wallet(self):
        wallet_address = self.wallet_entry.get().strip()
        if not wallet_address:
            messagebox.showwarning("هشدار", "لطفاً آدرس کیف پول را وارد کنید")
            return
    
        self.search_btn.config(state=tk.DISABLED)
        self.status_var.set("در حال جستجو...")
        self.root.update()
    
    # پاک کردن نتایج قبلی
        for item in self.tree.get_children():
            self.tree.delete(item)
    
        all_transactions = []
    
        if self.arkham_var.get():
            arkham_data = self.get_arkham_data(wallet_address)
            all_transactions.extend(self.parse_transactions(arkham_data, "arkham"))
    
        if self.tronscan_var.get():
            tronscan_data = self.get_tronscan_data(wallet_address)
            all_transactions.extend(self.parse_transactions(tronscan_data, "tronscan"))
    
        if self.etherscan_var.get():
            etherscan_data = self.get_etherscan_data(wallet_address)
            all_transactions.extend(self.parse_transactions(etherscan_data, "etherscan"))
    
        if not all_transactions:
            messagebox.showinfo("اطلاع", "هیچ تراکنشی یافت نشد")
            self.status_var.set("آماده - هیچ تراکنشی یافت نشد")
        
        if all_transactions:
            transaction_groups = {}
            #from_to_info = {}
        # محاسبه تعداد تکرارها
            #from_to_counts = Counter((tx['from'], tx['to']) for tx in all_transactions)
            # ایجاد لیست منحصر به فرد از تراکنش‌ها
            #unique_transactions = []
            #seen = set()
            # اضافه کردن تعداد تکرار به هر تراکنش
            for tx in all_transactions:
                key = (tx['from'], tx['to'], tx['token'])
                if key not in transaction_groups:
                    transaction_groups[key] = {
                        'source': tx['source'],  # منبع را جداگانه ذخیره کنید
                        'amounts': [],
                        'dates': [],
                        'count': 0
                    }
                transaction_groups[key]['amounts'].append(str(tx['amount']))
                transaction_groups[key]['dates'].append(tx['date'])
                transaction_groups[key]['count'] += 1
               

            # پاک کردن Treeview قبل از اضافه کردن رکوردهای جدید
            for item in self.tree.get_children():
                self.tree.delete(item)

           # نمایش در Treeview
            for key, group in transaction_groups.items():
                from_addr, to_addr, token = key  # حالا فقط 3 مقدار داریم

                amounts_str = "\n".join(group['amounts'])
                dates_str = "\n".join(group['dates'])
            
                self.tree.insert('', tk.END, values=(
                    group['source'],  # منبع از داخل group خوانده می‌شود
                    from_addr,
                    to_addr,
                    token,
                    amounts_str,  # نمایش تمام مقادیر
                    dates_str,    # نمایش تمام تاریخ‌ها
                    group['count']  # تعداد تکرار
                ))
        
        else:
        # نمایش تراکنش‌ها
            for tx in all_transactions:
                self.tree.insert('', tk.END, values=(
                    tx['source'],
                    tx['from'],
                    tx['to'],
                    tx['token'],
                    f"{tx['amount']:.8f}",
                    tx['date']
                ))
        
        # محاسبه و نمایش آمار
            self.show_statistics(all_transactions)
            self.status_var.set(f"آماده - {len(all_transactions)} تراکنش یافت شد")
    
        self.search_btn.config(state=tk.NORMAL)

    def show_statistics(self, transactions):
        if not transactions:
            return
    
    # ایجاد پنجره آمار
        stats_window = tk.Toplevel(self.root)
        stats_window.title("آمار تراکنش‌ها")
        stats_window.geometry("600x400")
    
    # محاسبه آمار
        token_counter = Counter(tx['token'] for tx in transactions)
        source_counter = Counter(tx['source'] for tx in transactions)
        top_tokens = token_counter.most_common(5)
        top_sources = source_counter.most_common()
    
    # نمایش آمار
        stats_text = ScrolledText(stats_window, wrap=tk.WORD)
        stats_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
    
        stats_text.insert(tk.END, "═"*50 + "\n")
        stats_text.insert(tk.END, " آمار کلی تراکنش‌ها \n")
        stats_text.insert(tk.END, "═"*50 + "\n\n")
    
        stats_text.insert(tk.END, f"▪ تعداد کل تراکنش‌ها: {len(transactions)}\n\n")
    
        stats_text.insert(tk.END, "═"*50 + "\n")
        stats_text.insert(tk.END, " پرتکرارترین توکن‌ها \n")
        stats_text.insert(tk.END, "═"*50 + "\n")
        for token, count in top_tokens:
            stats_text.insert(tk.END, f"▪ {token}: {count} تراکنش ({count/len(transactions):.1%})\n")
    
        stats_text.insert(tk.END, "\n" + "═"*50 + "\n")
        stats_text.insert(tk.END, " توزیع تراکنش‌ها بر اساس منبع \n")
        stats_text.insert(tk.END, "═"*50 + "\n")
        for source, count in top_sources:
            stats_text.insert(tk.END, f"▪ {source}: {count} تراکنش ({count/len(transactions):.1%})\n")
    
        stats_text.config(state=tk.DISABLED)

    def show_stats_from_tree(self):
        transactions = []
        for item in self.tree.get_children():
            values = self.tree.item(item, 'values')
            transactions.append({
                'source': values[0],
                'token': values[3],
            # سایر فیلدها در صورت نیاز
            })
    
        self.show_statistics(transactions)

    def save_results(self):
        if not self.tree.get_children():
            messagebox.showwarning("هشدار", "هیچ داده‌ای برای ذخیره وجود ندارد")
            return
        
        file_path = filedialog.asksaveasfilename(
            defaultextension=".csv",
            filetypes=[("Excel Files", "*.xlsx"), ("CSV Files", "*.csv"), ("All Files", "*.*")],
            title="ذخیره نتایج به عنوان"
        )
        
        if not file_path:
            return
        
        try:
            data = []
            # جمع‌آوری داده‌های منحصر به فرد از Treeview
            #unique_data = []
            #seen = set()
            for item in self.tree.get_children():
                values = self.tree.item(item, 'values')
                data.append({
                    'منبع': values[0],
                    'مبدا': values[1],
                    'مقصد': values[2],
                    'توکن': values[3],
                    'مقادیر': values[4].replace("\n", " | "),
                    'تاریخ‌ها': values[5].replace("\n", " | "),
                    'تعداد': values[6]
                })
            
            df = pd.DataFrame(data)
            
            # محاسبه تعداد تکرار هر ترکیب مبدا/مقصد
            #df['تعداد تکرار'] = df.groupby(['مبدا', 'مقصد'])['مبدا'].transform('count')
            # مرتب‌سازی بر اساس تعداد تکرار (نزولی)
            #df = df.sort_values(by='تعداد تکرار', ascending=False)
            if file_path.endswith('.xlsx'):
                df.to_excel(file_path, index=False, engine='openpyxl')
            else:  # برای فایل‌های CSV
                df.to_csv(file_path, index=False, encoding='utf-8-sig')
            #df.to_csv(file_path, index=False, encoding='utf-8-sig')
            messagebox.showinfo("موفق", "نتایج با موفقیت ذخیره شد")
        except Exception as e:
            messagebox.showerror("خطا", f"خطا در ذخیره فایل: {str(e)}")
    
    def show_help(self):
        help_text = """...متن راهنما"""
        messagebox.showinfo("راهنما", help_text)
    
    def show_about(self):
        about_text = """...متن درباره برنامه"""
        messagebox.showinfo("درباره برنامه", about_text)

if __name__ == "__main__":
    root = tk.Tk()
    app = CryptoWalletTrackerApp(root)
    root.mainloop()