# Generated by Django 5.2.1 on 2025-05-11 12:07

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0001_initial'),
        ('auth', '0012_alter_user_first_name_max_length'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='customuser',
            options={'ordering': ['username'], 'verbose_name': 'کاربر', 'verbose_name_plural': 'کاربران'},
        ),
        migrations.AddField(
            model_name='customuser',
            name='address',
            field=models.TextField(blank=True, verbose_name='آدرس'),
        ),
        migrations.AddField(
            model_name='customuser',
            name='phone',
            field=models.CharField(blank=True, max_length=15, verbose_name='شماره تماس'),
        ),
        migrations.AlterField(
            model_name='customuser',
            name='role',
            field=models.CharField(choices=[('admin', 'مدیر'), ('doctor', 'پزشک'), ('nurse', 'پرستار'), ('pharmacist', 'داروساز'), ('receptionist', 'منشی')], default='receptionist', max_length=20, verbose_name='نقش'),
        ),
        migrations.CreateModel(
            name='Role',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50, unique=True, verbose_name='نام نقش')),
                ('description', models.TextField(blank=True, verbose_name='توضیحات')),
                ('permissions', models.ManyToManyField(blank=True, to='auth.permission', verbose_name='دسترسی\u200cها')),
            ],
            options={
                'verbose_name': 'نقش',
                'verbose_name_plural': 'نقش\u200cها',
                'ordering': ['name'],
            },
        ),
        migrations.AddField(
            model_name='customuser',
            name='roles',
            field=models.ManyToManyField(blank=True, related_name='users', to='accounts.role', verbose_name='نقش\u200cهای اضافی'),
        ),
    ]
