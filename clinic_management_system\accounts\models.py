from django.db import models
from django.contrib.auth.models import AbstractUser
from django.utils.translation import gettext_lazy as _

# Create your models here.
class Role(models.Model):
    name = models.CharField(max_length=50, unique=True, verbose_name=_('نام نقش'))
    description = models.TextField(blank=True, verbose_name=_('توضیحات'))
    permissions = models.ManyToManyField(
        'auth.Permission',
        blank=True,
        verbose_name=_('دسترسی‌ها')
    )

    def __str__(self):
        return self.name

    class Meta:
        verbose_name = _('نقش')
        verbose_name_plural = _('نقش‌ها')
        ordering = ['name']

class CustomUser(AbstractUser):
    ROLE_CHOICES = [
        ('admin', _('مدیر')),
        ('doctor', _('پزشک')),
        ('nurse', _('پرستار')),
        ('pharmacist', _('داروساز')),
        ('receptionist', _('منشی')),
    ]
    
    role = models.CharField(
        max_length=20,
        choices=ROLE_CHOICES,
        default='receptionist',
        verbose_name=_('نقش')
    )
    roles = models.ManyToManyField(
        Role,
        blank=True,
        related_name='users',
        verbose_name=_('نقش‌های اضافی')
    )
    phone = models.CharField(max_length=15, blank=True, verbose_name=_('شماره تماس'))
    address = models.TextField(blank=True, verbose_name=_('آدرس'))
    # You can add other common fields like mobile_number, national_id if needed for all users
    # For example:
    # mobile_number = models.CharField(max_length=15, unique=True, null=True, blank=True, verbose_name='شماره موبایل')

    class Meta:
        verbose_name = _('کاربر')
        verbose_name_plural = _('کاربران')
        ordering = ['username']

    def __str__(self):
        return f"{self.username} ({self.get_role_display()})"

    def get_role_display(self):
        return dict(self.ROLE_CHOICES).get(self.role, self.role)

    def has_role(self, role_name):
        return self.role == role_name or self.roles.filter(name=role_name).exists()
