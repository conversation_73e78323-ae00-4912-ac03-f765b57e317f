from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required, user_passes_test
from django.contrib.auth.views import LogoutView
from django.contrib import messages
from django.contrib.auth import update_session_auth_hash, logout
from django.contrib.auth.forms import PasswordChangeForm
from django.views.generic import View
from django.contrib.auth import get_user_model
from .forms import RoleForm, UserRoleForm, UserCreateForm, UserEditForm
from .models import Role

User = get_user_model()

def is_staff_user(user):
    return user.is_staff

# Create your views here.

class CustomLogoutView(View):
    def get(self, request):
        logout(request)
        messages.success(request, 'شما با موفقیت از سیستم خارج شدید.')
        return redirect('login')

@login_required
@user_passes_test(is_staff_user)
def user_list(request):
    users = User.objects.all().order_by('username')
    context = {
        'users': users
    }
    return render(request, 'accounts/user_list.html', context)

@login_required
@user_passes_test(is_staff_user)
def user_create(request):
    if request.method == 'POST':
        form = UserCreateForm(request.POST)
        if form.is_valid():
            user = form.save()
            messages.success(request, 'کاربر با موفقیت ایجاد شد.')
            return redirect('user_list')
    else:
        form = UserCreateForm()
    
    context = {
        'form': form,
        'title': 'ایجاد کاربر جدید'
    }
    return render(request, 'accounts/user_form.html', context)

@login_required
@user_passes_test(is_staff_user)
def user_edit(request, pk):
    user = get_object_or_404(User, pk=pk)
    if request.method == 'POST':
        form = UserEditForm(request.POST, instance=user)
        if form.is_valid():
            form.save()
            messages.success(request, 'اطلاعات کاربر با موفقیت بروزرسانی شد.')
            return redirect('user_list')
    else:
        form = UserEditForm(instance=user)
    
    context = {
        'form': form,
        'user': user,
        'title': 'ویرایش کاربر'
    }
    return render(request, 'accounts/user_form.html', context)

@login_required
@user_passes_test(is_staff_user)
def user_delete(request, pk):
    user = get_object_or_404(User, pk=pk)
    if request.method == 'POST':
        user.delete()
        messages.success(request, 'کاربر با موفقیت حذف شد.')
        return redirect('user_list')
    
    context = {
        'user': user
    }
    return render(request, 'accounts/user_confirm_delete.html', context)

@login_required
@user_passes_test(is_staff_user)
def role_list(request):
    roles = Role.objects.all().order_by('name')
    context = {
        'roles': roles
    }
    return render(request, 'accounts/role_list.html', context)

@login_required
@user_passes_test(is_staff_user)
def role_create(request):
    if request.method == 'POST':
        form = RoleForm(request.POST)
        if form.is_valid():
            role = form.save()
            messages.success(request, 'نقش با موفقیت ایجاد شد.')
            return redirect('role_list')
    else:
        form = RoleForm()
    
    context = {
        'form': form,
        'title': 'ایجاد نقش جدید'
    }
    return render(request, 'accounts/role_form.html', context)

@login_required
@user_passes_test(is_staff_user)
def role_edit(request, pk):
    role = get_object_or_404(Role, pk=pk)
    if request.method == 'POST':
        form = RoleForm(request.POST, instance=role)
        if form.is_valid():
            form.save()
            messages.success(request, 'نقش با موفقیت بروزرسانی شد.')
            return redirect('role_list')
    else:
        form = RoleForm(instance=role)
    
    context = {
        'form': form,
        'role': role,
        'title': 'ویرایش نقش'
    }
    return render(request, 'accounts/role_form.html', context)

@login_required
@user_passes_test(is_staff_user)
def role_delete(request, pk):
    role = get_object_or_404(Role, pk=pk)
    if request.method == 'POST':
        role.delete()
        messages.success(request, 'نقش با موفقیت حذف شد.')
        return redirect('role_list')
    
    context = {
        'role': role
    }
    return render(request, 'accounts/role_confirm_delete.html', context)

@login_required
def profile(request):
    if request.method == 'POST':
        form = PasswordChangeForm(request.user, request.POST)
        if form.is_valid():
            user = form.save()
            update_session_auth_hash(request, user)
            messages.success(request, 'رمز عبور شما با موفقیت تغییر کرد.')
            return redirect('profile')
        else:
            messages.error(request, 'لطفا خطاهای زیر را اصلاح کنید.')
    else:
        form = PasswordChangeForm(request.user)
    
    context = {
        'form': form,
        'user': request.user
    }
    return render(request, 'accounts/profile.html', context)
