from django.shortcuts import render
from django.contrib.auth.decorators import login_required
from django.utils import timezone
from django.db.models import Count
from patients.models import Patient
from visits.models import Visit, Prescription
from pharmacy.models import Invoice

@login_required
def dashboard(request):
    # Get today's date
    today = timezone.now().date()
    
    # Get statistics
    total_patients = Patient.objects.count()
    today_visits = Visit.objects.filter(visit_datetime__date=today).count()
    today_prescriptions = Prescription.objects.filter(created_at__date=today).count()
    pending_pharmacy = Invoice.objects.filter(status='pending').count()
    
    # Get today's schedule
    today_schedule = Visit.objects.filter(visit_datetime__date=today).select_related(
        'patient', 'doctor'
    ).order_by('visit_datetime')
    
    # Format schedule data
    schedule_data = []
    for visit in today_schedule:
        status_color = {
            'completed': 'success',
            'in_progress': 'warning',
            'cancelled': 'danger',
            'scheduled': 'info'
        }.get(visit.status, 'secondary')
        
        schedule_data.append({
            'id': visit.id,
            'time': visit.visit_datetime.strftime('%H:%M'),
            'patient_name': f"{visit.patient.first_name} {visit.patient.last_name}",
            'doctor_name': visit.doctor.get_full_name() if visit.doctor else 'نامشخص',
            'status': visit.get_status_display(),
            'status_color': status_color
        })
    
    # Get recent activities
    recent_activities = []
    
    # Add recent visits
    recent_visits = Visit.objects.select_related('patient', 'doctor').order_by('-visit_datetime')[:5]
    for visit in recent_visits:
        recent_activities.append({
            'title': f"ویزیت جدید برای {visit.patient.first_name} {visit.patient.last_name}",
            'description': f"توسط دکتر {visit.doctor.get_full_name() if visit.doctor else 'نامشخص'}",
            'time': visit.visit_datetime.strftime('%H:%M')
        })
    
    # Add recent prescriptions
    recent_prescriptions = Prescription.objects.select_related('visit__patient').order_by('-created_at')[:5]
    for prescription in recent_prescriptions:
        recent_activities.append({
            'title': f"نسخه جدید برای {prescription.visit.patient.first_name} {prescription.visit.patient.last_name}",
            'description': f"تعداد دارو: {prescription.prescribed_items.count()}",
            'time': prescription.created_at.strftime('%H:%M')
        })
    
    # Sort activities by time
    recent_activities.sort(key=lambda x: x['time'], reverse=True)
    recent_activities = recent_activities[:5]  # Keep only 5 most recent activities
    
    context = {
        'total_patients': total_patients,
        'today_visits': today_visits,
        'today_prescriptions': today_prescriptions,
        'pending_pharmacy': pending_pharmacy,
        'today_schedule': schedule_data,
        'recent_activities': recent_activities
    }
    
    return render(request, 'dashboard.html', context) 