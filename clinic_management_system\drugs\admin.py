from django.contrib import admin
from django.utils.html import format_html
from django.db import models
from .models import Drug

class StockStatusFilter(admin.SimpleListFilter):
    title = 'وضعیت موجودی'
    parameter_name = 'stock_status'

    def lookups(self, request, model_admin):
        return (
            ('out', 'ناموجود'),
            ('low', 'کم'),
            ('available', 'موجود'),
        )

    def queryset(self, request, queryset):
        if self.value() == 'out':
            return queryset.filter(stock=0)
        if self.value() == 'low':
            return queryset.filter(stock__gt=0, stock__lte=models.F('min_stock_level'))
        if self.value() == 'available':
            return queryset.filter(stock__gt=models.F('min_stock_level'))

# Register your models here.
@admin.register(Drug)
class DrugAdmin(admin.ModelAdmin):
    list_display = ('name', 'price', 'stock_display', 'stock_status_display', 'created_by', 'created_at')
    search_fields = ('name', 'description')
    list_filter = ('price', StockStatusFilter, 'created_at', 'created_by')
    ordering = ('name',)
    readonly_fields = ('created_by', 'created_at', 'updated_at')

    def stock_display(self, obj):
        return f"{obj.stock} عدد"
    stock_display.short_description = 'موجودی'

    def stock_status_display(self, obj):
        if obj.stock <= 0:
            return format_html('<span style="color: red; font-weight: bold;">ناموجود</span>')
        elif obj.is_low_stock:
            return format_html('<span style="color: orange; font-weight: bold;">کم</span>')
        else:
            return format_html('<span style="color: green; font-weight: bold;">موجود</span>')
    stock_status_display.short_description = 'وضعیت'

    def save_model(self, request, obj, form, change):
        if not change:  # If creating a new object
            obj.created_by = request.user
        super().save_model(request, obj, form, change)
