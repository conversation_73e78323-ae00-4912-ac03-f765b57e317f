from django import forms
from .models import Drug

class DrugForm(forms.ModelForm):
    class Meta:
        model = Drug
        fields = ['name', 'description', 'price', 'stock', 'min_stock_level']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'نام دارو'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'توضیحات دارو'}),
            'price': forms.NumberInput(attrs={'class': 'form-control', 'placeholder': 'قیمت (ریال)'}),
            'stock': forms.NumberInput(attrs={'class': 'form-control', 'placeholder': 'موجودی'}),
            'min_stock_level': forms.NumberInput(attrs={'class': 'form-control', 'placeholder': 'حداقل موجودی'}),
        }
        labels = {
            'name': 'نام دارو',
            'description': 'توضیحات',
            'price': 'قیمت (ریال)',
            'stock': 'موجودی',
            'min_stock_level': 'حداقل موجودی',
        }
        error_messages = {
            'name': {
                'required': 'لطفا نام دارو را وارد کنید.',
                'unique': 'این دارو قبلا ثبت شده است.',
            },
            'price': {
                'required': 'لطفا قیمت دارو را وارد کنید.',
                'min_value': 'قیمت دارو باید بیشتر از صفر باشد.',
            },
            'stock': {
                'required': 'لطفا موجودی دارو را وارد کنید.',
                'min_value': 'موجودی دارو نمی‌تواند منفی باشد.',
            },
        }

class StockUpdateForm(forms.ModelForm):
    add_stock = forms.IntegerField(
        min_value=1,
        required=True,
        widget=forms.NumberInput(attrs={'class': 'form-control', 'placeholder': 'تعداد افزایش موجودی'}),
        label='افزایش موجودی'
    )

    class Meta:
        model = Drug
        fields = []  # No fields from the model, we're using our custom field

    def save(self, commit=True):
        drug = self.instance
        add_amount = self.cleaned_data.get('add_stock', 0)
        drug.stock += add_amount
        if commit:
            drug.save()
        return drug