# Generated by Django 5.1.7 on 2025-05-11 19:09

import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('drugs', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='drug',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, default=django.utils.timezone.now, verbose_name='تاریخ ثبت'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='drug',
            name='created_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_drugs', to=settings.AUTH_USER_MODEL, verbose_name='ثبت کننده'),
        ),
        migrations.AddField(
            model_name='drug',
            name='min_stock_level',
            field=models.PositiveIntegerField(default=10, verbose_name='حداقل موجودی'),
        ),
        migrations.AddField(
            model_name='drug',
            name='stock',
            field=models.PositiveIntegerField(default=0, verbose_name='موجودی'),
        ),
        migrations.AddField(
            model_name='drug',
            name='updated_at',
            field=models.DateTimeField(auto_now=True, verbose_name='تاریخ بروزرسانی'),
        ),
    ]
