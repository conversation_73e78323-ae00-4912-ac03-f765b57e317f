from django.db import models
from django.utils.translation import gettext_lazy as _
from django.conf import settings

# Create your models here.
class Drug(models.Model):
    name = models.CharField(max_length=255, unique=True, verbose_name=_('نام دارو'))
    description = models.TextField(blank=True, null=True, verbose_name=_('توضیحات'))
    # Price is stored in the smallest currency unit (e.g., Rials) to avoid floating point issues
    price = models.DecimalField(max_digits=12, decimal_places=0, verbose_name=_('قیمت (ریال)'))
    stock = models.PositiveIntegerField(default=0, verbose_name=_('موجودی'))
    min_stock_level = models.PositiveIntegerField(default=10, verbose_name=_('حداقل موجودی'))
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        related_name='created_drugs',
        verbose_name=_('ثبت کننده')
    )
    created_at = models.DateTimeField(auto_now_add=True, verbose_name=_('تاریخ ثبت'))
    updated_at = models.DateTimeField(auto_now=True, verbose_name=_('تاریخ بروزرسانی'))

    @property
    def is_low_stock(self):
        return self.stock <= self.min_stock_level

    @property
    def stock_status(self):
        if self.stock <= 0:
            return _('ناموجود')
        elif self.is_low_stock:
            return _('کم')
        else:
            return _('موجود')

    def __str__(self):
        return self.name

    class Meta:
        verbose_name = _('دارو')
        verbose_name_plural = _('داروها')
        ordering = ['name']
