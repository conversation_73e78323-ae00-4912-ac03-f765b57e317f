from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required, user_passes_test
from django.contrib import messages
from django.db.models import Q, Sum, F
from .models import Drug
from .forms import DrugForm, StockUpdateForm
from visits.models import PrescribedDrug

# Helper function to check if user is a pharmacist
def is_pharmacist(user):
    return user.role == 'pharmacist' or user.is_staff

# Create your views here.

@login_required
def drug_list(request):
    search_query = request.GET.get('search', '')
    stock_filter = request.GET.get('stock', '')
    drugs = Drug.objects.all().order_by('name')

    if search_query:
        drugs = drugs.filter(
            Q(name__icontains=search_query) |
            Q(description__icontains=search_query)
        )

    if stock_filter == 'out':
        drugs = drugs.filter(stock=0)
    elif stock_filter == 'low':
        drugs = drugs.filter(stock__gt=0, stock__lte=F('min_stock_level'))
    elif stock_filter == 'available':
        drugs = drugs.filter(stock__gt=F('min_stock_level'))

    context = {
        'drugs': drugs,
        'search_query': search_query,
        'stock_filter': stock_filter
    }
    return render(request, 'drugs/drug_list.html', context)

@login_required
@user_passes_test(is_pharmacist)
def drug_create(request):
    if request.method == 'POST':
        form = DrugForm(request.POST)
        if form.is_valid():
            drug = form.save(commit=False)
            drug.created_by = request.user
            drug.save()
            messages.success(request, 'دارو با موفقیت ثبت شد.')
            return redirect('drug_detail', pk=drug.pk)
    else:
        form = DrugForm()

    context = {
        'form': form,
        'title': 'ثبت داروی جدید'
    }
    return render(request, 'drugs/drug_form.html', context)

@login_required
def drug_detail(request, pk):
    drug = get_object_or_404(Drug, pk=pk)

    # Get usage statistics
    prescribed_count = PrescribedDrug.objects.filter(drug=drug).count()
    total_prescribed = PrescribedDrug.objects.filter(drug=drug).aggregate(total=Sum('quantity'))['total'] or 0

    context = {
        'drug': drug,
        'prescribed_count': prescribed_count,
        'total_prescribed': total_prescribed
    }
    return render(request, 'drugs/drug_detail.html', context)

@login_required
@user_passes_test(is_pharmacist)
def drug_edit(request, pk):
    drug = get_object_or_404(Drug, pk=pk)
    if request.method == 'POST':
        form = DrugForm(request.POST, instance=drug)
        if form.is_valid():
            form.save()
            messages.success(request, 'اطلاعات دارو با موفقیت بروزرسانی شد.')
            return redirect('drug_detail', pk=drug.pk)
    else:
        form = DrugForm(instance=drug)

    context = {
        'form': form,
        'drug': drug,
        'title': 'ویرایش اطلاعات دارو'
    }
    return render(request, 'drugs/drug_form.html', context)

@login_required
@user_passes_test(is_pharmacist)
def drug_delete(request, pk):
    drug = get_object_or_404(Drug, pk=pk)

    # Check if drug is used in any prescription
    if PrescribedDrug.objects.filter(drug=drug).exists():
        messages.error(request, 'این دارو در نسخه‌ها استفاده شده است و قابل حذف نیست.')
        return redirect('drug_detail', pk=drug.pk)

    if request.method == 'POST':
        drug.delete()
        messages.success(request, 'دارو با موفقیت حذف شد.')
        return redirect('drug_list')

    context = {
        'drug': drug
    }
    return render(request, 'drugs/drug_confirm_delete.html', context)

@login_required
@user_passes_test(is_pharmacist)
def update_stock(request, pk):
    drug = get_object_or_404(Drug, pk=pk)

    if request.method == 'POST':
        form = StockUpdateForm(request.POST, instance=drug)
        if form.is_valid():
            form.save()
            messages.success(request, f'موجودی دارو با موفقیت به {drug.stock} عدد افزایش یافت.')
            return redirect('drug_detail', pk=drug.pk)
    else:
        form = StockUpdateForm(instance=drug)

    context = {
        'form': form,
        'drug': drug,
        'title': 'افزایش موجودی دارو'
    }
    return render(request, 'drugs/stock_update_form.html', context)

@login_required
def inventory_report(request):
    low_stock_drugs = Drug.objects.filter(stock__lte=F('min_stock_level')).order_by('stock')
    out_of_stock_drugs = Drug.objects.filter(stock=0)

    context = {
        'low_stock_drugs': low_stock_drugs,
        'out_of_stock_drugs': out_of_stock_drugs,
        'total_drugs': Drug.objects.count(),
        'total_stock_value': Drug.objects.aggregate(value=Sum(F('stock') * F('price')))['value'] or 0
    }
    return render(request, 'drugs/inventory_report.html', context)
