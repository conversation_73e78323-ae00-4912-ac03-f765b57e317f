from django.contrib import admin
from .models import Patient

# Register your models here.
@admin.register(Patient)
class PatientAdmin(admin.ModelAdmin):
    list_display = ('national_id', 'first_name', 'last_name', 'mobile_number', 'registration_date', 'registered_by')
    list_filter = ('registration_date', 'registered_by')
    search_fields = ('first_name', 'last_name', 'national_id', 'mobile_number')
    readonly_fields = ('registration_date',) # This is auto-set
    fieldsets = (
        (None, {
            'fields': ('national_id', ('first_name', 'last_name'), 'age')
        }),
        ('اطلاعات تماس', {
            'fields': ('mobile_number', 'address')
        }),
        ('اطلاعات سیستم', {
            'fields': ('registration_date', 'registered_by')
        }),
    )
