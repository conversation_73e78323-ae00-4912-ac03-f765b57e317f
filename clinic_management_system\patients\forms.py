from django import forms
from .models import Patient

class PatientForm(forms.ModelForm):
    class Meta:
        model = Patient
        fields = ['first_name', 'last_name', 'national_id', 'age', 'mobile_number', 'address']
        widgets = {
            'first_name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'نام'}),
            'last_name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'نام خانوادگی'}),
            'national_id': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'کد ملی'}),
            'age': forms.NumberInput(attrs={'class': 'form-control', 'placeholder': 'سن'}),
            'mobile_number': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'شماره موبایل'}),
            'address': forms.Textarea(attrs={'class': 'form-control', 'placeholder': 'آدرس', 'rows': 3}),
        }
        labels = {
            'first_name': 'نام',
            'last_name': 'نام خانوادگی',
            'national_id': 'کد ملی',
            'age': 'سن',
            'mobile_number': 'شماره موبایل',
            'address': 'آدرس',
        }
        error_messages = {
            'first_name': {
                'required': 'لطفا نام را وارد کنید.',
            },
            'last_name': {
                'required': 'لطفا نام خانوادگی را وارد کنید.',
            },
            'national_id': {
                'required': 'لطفا کد ملی را وارد کنید.',
                'unique': 'این کد ملی قبلا ثبت شده است.',
            },
            'age': {
                'required': 'لطفا سن را وارد کنید.',
            },
            'mobile_number': {
                'required': 'لطفا شماره موبایل را وارد کنید.',
            },
        } 