# Generated by Django 5.2 on 2025-05-09 16:14

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Patient',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('first_name', models.CharField(max_length=100, verbose_name='نام')),
                ('last_name', models.CharField(max_length=100, verbose_name='نام خانوادگی')),
                ('national_id', models.CharField(help_text='کد ملی ۱۰ رقمی بیمار', max_length=10, unique=True, verbose_name='کد ملی')),
                ('age', models.PositiveIntegerField(verbose_name='سن')),
                ('address', models.TextField(verbose_name='آدرس')),
                ('mobile_number', models.CharField(max_length=15, verbose_name='شماره موبایل')),
                ('registration_date', models.DateTimeField(auto_now_add=True, verbose_name='تاریخ اولین ثبت نام')),
                ('registered_by', models.ForeignKey(blank=True, limit_choices_to={'role__in': ['receptionist', 'admin']}, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='registered_patients', to=settings.AUTH_USER_MODEL, verbose_name='ثبت شده توسط')),
            ],
            options={
                'verbose_name': 'بیمار',
                'verbose_name_plural': 'بیماران',
                'ordering': ['-registration_date', 'last_name', 'first_name'],
            },
        ),
    ]
