from django.db import models
from django.conf import settings # For ForeignKey to CustomUser
from django.utils.translation import gettext_lazy as _ # For verbose names

# Create your models here.
class Patient(models.Model):
    first_name = models.CharField(max_length=100, verbose_name=_('نام'))
    last_name = models.CharField(max_length=100, verbose_name=_('نام خانوادگی'))
    national_id = models.CharField(max_length=10, unique=True, verbose_name=_('کد ملی'), help_text=_('کد ملی ۱۰ رقمی بیمار'))
    age = models.PositiveIntegerField(verbose_name=_('سن'))
    address = models.TextField(verbose_name=_('آدرس'))
    mobile_number = models.CharField(max_length=15, verbose_name=_('شماره موبایل'))
    # As per user's request, this is the first registration date
    registration_date = models.DateTimeField(auto_now_add=True, verbose_name=_('تاریخ اولین ثبت نام'))
    registered_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL, # If registrar user is deleted, keep patient record but set this to NULL
        null=True,
        blank=True, # Can be blank if registered through other means or if registrar is not a system user
        related_name='registered_patients',
        verbose_name=_('ثبت شده توسط'),
        limit_choices_to={'role__in': ['receptionist', 'admin']} # Only receptionists or admins can register patients
    )

    def __str__(self):
        return f"{self.first_name} {self.last_name} ({self.national_id})"

    class Meta:
        verbose_name = _('بیمار')
        verbose_name_plural = _('بیماران')
        ordering = ['-registration_date', 'last_name', 'first_name']
