from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.db.models import Q
from .models import Patient
from .forms import PatientForm

# Create your views here.

@login_required
def patient_list(request):
    search_query = request.GET.get('search', '')
    patients = Patient.objects.all()
    
    if search_query:
        patients = patients.filter(
            Q(first_name__icontains=search_query) |
            Q(last_name__icontains=search_query) |
            Q(national_id__icontains=search_query) |
            Q(mobile_number__icontains=search_query)
        )
    
    context = {
        'patients': patients,
        'search_query': search_query
    }
    return render(request, 'patients/patient_list.html', context)

@login_required
def patient_create(request):
    if request.method == 'POST':
        form = PatientForm(request.POST)
        if form.is_valid():
            patient = form.save(commit=False)
            patient.registered_by = request.user
            patient.save()
            messages.success(request, 'بیمار با موفقیت ثبت شد.')
            return redirect('patient_detail', pk=patient.pk)
    else:
        form = PatientForm()
    
    context = {
        'form': form,
        'title': 'ثبت بیمار جدید'
    }
    return render(request, 'patients/patient_form.html', context)

@login_required
def patient_detail(request, pk):
    patient = get_object_or_404(Patient, pk=pk)
    context = {
        'patient': patient
    }
    return render(request, 'patients/patient_detail.html', context)

@login_required
def patient_edit(request, pk):
    patient = get_object_or_404(Patient, pk=pk)
    if request.method == 'POST':
        form = PatientForm(request.POST, instance=patient)
        if form.is_valid():
            form.save()
            messages.success(request, 'اطلاعات بیمار با موفقیت بروزرسانی شد.')
            return redirect('patient_detail', pk=patient.pk)
    else:
        form = PatientForm(instance=patient)
    
    context = {
        'form': form,
        'patient': patient,
        'title': 'ویرایش اطلاعات بیمار'
    }
    return render(request, 'patients/patient_form.html', context)

@login_required
def patient_delete(request, pk):
    patient = get_object_or_404(Patient, pk=pk)
    if request.method == 'POST':
        patient.delete()
        messages.success(request, 'بیمار با موفقیت حذف شد.')
        return redirect('patient_list')
    
    context = {
        'patient': patient
    }
    return render(request, 'patients/patient_confirm_delete.html', context)
