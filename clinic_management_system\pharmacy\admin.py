from django.contrib import admin
from .models import Invoice, DirectSale, DirectSaleItem # Add DirectSale, DirectSaleItem
from django.utils.translation import gettext_lazy as _ # For pharmacist_username

# Register your models here.
@admin.register(Invoice)
class InvoiceAdmin(admin.ModelAdmin):
    list_display = ('id', 'prescription_info', 'total_amount', 'status', 'pharmacist', 'created_at', 'paid_at')
    list_filter = ('status', 'created_at', 'paid_at', 'pharmacist')
    search_fields = (
        'id',
        'prescription__visit__patient__first_name',
        'prescription__visit__patient__last_name',
        'prescription__visit__patient__national_id',
        'pharmacist__username'
    )
    readonly_fields = ('created_at', 'paid_at', 'total_amount') # total_amount is set on save
    autocomplete_fields = ['prescription', 'pharmacist']
    date_hierarchy = 'created_at'
    list_select_related = ('prescription__visit__patient', 'pharmacist') # Optimize queries

    fieldsets = (
        (None, {
            'fields': ('prescription', 'status')
        }),
        ('اطلاعات مالی', {
            'fields': ('total_amount', 'paid_at')
        }),
        ('اطلاعات سیستم', {
            'fields': ('pharmacist', 'created_at')
        }),
    )

    def prescription_info(self, obj):
        if obj.prescription and obj.prescription.visit and obj.prescription.visit.patient:
            return f"نسخه برای: {obj.prescription.visit.patient}"
        return "N/A"
    prescription_info.short_description = 'اطلاعات نسخه'


class DirectSaleItemInline(admin.TabularInline): # Or admin.StackedInline for a different layout
    model = DirectSaleItem
    extra = 1 # Number of empty forms to display
    readonly_fields = ('item_total', 'price_at_sale') # price_at_sale is set from drug, item_total is calculated
    autocomplete_fields = ['drug'] # Assuming DrugAdmin has search_fields configured

    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        if db_field.name == "drug":
            # Optionally, filter drugs (e.g., only show drugs with stock > 0)
            # from drugs.models import Drug # Import here to avoid circular dependency at module level
            # kwargs["queryset"] = Drug.objects.filter(stock__gt=0)
            pass
        return super().formfield_for_foreignkey(db_field, request, **kwargs)


@admin.register(DirectSale)
class DirectSaleAdmin(admin.ModelAdmin):
    list_display = ('id', 'customer_name', 'total_amount', 'status', 'pharmacist_username', 'created_at')
    list_filter = ('status', 'created_at', 'pharmacist')
    search_fields = ('id', 'customer_name', 'pharmacist__username')
    readonly_fields = ('created_at', 'updated_at', 'total_amount') # total_amount is calculated/updated by items
    autocomplete_fields = ['pharmacist']
    date_hierarchy = 'created_at'
    inlines = [DirectSaleItemInline]

    fieldsets = (
        (None, {
            'fields': ('status', 'customer_name', 'pharmacist')
        }),
        (_('اطلاعات مالی'), { # Use _ for translation
            'fields': ('total_amount',)
        }),
        (_('اطلاعات سیستم'), { # Use _ for translation
            'fields': ('created_at', 'updated_at')
        }),
    )

    def pharmacist_username(self, obj):
        if obj.pharmacist:
            return obj.pharmacist.username # Assuming User model has a username
        return "-"
    pharmacist_username.short_description = _('داروساز / فروشنده')

    def save_model(self, request, obj, form, change):
        super().save_model(request, obj, form, change)
        # Recalculate total_amount after main object and inlines are saved,
        # as inline saves happen after the main model's save.
        # The DirectSaleItem.save() method already tries to update this,
        # but a final recalculation here can be a safeguard.
        # This might be better handled purely by signals if DirectSaleItem.save() is robust
        # or if items can be deleted, which DirectSaleItem.save() doesn't cover for deletions.
        new_total = obj.calculate_total_amount()
        if obj.total_amount != new_total:
            obj.total_amount = new_total
            obj.save(update_fields=['total_amount'])

    def save_related(self, request, form, formsets, change):
        super().save_related(request, form, formsets, change)
        # Also recalculate after related objects (inlines) are saved.
        obj = form.instance
        new_total = obj.calculate_total_amount()
        if obj.total_amount != new_total:
            obj.total_amount = new_total
            obj.save(update_fields=['total_amount'])

# Note: For autocomplete_fields like 'drug' in DirectSaleItemInline and 'pharmacist' in DirectSaleAdmin
# to work effectively, the corresponding ModelAdmin (e.g., DrugAdmin, UserAdmin)
# should have 'search_fields' defined.
