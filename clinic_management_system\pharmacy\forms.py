from django import forms
from django.forms import inlineformset_factory
from .models import DirectSale, DirectSaleItem
from drugs.models import Drug # Corrected import for Drug model
from django.utils.translation import gettext_lazy as _

class DirectSaleForm(forms.ModelForm):
    class Meta:
        model = DirectSale
        fields = ['customer_name'] # Pharmacist will be set in view, status defaults to 'completed'
        widgets = {
            'customer_name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': _('نام مشتری (اختیاری)')}),
        }
        labels = {
            'customer_name': _('نام مشتری'),
        }

class DirectSaleItemForm(forms.ModelForm):
    class Meta:
        model = DirectSaleItem
        fields = ['drug', 'quantity']
        widgets = {
            'drug': forms.Select(attrs={'class': 'form-control drug-select'}), # Add class for JS if needed
            'quantity': forms.NumberInput(attrs={'class': 'form-control', 'min': '1'}),
        }
        labels = {
            'drug': _('دارو'),
            'quantity': _('تعداد'),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Optionally, filter the drug queryset, e.g., only show drugs with stock
        self.fields['drug'].queryset = Drug.objects.filter(stock__gt=0).order_by('name') # Example: Show only in-stock drugs
        # pass # Add custom queryset filtering here if needed


# Inline Formset for DirectSaleItems
# This will allow adding multiple drug items to a single direct sale transaction.
DirectSaleItemFormSet = inlineformset_factory(
    DirectSale,  # Parent model
    DirectSaleItem,  # Child model
    form=DirectSaleItemForm,  # Custom form for child model items
    extra=1,  # Number of empty forms to display by default
    can_delete=True,  # Allow deletion of items from the formset
    can_delete_extra=True,
    min_num=1, # At least one item is required
    validate_min=True,
)
