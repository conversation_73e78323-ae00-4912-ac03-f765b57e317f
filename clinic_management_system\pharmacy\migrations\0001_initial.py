# Generated by Django 5.2 on 2025-05-09 16:25

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('visits', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Invoice',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('total_amount', models.DecimalField(decimal_places=0, max_digits=12, verbose_name='مبلغ کل (ریال)')),
                ('status', models.CharField(choices=[('unpaid', 'پرداخت نشده'), ('paid', 'پرداخت شده'), ('cancelled', 'لغو شده')], default='unpaid', max_length=10, verbose_name='وضعیت پرداخت')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاریخ ایجاد فاکتور')),
                ('paid_at', models.DateTimeField(blank=True, null=True, verbose_name='تاریخ پرداخت')),
                ('pharmacist', models.ForeignKey(blank=True, limit_choices_to={'role__in': ['pharmacist', 'admin']}, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='processed_invoices', to=settings.AUTH_USER_MODEL, verbose_name='داروساز پردازش کننده')),
                ('prescription', models.OneToOneField(on_delete=django.db.models.deletion.PROTECT, related_name='invoice', to='visits.prescription', verbose_name='نسخه مربوطه')),
            ],
            options={
                'verbose_name': 'فاکتور',
                'verbose_name_plural': 'فاکتورها',
                'ordering': ['-created_at'],
            },
        ),
    ]
