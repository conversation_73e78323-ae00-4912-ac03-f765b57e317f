from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('visits', '0001_initial'),
        ('accounts', '0001_initial'),
        ('pharmacy', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='DispensedPrescription',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('dispensed_at', models.DateTimeField(auto_now_add=True, verbose_name='تاریخ صدور')),
                ('pharmacist', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='dispensed_prescriptions', to='accounts.customuser', verbose_name='داروساز')),
                ('prescription', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='dispensed', to='visits.prescription', verbose_name='نسخه')),
            ],
            options={
                'verbose_name': 'نسخه صادر شده',
                'verbose_name_plural': 'نسخه\u200cهای صادر شده',
                'ordering': ['-dispensed_at'],
            },
        ),
    ]
