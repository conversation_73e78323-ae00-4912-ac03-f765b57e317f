# Generated by Django 5.1.7 on 2025-05-12 02:25

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('drugs', '0002_add_stock_field'),
        ('pharmacy', '0002_dispensedprescription'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='DirectSale',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('customer_name', models.CharField(blank=True, max_length=255, null=True, verbose_name='نام مشتری')),
                ('total_amount', models.DecimalField(decimal_places=0, default=0, max_digits=12, verbose_name='مبلغ کل (ریال)')),
                ('status', models.CharField(choices=[('completed', 'تکمیل شده'), ('cancelled', 'لغو شده')], default='completed', max_length=10, verbose_name='وضعیت فروش')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاریخ و زمان فروش')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاریخ بروزرسانی')),
                ('pharmacist', models.ForeignKey(blank=True, limit_choices_to={'role__in': ['pharmacist', 'admin']}, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='direct_sales_processed', to=settings.AUTH_USER_MODEL, verbose_name='داروساز / فروشنده')),
            ],
            options={
                'verbose_name': 'فروش مستقیم',
                'verbose_name_plural': 'فروش\u200cهای مستقیم',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='DirectSaleItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.PositiveIntegerField(default=1, verbose_name='تعداد/مقدار')),
                ('price_at_sale', models.DecimalField(decimal_places=0, max_digits=12, verbose_name='قیمت فروش واحد (ریال)')),
                ('item_total', models.DecimalField(decimal_places=0, max_digits=12, verbose_name='مبلغ کل قلم (ریال)')),
                ('direct_sale', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='pharmacy.directsale', verbose_name='فروش مستقیم مربوطه')),
                ('drug', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='drugs.drug', verbose_name='دارو')),
            ],
            options={
                'verbose_name': 'قلم فروش مستقیم',
                'verbose_name_plural': 'اقلام فروش مستقیم',
                'ordering': ['drug__name'],
                'unique_together': {('direct_sale', 'drug')},
            },
        ),
    ]
