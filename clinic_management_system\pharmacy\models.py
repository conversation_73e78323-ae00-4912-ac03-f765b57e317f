from django.db import models
from django.conf import settings
from django.utils.translation import gettext_lazy as _
# from visits.models import Prescription # Using string reference

# Create your models here.
class Invoice(models.Model):
    STATUS_CHOICES = (
        ('unpaid', _('پرداخت نشده')),
        ('paid', _('پرداخت شده')),
        ('cancelled', _('لغو شده')), # Added cancelled status
    )
    prescription = models.OneToOneField(
        'visits.Prescription', # Ensures one invoice per prescription
        on_delete=models.PROTECT, # Prevent deleting prescription if invoice exists
        verbose_name=_('نسخه مربوطه'),
        related_name='invoice'
    )
    # total_amount will be derived from prescription.total_cost, can be stored for history or calculated on the fly.
    # Storing it makes querying faster but requires updating if prescription changes (though prescriptions are usually immutable after creation).
    total_amount = models.DecimalField(max_digits=12, decimal_places=0, verbose_name=_('مبلغ کل (ریال)'))
    status = models.CharField(max_length=10, choices=STATUS_CHOICES, default='unpaid', verbose_name=_('وضعیت پرداخت'))
    pharmacist = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True, # Pharmacist might not be logged or invoice generated automatically
        limit_choices_to={'role__in': ['pharmacist', 'admin']},
        related_name='processed_invoices',
        verbose_name=_('داروساز پردازش کننده')
    )
    created_at = models.DateTimeField(auto_now_add=True, verbose_name=_('تاریخ ایجاد فاکتور'))
    paid_at = models.DateTimeField(null=True, blank=True, verbose_name=_('تاریخ پرداخت'))

    def save(self, *args, **kwargs):
        # Automatically set total_amount from prescription when creating invoice
        if not self.pk and self.prescription: # If new invoice and prescription is set
             self.total_amount = self.prescription.total_cost
        super().save(*args, **kwargs)

    def __str__(self):
        return _("فاکتور برای {} - مبلغ: {} - وضعیت: {}").format(
            str(self.prescription),
            self.total_amount,
            self.get_status_display()
        )

    class Meta:
        verbose_name = _('فاکتور')
        verbose_name_plural = _('فاکتورها')
        ordering = ['-created_at']


class DispensedPrescription(models.Model):
    prescription = models.OneToOneField(
        'visits.Prescription',
        on_delete=models.CASCADE,
        verbose_name=_('نسخه'),
        related_name='dispensed'
    )
    dispensed_at = models.DateTimeField(auto_now_add=True, verbose_name=_('تاریخ صدور'))
    pharmacist = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        related_name='dispensed_prescriptions',
        verbose_name=_('داروساز')
    )

    def __str__(self):
        return f"صدور نسخه {self.prescription}"

    class Meta:
        verbose_name = _('نسخه صادر شده')
        verbose_name_plural = _('نسخه‌های صادر شده')
        ordering = ['-dispensed_at']


# New models for Direct Sales (Over-the-Counter)

class DirectSale(models.Model):
    STATUS_CHOICES = (
        ('completed', _('تکمیل شده')),
        ('cancelled', _('لغو شده')),
    )
    pharmacist = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True, # In case the sale is processed by a non-logged-in user or system
        limit_choices_to={'role__in': ['pharmacist', 'admin']}, # Assuming 'role' field in User model
        related_name='direct_sales_processed',
        verbose_name=_('داروساز / فروشنده')
    )
    customer_name = models.CharField(max_length=255, blank=True, null=True, verbose_name=_('نام مشتری'))
    total_amount = models.DecimalField(max_digits=12, decimal_places=0, default=0, verbose_name=_('مبلغ کل (ریال)'))
    status = models.CharField(max_length=10, choices=STATUS_CHOICES, default='completed', verbose_name=_('وضعیت فروش'))
    created_at = models.DateTimeField(auto_now_add=True, verbose_name=_('تاریخ و زمان فروش'))
    updated_at = models.DateTimeField(auto_now=True, verbose_name=_('تاریخ بروزرسانی'))

    def calculate_total_amount(self):
        """Calculates total amount from its items."""
        return sum(item.item_total for item in self.items.all())

    def save(self, *args, **kwargs):
        # Calculate total_amount before saving if not explicitly set or if items might have changed.
        # This is better handled by signals from DirectSaleItem or if items are managed via formsets.
        # For now, we assume it might be set by a view or a signal.
        # If you want to ensure it's always calculated on save:
        # if self.pk: # if instance already exists
        #     self.total_amount = self.calculate_total_amount()
        super().save(*args, **kwargs)
        # If it's a new sale and total_amount is not calculated yet,
        # it should be updated after items are added, possibly via a signal from DirectSaleItem.

    def __str__(self):
        return _("فروش مستقیم شماره {} - مبلغ: {}").format(self.id, self.total_amount)

    class Meta:
        verbose_name = _('فروش مستقیم')
        verbose_name_plural = _('فروش‌های مستقیم')
        ordering = ['-created_at']


class DirectSaleItem(models.Model):
    direct_sale = models.ForeignKey(DirectSale, related_name='items', on_delete=models.CASCADE, verbose_name=_('فروش مستقیم مربوطه'))
    drug = models.ForeignKey('drugs.Drug', on_delete=models.PROTECT, verbose_name=_('دارو')) # PROTECT to prevent drug deletion if sold
    quantity = models.PositiveIntegerField(default=1, verbose_name=_('تعداد/مقدار'))
    price_at_sale = models.DecimalField(max_digits=12, decimal_places=0, verbose_name=_('قیمت فروش واحد (ریال)'))
    # item_total will be quantity * price_at_sale
    item_total = models.DecimalField(max_digits=12, decimal_places=0, verbose_name=_('مبلغ کل قلم (ریال)'))

    def save(self, *args, **kwargs):
        # Set price_at_sale from drug's current price if not already set (e.g., when creating)
        if not self.price_at_sale and self.drug:
            self.price_at_sale = self.drug.price
        
        # Calculate item_total
        if self.price_at_sale and self.quantity:
            self.item_total = self.price_at_sale * self.quantity
        else:
            self.item_total = 0 # Or handle as an error

        super().save(*args, **kwargs)
        # After saving an item, the DirectSale's total_amount might need an update.
        # This is a good place for a signal or to call a method on direct_sale.
        if self.direct_sale:
             new_total = self.direct_sale.calculate_total_amount()
             if self.direct_sale.total_amount != new_total:
                 self.direct_sale.total_amount = new_total
                 self.direct_sale.save(update_fields=['total_amount'])


    def __str__(self):
        return _("{} عدد {} در فروش {}").format(self.quantity, self.drug.name, self.direct_sale.id)

    class Meta:
        verbose_name = _('قلم فروش مستقیم')
        verbose_name_plural = _('اقلام فروش مستقیم')
        unique_together = ('direct_sale', 'drug') # Optional: prevent adding same drug multiple times to one sale; better to sum quantities.
                                                 # If allowing multiple lines for same drug (e.g. different batches), remove this.
                                                 # For simplicity, let's assume one line per drug, quantity is aggregated.
        ordering = ['drug__name']
