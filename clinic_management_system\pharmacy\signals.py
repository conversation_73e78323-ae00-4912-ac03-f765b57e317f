from django.db.models.signals import post_save
from django.dispatch import receiver
from django.db import transaction
from .models import DispensedPrescription, DirectSale # Add DirectSale
# PrescribedDrug is in visits.models, Drug is in drugs.models
# We need to import them to avoid issues if this signal runs before apps are fully loaded.
# However, direct model imports can sometimes lead to AppRegistryNotReady errors if not handled carefully.
# A common practice is to get the model using apps.get_model inside the function if needed,
# but for ForeignKey relationships like instance.prescription.prescribed_items.all(), Django handles it.

@receiver(post_save, sender=DispensedPrescription)
def update_stock_on_dispense(sender, instance, created, **kwargs):
    """
    Signal to update drug stock and prescription status when a prescription is dispensed.
    """
    if created: # Only run when a new DispensedPrescription record is created
        prescription = instance.prescription
        try:
            with transaction.atomic():
                # Lock the related drug rows to prevent race conditions if desired,
                # though for moderate traffic, atomic transactions might be sufficient.
                # For high concurrency, consider select_for_update on drugs.
                
                items_to_update_stock = []
                for prescribed_item in prescription.prescribed_items.all():
                    drug = prescribed_item.drug
                    quantity_dispensed = prescribed_item.quantity

                    if drug.stock < quantity_dispensed:
                        # This is a critical issue: trying to dispense more than available.
                        # This check should ideally happen *before* creating DispensedPrescription.
                        # For now, we'll log an error or raise one.
                        # Depending on policy, either stop the whole transaction or skip this drug.
                        # For this example, we'll proceed but this needs robust handling.
                        # Consider raising an IntegrityError or a custom exception.
                        print(f"CRITICAL: Insufficient stock for {drug.name}. Stock: {drug.stock}, Needed: {quantity_dispensed}")
                        # To prevent stock going negative, you might do:
                        # raise ValueError(f"Insufficient stock for {drug.name}")
                        # Or skip:
                        # continue 

                    drug.stock -= quantity_dispensed
                    items_to_update_stock.append(drug)

                # Bulk update can be more efficient if many drugs, but Django ORM doesn't have a simple bulk_update for different objects with different values.
                # Saving one by one is fine for a typical number of drugs per prescription.
                for drug_to_save in items_to_update_stock:
                    drug_to_save.save(update_fields=['stock'])

                # Mark the prescription as dispensed
                if not prescription.is_dispensed:
                    prescription.is_dispensed = True
                    prescription.save(update_fields=['is_dispensed'])
        except Exception as e:
            # Log the error appropriately
            print(f"Error updating stock for prescription {prescription.id}: {e}")
            # The transaction.atomic() will ensure rollback on error.
            # Potentially re-raise the exception if you want the save operation of DispensedPrescription to fail.
            raise


@receiver(post_save, sender=DirectSale)
def update_stock_on_direct_sale(sender, instance, created, **kwargs):
    """
    Signal to update drug stock when a direct sale is completed.
    """
    if instance.status == 'completed':
        if created: # Only run for newly created DirectSale instances that are completed
            try:
                with transaction.atomic():
                    items_to_update_stock = []
                    for sale_item in instance.items.all(): # 'items' is the related_name for DirectSaleItem
                        drug = sale_item.drug
                        quantity_sold = sale_item.quantity

                        if drug.stock < quantity_sold:
                            # Critical: Insufficient stock.
                            # This check should also be in the form/view creating the DirectSale.
                            print(f"CRITICAL: Insufficient stock for {drug.name} in DirectSale {instance.id}. Stock: {drug.stock}, Needed: {quantity_sold}")
                            # raise ValueError(f"Insufficient stock for {drug.name} to complete DirectSale {instance.id}")
                            # Or skip this item / entire sale based on policy
                            continue

                        drug.stock -= quantity_sold
                        items_to_update_stock.append(drug)

                    for drug_to_save in items_to_update_stock:
                        drug_to_save.save(update_fields=['stock'])

            except Exception as e:
                print(f"Error updating stock for DirectSale {instance.id}: {e}")
                # transaction.atomic() ensures rollback.
                raise
    # else if instance.status == 'cancelled' and stock was previously deducted:
    #    Add logic here to revert stock if a 'completed' sale is later 'cancelled'.
    #    This requires knowing if stock was deducted (e.g., via a 'stock_deducted' flag on DirectSale).
