from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.db.models import Q
from visits.models import Prescription, PrescribedDrug
# from drugs.models import Drug # Drug is imported in forms.py, not directly used here yet
from .models import DispensedPrescription # DirectSale might be needed if querying
from django.urls import reverse_lazy # For redirecting
from django.db import transaction # For atomic operations
from .forms import DirectSaleForm, DirectSaleItemFormSet
from django.utils.translation import gettext_lazy as _ # For messages

@login_required
def pharmacy_list(request):
    search_query = request.GET.get('search', '')
    prescriptions = Prescription.objects.all().order_by('-created_at')

    if search_query:
        prescriptions = prescriptions.filter(
            Q(visit__patient__first_name__icontains=search_query) |
            Q(visit__patient__last_name__icontains=search_query) |
            Q(visit__patient__national_id__icontains=search_query)
        )

    context = {
        'prescriptions': prescriptions,
        'search_query': search_query
    }
    return render(request, 'pharmacy/prescription_list.html', context)

@login_required
def prescription_detail(request, pk):
    prescription = get_object_or_404(Prescription, pk=pk)
    context = {
        'prescription': prescription
    }
    return render(request, 'pharmacy/prescription_detail.html', context)

@login_required
def dispense_prescription(request, pk):
    prescription = get_object_or_404(Prescription, pk=pk)

    # Check if prescription is already dispensed
    if hasattr(prescription, 'dispensed'):
        messages.warning(request, 'این نسخه قبلاً صادر شده است.')
        return redirect('pharmacy:prescription_detail', pk=prescription.pk)

    if request.method == 'POST':
        # Check if all drugs are available
        all_available = True
        for item in prescription.prescribed_items.all():
            if item.drug.stock < item.quantity:
                all_available = False
                messages.error(request, f'موجودی داروی {item.drug.name} کافی نیست.')
                break

        if all_available:
            # Update drug stock
            for item in prescription.prescribed_items.all():
                drug = item.drug
                drug.stock -= item.quantity
                drug.save()

            # Create dispensed prescription record
            DispensedPrescription.objects.create(
                prescription=prescription,
                pharmacist=request.user
            )

            messages.success(request, 'نسخه با موفقیت صادر شد.')
            return redirect('pharmacy:prescription_detail', pk=prescription.pk)

    context = {
        'prescription': prescription
    }
    return render(request, 'pharmacy/prescription_dispense.html', context)


@login_required
# @permission_required('pharmacy.add_directsale', raise_exception=True) # Uncomment if using permissions
def create_direct_sale(request):
    if request.method == 'POST':
        main_form = DirectSaleForm(request.POST, prefix='main')
        item_formset = DirectSaleItemFormSet(request.POST, prefix='items')

        if main_form.is_valid() and item_formset.is_valid():
            try:
                with transaction.atomic(): # Ensure all saves are one operation
                    direct_sale = main_form.save(commit=False)
                    direct_sale.pharmacist = request.user # Assign logged-in user as pharmacist
                    # Status defaults to 'completed' as per model, so stock deduction signal will fire.
                    direct_sale.save() # Save the main DirectSale instance

                    # Save the formset with the instance of the parent
                    item_formset.instance = direct_sale
                    item_formset.save() # This saves DirectSaleItem instances
                    
                    messages.success(request, _('فروش مستقیم با موفقیت ثبت شد.'))
                    return redirect('pharmacy:create_direct_sale') 
            except Exception as e:
                messages.error(request, _('خطایی در هنگام ثبت فروش رخ داد: {}').format(str(e)))
        else:
            messages.error(request, _('لطفاً خطاهای فرم را اصلاح کنید.'))
    else:
        main_form = DirectSaleForm(prefix='main')
        item_formset = DirectSaleItemFormSet(prefix='items')

    context = {
        'main_form': main_form,
        'item_formset': item_formset,
        'page_title': _('ثبت فروش مستقیم جدید') # For use in template
    }
    return render(request, 'pharmacy/create_direct_sale.html', context)
