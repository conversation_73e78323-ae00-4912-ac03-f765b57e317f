{% extends 'base.html' %}

{% block title %}ورود به سیستم{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-white">
                <h5 class="card-title mb-0">ورود به سیستم</h5>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    {% if form.errors %}
                    <div class="alert alert-danger">
                        نام کاربری یا رمز عبور اشتباه است.
                    </div>
                    {% endif %}
                    
                    <div class="mb-3">
                        <label for="id_username" class="form-label">نام کاربری</label>
                        <input type="text" name="username" id="id_username" class="form-control" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="id_password" class="form-label">رمز عبور</label>
                        <input type="password" name="password" id="id_password" class="form-control" required>
                    </div>
                    
                    <input type="hidden" name="next" value="{{ next }}">
                    
                    <button type="submit" class="btn btn-primary w-100">ورود</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    body {
        background-color: #f8f9fa;
    }
    
    .card {
        margin-top: 2rem;
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }
    
    .form-control {
        padding: 0.75rem 1rem;
    }
    
    .btn-primary {
        padding: 0.75rem 1rem;
    }
</style>
{% endblock %} 