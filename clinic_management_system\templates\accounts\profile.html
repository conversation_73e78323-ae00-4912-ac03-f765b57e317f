{% extends 'base.html' %}

{% block title %}پروفایل - {{ user.get_full_name|default:user.username }}{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header bg-white">
                <h5 class="card-title mb-0">اطلاعات کاربری</h5>
            </div>
            <div class="card-body">
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h6>نام کاربری</h6>
                        <p>{{ user.username }}</p>
                    </div>
                    <div class="col-md-6">
                        <h6>نام و نام خانوادگی</h6>
                        <p>{{ user.get_full_name|default:"ثبت نشده" }}</p>
                    </div>
                </div>
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h6>ایمیل</h6>
                        <p>{{ user.email|default:"ثبت نشده" }}</p>
                    </div>
                    <div class="col-md-6">
                        <h6>نقش</h6>
                        <p>{{ user.get_role_display }}</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="card mt-4">
            <div class="card-header bg-white">
                <h5 class="card-title mb-0">تغییر رمز عبور</h5>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    {% for field in form %}
                    <div class="mb-3">
                        <label for="{{ field.id_for_label }}" class="form-label">{{ field.label }}</label>
                        {{ field.errors }}
                        {{ field }}
                        {% if field.help_text %}
                        <div class="form-text">{{ field.help_text }}</div>
                        {% endif %}
                    </div>
                    {% endfor %}
                    <button type="submit" class="btn btn-primary">تغییر رمز عبور</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    /* Style form fields */
    form input {
        width: 100%;
        padding: 0.375rem 0.75rem;
        border: 1px solid #ced4da;
        border-radius: 0.25rem;
    }
    
    /* Style error messages */
    .errorlist {
        color: #dc3545;
        list-style: none;
        padding: 0;
        margin: 0.25rem 0;
    }
</style>
{% endblock %} 