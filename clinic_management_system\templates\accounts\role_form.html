{% extends 'base.html' %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">{{ title }}</h5>
    </div>
    <div class="card-body">
        <form method="post">
            {% csrf_token %}
            
            {% if form.non_field_errors %}
            <div class="alert alert-danger">
                {% for error in form.non_field_errors %}
                {{ error }}
                {% endfor %}
            </div>
            {% endif %}
            
            <div class="form-group">
                <label for="{{ form.name.id_for_label }}">نام نقش</label>
                {{ form.name }}
                {% if form.name.errors %}
                <div class="invalid-feedback d-block">
                    {% for error in form.name.errors %}
                    {{ error }}
                    {% endfor %}
                </div>
                {% endif %}
            </div>
            
            <div class="form-group">
                <label for="{{ form.description.id_for_label }}">توضیحات</label>
                {{ form.description }}
                {% if form.description.errors %}
                <div class="invalid-feedback d-block">
                    {% for error in form.description.errors %}
                    {{ error }}
                    {% endfor %}
                </div>
                {% endif %}
            </div>
            
            <div class="form-group">
                <label>دسترسی‌ها</label>
                {{ form.permissions }}
                {% if form.permissions.errors %}
                <div class="invalid-feedback d-block">
                    {% for error in form.permissions.errors %}
                    {{ error }}
                    {% endfor %}
                </div>
                {% endif %}
            </div>
            
            <div class="mt-3">
                <button type="submit" class="btn btn-primary">ذخیره</button>
                <a href="{% url 'role_list' %}" class="btn btn-secondary">انصراف</a>
            </div>
        </form>
    </div>
</div>
{% endblock %} 