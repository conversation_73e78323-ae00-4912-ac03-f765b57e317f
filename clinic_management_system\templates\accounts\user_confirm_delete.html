{% extends 'base.html' %}

{% block title %}حذف کاربر{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header bg-white">
        <h5 class="card-title mb-0">حذف کاربر</h5>
    </div>
    <div class="card-body">
        <div class="alert alert-danger">
            <h5 class="alert-heading">
                <i class="fas fa-exclamation-triangle"></i> هشدار!
            </h5>
            <p class="mb-0">
                آیا از حذف کاربر "{{ user.username }}" اطمینان دارید؟
                این عمل غیرقابل بازگشت است و تمام اطلاعات مرتبط با این کاربر نیز حذف خواهند شد.
            </p>
        </div>

        <div class="table-responsive mb-4">
            <table class="table table-bordered">
                <tr>
                    <th class="bg-light" style="width: 40%">نام کاربری</th>
                    <td>{{ user.username }}</td>
                </tr>
                <tr>
                    <th class="bg-light">نام و نام خانوادگی</th>
                    <td>{{ user.get_full_name|default:"-" }}</td>
                </tr>
                <tr>
                    <th class="bg-light">ایمیل</th>
                    <td>{{ user.email|default:"-" }}</td>
                </tr>
                <tr>
                    <th class="bg-light">نقش</th>
                    <td>{{ user.get_role_display }}</td>
                </tr>
                <tr>
                    <th class="bg-light">وضعیت</th>
                    <td>
                        {% if user.is_active %}
                        <span class="badge bg-success">فعال</span>
                        {% else %}
                        <span class="badge bg-danger">غیرفعال</span>
                        {% endif %}
                    </td>
                </tr>
            </table>
        </div>

        <form method="post">
            {% csrf_token %}
            <div class="text-end">
                <a href="{% url 'user_list' %}" class="btn btn-secondary">
                    <i class="fas fa-times"></i> انصراف
                </a>
                <button type="submit" class="btn btn-danger">
                    <i class="fas fa-trash"></i> حذف
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}
