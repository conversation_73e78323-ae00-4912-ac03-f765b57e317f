{% extends 'base.html' %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header bg-white d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">{{ title }}</h5>
        <a href="{% url 'user_list' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-right"></i> بازگشت به لیست کاربران
        </a>
    </div>
    <div class="card-body">
        <form method="post">
            {% csrf_token %}
            
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="{{ form.username.id_for_label }}" class="form-label">نام کاربری</label>
                    {{ form.username }}
                    {% if form.username.errors %}
                        <div class="text-danger">{{ form.username.errors }}</div>
                    {% endif %}
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="{{ form.email.id_for_label }}" class="form-label">ایمیل</label>
                    {{ form.email }}
                    {% if form.email.errors %}
                        <div class="text-danger">{{ form.email.errors }}</div>
                    {% endif %}
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="{{ form.first_name.id_for_label }}" class="form-label">نام</label>
                    {{ form.first_name }}
                    {% if form.first_name.errors %}
                        <div class="text-danger">{{ form.first_name.errors }}</div>
                    {% endif %}
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="{{ form.last_name.id_for_label }}" class="form-label">نام خانوادگی</label>
                    {{ form.last_name }}
                    {% if form.last_name.errors %}
                        <div class="text-danger">{{ form.last_name.errors }}</div>
                    {% endif %}
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="{{ form.role.id_for_label }}" class="form-label">نقش اصلی</label>
                    {{ form.role }}
                    {% if form.role.errors %}
                        <div class="text-danger">{{ form.role.errors }}</div>
                    {% endif %}
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="{{ form.phone.id_for_label }}" class="form-label">شماره تماس</label>
                    {{ form.phone }}
                    {% if form.phone.errors %}
                        <div class="text-danger">{{ form.phone.errors }}</div>
                    {% endif %}
                </div>
            </div>
            
            <div class="mb-3">
                <label for="{{ form.address.id_for_label }}" class="form-label">آدرس</label>
                {{ form.address }}
                {% if form.address.errors %}
                    <div class="text-danger">{{ form.address.errors }}</div>
                {% endif %}
            </div>
            
            <div class="mb-3">
                <label for="{{ form.roles.id_for_label }}" class="form-label">نقش‌های اضافی</label>
                <div class="border rounded p-3">
                    {{ form.roles }}
                </div>
                {% if form.roles.errors %}
                    <div class="text-danger">{{ form.roles.errors }}</div>
                {% endif %}
            </div>
            
            {% if form.password1 %}
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="{{ form.password1.id_for_label }}" class="form-label">رمز عبور</label>
                    {{ form.password1 }}
                    {% if form.password1.errors %}
                        <div class="text-danger">{{ form.password1.errors }}</div>
                    {% endif %}
                    {% if form.password1.help_text %}
                        <div class="form-text text-muted">{{ form.password1.help_text }}</div>
                    {% endif %}
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="{{ form.password2.id_for_label }}" class="form-label">تکرار رمز عبور</label>
                    {{ form.password2 }}
                    {% if form.password2.errors %}
                        <div class="text-danger">{{ form.password2.errors }}</div>
                    {% endif %}
                </div>
            </div>
            {% endif %}
            
            {% if form.is_active %}
            <div class="mb-3 form-check">
                {{ form.is_active }}
                <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                    کاربر فعال است
                </label>
            </div>
            {% endif %}
            
            <div class="text-end">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> ذخیره
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Add Bootstrap classes to form fields
    document.addEventListener('DOMContentLoaded', function() {
        const formControls = document.querySelectorAll('input, select, textarea');
        formControls.forEach(function(element) {
            if (!element.classList.contains('form-check-input')) {
                element.classList.add('form-control');
            }
        });
    });
</script>
{% endblock %}
