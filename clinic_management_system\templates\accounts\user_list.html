{% extends 'base.html' %}

{% block title %}مدیریت کاربران{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header bg-white d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">مدیریت کاربران</h5>
        <a href="{% url 'user_create' %}" class="btn btn-primary">
            <i class="fas fa-user-plus"></i> ایجاد کاربر جدید
        </a>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>نام کاربری</th>
                        <th>نام و نام خانوادگی</th>
                        <th>ایمیل</th>
                        <th>نقش</th>
                        <th>وضعیت</th>
                        <th>عملیات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for user in users %}
                    <tr>
                        <td>{{ user.username }}</td>
                        <td>{{ user.get_full_name|default:"-" }}</td>
                        <td>{{ user.email|default:"-" }}</td>
                        <td>
                            <span class="badge bg-{{ user.role|yesno:'primary,secondary' }}">
                                {{ user.get_role_display }}
                            </span>
                        </td>
                        <td>
                            {% if user.is_active %}
                            <span class="badge bg-success">فعال</span>
                            {% else %}
                            <span class="badge bg-danger">غیرفعال</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group">
                                <a href="{% url 'user_edit' user.pk %}" class="btn btn-sm btn-warning" title="ویرایش">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="{% url 'user_delete' user.pk %}" class="btn btn-sm btn-danger" title="حذف">
                                    <i class="fas fa-trash"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="6" class="text-center text-muted">
                            <i class="fas fa-info-circle"></i> هیچ کاربری یافت نشد
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}