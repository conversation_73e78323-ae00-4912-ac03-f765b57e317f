<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}سیستم مدیریت کلینیک{% endblock %}</title>
    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Custom CSS -->
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --accent-color: #e74c3c;
            --light-bg: #f8f9fa;
        }

        body {
            font-family: 'Vazirmatn', sans-serif;
            background-color: var(--light-bg);
        }

        .navbar {
            background-color: var(--primary-color);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .navbar-brand {
            color: white !important;
            font-weight: bold;
        }

        .nav-link {
            color: rgba(255,255,255,0.8) !important;
            transition: color 0.3s ease;
        }

        .nav-link:hover {
            color: white !important;
        }

        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            transition: transform 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
        }

        .btn-primary {
            background-color: var(--secondary-color);
            border-color: var(--secondary-color);
        }

        .btn-primary:hover {
            background-color: #2980b9;
            border-color: #2980b9;
        }

        .sidebar {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            padding: 20px;
        }

        .main-content {
            padding: 20px;
        }

        .footer {
            background-color: var(--primary-color);
            color: white;
            padding: 20px 0;
            margin-top: 50px;
        }
    </style>
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark mb-4">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-hospital"></i> سیستم مدیریت کلینیک
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'patient_list' %}">
                            <i class="fas fa-users"></i> بیماران
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="visitsDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-calendar-check"></i> ویزیت‌ها و نوبت‌ها
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="visitsDropdown">
                            <li><a class="dropdown-item" href="{% url 'visit_list' %}">لیست ویزیت‌ها</a></li>
                            <li><a class="dropdown-item" href="{% url 'appointment_list' %}">لیست نوبت‌ها</a></li>
                            <li><a class="dropdown-item" href="{% url 'appointment_create' %}">ثبت نوبت جدید</a></li>
                            <li><a class="dropdown-item" href="{% url 'doctor_schedule' %}">برنامه زمانی پزشکان</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown"> {# Changed to dropdown #}
                        <a class="nav-link dropdown-toggle" href="#" id="pharmacyDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false"> {# Changed link attributes #}
                            <i class="fas fa-pills"></i> داروخانه
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="pharmacyDropdown"> {# Added dropdown menu #}
                            <li><a class="dropdown-item" href="{% url 'pharmacy:pharmacy_list' %}">لیست نسخه‌ها</a></li>
                            <li><a class="dropdown-item" href="{% url 'pharmacy:create_direct_sale' %}">ثبت فروش مستقیم</a></li>
                            {# Add permission check if needed: {% if perms.pharmacy.add_directsale %}...{% endif %} #}
                        </ul>
                    </li>
                    {% if user.is_authenticated and user.is_staff %}
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'user_list' %}">
                            <i class="fas fa-users"></i> مدیریت کاربران
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'role_list' %}">
                            <i class="fas fa-user-tag"></i> مدیریت نقش‌ها
                        </a>
                    </li>
                    {% endif %}
                </ul>
                <ul class="navbar-nav">
                    {% if user.is_authenticated %}
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user"></i> {{ user.get_full_name|default:user.username }}
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end">
                                {% if user.is_staff %}
                                <li><a class="dropdown-item" href="{% url 'user_list' %}">مدیریت کاربران</a></li>
                                <li><hr class="dropdown-divider"></li>
                                {% endif %}
                                <li><a class="dropdown-item" href="{% url 'profile' %}">پروفایل</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{% url 'logout' %}">خروج</a></li>
                            </ul>
                        </li>
                    {% else %}
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'login' %}">ورود</a>
                        </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container">
        {% if messages %}
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            {% endfor %}
        {% endif %}

        {% block content %}{% endblock %}
    </div>

    <!-- Footer -->
    <footer class="footer mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5>تماس با ما</h5>
                    <p>
                        <i class="fas fa-phone"></i> تلفن: ۰۲۱-XXXXXXXX<br>
                        <i class="fas fa-envelope"></i> ایمیل: <EMAIL><br>
                        <i class="fas fa-map-marker-alt"></i> آدرس: تبریز خیابان XXXX
                    </p>
                </div>
                <div class="col-md-6 text-md-end">
                    <h5>دسترسی سریع</h5>
                    <ul class="list-unstyled">
                        <li><a href="#" class="text-white">درباره ما</a></li>
                        <li><a href="#" class="text-white">تماس با ما</a></li>
                        <li><a href="#" class="text-white">قوانین و مقررات</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Select2 -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <!-- Vazirmatn Font -->
    <link href="https://cdn.jsdelivr.net/gh/rastikerdar/vazirmatn@v33.003/Vazirmatn-font-face.css" rel="stylesheet" type="text/css" />
    {% block extra_js %}{% endblock %}
</body>
</html>
