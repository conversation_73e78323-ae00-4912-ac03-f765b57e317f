{% extends 'base.html' %}

{% block title %}داشبورد - سیستم مدیریت کلینیک{% endblock %}

{% block content %}
<div class="row">
    <!-- Statistics Cards -->
    <div class="col-md-3 mb-4">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title">تعداد بیماران</h6>
                        <h2 class="mb-0">{{ total_patients }}</h2>
                    </div>
                    <i class="fas fa-users fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-4">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title">ویزیت‌های امروز</h6>
                        <h2 class="mb-0">{{ today_visits }}</h2>
                    </div>
                    <i class="fas fa-calendar-check fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-4">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title">نسخه‌های امروز</h6>
                        <h2 class="mb-0">{{ today_prescriptions }}</h2>
                    </div>
                    <i class="fas fa-prescription fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-4">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title">در انتظار داروخانه</h6>
                        <h2 class="mb-0">{{ pending_pharmacy }}</h2>
                    </div>
                    <i class="fas fa-pills fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Quick Actions -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header bg-white">
                <h5 class="card-title mb-0">دسترسی سریع</h5>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-6">
                        <a href="{% url 'patient_create' %}" class="btn btn-outline-primary w-100">
                            <i class="fas fa-user-plus"></i> ثبت بیمار جدید
                        </a>
                    </div>
                    <div class="col-6">
                        <a href="{% url 'visit_create' %}" class="btn btn-outline-success w-100">
                            <i class="fas fa-calendar-plus"></i> ثبت ویزیت جدید
                        </a>
                    </div>
                    <div class="col-6">
                        <a href="{% url 'pharmacy:pharmacy_list' %}" class="btn btn-outline-info w-100">
                            <i class="fas fa-file-medical"></i> مدیریت نسخه‌ها
                        </a>
                    </div>
                    <div class="col-6">
                        <a href="{% url 'drug_list' %}" class="btn btn-outline-warning w-100">
                            <i class="fas fa-pills"></i> مدیریت داروها
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Recent Activities -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header bg-white">
                <h5 class="card-title mb-0">فعالیت‌های اخیر</h5>
            </div>
            <div class="card-body">
                <div class="list-group list-group-flush">
                    {% for activity in recent_activities %}
                    <div class="list-group-item">
                        <div class="d-flex w-100 justify-content-between">
                            <h6 class="mb-1">{{ activity.title }}</h6>
                            <small>{{ activity.time }}</small>
                        </div>
                        <p class="mb-1">{{ activity.description }}</p>
                    </div>
                    {% empty %}
                    <div class="text-center text-muted py-3">
                        <i class="fas fa-info-circle"></i> هیچ فعالیتی ثبت نشده است
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Today's Schedule -->
    <div class="col-md-12">
        <div class="card">
            <div class="card-header bg-white">
                <h5 class="card-title mb-0">برنامه امروز</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>ساعت</th>
                                <th>نام بیمار</th>
                                <th>دکتر</th>
                                <th>وضعیت</th>
                                <th>عملیات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for visit in today_schedule %}
                            <tr>
                                <td>{{ visit.time }}</td>
                                <td>{{ visit.patient_name }}</td>
                                <td>{{ visit.doctor_name }}</td>
                                <td>
                                    <span class="badge bg-{{ visit.status_color }}">
                                        {{ visit.status }}
                                    </span>
                                </td>
                                <td>
                                    <a href="{% url 'visit_detail' visit.id %}" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="5" class="text-center text-muted">
                                    <i class="fas fa-calendar-times"></i> هیچ ویزیتی برای امروز ثبت نشده است
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Add any dashboard-specific JavaScript here
    document.addEventListener('DOMContentLoaded', function() {
        // Example: Auto-hide alerts after 5 seconds
        setTimeout(function() {
            document.querySelectorAll('.alert').forEach(function(alert) {
                new bootstrap.Alert(alert).close();
            });
        }, 5000);
    });
</script>
{% endblock %} 