{% extends 'base.html' %}

{% block title %}حذف دارو{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header bg-white d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">حذف دارو</h5>
        <a href="{% url 'drug_list' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-right"></i> بازگشت به لیست
        </a>
    </div>
    <div class="card-body">
        <div class="alert alert-warning">
            <h5 class="alert-heading">هشدار!</h5>
            <p>آیا از حذف داروی "{{ drug.name }}" اطمینان دارید؟</p>
            <hr>
            <p class="mb-0">این عملیات غیرقابل بازگشت است.</p>
        </div>

        <div class="table-responsive mb-4">
            <table class="table table-bordered">
                <tr>
                    <th class="bg-light" style="width: 40%">نام دارو</th>
                    <td>{{ drug.name }}</td>
                </tr>
                <tr>
                    <th class="bg-light">قیمت</th>
                    <td>{{ drug.price }} ریال</td>
                </tr>
            </table>
        </div>

        <form method="post">
            {% csrf_token %}
            <div class="text-end">
                <a href="{% url 'drug_list' %}" class="btn btn-secondary">
                    <i class="fas fa-times"></i> انصراف
                </a>
                <button type="submit" class="btn btn-danger">
                    <i class="fas fa-trash"></i> حذف
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %} 