{% extends 'base.html' %}

{% block title %}اطلاعات دارو{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header bg-white d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">اطلاعات دارو</h5>
        <div>
            {% if user.role == 'pharmacist' or user.is_staff %}
            <a href="{% url 'update_stock' drug.pk %}" class="btn btn-success">
                <i class="fas fa-plus-circle"></i> افزایش موجودی
            </a>
            <a href="{% url 'drug_edit' drug.pk %}" class="btn btn-warning">
                <i class="fas fa-edit"></i> ویرایش
            </a>
            <a href="{% url 'drug_delete' drug.pk %}" class="btn btn-danger">
                <i class="fas fa-trash"></i> حذف
            </a>
            {% endif %}
            <a href="{% url 'drug_list' %}" class="btn btn-secondary">
                <i class="fas fa-arrow-right"></i> بازگشت به لیست
            </a>
        </div>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <div class="mb-4">
                    <h6 class="text-muted mb-3">اطلاعات دارو</h6>
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <tr>
                                <th class="bg-light" style="width: 40%">نام دارو</th>
                                <td>{{ drug.name }}</td>
                            </tr>
                            <tr>
                                <th class="bg-light">قیمت</th>
                                <td>{{ drug.price }} ریال</td>
                            </tr>
                            <tr>
                                <th class="bg-light">موجودی</th>
                                <td>
                                    {{ drug.stock }} عدد
                                    {% if drug.stock <= 0 %}
                                    <span class="badge bg-danger ms-2">ناموجود</span>
                                    {% elif drug.is_low_stock %}
                                    <span class="badge bg-warning text-dark ms-2">کم</span>
                                    {% else %}
                                    <span class="badge bg-success ms-2">موجود</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <th class="bg-light">حداقل موجودی</th>
                                <td>{{ drug.min_stock_level }} عدد</td>
                            </tr>
                            <tr>
                                <th class="bg-light">ثبت کننده</th>
                                <td>{{ drug.created_by.get_full_name|default:drug.created_by.username }}</td>
                            </tr>
                            <tr>
                                <th class="bg-light">تاریخ ثبت</th>
                                <td>{{ drug.created_at|date:"Y/m/d H:i" }}</td>
                            </tr>
                        </table>
                    </div>
                </div>

                <div class="mb-4">
                    <h6 class="text-muted mb-3">آمار مصرف</h6>
                    <div class="card">
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-6">
                                    <h3>{{ prescribed_count }}</h3>
                                    <p class="text-muted">تعداد نسخه‌ها</p>
                                </div>
                                <div class="col-6">
                                    <h3>{{ total_prescribed }}</h3>
                                    <p class="text-muted">تعداد کل تجویز شده</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-4">
                    <h6 class="text-muted mb-3">توضیحات</h6>
                    <div class="card">
                        <div class="card-body">
                            {% if drug.description %}
                            {{ drug.description|linebreaks }}
                            {% else %}
                            <p class="text-muted">توضیحاتی ثبت نشده است.</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}