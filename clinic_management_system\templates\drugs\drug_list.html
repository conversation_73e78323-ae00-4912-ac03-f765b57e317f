{% extends 'base.html' %}

{% block title %}لیست داروها{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header bg-white d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">لیست داروها</h5>
        <div>
            <a href="{% url 'inventory_report' %}" class="btn btn-info me-2">
                <i class="fas fa-chart-bar"></i> گزارش موجودی
            </a>
            {% if user.role == 'pharmacist' or user.is_staff %}
            <a href="{% url 'drug_create' %}" class="btn btn-primary">
                <i class="fas fa-plus"></i> ثبت داروی جدید
            </a>
            {% endif %}
        </div>
    </div>
    <div class="card-body">
        <!-- Search and Filter Form -->
        <form method="get" class="mb-4">
            <div class="row">
                <div class="col-md-8">
                    <div class="input-group">
                        <input type="text" name="search" class="form-control" placeholder="جستجو در نام یا توضیحات دارو..." value="{{ search_query }}">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i> جستجو
                        </button>
                    </div>
                </div>
                <div class="col-md-4">
                    <select name="stock" class="form-select" onchange="this.form.submit()">
                        <option value="" {% if not stock_filter %}selected{% endif %}>همه داروها</option>
                        <option value="out" {% if stock_filter == 'out' %}selected{% endif %}>ناموجود</option>
                        <option value="low" {% if stock_filter == 'low' %}selected{% endif %}>موجودی کم</option>
                        <option value="available" {% if stock_filter == 'available' %}selected{% endif %}>موجود</option>
                    </select>
                </div>
            </div>
        </form>

        <!-- Drugs Table -->
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>نام دارو</th>
                        <th>قیمت (ریال)</th>
                        <th>موجودی</th>
                        <th>وضعیت</th>
                        <th>عملیات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for drug in drugs %}
                    <tr>
                        <td>
                            <a href="{% url 'drug_detail' drug.pk %}" class="text-decoration-none">
                                {{ drug.name }}
                            </a>
                        </td>
                        <td>{{ drug.price }}</td>
                        <td>{{ drug.stock }} عدد</td>
                        <td>
                            {% if drug.stock <= 0 %}
                            <span class="badge bg-danger">ناموجود</span>
                            {% elif drug.is_low_stock %}
                            <span class="badge bg-warning text-dark">کم</span>
                            {% else %}
                            <span class="badge bg-success">موجود</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group">
                                <a href="{% url 'drug_detail' drug.pk %}" class="btn btn-sm btn-info" title="مشاهده جزئیات">
                                    <i class="fas fa-eye"></i>
                                </a>
                                {% if user.role == 'pharmacist' or user.is_staff %}
                                <a href="{% url 'update_stock' drug.pk %}" class="btn btn-sm btn-success" title="افزایش موجودی">
                                    <i class="fas fa-plus-circle"></i>
                                </a>
                                <a href="{% url 'drug_edit' drug.pk %}" class="btn btn-sm btn-warning" title="ویرایش">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="{% url 'drug_delete' drug.pk %}" class="btn btn-sm btn-danger" title="حذف">
                                    <i class="fas fa-trash"></i>
                                </a>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="5" class="text-center text-muted">
                            <i class="fas fa-info-circle"></i> هیچ دارویی ثبت نشده است
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}