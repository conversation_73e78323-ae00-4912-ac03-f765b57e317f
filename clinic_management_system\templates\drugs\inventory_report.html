{% extends 'base.html' %}

{% block title %}گزارش موجودی داروها{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header bg-white d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">گزارش موجودی داروها</h5>
        <a href="{% url 'drug_list' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-right"></i> بازگشت به لیست داروها
        </a>
    </div>
    <div class="card-body">
        <!-- Summary Cards -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                        <h3>{{ total_drugs }}</h3>
                        <p class="mb-0">تعداد کل داروها</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body text-center">
                        <h3>{{ total_drugs|default:0|add:"-"|add:out_of_stock_drugs.count|add:"-"|add:low_stock_drugs.count }}</h3>
                        <p class="mb-0">داروهای با موجودی کافی</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-dark">
                    <div class="card-body text-center">
                        <h3>{{ low_stock_drugs.count }}</h3>
                        <p class="mb-0">داروهای با موجودی کم</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-danger text-white">
                    <div class="card-body text-center">
                        <h3>{{ out_of_stock_drugs.count }}</h3>
                        <p class="mb-0">داروهای ناموجود</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Financial Summary -->
        <div class="card mb-4">
            <div class="card-header bg-light">
                <h6 class="card-title mb-0">خلاصه مالی</h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-6">
                        <h4>{{ total_stock_value|floatformat:0 }} ریال</h4>
                        <p class="text-muted">ارزش کل موجودی</p>
                    </div>
                    <div class="col-md-6">
                        <h4>{{ total_drugs }}</h4>
                        <p class="text-muted">تعداد اقلام دارویی</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Out of Stock Drugs -->
        <div class="card mb-4">
            <div class="card-header bg-light">
                <h6 class="card-title mb-0">داروهای ناموجود</h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>نام دارو</th>
                                <th>قیمت (ریال)</th>
                                <th>حداقل موجودی</th>
                                <th>عملیات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for drug in out_of_stock_drugs %}
                            <tr>
                                <td>
                                    <a href="{% url 'drug_detail' drug.pk %}" class="text-decoration-none">
                                        {{ drug.name }}
                                    </a>
                                </td>
                                <td>{{ drug.price }}</td>
                                <td>{{ drug.min_stock_level }}</td>
                                <td>
                                    {% if user.role == 'pharmacist' or user.is_staff %}
                                    <a href="{% url 'update_stock' drug.pk %}" class="btn btn-sm btn-success">
                                        <i class="fas fa-plus-circle"></i> افزایش موجودی
                                    </a>
                                    {% endif %}
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="4" class="text-center text-muted">
                                    <i class="fas fa-check-circle"></i> همه داروها موجود هستند
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Low Stock Drugs -->
        <div class="card">
            <div class="card-header bg-light">
                <h6 class="card-title mb-0">داروهای با موجودی کم</h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>نام دارو</th>
                                <th>قیمت (ریال)</th>
                                <th>موجودی فعلی</th>
                                <th>حداقل موجودی</th>
                                <th>عملیات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for drug in low_stock_drugs %}
                            {% if drug.stock > 0 %}
                            <tr>
                                <td>
                                    <a href="{% url 'drug_detail' drug.pk %}" class="text-decoration-none">
                                        {{ drug.name }}
                                    </a>
                                </td>
                                <td>{{ drug.price }}</td>
                                <td>{{ drug.stock }}</td>
                                <td>{{ drug.min_stock_level }}</td>
                                <td>
                                    {% if user.role == 'pharmacist' or user.is_staff %}
                                    <a href="{% url 'update_stock' drug.pk %}" class="btn btn-sm btn-success">
                                        <i class="fas fa-plus-circle"></i> افزایش موجودی
                                    </a>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endif %}
                            {% empty %}
                            <tr>
                                <td colspan="5" class="text-center text-muted">
                                    <i class="fas fa-check-circle"></i> همه داروها موجودی کافی دارند
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
