{% extends 'base.html' %}

{% block title %}افزایش موجودی دارو{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header bg-white d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">افزایش موجودی دارو</h5>
        <a href="{% url 'drug_detail' drug.pk %}" class="btn btn-secondary">
            <i class="fas fa-arrow-right"></i> بازگشت به جزئیات دارو
        </a>
    </div>
    <div class="card-body">
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-body">
                        <h6 class="card-title">اطلاعات دارو</h6>
                        <table class="table table-bordered mt-3">
                            <tr>
                                <th class="bg-light" style="width: 40%">نام دارو</th>
                                <td>{{ drug.name }}</td>
                            </tr>
                            <tr>
                                <th class="bg-light">موجودی فعلی</th>
                                <td>
                                    {{ drug.stock }} عدد
                                    {% if drug.stock <= 0 %}
                                    <span class="badge bg-danger ms-2">ناموجود</span>
                                    {% elif drug.is_low_stock %}
                                    <span class="badge bg-warning text-dark ms-2">کم</span>
                                    {% else %}
                                    <span class="badge bg-success ms-2">موجود</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <th class="bg-light">حداقل موجودی</th>
                                <td>{{ drug.min_stock_level }} عدد</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-body">
                        <h6 class="card-title">افزایش موجودی</h6>
                        <form method="post" class="mt-3">
                            {% csrf_token %}
                            <div class="mb-3">
                                <label for="{{ form.add_stock.id_for_label }}" class="form-label">تعداد افزایش موجودی</label>
                                {{ form.add_stock }}
                                {% if form.add_stock.errors %}
                                <div class="text-danger mt-1">
                                    {{ form.add_stock.errors }}
                                </div>
                                {% endif %}
                                <div class="form-text text-muted">
                                    موجودی جدید: {{ drug.stock }} + مقدار وارد شده
                                </div>
                            </div>
                            <div class="text-end">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> ذخیره تغییرات
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
