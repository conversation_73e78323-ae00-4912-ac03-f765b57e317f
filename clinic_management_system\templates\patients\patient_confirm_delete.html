{% extends 'base.html' %}

{% block title %}حذف بیمار{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header bg-white">
        <h5 class="card-title mb-0">حذف بیمار</h5>
    </div>
    <div class="card-body">
        <div class="alert alert-danger">
            <h5 class="alert-heading">
                <i class="fas fa-exclamation-triangle"></i> هشدار!
            </h5>
            <p class="mb-0">
                آیا از حذف بیمار "{{ patient.first_name }} {{ patient.last_name }}" اطمینان دارید؟
                این عمل غیرقابل بازگشت است و تمام اطلاعات مرتبط با این بیمار نیز حذف خواهند شد.
            </p>
        </div>

        <div class="table-responsive mb-4">
            <table class="table table-bordered">
                <tr>
                    <th class="bg-light" style="width: 40%">نام و نام خانوادگی</th>
                    <td>{{ patient.first_name }} {{ patient.last_name }}</td>
                </tr>
                <tr>
                    <th class="bg-light">کد ملی</th>
                    <td>{{ patient.national_id }}</td>
                </tr>
                <tr>
                    <th class="bg-light">تعداد ویزیت‌ها</th>
                    <td>{{ patient.visits.count }}</td>
                </tr>
                <tr>
                    <th class="bg-light">تعداد نسخه‌ها</th>
                    <td>{{ patient.prescriptions.count }}</td>
                </tr>
            </table>
        </div>

        <form method="post">
            {% csrf_token %}
            <div class="text-end">
                <a href="{% url 'patient_list' %}" class="btn btn-secondary">
                    <i class="fas fa-times"></i> انصراف
                </a>
                <button type="submit" class="btn btn-danger">
                    <i class="fas fa-trash"></i> حذف
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %} 