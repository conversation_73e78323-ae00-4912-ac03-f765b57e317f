{% extends 'base.html' %}

{% block title %}اطلاعات بیمار{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header bg-white d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">اطلاعات بیمار</h5>
        <div>
            <a href="{% url 'patient_edit' patient.pk %}" class="btn btn-warning">
                <i class="fas fa-edit"></i> ویرایش
            </a>
            <a href="{% url 'patient_list' %}" class="btn btn-secondary">
                <i class="fas fa-arrow-right"></i> بازگشت به لیست
            </a>
        </div>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <div class="mb-4">
                    <h6 class="text-muted mb-2">اطلاعات شخصی</h6>
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <tr>
                                <th class="bg-light" style="width: 40%">نام و نام خانوادگی</th>
                                <td>{{ patient.first_name }} {{ patient.last_name }}</td>
                            </tr>
                            <tr>
                                <th class="bg-light">کد ملی</th>
                                <td>{{ patient.national_id }}</td>
                            </tr>
                            <tr>
                                <th class="bg-light">سن</th>
                                <td>{{ patient.age }}</td>
                            </tr>
                            <tr>
                                <th class="bg-light">شماره موبایل</th>
                                <td>{{ patient.mobile_number }}</td>
                            </tr>
                            <tr>
                                <th class="bg-light">آدرس</th>
                                <td>{{ patient.address|default:"-" }}</td>
                            </tr>
                            <tr>
                                <th class="bg-light">تاریخ ثبت نام</th>
                                <td>{{ patient.registration_date|date:"Y/m/d" }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-4">
                    <h6 class="text-muted mb-2">سوابق پزشکی</h6>
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <tr>
                                <th class="bg-light" style="width: 40%">تعداد ویزیت‌ها</th>
                                <td>{{ patient.visits.count }}</td>
                            </tr>
                            <tr>
                                <th class="bg-light">آخرین ویزیت</th>
                                <td>{{ patient.visits.last.visit_date|date:"Y/m/d"|default:"-" }}</td>
                            </tr>
                            <tr>
                                <th class="bg-light">تعداد نسخه‌ها</th>
                                <td>{{ patient.prescriptions.count }}</td>
                            </tr>
                            <tr>
                                <th class="bg-light">آخرین نسخه</th>
                                <td>{{ patient.prescriptions.last.created_at|date:"Y/m/d"|default:"-" }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Visits -->
        <div class="mt-4">
            <h6 class="text-muted mb-3">آخرین ویزیت‌ها</h6>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>تاریخ ویزیت</th>
                            <th>پزشک معالج</th>
                            <th>تشخیص</th>
                            <th>عملیات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for visit in patient.visits.all|slice:":5" %}
                        <tr>
                            <td>{{ visit.visit_date|date:"Y/m/d" }}</td>
                            <td>{{ visit.doctor.get_full_name }}</td>
                            <td>{{ visit.diagnosis|truncatechars:50 }}</td>
                            <td>
                                <a href="{% url 'visit_detail' visit.pk %}" class="btn btn-sm btn-info">
                                    <i class="fas fa-eye"></i> جزئیات
                                </a>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="4" class="text-center text-muted">
                                <i class="fas fa-info-circle"></i> هیچ ویزیتی ثبت نشده است
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %} 