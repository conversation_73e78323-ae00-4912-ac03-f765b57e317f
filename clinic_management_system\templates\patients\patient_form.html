{% extends 'base.html' %}

{% block title %}
    {% if form.instance.pk %}ویرایش بیمار{% else %}ثبت بیمار جدید{% endif %}
{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header bg-white d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">
            {% if form.instance.pk %}ویرایش بیمار{% else %}ثبت بیمار جدید{% endif %}
        </h5>
        <a href="{% url 'patient_list' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-right"></i> بازگشت به لیست
        </a>
    </div>
    <div class="card-body">
        <form method="post" novalidate>
            {% csrf_token %}
            
            {% if form.non_field_errors %}
            <div class="alert alert-danger">
                {% for error in form.non_field_errors %}
                    {{ error }}
                {% endfor %}
            </div>
            {% endif %}

            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="{{ form.first_name.id_for_label }}" class="form-label">{{ form.first_name.label }}</label>
                        {{ form.first_name }}
                        {% if form.first_name.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.first_name.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="{{ form.last_name.id_for_label }}" class="form-label">{{ form.last_name.label }}</label>
                        {{ form.last_name }}
                        {% if form.last_name.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.last_name.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="{{ form.national_id.id_for_label }}" class="form-label">{{ form.national_id.label }}</label>
                        {{ form.national_id }}
                        {% if form.national_id.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.national_id.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="{{ form.age.id_for_label }}" class="form-label">{{ form.age.label }}</label>
                        {{ form.age }}
                        {% if form.age.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.age.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="{{ form.mobile_number.id_for_label }}" class="form-label">{{ form.mobile_number.label }}</label>
                        {{ form.mobile_number }}
                        {% if form.mobile_number.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.mobile_number.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <div class="mb-3">
                <label for="{{ form.address.id_for_label }}" class="form-label">{{ form.address.label }}</label>
                {{ form.address }}
                {% if form.address.errors %}
                <div class="invalid-feedback d-block">
                    {% for error in form.address.errors %}
                        {{ error }}
                    {% endfor %}
                </div>
                {% endif %}
            </div>

            <div class="text-end">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> ذخیره
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %} 