{% extends 'base.html' %}

{% block title %}لیست بیماران{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header bg-white d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">لیست بیماران</h5>
        <a href="{% url 'patient_create' %}" class="btn btn-primary">
            <i class="fas fa-user-plus"></i> ثبت بیمار جدید
        </a>
    </div>
    <div class="card-body">
        <!-- Search Form -->
        <form method="get" class="mb-4">
            <div class="input-group">
                <input type="text" name="search" class="form-control" placeholder="جستجو بر اساس نام، نام خانوادگی، کد ملی یا شماره موبایل..." value="{{ search_query }}">
                <button class="btn btn-primary" type="submit">
                    <i class="fas fa-search"></i> جستجو
                </button>
            </div>
        </form>

        <!-- Patients Table -->
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>نام و نام خانوادگی</th>
                        <th>کد ملی</th>
                        <th>سن</th>
                        <th>شماره موبایل</th>
                        <th>تاریخ ثبت نام</th>
                        <th>عملیات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for patient in patients %}
                    <tr>
                        <td>{{ patient.first_name }} {{ patient.last_name }}</td>
                        <td>{{ patient.national_id }}</td>
                        <td>{{ patient.age }}</td>
                        <td>{{ patient.mobile_number }}</td>
                        <td>{{ patient.registration_date|date:"Y/m/d" }}</td>
                        <td>
                            <div class="btn-group">
                                <a href="{% url 'patient_detail' patient.pk %}" class="btn btn-sm btn-info">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{% url 'patient_edit' patient.pk %}" class="btn btn-sm btn-warning">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="{% url 'patient_delete' patient.pk %}" class="btn btn-sm btn-danger">
                                    <i class="fas fa-trash"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="6" class="text-center text-muted">
                            <i class="fas fa-info-circle"></i> هیچ بیمارى ثبت نشده است
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %} 