{% extends "base.html" %}
{% load i18n %} {# For translating text if your base template uses it #}
{# Removed crispy_forms_tags load #}

{% block title %}{% trans "ثبت فروش مستقیم جدید" %}{% endblock %}

{% block content %}
<div class="container mt-4">
    <h2>{% trans "ثبت فروش مستقیم جدید" %}</h2>

    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        {% endfor %}
    {% endif %}

    <form method="post" novalidate>
        {% csrf_token %}
        
        <div class="card mb-3">
            <div class="card-header">{% trans "اطلاعات کلی فروش" %}</div>
            <div class="card-body">
                {% if main_form.non_field_errors %}
                    <div class="alert alert-danger">
                        {{ main_form.non_field_errors }}
                    </div>
                {% endif %}
                {{ main_form.as_p }} {# Changed to standard Django form rendering #}
            </div>
        </div>

        <div class="card">
            <div class="card-header">{% trans "اقلام فروش" %}</div>
            <div class="card-body">
                {{ item_formset.management_form }}
                {% if item_formset.non_form_errors %}
                    <div class="alert alert-danger">
                        {{ item_formset.non_form_errors }}
                    </div>
                {% endif %}

                <table class="table table-sm table-bordered"> {# Added table-bordered for clarity #}
                    <thead class="table-light"> {# Added a header style #}
                        <tr>
                            <th style="width: 60%;">{% trans "دارو" %}</th>
                            <th style="width: 25%;">{% trans "تعداد" %}</th>
                            {% if item_formset.can_delete %}<th style="width: 15%;">{% trans "حذف؟" %}</th>{% endif %}
                        </tr>
                    </thead>
                    <tbody>
                        {% for form in item_formset %}
                            <tr class="item-form">
                                {% for hidden_field in form.hidden_fields %}
                                     {{ hidden_field }}
                                {% endfor %}
                                <td>
                                    {{ form.drug }} {# Removed as_crispy_field #}
                                    {% if form.drug.errors %}<div class="text-danger small">{{ form.drug.errors|join:", " }}</div>{% endif %} {# Adjusted error display slightly #}
                                </td>
                                <td>
                                    {{ form.quantity }} {# Removed as_crispy_field #}
                                    {% if form.quantity.errors %}<div class="text-danger small">{{ form.quantity.errors|join:", " }}</div>{% endif %} {# Adjusted error display slightly #}
                                </td>
                                {% if item_formset.can_delete %}
                                <td>
                                    {% if form.instance.pk or formset.can_delete_extra %}
                                     {{ form.DELETE }} {# Removed as_crispy_field #}
                                    {% endif %}
                                </td>
                                {% endif %}
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
                {% if item_formset.forms|length < item_formset.max_num or not item_formset.max_num %}
                {# Placeholder for "Add another item" button if using JS #}
                {% endif %}
                <small class="form-text text-muted">
                    {% if item_formset.min_num == 1 %}
                        {% blocktrans %}حداقل یک قلم دارو باید اضافه شود.{% endblocktrans %}
                    {% elif item_formset.min_num > 1 %}
                        {% blocktrans with count=item_formset.min_num %}حداقل {{ count }} قلم دارو باید اضافه شود.{% endblocktrans %}
                    {% endif %}
                </small>
            </div>
        </div>
        
        <div class="mt-3">
            <button type="submit" class="btn btn-primary">{% trans "ثبت فروش" %}</button>
            <a href="{% url 'dashboard' %}" class="btn btn-secondary">{% trans "انصراف" %}</a> {# Adjust link as needed, assuming 'dashboard' is a valid URL name #}
        </div>
    </form>
</div>

{% comment %}
Add JavaScript here for dynamically adding/removing forms if needed,
beyond the 'extra' forms provided by the formset.
Example for adding forms (requires more setup):
<script>
// Basic JavaScript to handle adding more forms for the formset
// This is a simplified example and might need adjustments based on your specific setup
// and if you're using libraries like jQuery.
document.addEventListener('DOMContentLoaded', function() {
    // This part is usually handled by Django's formset rendering for 'extra' forms.
    // For dynamic "add another item" button, you'd clone an empty form template.
    // A full implementation of dynamic formset additions is more involved.
});
</script>
{% endcomment %}

{% endblock %}
