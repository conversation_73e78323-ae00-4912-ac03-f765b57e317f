{% extends 'base.html' %}

{% block title %}اطلاعات نسخه{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header bg-white d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">اطلاعات نسخه</h5>
        <div>
            {% if not prescription.dispensed %}
            <a href="{% url 'pharmacy:dispense_prescription' prescription.pk %}" class="btn btn-success">
                <i class="fas fa-pills"></i> صدور نسخه
            </a>
            {% endif %}
            <a href="{% url 'pharmacy:pharmacy_list' %}" class="btn btn-secondary">
                <i class="fas fa-arrow-right"></i> بازگشت به لیست
            </a>
        </div>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <div class="mb-4">
                    <h6 class="text-muted mb-3">اطلاعات نسخه</h6>
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <tr>
                                <th class="bg-light" style="width: 40%">تاریخ نسخه</th>
                                <td>{{ prescription.created_at|date:"Y/m/d H:i" }}</td>
                            </tr>
                            <tr>
                                <th class="bg-light">وضعیت</th>
                                <td>
                                    {% if prescription.is_dispensed %}
                                    <span class="badge bg-success">صادر شده</span>
                                    {% else %}
                                    <span class="badge bg-warning">در انتظار صدور</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <th class="bg-light">مجموع هزینه</th>
                                <td>{{ prescription.total_cost }} ریال</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-4">
                    <h6 class="text-muted mb-3">اطلاعات ویزیت</h6>
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <tr>
                                <th class="bg-light" style="width: 40%">بیمار</th>
                                <td>
                                    <a href="{% url 'patient_detail' prescription.visit.patient.pk %}">
                                        {{ prescription.visit.patient.first_name }} {{ prescription.visit.patient.last_name }}
                                    </a>
                                </td>
                            </tr>
                            <tr>
                                <th class="bg-light">تاریخ ویزیت</th>
                                <td>{{ prescription.visit.visit_datetime|date:"Y/m/d H:i" }}</td>
                            </tr>
                            <tr>
                                <th class="bg-light">پزشک معالج</th>
                                <td>{{ prescription.visit.doctor.get_full_name }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Prescribed Drugs -->
        <div class="mt-4">
            <h6 class="text-muted mb-3">داروهای تجویز شده</h6>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>نام دارو</th>
                            <th>تعداد/مقدار</th>
                            <th>دستور مصرف</th>
                            <th>قیمت واحد</th>
                            <th>قیمت کل</th>
                            <th>وضعیت موجودی</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for item in prescription.prescribed_items.all %}
                        <tr>
                            <td>{{ item.drug.name }}</td>
                            <td>{{ item.quantity }}</td>
                            <td>{{ item.dosage_instructions }}</td>
                            <td>{{ item.drug.price }} ریال</td>
                            <td>{{ item.cost }} ریال</td>
                            <td>
                                {% if item.drug.stock >= item.quantity %}
                                <span class="badge bg-success">موجود</span>
                                {% else %}
                                <span class="badge bg-danger">ناموجود</span>
                                {% endif %}
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="6" class="text-center text-muted">
                                <i class="fas fa-info-circle"></i> هیچ دارویی در این نسخه ثبت نشده است
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                    <tfoot>
                        <tr>
                            <th colspan="4" class="text-end">مجموع کل:</th>
                            <th colspan="2">{{ prescription.total_cost }} ریال</th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}