{% extends 'base.html' %}

{% block title %}صدور نسخه{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header bg-white d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">صدور نسخه</h5>
        <a href="{% url 'pharmacy:prescription_detail' prescription.pk %}" class="btn btn-secondary">
            <i class="fas fa-arrow-right"></i> بازگشت به نسخه
        </a>
    </div>
    <div class="card-body">
        <div class="alert alert-info">
            <h5 class="alert-heading">اطلاعات نسخه</h5>
            <p>در حال صدور نسخه برای {{ prescription.visit.patient.first_name }} {{ prescription.visit.patient.last_name }}</p>
            <hr>
            <p class="mb-0">لطفا قبل از صدور نسخه، موجودی داروها را بررسی کنید.</p>
        </div>

        <div class="table-responsive mb-4">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>نام دارو</th>
                        <th>تعداد/مقدار</th>
                        <th>موجودی فعلی</th>
                        <th>وضعیت</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in prescription.prescribed_items.all %}
                    <tr>
                        <td>{{ item.drug.name }}</td>
                        <td>{{ item.quantity }}</td>
                        <td>{{ item.drug.stock }}</td>
                        <td>
                            {% if item.drug.stock >= item.quantity %}
                            <span class="badge bg-success">موجود</span>
                            {% else %}
                            <span class="badge bg-danger">ناموجود</span>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <form method="post">
            {% csrf_token %}
            <div class="text-end">
                <a href="{% url 'pharmacy:prescription_detail' prescription.pk %}" class="btn btn-secondary">
                    <i class="fas fa-times"></i> انصراف
                </a>
                <button type="submit" class="btn btn-success">
                    <i class="fas fa-pills"></i> صدور نسخه
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %} 