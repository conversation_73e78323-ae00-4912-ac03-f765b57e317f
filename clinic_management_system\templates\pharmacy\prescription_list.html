{% extends 'base.html' %}

{% block title %}لیست نسخه‌ها{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header bg-white d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">لیست نسخه‌ها</h5>
    </div>
    <div class="card-body">
        <!-- Search Form -->
        <form method="get" class="mb-4">
            <div class="input-group">
                <input type="text" name="search" class="form-control" placeholder="جستجو در نام یا کد ملی بیمار..." value="{{ search_query }}">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search"></i> جستجو
                </button>
            </div>
        </form>

        <!-- Prescriptions Table -->
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>تاریخ نسخه</th>
                        <th>بیمار</th>
                        <th>پزشک</th>
                        <th>وضعیت</th>
                        <th>عملیات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for prescription in prescriptions %}
                    <tr>
                        <td>{{ prescription.created_at|date:"Y/m/d H:i" }}</td>
                        <td>
                            <a href="{% url 'patient_detail' prescription.visit.patient.pk %}" class="text-decoration-none">
                                {{ prescription.visit.patient.first_name }} {{ prescription.visit.patient.last_name }}
                            </a>
                        </td>
                        <td>{{ prescription.visit.doctor.get_full_name }}</td>
                        <td>
                            {% if prescription.dispensed %}
                            <span class="badge bg-success">صادر شده</span>
                            {% else %}
                            <span class="badge bg-warning">در انتظار صدور</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group">
                                <a href="{% url 'pharmacy:prescription_detail' prescription.pk %}" class="btn btn-sm btn-info">
                                    <i class="fas fa-eye"></i>
                                </a>
                                {% if not prescription.dispensed %}
                                <a href="{% url 'pharmacy:dispense_prescription' prescription.pk %}" class="btn btn-sm btn-success">
                                    <i class="fas fa-pills"></i>
                                </a>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="5" class="text-center text-muted">
                            <i class="fas fa-info-circle"></i> هیچ نسخه‌ای ثبت نشده است
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}