{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "لغو نوبت" %}{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header bg-white">
        <h5 class="card-title mb-0">{% trans "لغو نوبت" %}</h5>
    </div>
    <div class="card-body">
        <div class="alert alert-warning">
            <h5 class="alert-heading">
                <i class="fas fa-exclamation-triangle"></i> {% trans "هشدار!" %}
            </h5>
            <p>{% trans "آیا از لغو این نوبت اطمینان دارید؟" %}</p>
            <hr>
            <p class="mb-0">{% trans "این عملیات غیرقابل بازگشت است." %}</p>
        </div>
        
        <div class="table-responsive mb-4">
            <table class="table table-bordered">
                <tr>
                    <th class="bg-light" style="width: 30%">{% trans "بیمار" %}</th>
                    <td>{{ appointment.patient.first_name }} {{ appointment.patient.last_name }}</td>
                </tr>
                <tr>
                    <th class="bg-light">{% trans "پزشک" %}</th>
                    <td>{{ appointment.doctor.get_full_name }}</td>
                </tr>
                <tr>
                    <th class="bg-light">{% trans "تاریخ و زمان نوبت" %}</th>
                    <td>{{ appointment.appointment_datetime|date:"Y/m/d H:i" }}</td>
                </tr>
                <tr>
                    <th class="bg-light">{% trans "دلیل مراجعه" %}</th>
                    <td>{{ appointment.reason|default:"-" }}</td>
                </tr>
            </table>
        </div>
        
        <form method="post">
            {% csrf_token %}
            <div class="d-flex justify-content-end">
                <a href="{% url 'appointment_detail' appointment.pk %}" class="btn btn-secondary me-2">
                    <i class="fas fa-times"></i> {% trans "انصراف" %}
                </a>
                <button type="submit" class="btn btn-danger">
                    <i class="fas fa-check"></i> {% trans "بله، نوبت لغو شود" %}
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}
