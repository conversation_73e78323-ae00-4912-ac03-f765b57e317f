{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "جزئیات نوبت" %}{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header bg-white d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">{% trans "جزئیات نوبت" %}</h5>
        <div>
            {% if appointment.status == 'scheduled' %}
                {% if user.role == 'doctor' and appointment.doctor == user %}
                <a href="{% url 'appointment_to_visit' appointment.pk %}" class="btn btn-success">
                    <i class="fas fa-check"></i> {% trans "تبدیل به ویزیت" %}
                </a>
                {% endif %}
                <a href="{% url 'appointment_edit' appointment.pk %}" class="btn btn-warning">
                    <i class="fas fa-edit"></i> {% trans "ویرایش" %}
                </a>
                <a href="{% url 'appointment_cancel' appointment.pk %}" class="btn btn-danger">
                    <i class="fas fa-times"></i> {% trans "لغو نوبت" %}
                </a>
            {% endif %}
            <a href="{% url 'appointment_list' %}" class="btn btn-secondary">
                <i class="fas fa-arrow-right"></i> {% trans "بازگشت به لیست" %}
            </a>
        </div>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <div class="mb-4">
                    <h6 class="text-muted mb-2">{% trans "اطلاعات نوبت" %}</h6>
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <tr>
                                <th class="bg-light" style="width: 40%">{% trans "شماره نوبت" %}</th>
                                <td>{{ appointment.pk }}</td>
                            </tr>
                            <tr>
                                <th class="bg-light">{% trans "تاریخ و زمان نوبت" %}</th>
                                <td>{{ appointment.appointment_datetime|date:"Y/m/d H:i" }}</td>
                            </tr>
                            <tr>
                                <th class="bg-light">{% trans "وضعیت" %}</th>
                                <td>
                                    <span class="badge bg-{{ appointment.status_color }}">
                                        {{ appointment.get_status_display }}
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <th class="bg-light">{% trans "تاریخ ایجاد" %}</th>
                                <td>{{ appointment.created_at|date:"Y/m/d H:i" }}</td>
                            </tr>
                            <tr>
                                <th class="bg-light">{% trans "آخرین بروزرسانی" %}</th>
                                <td>{{ appointment.updated_at|date:"Y/m/d H:i" }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-4">
                    <h6 class="text-muted mb-2">{% trans "اطلاعات بیمار و پزشک" %}</h6>
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <tr>
                                <th class="bg-light" style="width: 40%">{% trans "نام بیمار" %}</th>
                                <td>
                                    <a href="{% url 'patient_detail' appointment.patient.pk %}">
                                        {{ appointment.patient.first_name }} {{ appointment.patient.last_name }}
                                    </a>
                                </td>
                            </tr>
                            <tr>
                                <th class="bg-light">{% trans "کد ملی بیمار" %}</th>
                                <td>{{ appointment.patient.national_id }}</td>
                            </tr>
                            <tr>
                                <th class="bg-light">{% trans "شماره تماس بیمار" %}</th>
                                <td>{{ appointment.patient.mobile_number }}</td>
                            </tr>
                            <tr>
                                <th class="bg-light">{% trans "پزشک" %}</th>
                                <td>{{ appointment.doctor.get_full_name }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <div class="mb-4">
                    <h6 class="text-muted mb-2">{% trans "اطلاعات تکمیلی" %}</h6>
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <tr>
                                <th class="bg-light" style="width: 20%">{% trans "دلیل مراجعه" %}</th>
                                <td>{{ appointment.reason|default:"-" }}</td>
                            </tr>
                            <tr>
                                <th class="bg-light">{% trans "یادداشت‌ها" %}</th>
                                <td>{{ appointment.notes|default:"-" }}</td>
                            </tr>
                            {% if appointment.visit %}
                            <tr>
                                <th class="bg-light">{% trans "ویزیت مرتبط" %}</th>
                                <td>
                                    <a href="{% url 'visit_detail' appointment.visit.pk %}" class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i> {% trans "مشاهده ویزیت" %}
                                    </a>
                                </td>
                            </tr>
                            {% endif %}
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
