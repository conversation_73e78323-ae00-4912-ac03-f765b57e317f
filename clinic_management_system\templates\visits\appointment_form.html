{% extends 'base.html' %}
{% load i18n %}

{% block title %}
    {% if form.instance.pk %}{% trans "ویرایش نوبت" %}{% else %}{% trans "ثبت نوبت جدید" %}{% endif %}
{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header bg-white d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">
            {% if form.instance.pk %}{% trans "ویرایش نوبت" %}{% else %}{% trans "ثبت نوبت جدید" %}{% endif %}
        </h5>
        <a href="{% url 'appointment_list' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-right"></i> {% trans "بازگشت به لیست" %}
        </a>
    </div>
    <div class="card-body">
        <form method="post" novalidate>
            {% csrf_token %}
            
            {% if form.non_field_errors %}
            <div class="alert alert-danger">
                {% for error in form.non_field_errors %}
                    {{ error }}
                {% endfor %}
            </div>
            {% endif %}
            
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header bg-light">{% trans "اطلاعات بیمار و پزشک" %}</div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label for="{{ form.patient.id_for_label }}" class="form-label">{{ form.patient.label }}</label>
                                {{ form.patient }}
                                {% if form.patient.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.patient.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                            
                            <div class="mb-3">
                                <label for="{{ form.doctor.id_for_label }}" class="form-label">{{ form.doctor.label }}</label>
                                {{ form.doctor }}
                                {% if form.doctor.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.doctor.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header bg-light">{% trans "زمان نوبت" %}</div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label for="{{ form.appointment_date.id_for_label }}" class="form-label">{{ form.appointment_date.label }}</label>
                                {{ form.appointment_date }}
                                {% if form.appointment_date.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.appointment_date.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                            
                            <div class="mb-3">
                                <label for="{{ form.appointment_time.id_for_label }}" class="form-label">{{ form.appointment_time.label }}</label>
                                {{ form.appointment_time }}
                                {% if form.appointment_time.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.appointment_time.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-4">
                <div class="card-header bg-light">{% trans "اطلاعات تکمیلی" %}</div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="{{ form.reason.id_for_label }}" class="form-label">{{ form.reason.label }}</label>
                        {{ form.reason }}
                        {% if form.reason.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.reason.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.notes.id_for_label }}" class="form-label">{{ form.notes.label }}</label>
                        {{ form.notes }}
                        {% if form.notes.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.notes.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <div class="text-end">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> {% trans "ذخیره" %}
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        $('.select2').select2({
            theme: 'bootstrap-5',
            placeholder: '{% trans "انتخاب کنید" %}',
            allowClear: true
        });
    });
</script>
{% endblock %}
