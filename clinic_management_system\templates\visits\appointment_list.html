{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "لیست نوبت‌ها" %}{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header bg-white d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">{% trans "لیست نوبت‌ها" %}</h5>
        <div>
            <a href="{% url 'appointment_create' %}" class="btn btn-primary">
                <i class="fas fa-plus"></i> {% trans "ثبت نوبت جدید" %}
            </a>
            <a href="{% url 'doctor_schedule' %}" class="btn btn-info">
                <i class="fas fa-calendar-alt"></i> {% trans "مشاهده برنامه زمانی پزشکان" %}
            </a>
        </div>
    </div>
    <div class="card-body">
        <!-- فیلترها و جستجو -->
        <form method="get" class="mb-4">
            <div class="row g-3">
                <div class="col-md-4">
                    <div class="input-group">
                        <input type="text" name="search" class="form-control" placeholder="{% trans 'جستجو...' %}" value="{{ search_query }}">
                        <button class="btn btn-primary" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
                <div class="col-md-3">
                    <input type="date" name="date" class="form-control" value="{{ date_filter }}" placeholder="{% trans 'تاریخ' %}">
                </div>
                <div class="col-md-3">
                    <select name="status" class="form-select">
                        <option value="">{% trans "همه وضعیت‌ها" %}</option>
                        <option value="scheduled" {% if status_filter == 'scheduled' %}selected{% endif %}>{% trans "رزرو شده" %}</option>
                        <option value="completed" {% if status_filter == 'completed' %}selected{% endif %}>{% trans "تکمیل شده" %}</option>
                        <option value="cancelled" {% if status_filter == 'cancelled' %}selected{% endif %}>{% trans "لغو شده" %}</option>
                        <option value="no_show" {% if status_filter == 'no_show' %}selected{% endif %}>{% trans "عدم مراجعه" %}</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <button type="submit" class="btn btn-primary w-100">{% trans "فیلتر" %}</button>
                </div>
            </div>
        </form>

        <!-- جدول نوبت‌ها -->
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>{% trans "تاریخ و زمان" %}</th>
                        <th>{% trans "بیمار" %}</th>
                        <th>{% trans "پزشک" %}</th>
                        <th>{% trans "دلیل مراجعه" %}</th>
                        <th>{% trans "وضعیت" %}</th>
                        <th>{% trans "عملیات" %}</th>
                    </tr>
                </thead>
                <tbody>
                    {% for appointment in appointments %}
                    <tr>
                        <td>{{ appointment.appointment_datetime|date:"Y/m/d H:i" }}</td>
                        <td>
                            <a href="{% url 'patient_detail' appointment.patient.pk %}">
                                {{ appointment.patient.first_name }} {{ appointment.patient.last_name }}
                            </a>
                        </td>
                        <td>{{ appointment.doctor.get_full_name }}</td>
                        <td>{{ appointment.reason|truncatechars:50|default:"-" }}</td>
                        <td>
                            <span class="badge bg-{{ appointment.status_color }}">
                                {{ appointment.get_status_display }}
                            </span>
                        </td>
                        <td>
                            <div class="btn-group">
                                <a href="{% url 'appointment_detail' appointment.pk %}" class="btn btn-sm btn-info">
                                    <i class="fas fa-eye"></i>
                                </a>
                                {% if appointment.status == 'scheduled' %}
                                    <a href="{% url 'appointment_edit' appointment.pk %}" class="btn btn-sm btn-warning">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="{% url 'appointment_cancel' appointment.pk %}" class="btn btn-sm btn-danger">
                                        <i class="fas fa-times"></i>
                                    </a>
                                    {% if user.role == 'doctor' and appointment.doctor == user %}
                                    <a href="{% url 'appointment_to_visit' appointment.pk %}" class="btn btn-sm btn-success">
                                        <i class="fas fa-check"></i> {% trans "تبدیل به ویزیت" %}
                                    </a>
                                    {% endif %}
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="6" class="text-center py-4">
                            <div class="text-muted">
                                <i class="fas fa-calendar-times fa-3x mb-3"></i>
                                <p>{% trans "هیچ نوبتی یافت نشد." %}</p>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}
