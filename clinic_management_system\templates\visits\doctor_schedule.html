{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "برنامه زمانی پزشکان" %}{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header bg-white d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">{% trans "برنامه زمانی پزشکان" %}</h5>
        <a href="{% url 'appointment_list' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-right"></i> {% trans "بازگشت به لیست نوبت‌ها" %}
        </a>
    </div>
    <div class="card-body">
        <!-- فیلترها -->
        <form method="get" class="mb-4">
            <div class="row g-3">
                <div class="col-md-5">
                    <label for="doctor" class="form-label">{% trans "انتخاب پزشک" %}</label>
                    <select name="doctor" id="doctor" class="form-select" required>
                        <option value="">{% trans "لطفاً پزشک را انتخاب کنید" %}</option>
                        {% for doctor in doctors %}
                            <option value="{{ doctor.pk }}" {% if selected_doctor == doctor.pk|stringformat:"s" %}selected{% endif %}>
                                {{ doctor.get_full_name }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-5">
                    <label for="date" class="form-label">{% trans "انتخاب تاریخ" %}</label>
                    <input type="date" name="date" id="date" class="form-control" 
                           value="{{ selected_date|date:'Y-m-d' }}" 
                           min="{{ today|date:'Y-m-d' }}" required>
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search"></i> {% trans "نمایش" %}
                    </button>
                </div>
            </div>
        </form>
        
        {% if selected_doctor %}
            <div class="card">
                <div class="card-header bg-light">
                    {% trans "برنامه زمانی برای تاریخ" %}: {{ selected_date|date:"Y/m/d" }}
                </div>
                <div class="card-body">
                    {% if schedule %}
                        <div class="row row-cols-2 row-cols-md-4 row-cols-lg-6 g-3">
                            {% for slot in schedule %}
                                <div class="col">
                                    <div class="card h-100 {% if slot.is_booked %}bg-light{% endif %}">
                                        <div class="card-body text-center p-3">
                                            <h5 class="card-title mb-3">{{ slot.time }}</h5>
                                            {% if slot.is_booked %}
                                                <span class="badge bg-danger">{% trans "رزرو شده" %}</span>
                                            {% else %}
                                                <a href="{{ slot.appointment_url }}" class="btn btn-sm btn-success">
                                                    <i class="fas fa-calendar-plus"></i> {% trans "رزرو" %}
                                                </a>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i> {% trans "لطفاً پزشک و تاریخ را انتخاب کنید." %}
                        </div>
                    {% endif %}
                </div>
            </div>
        {% else %}
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i> {% trans "لطفاً پزشک و تاریخ را انتخاب کنید تا برنامه زمانی نمایش داده شود." %}
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}
