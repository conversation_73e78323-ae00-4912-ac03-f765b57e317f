{% extends 'base.html' %}

{% block title %}ثبت نسخه جدید{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header bg-white d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">ثبت نسخه جدید</h5>
        <a href="{% url 'visit_detail' visit.pk %}" class="btn btn-secondary">
            <i class="fas fa-arrow-right"></i> بازگشت به ویزیت
        </a>
    </div>
    <div class="card-body">
        <form method="post" id="prescription-form">
            {% csrf_token %}
            <input type="hidden" name="visit" value="{{ visit.pk }}">

            <div class="mb-4">
                <h6 class="text-muted mb-3">اطلاعات ویزیت</h6>
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <tr>
                            <th class="bg-light" style="width: 40%">بیمار</th>
                            <td>{{ visit.patient.first_name }} {{ visit.patient.last_name }}</td>
                        </tr>
                        <tr>
                            <th class="bg-light">تاریخ ویزیت</th>
                            <td>{{ visit.visit_datetime|date:"Y/m/d H:i" }}</td>
                        </tr>
                        <tr>
                            <th class="bg-light">پزشک معالج</th>
                            <td>{{ visit.doctor.get_full_name }}</td>
                        </tr>
                    </table>
                </div>
            </div>

            <div class="mb-4">
                <h6 class="text-muted mb-3">داروهای تجویز شده</h6>
                <div id="drugs-container">
                    <div class="drug-item mb-3 p-3 border rounded">
                        <div class="row">
                            <div class="col-md-4">
                                <label class="form-label">دارو</label>
                                <select name="drugs" class="form-control select2" required>
                                    <option value="">انتخاب دارو</option>
                                    {% for drug in drugs %}
                                    <option value="{{ drug.pk }}" {% if drug.stock <= 0 %}disabled{% endif %} data-stock="{{ drug.stock }}">
                                        {{ drug.name }}
                                        {% if drug.stock <= 0 %}
                                        (ناموجود)
                                        {% elif drug.is_low_stock %}
                                        (موجودی کم: {{ drug.stock }} عدد)
                                        {% else %}
                                        (موجود: {{ drug.stock }} عدد)
                                        {% endif %}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">تعداد/مقدار</label>
                                <input type="number" name="quantities" class="form-control" min="1" required>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">دستور مصرف</label>
                                <input type="text" name="instructions" class="form-control" required>
                            </div>
                            <div class="col-md-1 d-flex align-items-end">
                                <button type="button" class="btn btn-danger remove-drug" style="display: none;">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <button type="button" class="btn btn-success mt-2" id="add-drug">
                    <i class="fas fa-plus"></i> افزودن دارو
                </button>
            </div>

            <div class="text-end">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> ذخیره نسخه
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // Initialize Select2
        $('.select2').select2({
            theme: 'bootstrap-5',
            placeholder: 'انتخاب دارو',
            allowClear: true
        });

        // Add new drug row
        $('#add-drug').click(function() {
            var newRow = $('.drug-item:first').clone();
            newRow.find('input').val('');
            newRow.find('select').val('').trigger('change');
            newRow.find('.remove-drug').show();
            $('#drugs-container').append(newRow);
            newRow.find('.select2').select2({
                theme: 'bootstrap-5',
                placeholder: 'انتخاب دارو',
                allowClear: true
            });
        });

        // Remove drug row
        $(document).on('click', '.remove-drug', function() {
            $(this).closest('.drug-item').remove();
        });

        // Validate quantity against stock
        $(document).on('change', 'select[name="drugs"]', function() {
            var selectedOption = $(this).find('option:selected');
            var stock = selectedOption.data('stock');
            var quantityInput = $(this).closest('.drug-item').find('input[name="quantities"]');

            quantityInput.attr('max', stock);
            quantityInput.attr('data-original-stock', stock);

            if (stock > 0) {
                quantityInput.after('<small class="form-text text-muted stock-info">موجودی: ' + stock + ' عدد</small>');
            } else {
                quantityInput.after('<small class="form-text text-danger stock-info">این دارو ناموجود است</small>');
            }
        });

        $(document).on('change', 'input[name="quantities"]', function() {
            var value = parseInt($(this).val());
            var max = parseInt($(this).attr('max'));

            if (value > max) {
                alert('تعداد وارد شده بیشتر از موجودی است. حداکثر تعداد مجاز: ' + max);
                $(this).val(max);
            }
        });

        // Form validation before submit
        $('#prescription-form').on('submit', function(e) {
            var isValid = true;

            // Check if at least one drug is selected
            if ($('select[name="drugs"]').length === 0 || $('select[name="drugs"]').first().val() === '') {
                alert('لطفاً حداقل یک دارو انتخاب کنید.');
                isValid = false;
            }

            // Check quantities against stock
            $('input[name="quantities"]').each(function() {
                var value = parseInt($(this).val());
                var max = parseInt($(this).attr('max'));

                if (value > max) {
                    alert('تعداد وارد شده برای یکی از داروها بیشتر از موجودی است.');
                    isValid = false;
                    return false;
                }
            });

            return isValid;
        });
    });
</script>
{% endblock %}