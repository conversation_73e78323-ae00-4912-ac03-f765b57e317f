{% extends 'base.html' %}

{% block title %}حذف ویزیت{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header bg-white">
        <h5 class="card-title mb-0">حذف ویزیت</h5>
    </div>
    <div class="card-body">
        <div class="alert alert-danger">
            <h5 class="alert-heading">
                <i class="fas fa-exclamation-triangle"></i> هشدار!
            </h5>
            <p class="mb-0">
                آیا از حذف ویزیت بیمار "{{ visit.patient.first_name }} {{ visit.patient.last_name }}" در تاریخ {{ visit.visit_date|date:"Y/m/d" }} اطمینان دارید؟
                این عمل غیرقابل بازگشت است و تمام اطلاعات مرتبط با این ویزیت نیز حذف خواهند شد.
            </p>
        </div>

        <div class="table-responsive mb-4">
            <table class="table table-bordered">
                <tr>
                    <th class="bg-light" style="width: 40%">بیمار</th>
                    <td>{{ visit.patient.first_name }} {{ visit.patient.last_name }}</td>
                </tr>
                <tr>
                    <th class="bg-light">تاریخ ویزیت</th>
                    <td>{{ visit.visit_date|date:"Y/m/d" }}</td>
                </tr>
                <tr>
                    <th class="bg-light">پزشک معالج</th>
                    <td>{{ visit.doctor.get_full_name }}</td>
                </tr>
                <tr>
                    <th class="bg-light">تعداد نسخه‌ها</th>
                    <td>{{ visit.prescriptions.count }}</td>
                </tr>
            </table>
        </div>

        <form method="post">
            {% csrf_token %}
            <div class="text-end">
                <a href="{% url 'visit_list' %}" class="btn btn-secondary">
                    <i class="fas fa-times"></i> انصراف
                </a>
                <button type="submit" class="btn btn-danger">
                    <i class="fas fa-trash"></i> حذف
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %} 