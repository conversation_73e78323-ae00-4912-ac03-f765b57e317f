{% extends 'base.html' %}

{% block title %}اطلاعات ویزیت{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header bg-white d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">اطلاعات ویزیت</h5>
        <div>
            <a href="{% url 'visit_edit' visit.pk %}" class="btn btn-warning">
                <i class="fas fa-edit"></i> ویرایش
            </a>
            <a href="{% url 'visit_list' %}" class="btn btn-secondary">
                <i class="fas fa-arrow-right"></i> بازگشت به لیست
            </a>
        </div>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <div class="mb-4">
                    <h6 class="text-muted mb-2">اطلاعات ویزیت</h6>
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <tr>
                                <th class="bg-light" style="width: 40%">تاریخ و زمان ویزیت</th>
                                <td>{{ visit.visit_datetime|date:"Y/m/d H:i" }}</td>
                            </tr>
                            <tr>
                                <th class="bg-light">پزشک معالج</th>
                                <td>{{ visit.doctor.get_full_name }}</td>
                            </tr>
                            <tr>
                                <th class="bg-light">علائم بیمار</th>
                                <td>{{ visit.symptoms }}</td>
                            </tr>
                            <tr>
                                <th class="bg-light">تشخیص</th>
                                <td>{{ visit.diagnosis }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-4">
                    <h6 class="text-muted mb-2">اطلاعات بیمار</h6>
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <tr>
                                <th class="bg-light" style="width: 40%">نام و نام خانوادگی</th>
                                <td>
                                    <a href="{% url 'patient_detail' visit.patient.pk %}">
                                        {{ visit.patient.first_name }} {{ visit.patient.last_name }}
                                    </a>
                                </td>
                            </tr>
                            <tr>
                                <th class="bg-light">کد ملی</th>
                                <td>{{ visit.patient.national_id }}</td>
                            </tr>
                            <tr>
                                <th class="bg-light">سن</th>
                                <td>{{ visit.patient.age }}</td>
                            </tr>
                            <tr>
                                <th class="bg-light">شماره موبایل</th>
                                <td>{{ visit.patient.mobile_number }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Prescriptions -->
        <div class="mt-4">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h6 class="text-muted mb-0">نسخه‌های تجویز شده</h6>
                {% if user.role == 'doctor' %}
                <a href="{% url 'prescription_create' %}?visit={{ visit.pk }}" class="btn btn-primary btn-sm">
                    <i class="fas fa-prescription-bottle-alt"></i> تجویز دارو
                </a>
                {% endif %}
            </div>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>تاریخ تجویز</th>
                            <th>داروها</th>
                            <th>دستور مصرف</th>
                            <th>عملیات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% if visit.prescription %}
                        <tr>
                            <td>{{ visit.prescription.created_at|date:"Y/m/d" }}</td>
                            <td>
                                {% for item in visit.prescription.prescribed_items.all %}
                                {{ item.drug.name }} ({{ item.quantity }}){% if not forloop.last %}, {% endif %}
                                {% endfor %}
                            </td>
                            <td>
                                {% for item in visit.prescription.prescribed_items.all %}
                                {{ item.dosage_instructions }}{% if not forloop.last %}<br>{% endif %}
                                {% endfor %}
                            </td>
                            <td>
                                <a href="{% url 'prescription_detail' visit.prescription.pk %}" class="btn btn-sm btn-info">
                                    <i class="fas fa-eye"></i> جزئیات
                                </a>
                            </td>
                        </tr>
                        {% else %}
                        <tr>
                            <td colspan="4" class="text-center text-muted">
                                <i class="fas fa-info-circle"></i> هیچ نسخه‌ای تجویز نشده است
                            </td>
                        </tr>
                        {% endif %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}