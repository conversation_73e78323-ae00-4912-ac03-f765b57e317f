{% extends 'base.html' %}

{% block title %}
    {% if form.instance.pk %}ویرایش ویزیت{% else %}ثبت ویزیت جدید{% endif %}
{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header bg-white d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">
            {% if form.instance.pk %}ویرایش ویزیت{% else %}ثبت ویزیت جدید{% endif %}
        </h5>
        <a href="{% url 'visit_list' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-right"></i> بازگشت به لیست
        </a>
    </div>
    <div class="card-body">
        <form method="post" novalidate>
            {% csrf_token %}
            
            {% if form.non_field_errors %}
            <div class="alert alert-danger">
                {% for error in form.non_field_errors %}
                    {{ error }}
                {% endfor %}
            </div>
            {% endif %}

            <div class="mb-3">
                <label for="{{ form.patient.id_for_label }}" class="form-label">{{ form.patient.label }}</label>
                {{ form.patient }}
                {% if form.patient.errors %}
                <div class="invalid-feedback d-block">
                    {% for error in form.patient.errors %}
                        {{ error }}
                    {% endfor %}
                </div>
                {% endif %}
            </div>

            <div class="mb-3">
                <label for="{{ form.symptoms.id_for_label }}" class="form-label">{{ form.symptoms.label }}</label>
                {{ form.symptoms }}
                {% if form.symptoms.errors %}
                <div class="invalid-feedback d-block">
                    {% for error in form.symptoms.errors %}
                        {{ error }}
                    {% endfor %}
                </div>
                {% endif %}
            </div>

            <div class="mb-3">
                <label for="{{ form.diagnosis.id_for_label }}" class="form-label">{{ form.diagnosis.label }}</label>
                {{ form.diagnosis }}
                {% if form.diagnosis.errors %}
                <div class="invalid-feedback d-block">
                    {% for error in form.diagnosis.errors %}
                        {{ error }}
                    {% endfor %}
                </div>
                {% endif %}
            </div>

            <div class="mb-3 form-check">
                {{ form.is_online }}
                <label for="{{ form.is_online.id_for_label }}" class="form-check-label">{{ form.is_online.label }}</label>
                {% if form.is_online.errors %}
                <div class="invalid-feedback d-block">
                    {% for error in form.is_online.errors %}
                        {{ error }}
                    {% endfor %}
                </div>
                {% endif %}
            </div>

            <div class="text-end">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> ذخیره
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        $('.select2').select2({
            theme: 'bootstrap-5',
            placeholder: 'انتخاب بیمار',
            allowClear: true
        });
    });
</script>
{% endblock %}
