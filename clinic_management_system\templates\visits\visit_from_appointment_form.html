{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "تبدیل نوبت به ویزیت" %}{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header bg-white d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">{% trans "تبدیل نوبت به ویزیت" %}</h5>
        <a href="{% url 'appointment_detail' appointment.pk %}" class="btn btn-secondary">
            <i class="fas fa-arrow-right"></i> {% trans "بازگشت به نوبت" %}
        </a>
    </div>
    <div class="card-body">
        <div class="alert alert-info mb-4">
            <h6 class="alert-heading">
                <i class="fas fa-info-circle"></i> {% trans "اطلاعات نوبت" %}
            </h6>
            <div class="row">
                <div class="col-md-6">
                    <p><strong>{% trans "بیمار" %}:</strong> {{ appointment.patient.first_name }} {{ appointment.patient.last_name }}</p>
                    <p><strong>{% trans "پزشک" %}:</strong> {{ appointment.doctor.get_full_name }}</p>
                </div>
                <div class="col-md-6">
                    <p><strong>{% trans "تاریخ و زمان نوبت" %}:</strong> {{ appointment.appointment_datetime|date:"Y/m/d H:i" }}</p>
                    <p><strong>{% trans "دلیل مراجعه" %}:</strong> {{ appointment.reason|default:"-" }}</p>
                </div>
            </div>
        </div>
        
        <form method="post" novalidate>
            {% csrf_token %}
            
            {% if form.non_field_errors %}
            <div class="alert alert-danger">
                {% for error in form.non_field_errors %}
                    {{ error }}
                {% endfor %}
            </div>
            {% endif %}
            
            <div class="card mb-4">
                <div class="card-header bg-light">{% trans "اطلاعات ویزیت" %}</div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="{{ form.symptoms.id_for_label }}" class="form-label">{{ form.symptoms.label }}</label>
                        {{ form.symptoms }}
                        {% if form.symptoms.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.symptoms.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.diagnosis.id_for_label }}" class="form-label">{{ form.diagnosis.label }}</label>
                        {{ form.diagnosis }}
                        {% if form.diagnosis.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.diagnosis.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <div class="text-end">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> {% trans "ثبت ویزیت" %}
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}
