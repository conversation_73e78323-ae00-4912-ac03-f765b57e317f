{% extends 'base.html' %}

{% block title %}لیست ویزیت‌ها{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header bg-white d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">لیست ویزیت‌ها</h5>
        <a href="{% url 'visit_create' %}" class="btn btn-primary">
            <i class="fas fa-plus"></i> ثبت ویزیت جدید
        </a>
    </div>
    <div class="card-body">
        <!-- Search and Filter Form -->
        <form method="get" class="mb-4">
            <div class="row g-3">
                <div class="col-md-8">
                    <div class="input-group">
                        <input type="text" name="search" class="form-control" placeholder="جستجو بر اساس نام بیمار، کد ملی یا تشخیص..." value="{{ search_query }}">
                        <button class="btn btn-primary" type="submit" id="button-search">
                            <i class="fas fa-search"></i> جستجو
                        </button>
                    </div>
                </div>
                <div class="col-md-4">
                    <select name="show_online" class="form-select" onchange="this.form.submit()">
                        <option value="" {% if not show_online_filter %}selected{% endif %}>همه ویزیت‌ها</option>
                        <option value="true" {% if show_online_filter == 'true' %}selected{% endif %}>فقط ویزیت‌های آنلاین</option>
                        <option value="false" {% if show_online_filter == 'false' %}selected{% endif %}>فقط ویزیت‌های حضوری</option>
                    </select>
                </div>
            </div>
        </form>

        <!-- Visits Table -->
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>تاریخ و زمان ویزیت</th>
                        <th>بیمار</th>
                        <th>پزشک معالج</th>
                        <th>نوع ویزیت</th>
                        <th>علائم</th>
                        <th>تشخیص</th>
                        <th>نسخه</th>
                        <th>عملیات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for visit in visits %}
                    <tr>
                        <td>{{ visit.visit_datetime|date:"Y/m/d H:i" }}</td>
                        <td>
                            <a href="{% url 'patient_detail' visit.patient.pk %}">
                                {{ visit.patient.first_name }} {{ visit.patient.last_name }}
                            </a>
                        </td>
                        <td>{{ visit.doctor.get_full_name }}</td>
                        <td>
                            {% if visit.is_online %}
                                <span class="badge bg-success">آنلاین</span>
                            {% else %}
                                <span class="badge bg-info">حضوری</span>
                            {% endif %}
                        </td>
                        <td>{{ visit.symptoms|truncatechars:50 }}</td>
                        <td>{{ visit.diagnosis|truncatechars:50 }}</td>
                        <td>
                            {% if visit.prescription %}
                                <a href="{% url 'prescription_detail' visit.prescription.pk %}" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-file-prescription"></i> مشاهده نسخه
                                </a>
                            {% else %}
                                <span class="text-muted">ندارد</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group">
                                <a href="{% url 'visit_detail' visit.pk %}" class="btn btn-sm btn-info">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{% url 'visit_edit' visit.pk %}" class="btn btn-sm btn-warning">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="{% url 'visit_delete' visit.pk %}" class="btn btn-sm btn-danger">
                                    <i class="fas fa-trash"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="7" class="text-center text-muted">
                            <i class="fas fa-info-circle"></i> هیچ ویزیتی ثبت نشده است
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}
