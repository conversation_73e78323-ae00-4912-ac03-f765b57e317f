from django.contrib import admin
from .models import Visit, Prescription, PrescribedDrug, Appointment

# Inline admin for PrescribedDrug to be used in PrescriptionAdmin
class PrescribedDrugInline(admin.TabularInline):
    model = PrescribedDrug
    extra = 1 # Number of empty forms to display
    autocomplete_fields = ['drug'] # Assuming DrugAdmin has search_fields configured

@admin.register(Visit)
class VisitAdmin(admin.ModelAdmin):
    list_display = ('patient', 'doctor', 'visit_datetime', 'symptoms', 'diagnosis')
    list_filter = ('visit_datetime', 'doctor', 'patient')
    search_fields = ('patient__first_name', 'patient__last_name', 'patient__national_id', 'doctor__username', 'symptoms', 'diagnosis')
    autocomplete_fields = ['patient', 'doctor'] # Assuming PatientAdmin and CustomUserAdmin have search_fields
    readonly_fields = ('visit_datetime',)
    date_hierarchy = 'visit_datetime'

@admin.register(Prescription)
class PrescriptionAdmin(admin.ModelAdmin):
    list_display = ('visit', 'created_at', 'display_total_cost')
    list_filter = ('created_at',)
    search_fields = ('visit__patient__first_name', 'visit__patient__last_name', 'visit__patient__national_id')
    inlines = [PrescribedDrugInline]
    readonly_fields = ('created_at', 'display_total_cost')
    autocomplete_fields = ['visit']

    def display_total_cost(self, obj):
        return obj.total_cost
    display_total_cost.short_description = 'مجموع هزینه (ریال)'

# PrescribedDrug is managed via PrescriptionAdmin inline, but can be registered separately if direct access is needed.
# @admin.register(PrescribedDrug)
# class PrescribedDrugAdmin(admin.ModelAdmin):
#     list_display = ('prescription', 'drug', 'quantity', 'cost')
#     autocomplete_fields = ['prescription', 'drug']

@admin.register(Appointment)
class AppointmentAdmin(admin.ModelAdmin):
    list_display = ('patient', 'doctor', 'appointment_datetime', 'status', 'reason')
    list_filter = ('appointment_datetime', 'status', 'doctor')
    search_fields = ('patient__first_name', 'patient__last_name', 'patient__national_id', 'doctor__username', 'reason')
    autocomplete_fields = ['patient', 'doctor', 'visit']
    readonly_fields = ('created_at', 'updated_at')
    date_hierarchy = 'appointment_datetime'
    fieldsets = (
        (None, {
            'fields': ('patient', 'doctor', 'appointment_datetime', 'status')
        }),
        ('اطلاعات تکمیلی', {
            'fields': ('reason', 'notes', 'visit')
        }),
        ('اطلاعات سیستم', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
