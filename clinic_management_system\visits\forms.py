from django import forms
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from datetime import datetime, timedelta
from .models import Visit, Appointment

class VisitForm(forms.ModelForm):
    class Meta:
        model = Visit
        fields = ['patient', 'symptoms', 'diagnosis', 'is_online']
        widgets = {
            'patient': forms.Select(attrs={'class': 'form-control select2'}),
            'symptoms': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'علائم بیمار'}),
            'diagnosis': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'تشخیص پزشک'}),
            'is_online': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }
        labels = {
            'patient': 'بیمار',
            'symptoms': 'علائم بیمار',
            'diagnosis': 'تشخیص',
            'is_online': 'ویزیت آنلاین',
        }
        error_messages = {
            'patient': {
                'required': 'لطفا بیمار را انتخاب کنید.',
            },
            'symptoms': {
                'required': 'لطفا علائم بیمار را وارد کنید.',
            },
            'diagnosis': {
                'required': 'لطفا تشخیص را وارد کنید.',
            },
        }


class AppointmentForm(forms.ModelForm):
    appointment_date = forms.DateField(
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
        label=_('تاریخ نوبت')
    )
    appointment_time = forms.TimeField(
        widget=forms.TimeInput(attrs={'type': 'time', 'class': 'form-control'}),
        label=_('زمان نوبت')
    )

    class Meta:
        model = Appointment
        fields = ['patient', 'doctor', 'reason', 'notes']
        widgets = {
            'patient': forms.Select(attrs={'class': 'form-control select2'}),
            'doctor': forms.Select(attrs={'class': 'form-control select2'}),
            'reason': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': _('دلیل مراجعه')}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': _('یادداشت‌های اضافی')}),
        }
        labels = {
            'patient': _('بیمار'),
            'doctor': _('پزشک'),
            'reason': _('دلیل مراجعه'),
            'notes': _('یادداشت‌ها'),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # اگر در حال ویرایش یک نوبت موجود هستیم، تاریخ و زمان را از آن استخراج می‌کنیم
        if self.instance.pk and self.instance.appointment_datetime:
            self.fields['appointment_date'].initial = self.instance.appointment_datetime.date()
            self.fields['appointment_time'].initial = self.instance.appointment_datetime.time()

    def clean(self):
        cleaned_data = super().clean()
        appointment_date = cleaned_data.get('appointment_date')
        appointment_time = cleaned_data.get('appointment_time')

        if appointment_date and appointment_time:
            # ترکیب تاریخ و زمان
            appointment_datetime = datetime.combine(appointment_date, appointment_time)

            # بررسی تاریخ گذشته
            if appointment_datetime < timezone.now():
                raise forms.ValidationError(_('تاریخ و زمان نوبت نمی‌تواند در گذشته باشد.'))

            # بررسی تداخل با نوبت‌های دیگر
            doctor = cleaned_data.get('doctor')
            if doctor:
                # بازه زمانی نوبت (مثلاً 30 دقیقه)
                appointment_end = appointment_datetime + timedelta(minutes=30)

                # بررسی تداخل با نوبت‌های دیگر همان پزشک
                conflicting_appointments = Appointment.objects.filter(
                    doctor=doctor,
                    status='scheduled',
                    appointment_datetime__lt=appointment_end,
                    appointment_datetime__gt=appointment_datetime - timedelta(minutes=30)
                )

                # اگر در حال ویرایش هستیم، نوبت فعلی را از بررسی حذف کنیم
                if self.instance.pk:
                    conflicting_appointments = conflicting_appointments.exclude(pk=self.instance.pk)

                if conflicting_appointments.exists():
                    raise forms.ValidationError(_('این زمان قبلاً رزرو شده است. لطفاً زمان دیگری انتخاب کنید.'))

            cleaned_data['appointment_datetime'] = appointment_datetime

        return cleaned_data

    def save(self, commit=True):
        instance = super().save(commit=False)
        if 'appointment_datetime' in self.cleaned_data:
            instance.appointment_datetime = self.cleaned_data['appointment_datetime']
        if commit:
            instance.save()
        return instance
