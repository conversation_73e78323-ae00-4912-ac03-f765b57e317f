# Generated by Django 5.2 on 2025-05-09 16:23

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('drugs', '0001_initial'),
        ('patients', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Visit',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('visit_datetime', models.DateTimeField(auto_now_add=True, verbose_name='تاریخ و زمان ویزیت')),
                ('symptoms', models.TextField(verbose_name='علائم بیمار')),
                ('diagnosis', models.TextField(verbose_name='تشخیص دکتر')),
                ('doctor', models.ForeignKey(limit_choices_to={'role': 'doctor'}, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='doctor_visits', to=settings.AUTH_USER_MODEL, verbose_name='دکتر')),
                ('patient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='patients.patient', verbose_name='بیمار')),
            ],
            options={
                'verbose_name': 'ویزیت',
                'verbose_name_plural': 'ویزیت\u200cها',
                'ordering': ['-visit_datetime'],
            },
        ),
        migrations.CreateModel(
            name='Prescription',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاریخ ایجاد نسخه')),
                ('visit', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='prescription', to='visits.visit', verbose_name='ویزیت مربوطه')),
            ],
            options={
                'verbose_name': 'نسخه',
                'verbose_name_plural': 'نسخه\u200cها',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='PrescribedDrug',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.PositiveIntegerField(default=1, verbose_name='تعداد/مقدار')),
                ('dosage_instructions', models.CharField(blank=True, max_length=255, null=True, verbose_name='دستور مصرف')),
                ('drug', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='drugs.drug', verbose_name='دارو')),
                ('prescription', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='prescribed_items', to='visits.prescription', verbose_name='نسخه')),
            ],
            options={
                'verbose_name': 'داروی تجویز شده',
                'verbose_name_plural': 'داروهای تجویز شده',
                'ordering': ['drug__name'],
                'unique_together': {('prescription', 'drug')},
            },
        ),
    ]
