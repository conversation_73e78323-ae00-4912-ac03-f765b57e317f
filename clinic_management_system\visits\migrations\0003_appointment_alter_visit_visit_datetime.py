# Generated by Django 5.2.1 on 2025-05-15 10:00

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('patients', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('visits', '0002_visit_status'),
    ]

    operations = [
        migrations.AlterField(
            model_name='visit',
            name='visit_datetime',
            field=models.DateTimeField(verbose_name='تاریخ و زمان ویزیت'),
        ),
        migrations.CreateModel(
            name='Appointment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('appointment_datetime', models.DateTimeField(verbose_name='تاریخ و زمان نوبت')),
                ('reason', models.TextField(blank=True, null=True, verbose_name='دلیل مراجعه')),
                ('status', models.CharField(choices=[('scheduled', 'رزرو شده'), ('completed', 'تکمیل شده'), ('cancelled', 'لغو شده'), ('no_show', 'عدم مراجعه')], default='scheduled', max_length=20, verbose_name='وضعیت')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاریخ ایجاد')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاریخ بروزرسانی')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='یادداشت‌ها')),
                ('doctor', models.ForeignKey(limit_choices_to={'role': 'doctor'}, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='doctor_appointments', to=settings.AUTH_USER_MODEL, verbose_name='دکتر')),
                ('patient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='appointments', to='patients.patient', verbose_name='بیمار')),
                ('visit', models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='from_appointment', to='visits.visit', verbose_name='ویزیت مرتبط')),
            ],
            options={
                'verbose_name': 'نوبت',
                'verbose_name_plural': 'نوبت‌ها',
                'ordering': ['appointment_datetime'],
            },
        ),
    ]
