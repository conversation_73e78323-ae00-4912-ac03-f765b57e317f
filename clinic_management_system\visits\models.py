from django.db import models
from django.conf import settings
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from datetime import datetime, timedelta
# Import related models. Ensure these apps are listed before 'visits' in INSTALLED_APPS if not using string references.
# However, using string references (e.g., 'patients.Patient') is safer to avoid circular import issues.
# from patients.models import Patient # Using string reference instead
# from drugs.models import Drug # Using string reference instead

# Create your models here.
class Visit(models.Model):
    STATUS_CHOICES = [
        ('pending', _('در انتظار')),
        ('completed', _('تکمیل شده')),
        ('cancelled', _('لغو شده')),
    ]

    patient = models.ForeignKey('patients.Patient', on_delete=models.CASCADE, verbose_name=_('بیمار'))
    doctor = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL, # If doctor user is deleted, keep visit record but set doctor to NULL
        null=True,
        limit_choices_to={'role': 'doctor'},
        related_name='doctor_visits',
        verbose_name=_('دکتر')
    )
    # This is the actual visit date/time, not the patient registration date
    visit_datetime = models.DateTimeField(verbose_name=_('تاریخ و زمان ویزیت'))
    symptoms = models.TextField(verbose_name=_('علائم بیمار'))
    diagnosis = models.TextField(verbose_name=_('تشخیص دکتر'))
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='pending',
        verbose_name=_('وضعیت')
    )
    is_online = models.BooleanField(default=False, verbose_name=_('ویزیت آنلاین'))

    def __str__(self):
        patient_name = str(self.patient) if self.patient else _("بیمار نامشخص")
        doctor_name = str(self.doctor) if self.doctor else _("دکتر نامشخص")
        return _("ویزیت {} توسط {} در {}").format(patient_name, doctor_name, self.visit_datetime.strftime('%Y-%m-%d %H:%M'))

    @property
    def status_color(self):
        return {
            'pending': 'warning',
            'completed': 'success',
            'cancelled': 'danger'
        }.get(self.status, 'secondary')

    class Meta:
        verbose_name = _('ویزیت')
        verbose_name_plural = _('ویزیت‌ها')
        ordering = ['-visit_datetime']


class Prescription(models.Model):
    visit = models.OneToOneField(Visit, on_delete=models.CASCADE, verbose_name=_('ویزیت مربوطه'), related_name='prescription')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name=_('تاریخ ایجاد نسخه'))
    is_dispensed = models.BooleanField(default=False, verbose_name=_('صادر شده'))
    # Drugs are linked via PrescribedDrug model

    def __str__(self):
        return _("نسخه برای {}").format(str(self.visit))

    @property
    def total_cost(self):
        return sum(item.cost for item in self.prescribed_items.all())

    class Meta:
        verbose_name = _('نسخه')
        verbose_name_plural = _('نسخه‌ها')
        ordering = ['-created_at']


class PrescribedDrug(models.Model):
    prescription = models.ForeignKey(Prescription, related_name='prescribed_items', on_delete=models.CASCADE, verbose_name=_('نسخه'))
    drug = models.ForeignKey('drugs.Drug', on_delete=models.PROTECT, verbose_name=_('دارو')) # PROTECT to prevent drug deletion if prescribed
    quantity = models.PositiveIntegerField(default=1, verbose_name=_('تعداد/مقدار'))
    dosage_instructions = models.CharField(max_length=255, blank=True, null=True, verbose_name=_('دستور مصرف'))

    @property
    def cost(self):
        return self.drug.price * self.quantity

    def __str__(self):
        return _("{} عدد {} برای {}").format(self.quantity, self.drug.name, str(self.prescription))

    class Meta:
        verbose_name = _('داروی تجویز شده')
        verbose_name_plural = _('داروهای تجویز شده')
        unique_together = ('prescription', 'drug') # Ensure a drug is not added twice to the same prescription
        ordering = ['drug__name']


class Appointment(models.Model):
    STATUS_CHOICES = [
        ('scheduled', _('رزرو شده')),
        ('completed', _('تکمیل شده')),
        ('cancelled', _('لغو شده')),
        ('no_show', _('عدم مراجعه')),
    ]

    patient = models.ForeignKey('patients.Patient', on_delete=models.CASCADE, verbose_name=_('بیمار'), related_name='appointments')
    doctor = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        limit_choices_to={'role': 'doctor'},
        related_name='doctor_appointments',
        verbose_name=_('دکتر')
    )
    appointment_datetime = models.DateTimeField(verbose_name=_('تاریخ و زمان نوبت'))
    reason = models.TextField(verbose_name=_('دلیل مراجعه'), blank=True, null=True)
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='scheduled',
        verbose_name=_('وضعیت')
    )
    created_at = models.DateTimeField(auto_now_add=True, verbose_name=_('تاریخ ایجاد'))
    updated_at = models.DateTimeField(auto_now=True, verbose_name=_('تاریخ بروزرسانی'))
    notes = models.TextField(verbose_name=_('یادداشت‌ها'), blank=True, null=True)

    # اگر نوبت به ویزیت تبدیل شود، این فیلد پر می‌شود
    visit = models.OneToOneField(
        'Visit',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='from_appointment',
        verbose_name=_('ویزیت مرتبط')
    )

    def __str__(self):
        patient_name = str(self.patient) if self.patient else _("بیمار نامشخص")
        doctor_name = str(self.doctor) if self.doctor else _("دکتر نامشخص")
        return _("نوبت {} با {} در {}").format(
            patient_name,
            doctor_name,
            self.appointment_datetime.strftime('%Y-%m-%d %H:%M')
        )

    @property
    def status_color(self):
        return {
            'scheduled': 'primary',
            'completed': 'success',
            'cancelled': 'danger',
            'no_show': 'warning'
        }.get(self.status, 'secondary')

    class Meta:
        verbose_name = _('نوبت')
        verbose_name_plural = _('نوبت‌ها')
        ordering = ['appointment_datetime']
