from django.urls import path
from . import views

urlpatterns = [
    # ویزیت‌ها
    path('', views.visit_list, name='visit_list'),
    path('create/', views.visit_create, name='visit_create'),
    path('<int:pk>/', views.visit_detail, name='visit_detail'),
    path('<int:pk>/edit/', views.visit_edit, name='visit_edit'),
    path('<int:pk>/delete/', views.visit_delete, name='visit_delete'),

    # نسخه‌ها
    path('prescription/create/', views.prescription_create, name='prescription_create'),
    path('prescription/<int:pk>/', views.prescription_detail, name='prescription_detail'),

    # نوبت‌ها
    path('appointments/', views.appointment_list, name='appointment_list'),
    path('appointments/create/', views.appointment_create, name='appointment_create'),
    path('appointments/<int:pk>/', views.appointment_detail, name='appointment_detail'),
    path('appointments/<int:pk>/edit/', views.appointment_edit, name='appointment_edit'),
    path('appointments/<int:pk>/cancel/', views.appointment_cancel, name='appointment_cancel'),
    path('appointments/<int:pk>/to-visit/', views.appointment_to_visit, name='appointment_to_visit'),

    # برنامه زمانی پزشکان
    path('schedule/', views.doctor_schedule, name='doctor_schedule'),
]