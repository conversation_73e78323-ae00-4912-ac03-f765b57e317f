from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required, user_passes_test
from django.contrib import messages
from django.db.models import Q, F
from django.http import HttpResponseForbidden
from django.utils import timezone
from django.db import transaction
from datetime import datetime, timedelta, time
from .models import Visit, Prescription, PrescribedDrug, Appointment
from .forms import VisitForm, AppointmentForm
from patients.models import Patient
from drugs.models import Drug
from accounts.models import CustomUser

def is_doctor(user):
    return user.role == 'doctor'

@login_required
def visit_list(request):
    search_query = request.GET.get('search', '')
    show_online_filter = request.GET.get('show_online', None) # 'true', 'false', or None

    visits = Visit.objects.select_related('patient', 'doctor', 'prescription').all().order_by('-visit_datetime')

    if search_query:
        visits = visits.filter(
            Q(patient__first_name__icontains=search_query) |
            Q(patient__last_name__icontains=search_query) |
            Q(patient__national_id__icontains=search_query) |
            Q(diagnosis__icontains=search_query) |
            Q(symptoms__icontains=search_query)
        )

    if show_online_filter == 'true':
        visits = visits.filter(is_online=True)
    elif show_online_filter == 'false':
        visits = visits.filter(is_online=False)
    # If None, show all

    context = {
        'visits': visits,
        'search_query': search_query,
        'show_online_filter': show_online_filter
    }
    return render(request, 'visits/visit_list.html', context)

@login_required
def visit_create(request):
    if request.method == 'POST':
        form = VisitForm(request.POST)
        if form.is_valid():
            visit = form.save(commit=False)
            visit.doctor = request.user
            visit.visit_datetime = timezone.now() # Set current datetime for the visit
            visit.save()
            messages.success(request, 'ویزیت با موفقیت ثبت شد.')
            return redirect('visit_detail', pk=visit.pk)
    else:
        form = VisitForm()

    context = {
        'form': form,
        'title': 'ثبت ویزیت جدید'
    }
    return render(request, 'visits/visit_form.html', context)

@login_required
def visit_detail(request, pk):
    visit = get_object_or_404(Visit, pk=pk)
    context = {
        'visit': visit
    }
    return render(request, 'visits/visit_detail.html', context)

@login_required
def visit_edit(request, pk):
    visit = get_object_or_404(Visit, pk=pk)
    if request.method == 'POST':
        form = VisitForm(request.POST, instance=visit)
        if form.is_valid():
            form.save()
            messages.success(request, 'اطلاعات ویزیت با موفقیت بروزرسانی شد.')
            return redirect('visit_detail', pk=visit.pk)
    else:
        form = VisitForm(instance=visit)

    context = {
        'form': form,
        'visit': visit,
        'title': 'ویرایش اطلاعات ویزیت'
    }
    return render(request, 'visits/visit_form.html', context)

@login_required
def visit_delete(request, pk):
    visit = get_object_or_404(Visit, pk=pk)
    if request.method == 'POST':
        visit.delete()
        messages.success(request, 'ویزیت با موفقیت حذف شد.')
        return redirect('visit_list')

    context = {
        'visit': visit
    }
    return render(request, 'visits/visit_confirm_delete.html', context)

@login_required
def patient_visits(request, patient_pk):
    patient = get_object_or_404(Patient, pk=patient_pk)
    visits = patient.visits.all().order_by('-visit_date')

    context = {
        'patient': patient,
        'visits': visits
    }
    return render(request, 'visits/patient_visits.html', context)

@login_required
@user_passes_test(is_doctor)
def prescription_create(request):
    if request.method == 'POST':
        visit_id = request.POST.get('visit')
        visit = get_object_or_404(Visit, pk=visit_id)

        # Check if the doctor is the one who performed the visit
        if visit.doctor != request.user and not request.user.is_staff:
            messages.error(request, 'شما مجاز به تجویز دارو برای این ویزیت نیستید.')
            return redirect('visit_detail', pk=visit.pk)

        # Check if prescription already exists
        if hasattr(visit, 'prescription'):
            messages.warning(request, 'برای این ویزیت قبلاً نسخه ثبت شده است.')
            return redirect('prescription_detail', pk=visit.prescription.pk)

        # Get form data
        drug_ids = request.POST.getlist('drugs')
        quantities = request.POST.getlist('quantities')
        instructions = request.POST.getlist('instructions')

        # Validate drug quantities against stock
        for i in range(len(drug_ids)):
            if i < len(quantities) and i < len(instructions):
                drug = get_object_or_404(Drug, pk=drug_ids[i])
                quantity = int(quantities[i])

                if quantity > drug.stock:
                    messages.error(request, f'موجودی داروی {drug.name} کافی نیست. موجودی فعلی: {drug.stock}')
                    return redirect(f'/visits/prescriptions/create/?visit={visit_id}')

        # Create prescription
        prescription = Prescription.objects.create(visit=visit)

        # Add prescribed drugs and update stock
        for i in range(len(drug_ids)):
            if i < len(quantities) and i < len(instructions):
                drug = get_object_or_404(Drug, pk=drug_ids[i])
                quantity = int(quantities[i])

                # Create prescribed drug record
                PrescribedDrug.objects.create(
                    prescription=prescription,
                    drug=drug,
                    quantity=quantity,
                    dosage_instructions=instructions[i]
                )

                # Update drug stock
                drug.stock -= quantity
                drug.save()

        messages.success(request, 'نسخه با موفقیت ثبت شد.')
        return redirect('prescription_detail', pk=prescription.pk)

    # GET request - show form
    visit_id = request.GET.get('visit')
    if not visit_id:
        messages.error(request, 'لطفاً ابتدا یک ویزیت را انتخاب کنید.')
        return redirect('visit_list')

    visit = get_object_or_404(Visit, pk=visit_id)

    # Check if the doctor is the one who performed the visit
    if visit.doctor != request.user and not request.user.is_staff:
        messages.error(request, 'شما مجاز به تجویز دارو برای این ویزیت نیستید.')
        return redirect('visit_detail', pk=visit.pk)

    # Check if prescription already exists
    if hasattr(visit, 'prescription'):
        messages.warning(request, 'برای این ویزیت قبلاً نسخه ثبت شده است.')
        return redirect('prescription_detail', pk=visit.prescription.pk)

    # Get drugs with stock information
    drugs = Drug.objects.all().order_by('name')
    context = {
        'visit': visit,
        'drugs': drugs
    }
    return render(request, 'visits/prescription_form.html', context)

@login_required
def prescription_detail(request, pk):
    prescription = get_object_or_404(Prescription, pk=pk)
    context = {
        'prescription': prescription
    }
    return render(request, 'visits/prescription_detail.html', context)


# ویوهای مربوط به نوبت‌دهی
@login_required
def appointment_list(request):
    """نمایش لیست نوبت‌ها"""
    # فیلتر بر اساس نقش کاربر
    if request.user.role == 'doctor':
        # پزشکان فقط نوبت‌های خود را می‌بینند
        appointments = Appointment.objects.filter(doctor=request.user).order_by('appointment_datetime')
    elif request.user.role == 'patient':
        # بیماران فقط نوبت‌های خود را می‌بینند (در صورتی که سیستم ثبت نام بیمار داشته باشد)
        appointments = Appointment.objects.filter(patient__user=request.user).order_by('appointment_datetime')
    else:
        # مدیران و منشی‌ها همه نوبت‌ها را می‌بینند
        appointments = Appointment.objects.all().order_by('appointment_datetime')

    # جستجو
    search_query = request.GET.get('search', '')
    if search_query:
        appointments = appointments.filter(
            Q(patient__first_name__icontains=search_query) |
            Q(patient__last_name__icontains=search_query) |
            Q(patient__national_id__icontains=search_query) |
            Q(doctor__first_name__icontains=search_query) |
            Q(doctor__last_name__icontains=search_query) |
            Q(reason__icontains=search_query)
        )

    # فیلتر بر اساس تاریخ
    date_filter = request.GET.get('date', '')
    if date_filter:
        try:
            filter_date = datetime.strptime(date_filter, '%Y-%m-%d').date()
            appointments = appointments.filter(appointment_datetime__date=filter_date)
        except ValueError:
            pass

    # فیلتر بر اساس وضعیت
    status_filter = request.GET.get('status', '')
    if status_filter:
        appointments = appointments.filter(status=status_filter)

    context = {
        'appointments': appointments,
        'search_query': search_query,
        'date_filter': date_filter,
        'status_filter': status_filter
    }
    return render(request, 'visits/appointment_list.html', context)

@login_required
def appointment_create(request):
    """ایجاد نوبت جدید"""
    if request.method == 'POST':
        form = AppointmentForm(request.POST)
        if form.is_valid():
            appointment = form.save(commit=False)
            appointment.appointment_datetime = form.cleaned_data['appointment_datetime']
            appointment.save()
            messages.success(request, 'نوبت با موفقیت ثبت شد.')
            return redirect('appointment_detail', pk=appointment.pk)
    else:
        # پیش‌فرض‌ها بر اساس پارامترهای URL
        initial = {}
        if 'patient' in request.GET:
            initial['patient'] = request.GET.get('patient')
        if 'doctor' in request.GET:
            initial['doctor'] = request.GET.get('doctor')

        form = AppointmentForm(initial=initial)

    context = {
        'form': form,
        'title': 'ثبت نوبت جدید'
    }
    return render(request, 'visits/appointment_form.html', context)

@login_required
def appointment_detail(request, pk):
    """نمایش جزئیات نوبت"""
    appointment = get_object_or_404(Appointment, pk=pk)

    # بررسی دسترسی
    if request.user.role == 'doctor' and appointment.doctor != request.user:
        return HttpResponseForbidden('شما مجاز به مشاهده این نوبت نیستید.')
    elif request.user.role == 'patient' and appointment.patient.user != request.user:
        return HttpResponseForbidden('شما مجاز به مشاهده این نوبت نیستید.')

    context = {
        'appointment': appointment
    }
    return render(request, 'visits/appointment_detail.html', context)

@login_required
def appointment_edit(request, pk):
    """ویرایش نوبت"""
    appointment = get_object_or_404(Appointment, pk=pk)

    # بررسی دسترسی
    if request.user.role not in ['admin', 'receptionist'] and appointment.doctor != request.user:
        return HttpResponseForbidden('شما مجاز به ویرایش این نوبت نیستید.')

    if request.method == 'POST':
        form = AppointmentForm(request.POST, instance=appointment)
        if form.is_valid():
            appointment = form.save(commit=False)
            appointment.appointment_datetime = form.cleaned_data['appointment_datetime']
            appointment.save()
            messages.success(request, 'نوبت با موفقیت بروزرسانی شد.')
            return redirect('appointment_detail', pk=appointment.pk)
    else:
        form = AppointmentForm(instance=appointment)

    context = {
        'form': form,
        'appointment': appointment,
        'title': 'ویرایش نوبت'
    }
    return render(request, 'visits/appointment_form.html', context)

@login_required
def appointment_cancel(request, pk):
    """لغو نوبت"""
    appointment = get_object_or_404(Appointment, pk=pk)

    # بررسی دسترسی
    if request.user.role not in ['admin', 'receptionist'] and appointment.doctor != request.user and (request.user.role == 'patient' and appointment.patient.user != request.user):
        return HttpResponseForbidden('شما مجاز به لغو این نوبت نیستید.')

    if request.method == 'POST':
        appointment.status = 'cancelled'
        appointment.save()
        messages.success(request, 'نوبت با موفقیت لغو شد.')
        return redirect('appointment_list')

    context = {
        'appointment': appointment
    }
    return render(request, 'visits/appointment_confirm_cancel.html', context)

@login_required
@user_passes_test(is_doctor)
def appointment_to_visit(request, pk):
    """تبدیل نوبت به ویزیت"""
    appointment = get_object_or_404(Appointment, pk=pk)

    # بررسی دسترسی
    if request.user.role != 'doctor' or appointment.doctor != request.user:
        return HttpResponseForbidden('فقط پزشک مربوطه می‌تواند نوبت را به ویزیت تبدیل کند.')

    if request.method == 'POST':
        form = VisitForm(request.POST)
        if form.is_valid():
            with transaction.atomic():
                visit = form.save(commit=False)
                visit.patient = appointment.patient
                visit.doctor = appointment.doctor
                visit.visit_datetime = timezone.now()
                visit.save()

                # به‌روزرسانی وضعیت نوبت
                appointment.status = 'completed'
                appointment.visit = visit
                appointment.save()

                messages.success(request, 'نوبت با موفقیت به ویزیت تبدیل شد.')
                return redirect('visit_detail', pk=visit.pk)
    else:
        # پیش‌پر کردن فرم با اطلاعات نوبت
        initial = {
            'patient': appointment.patient,
            'symptoms': appointment.reason if appointment.reason else '',
        }
        form = VisitForm(initial=initial)

    context = {
        'form': form,
        'appointment': appointment,
        'title': 'تبدیل نوبت به ویزیت'
    }
    return render(request, 'visits/visit_from_appointment_form.html', context)

@login_required
def doctor_schedule(request):
    """نمایش برنامه زمانی پزشکان"""
    doctors = CustomUser.objects.filter(role='doctor')
    selected_doctor = request.GET.get('doctor', '')
    selected_date = request.GET.get('date', '')

    try:
        if selected_date:
            selected_date = datetime.strptime(selected_date, '%Y-%m-%d').date()
        else:
            selected_date = timezone.now().date()
    except ValueError:
        selected_date = timezone.now().date()

    # ساعات کاری (مثال)
    working_hours = []
    start_hour = 8  # شروع از ساعت 8 صبح
    end_hour = 18   # پایان در ساعت 6 عصر
    interval = 30   # هر نوبت 30 دقیقه

    for hour in range(start_hour, end_hour):
        for minute in [0, 30]:
            working_hours.append(time(hour, minute))

    # اگر پزشک انتخاب شده باشد، نوبت‌های آن روز را بررسی می‌کنیم
    schedule = []
    if selected_doctor:
        doctor = get_object_or_404(CustomUser, pk=selected_doctor, role='doctor')

        # نوبت‌های رزرو شده در تاریخ انتخاب شده
        booked_appointments = Appointment.objects.filter(
            doctor=doctor,
            appointment_datetime__date=selected_date,
            status='scheduled'
        ).values_list('appointment_datetime__time', flat=True)

        # ایجاد برنامه زمانی
        for hour in working_hours:
            is_booked = hour in booked_appointments
            schedule.append({
                'time': hour.strftime('%H:%M'),
                'is_booked': is_booked,
                'appointment_url': None if is_booked else f'/visits/appointments/create/?doctor={doctor.pk}&date={selected_date}&time={hour.strftime("%H:%M")}'
            })

    context = {
        'doctors': doctors,
        'selected_doctor': selected_doctor,
        'selected_date': selected_date,
        'schedule': schedule,
        'today': timezone.now().date(),
    }
    return render(request, 'visits/doctor_schedule.html', context)
