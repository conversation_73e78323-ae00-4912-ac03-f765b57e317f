from tkinter import *
from tkinter import messagebox
from PIL import Image,ImageTk




def btnclick():
    search_item=txtcode.get().strip()
    if len(search_item) != 10 :
        messagebox.showerror("لطفا حداقل 3 رقم اول را وارد نمائید")
        return
    f = open("data.txt","r",encoding='utf-8')
    lines=f.readlines()
    for l in lines:
        parts=l.split(',')
        if search_item.startswith(parts[0])==True:
            city=parts[1]
            ostan=parts[2]
            l2.config(text=city +" | "+ostan)
            showimage(ostan)
            break
 
def showimage(ostan):
    l3.config(image="")
    image=Image.open(ostan.strip()+".png")
    photo=ImageTk.PhotoImage(image)
    l3.config(image=photo)
    l3.image=photo




form=Tk()
form.geometry('400x450')
form.configure(background='#EED5B7')
form.title("جست و جو کد ملی " )
l1=Label(form, text=':کد ملی را واریز نمائید  ', bg='#EED5B7', font=('arial', 14, 'bold'))
l1.pack()

txtcode=Entry(form)
txtcode.pack()

b1=Button(form, text='جست و جو', bg='#EEC591', font=('arial', 14, 'bold'), command=btnclick)
b1.pack()

l2=Label(form, text='lblcity', bg='#EED5B7', font=('arial', 14, 'bold'))
l2.pack()

l3=Label(form, text='lblimage', font=('arial', 18, 'bold'))
l3.pack()

form.mainloop()