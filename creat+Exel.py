import pandas as pd
import random

# تعریف داده‌ها
companies = ['شرکت الف', 'شرکت ب', 'شرکت ب', 'شرکت د', 'شرکت ب', 'شرکت ب', 'شرکت ز', 'شرکت ح', 'شرکت و', 'شرکت ز']
numbers = [str(random.randint(1, 10)) for _ in range(10)]  # تولید ۱۰ شماره تصادفی

# ایجاد DataFrame
data = {
    'شرکت پذیرنده': random.choices(companies, k=10),  # انتخاب تصادفی از لیست شرکت‌ها
    'شماره پذیرنده': numbers
}

df = pd.DataFrame(data)

# ذخیره DataFrame به عنوان یک فایل اکسل
file_path = 'book3.xlsx'
df.to_excel(file_path, index=False, sheet_name='Sheet1')

print(f"فایل اکسل با نام '{file_path}' با موفقیت ایجاد شد.")