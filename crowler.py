import nest_asyncio  
from telegram import Update  
from telegram.ext import Application, CommandHandler, ContextTypes  
import json  
import os  

TOKEN = '8143162592:AAGB7URyeX2Vekbje1m0I7czrONhVEIyAiA' 

# بارگذاری گروه‌ها از فایل  
def load_user_channels():  
    if os.path.exists('user_channels.json'):  
        with open('user_channels.json', 'r') as f:  
            return json.load(f)  
    return []  

# ذخیره گروه‌ها در فایل  
def save_user_channels(channels):  
    with open('user_channels.json', 'w') as f:  
        json.dump(channels, f)  

user_channels = load_user_channels()  

async def start(update: Update, context: ContextTypes.DEFAULT_TYPE):  
    await update.message.reply_text(  
        'به ربات خوش آمدید!\n'  
        'از /search برای جستجوی هشتگ‌ها استفاده کنید.\n'  
        'از /list برای نمایش گروه‌ها و کانال‌ها استفاده کنید.\n'  
        'از /addgroup برای اضافه کردن گروه استفاده کنید.\n'  
        'از /removegroup برای حذف گروه استفاده کنید.\n'  
        'از /help برای مشاهده دستورالعمل‌ها استفاده کنید.'  
    )  

"""
async def search_hashtags(update: Update, context: ContextTypes.DEFAULT_TYPE):  
    hashtags = context.args  
    if not hashtags:  
        await update.message.reply_text('لطفاً حداقل یک هشتگ وارد کنید.')  
    else:  
        await update.message.reply_text(f'جستجو برای هشتگ: {", ".join(hashtags)}')  
"""
async def search_hashtags(update: Update, context: ContextTypes.DEFAULT_TYPE):  
    hashtags = context.args  
    if not hashtags:  
        await update.message.reply_text('لطفاً حداقل یک هشتگ وارد کنید.')  
        return  

    results = []  
    
    for group in user_channels:  
        # بارگذاری پیام‌های مربوط به هر گروه  
        messages = load_messages_for_group(group)  # این تابع باید پیام‌ها را بارگذاری کند  
        for message in messages:  
            for hashtag in hashtags:  
                if hashtag in message:  
                    results.append(f'پیام: "{message}"\nهشتگ: "{hashtag}"\nگروه/کانال: "{group}"\n')  

    # ارسال نتایج جستجو  
    if results:  
        await update.message.reply_text('\n'.join(results))  
    else:  
        await update.message.reply_text('هیچ نتیجه‌ای پیدا نشد.')

# مثالی برای بارگذاری پیام‌ها از گروه  
def load_messages_for_group(group):  
    # اینجا باید منطق واقعی بارگذاری پیام‌ها را قرار دهید  
    # برای سادگی، از مثال‌های ثابت استفاده می‌شود  
    if group == "https://t.me/PythonForever":  
        return ["این یک نمونه پیام #هشتگ1", "پیام دوم با هشتگ #هشتگ2"]  
    elif group == "https://t.me/Computer_IT_Engineering":  
        return ["پیام از گروه دوم #هشتگ1", "پیام دیگر بدون هشتگ"]  
    return [] 


async def list_groups(update: Update, context: ContextTypes.DEFAULT_TYPE):  
    if user_channels:  
        await update.message.reply_text('گروه‌ها و کانال‌های شما:\n' + '\n'.join(user_channels))  
    else:  
        await update.message.reply_text('شما هنوز هیچ گروه یا کانالی ثبت نکرده‌اید.')  

async def add_group(update: Update, context: ContextTypes.DEFAULT_TYPE):  
    if not context.args:  
        await update.message.reply_text('لطفاً نام گروه را وارد کنید.')  
        return  
    group_name = ' '.join(context.args)  
    user_channels.append(group_name)  
    save_user_channels(user_channels)  
    await update.message.reply_text(f'گروه "{group_name}" با موفقیت اضافه شد.')  

async def remove_group(update: Update, context: ContextTypes.DEFAULT_TYPE):  
    if not context.args:  
        await update.message.reply_text('لطفاً نام گروهی که می‌خواهید حذف کنید را وارد کنید.')  
        return  
    group_name = ' '.join(context.args)  
    if group_name in user_channels:  
        user_channels.remove(group_name)  
        save_user_channels(user_channels)  
        await update.message.reply_text(f'گروه "{group_name}" با موفقیت حذف شد.')  
    else:  
        await update.message.reply_text(f'گروه "{group_name}" پیدا نشد.')  

async def help_command(update: Update, context: ContextTypes.DEFAULT_TYPE):  
    await update.message.reply_text(  
        'در اینجا لیستی از دستورات موجود وجود دارد:\n'  
        '/start - آغاز کن\n'  
        '/search <هشتگ> - جستجوی هشتگ\n'  
        '/list - نمایش گروه‌ها و کانال‌ها\n'  
        '/addgroup <نام گروه> - اضافه کردن گروه\n'  
        '/removegroup <نام گروه> - حذف گروه\n'  
        '/help - نمایش این پیام'  
    )  

def main():  
    application = Application.builder().token(TOKEN).build()  

    application.add_handler(CommandHandler("start", start))  
    application.add_handler(CommandHandler("search", search_hashtags))  
    application.add_handler(CommandHandler("list", list_groups))  
    application.add_handler(CommandHandler("addgroup", add_group))  
    application.add_handler(CommandHandler("removegroup", remove_group))  
    application.add_handler(CommandHandler("help", help_command))  

    application.run_polling()  

if __name__ == '__main__':  
    nest_asyncio.apply()  
    main()  