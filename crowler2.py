from dotenv import *
from pyrogram  import Client,filters

from telethon import TelegramClient, events  
from telethon.tl.types import Message  

# تاریخچه توکن ربات تلگرام  
YOUR_BOT_TOKEN = '8143162592:AAGB7URyeX2Vekbje1m0I7czrONhVEIyAiA'  
# می‌توانید به سادگی آن را از BotFather دریافت کنید.  
#API_ID = '9845448'  
#API_HASH = '3c183c2eccd0f700487205dc15324106'  
#PHONE = '+989143057625'

# تعریف داده‌های ربات  
bot_data = {  
    'api_id': '9845448',      # شناسه API شما  
    'api_hash': '3c183c2eccd0f700487205dc15324106',  # هش API شما  
    
} 

# تعریف CHANNEL_ID  
CHANNEL_ID = 'https://t.me/+7KE2E0jZrv1kNTE8'  # یا به صورت عددی مانند -1001234567890  

# تعریف کلاینت برای ربات  
api = TelegramClient('api', **bot_data, bot_token=YOUR_BOT_TOKEN)  

# تعریف کلاینت برای کاربر  
cli = TelegramClient('cli', **bot_data)  

@cli.on(events.NewMessage(filters=~filters.chat(CHANNEL_ID) & (filters.channel | filters.group) & filters.regex(r'(\u067E\u0627\u06CC\u062A\u0648\u0646)')))
async def inputmessage(client: TelegramClient, message: Message):  
    print(message.text)  

cli.start()  
cli.run_until_disconnected() 
