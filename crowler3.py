import asyncio  
import logging  
from telegram import Bo<PERSON>  
from telegram.ext import ApplicationBuilder  
from telegram import Update  
from telegram.ext import ContextTypes  

# توکن ربات تلگرام خود را اینجا وارد کنید  
TELEGRAM_TOKEN = '**********************************************'
CHANNEL_ID = '@khtesttbz'  # نام کاربری کانال که می‌خواهید نتایج را ارسال کنید  
# تنظیمات لاگ‌گذاری  
logging.basicConfig(format='%(asctime)s - %(name)s - %(levelname)s - %(message)s', level=logging.INFO)  

async def search_in_chat(bot: Bot, chat_id: str, hashtags: list):  
    messages_found = []  
    
    try:  
        # دریافت تاریخچه پیام‌ها  
        chat = await bot.get_chat(chat_id)  
        async for message in chat.history(limit=100):  
            for hashtag in hashtags:  
                if message.text and hashtag in message.text:  
                    messages_found.append(f"Found in {chat_id}: {message.text}")  
    except Exception as e:  
        logging.error(f"Error accessing chat {chat_id}: {e}")  
    
    return messages_found

async def search_hashtags(bot: Bot, hashtags: list):  
    # مشخص کنید که کدام گروه‌ها یا کانال‌ها را جستجو کنید  
    chat_ids = ['4643907052', 'https://t.me/+bICAZHLEaitjNzdk']  # شناسه گروه‌ها یا کانال‌هایی که ربات در آنها عضو است  

    all_found_messages = []  
    for chat_id in chat_ids:  
        found_messages = await search_in_chat(bot, chat_id, hashtags)  
        all_found_messages.extend(found_messages)  

    return all_found_messages  

async def main():  
    bot = Bot(token=TELEGRAM_TOKEN)  
    
    # مشخصات هشتگ‌ها  
    hashtags = ['پایتون', '#برنامه_نویسی', '#هک']  
    
    # جستجوی هشتگ‌ها  
    found_messages = await search_hashtags(bot, hashtags)  

    if found_messages:  
        results_message = "\n".join(found_messages)  
        await bot.send_message(chat_id=CHANNEL_ID, text=results_message)  
    else:  
        await bot.send_message(chat_id=CHANNEL_ID, text="هیچ نتیجه‌ای یافت نشد.")  

if __name__ == "__main__":  
    asyncio.run(main())  