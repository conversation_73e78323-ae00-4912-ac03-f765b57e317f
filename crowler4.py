import logging  
from telegram import Update  

from telegram.ext import <PERSON><PERSON><PERSON>,ApplicationBuilder, CommandHandler, MessageHandler, CallbackContext,ContextTypes
import telegram.ext.filters as filters


# تنظیمات لاگ  
logging.basicConfig(format='%(asctime)s - %(name)s - %(levelname)s - %(message)s', level=logging.INFO)  
logger = logging.getLogger(name)  

# لیست کلمات کلیدی که می‌خواهید شناسایی کنید  
KEYWORDS = ["اعتراض", "دلار", "تراکتور"]  

async def start(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:  
    await update.message.reply_text('ربات فعال شد!')  

async def echo(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:  
    # بررسی اینکه آیا پیام شامل کلمات کلیدی است یا نه  
    if any(keyword in update.message.text for keyword in KEYWORDS):  
        await update.message.reply_text(f'کلمه‌ای شناسایی شد: {update.message.text}')  

def main() -> None:  
    # ساخت اپلیکیشن  
    application = ApplicationBuilder().token('879707954:AAEHBcbL67RLw8l11cqDJzr6RtnLoyob5mY').build()  

    # اضافه کردن هندلرها  
    application.add_handler(CommandHandler("start", start))  
    application.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, echo))  

    # شروع ربات  
    application.run_polling()  

if name == 'main':  
    main()