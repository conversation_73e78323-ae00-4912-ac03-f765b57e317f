import asyncio
import sys
import json
import sqlite3
import logging
from PyQt5 import QtWidgets, <PERSON>t<PERSON><PERSON>, QtGui
from telethon import Telegram<PERSON>lient, events
from telethon.tl.types import P<PERSON><PERSON>han<PERSON>, PeerChat
from telethon.sessions import StringSession
from telethon.network import ConnectionTcpMTProxyRandomizedIntermediate


# تنظیمات پایه
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class ProxySettingsDialog(QtWidgets.QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        
    def setup_ui(self):
        self.setWindowTitle("تنظیمات پروکسی")
        self.resize(450, 300)
        
        layout = QtWidgets.QVBoxLayout()
        
        # انتخاب نوع اتصال
        self.connection_group = QtWidgets.QGroupBox("نوع اتصال")
        self.direct_radio = QtWidgets.QRadioButton("اتصال مستقیم (بدون پروکسی)")
        self.proxy_radio = QtWidgets.QRadioButton("استفاده از پروکسی MTProto")
        self.proxy_radio.setChecked(True)
        
        connection_layout = QtWidgets.QVBoxLayout()
        connection_layout.addWidget(self.direct_radio)
        connection_layout.addWidget(self.proxy_radio)
        self.connection_group.setLayout(connection_layout)
        layout.addWidget(self.connection_group)
        
        # تنظیمات پروکسی
        self.proxy_group = QtWidgets.QGroupBox("تنظیمات پروکسی")
        form_layout = QtWidgets.QFormLayout()
        
        self.host_input = QtWidgets.QLineEdit()
        self.host_input.setPlaceholderText("مثال: proxy.example.com")
        form_layout.addRow("آدرس سرور:", self.host_input)
        
        self.port_input = QtWidgets.QLineEdit()
        self.port_input.setPlaceholderText("مثال: 443")
        form_layout.addRow("پورت:", self.port_input)
        
        self.secret_input = QtWidgets.QLineEdit()
        self.secret_input.setPlaceholderText("مثال: d41d8cd98f00b204e9800998ecf8427e")
        form_layout.addRow("کد امنیتی:", self.secret_input)
        
        self.proxy_group.setLayout(form_layout)
        layout.addWidget(self.proxy_group)
        
        # نمونه پروکسی
        sample_label = QtWidgets.QLabel(
            "پروکسی‌های نمونه:\n"
            "1. proxy.digitalresistance.dog - پورت: 443\n"
            "2. nl.mtproto.pro - پورت: 443\n"
            "کد امنیتی: d41d8cd98f00b204e9800998ecf8427e"
        )
        sample_label.setStyleSheet("color: #666; font-style: italic;")
        layout.addWidget(sample_label)
        
        # دکمه‌ها
        button_box = QtWidgets.QDialogButtonBox(
            QtWidgets.QDialogButtonBox.Ok | QtWidgets.QDialogButtonBox.Cancel
        )
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)
        
        self.setLayout(layout)
        self.load_settings()
        self.direct_radio.toggled.connect(self.toggle_proxy_settings)
        
    def toggle_proxy_settings(self, checked):
        self.proxy_group.setEnabled(not checked)
        
    def load_settings(self):
        try:
            with open('config.json') as f:
                config = json.load(f)
                proxy = config.get('proxy', {})
                
                if proxy.get('enabled', True):
                    self.proxy_radio.setChecked(True)
                    self.host_input.setText(proxy.get('host', ''))
                    self.port_input.setText(str(proxy.get('port', '')))
                    self.secret_input.setText(proxy.get('secret', ''))
                else:
                    self.direct_radio.setChecked(True)
        except:
            self.proxy_radio.setChecked(True)
            
    def get_settings(self):
        return {
            'enabled': not self.direct_radio.isChecked(),
            'host': self.host_input.text().strip(),
            'port': int(self.port_input.text()) if self.port_input.text().strip() else 443,
            'secret': self.secret_input.text().strip()
        }

class KeywordDB:
    def __init__(self, db_file='keywords.db'):
        self.conn = sqlite3.connect(db_file)
        self.create_table()
        
    def create_table(self):
        self.conn.execute('''
            CREATE TABLE IF NOT EXISTS keywords (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                keyword TEXT UNIQUE
            )
        ''')
        self.conn.commit()
        
    def add_keyword(self, keyword):
        try:
            self.conn.execute('INSERT INTO keywords (keyword) VALUES (?)', (keyword,))
            self.conn.commit()
            return True
        except sqlite3.IntegrityError:
            return False
            
    def remove_keyword(self, keyword):
        self.conn.execute('DELETE FROM keywords WHERE keyword = ?', (keyword,))
        self.conn.commit()
        
    def get_keywords(self):
        cursor = self.conn.execute('SELECT keyword FROM keywords')
        return [row[0] for row in cursor]

'''
class TelegramThread(QtCore.QThread):
    new_result = QtCore.pyqtSignal(dict)

    def __init__(self, api_id, api_hash):
        super().__init__()
        self.api_id = api_id
        self.api_hash = api_hash
        self.keywords = []
        self.client = None
        self.loop = None

    def run(self):
        self.loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self.loop)
        
        #self.client = TelegramClient('session_name', self.api_id, self.api_hash, loop=self.loop)
        
        client = TelegramClient(
            'session_name',
            self.api_id,
            self.api_hash,
            connection=ConnectionTcpMTProxyRandomizedIntermediate,
            proxy=('144.76.118.219', 23, 'eeNEgYdJvXrFGRMCIMJdCQ'),
            connection_retries=10
        )
        
        async def main():
            await self.client.start()
            
            @self.client.on(events.NewMessage)
            async def handler(event):
                message_text = event.message.text.lower()
                for keyword in self.keywords:
                    if keyword.lower() in message_text:
                        chat = await event.get_chat()
                        result = {
                            'keyword': keyword,
                            'source': chat.title if hasattr(chat, 'title') else 'Private Chat',
                            'message': event.message.text,
                            'link': self._generate_message_link(event)
                        }
                        self.new_result.emit(result)
            
            await self.client.run_until_disconnected()

        self.loop.run_until_complete(main())

    def _generate_message_link(self, event):
        chat_id = event.chat_id
        if isinstance(event.peer_id, PeerChannel):
            return f'https://t.me/c/{chat_id}/{event.message.id}'
        elif isinstance(event.peer_id, PeerChat):
            return f'https://t.me/c/{abs(chat_id)}/{event.message.id}'
        return 'Link not available'

    def stop(self):
        if self.loop:
            self.loop.call_soon_threadsafe(self.loop.stop)
'''
'''
class TelegramThread(QtCore.QThread):
    new_result = QtCore.pyqtSignal(dict)

    def __init__(self, api_id, api_hash):
        super().__init__()
        self.api_id = api_id
        self.api_hash = api_hash
        self.keywords = []
        self.client = None
        self.loop = None

    async def create_client(self):
        return TelegramClient(
            'session_name',
            self.api_id,
            self.api_hash,
            connection=ConnectionTcpMTProxyRandomizedIntermediate,
            proxy=('144.76.118.219', 23, 'ddf1276b9a31653f7e39f2b5a80d1c01'),
            connection_retries=5
        )

    async def main(self):
        try:
            self.client = await self.create_client()
            
            # اتصال به سرور
            await self.client.connect()
            
            # احراز هویت اگر نیاز باشد
            if not await self.client.is_user_authorized():
                await self.client.start(phone='+989143057625')
            
            # هندلر پیام‌ها
            @self.client.on(events.NewMessage)
            async def handler(event):
                message_text = event.message.text.lower()
                for keyword in self.keywords:
                    if keyword.lower() in message_text:
                        chat = await event.get_chat()
                        result = {
                            'keyword': keyword,
                            'source': chat.title if hasattr(chat, 'title') else 'Private Chat',
                            'message': event.message.text,
                            'link': self._generate_message_link(event)
                        }
                        self.new_result.emit(result)
                
            
            await self.client.run_until_disconnected()
            
        except Exception as e:
            print(f"Error in main: {e}")
            self.new_result.emit({
                'keyword': 'SYSTEM',
                'source': 'Error',
                'message': str(e),
                'link': ''
            })

    def run(self):
        try:
            self.loop = asyncio.new_event_loop()
            asyncio.set_event_loop(self.loop)
            self.loop.run_until_complete(self.main())
        except Exception as e:
            print(f"Error in run: {e}")
        finally:
            if self.loop:
                self.loop.close()

    def stop(self):
        if self.client:
            self.loop.run_until_complete(self.client.disconnect())
        if self.loop:
            self.loop.stop()
'''
class TelegramThread(QtCore.QThread):
    new_message = QtCore.pyqtSignal(dict)
    status_changed = QtCore.pyqtSignal(str)
    show_code_dialog = QtCore.pyqtSignal(str)  # اضافه کردن این خط
    request_password = QtCore.pyqtSignal()  # سیگنال جدید برای درخواست رمز دو مرحله‌ای
    
    code_requested = QtCore.pyqtSignal(str)  # برای درخواست کد تأیید
    password_requested = QtCore.pyqtSignal()  # برای درخواست رمز 2FA
    auth_complete = QtCore.pyqtSignal(bool)   # برای نتیجه احراز هویت
    def __init__(self, api_id, api_hash, phone):
        super().__init__()
        self.api_id = api_id
        self.api_hash = api_hash
        self.phone = phone
        self.keywords = []
        self.proxy = None
        self.is_running = True
        self.show_code_dialog.connect(self._show_code_dialog_handler)
        self.request_password.connect(self._handle_password_request)
        self._code = None
        self._password = None
        # تنظیمات پروکسی اصلاح شده
        #self.proxy = ('jgc7td.co.uk',   9090, 'FgMBAgABAAH8AwOG4kw63Q'    )

    def _handle_password_request(self):
        """نمایش دیالوگ دریافت رمز دو مرحله‌ای"""
        dialog = QtWidgets.QDialog()
        dialog.setWindowTitle("رمز دو مرحله‌ای")
        dialog.setFixedSize(350, 200)
        
        layout = QtWidgets.QVBoxLayout()
        
        # متن راهنما
        label = QtWidgets.QLabel(
            "حساب شما دارای تأیید دو مرحله‌ای است.\n"
            "لطفاً رمز امنیتی خود را وارد کنید:"
        )
        layout.addWidget(label)
        
        # کادر ورود رمز
        self.password_input = QtWidgets.QLineEdit()
        self.password_input.setPlaceholderText("رمز دو مرحله‌ای")
        self.password_input.setEchoMode(QtWidgets.QLineEdit.Password)
        layout.addWidget(self.password_input)
        
        # دکمه‌ها
        btn_box = QtWidgets.QDialogButtonBox(
            QtWidgets.QDialogButtonBox.Ok | QtWidgets.QDialogButtonBox.Cancel
        )
        btn_box.accepted.connect(dialog.accept)
        btn_box.rejected.connect(dialog.reject)
        layout.addWidget(btn_box)
        
        dialog.setLayout(layout)
        
        if dialog.exec_() == QtWidgets.QDialog.Accepted:
            self._password = self.password_input.text().strip()
        else:
            self._password = None

    def _show_code_dialog_handler(self, phone):
        """مدیریت نمایش دیالوگ در Thread اصلی"""
        code, ok = QtWidgets.QInputDialog.getText(
            None,
            "کد تأیید تلگرام",
            f"کد 5 رقمی به شماره {phone} ارسال شد:",
            QtWidgets.QLineEdit.Normal
        )
        if ok and code:
            self._verification_code = code

    def update_proxy(self, proxy_settings):
        if proxy_settings.get('enabled', True):
            self.proxy = (
                proxy_settings.get('host'),
                proxy_settings.get('port'),
                proxy_settings.get('secret')
            )
        else:
            self.proxy = None

    async def create_client(self):
        if self.proxy:
            return TelegramClient(
                StringSession(),
                self.api_id,
                self.api_hash,
                proxy=self.proxy,
                connection=ConnectionTcpMTProxyRandomizedIntermediate,
                connection_retries=3,
                timeout=30
            )
        return TelegramClient(
            StringSession(),
            self.api_id,
            self.api_hash,
            connection_retries=3,
            timeout=30
        )

    async def main(self):
        try:
            self.status_changed.emit("در حال اتصال...")
            client = await self.create_client()
            
            await client.connect()
            self.status_changed.emit("اتصال برقرار شد")
            
            if not await client.is_user_authorized():
                if not await self.handle_authentication(client):
                    return
                    
            await self.start_monitoring(client)
            
        except Exception as e:
            self.status_changed.emit(f"خطا: {str(e)}")
            logger.error(f"Error in main: {e}")
        finally:
            if client and client.is_connected():
                await client.disconnect()
            self.status_changed.emit("اتصال قطع شد")
    '''
    async def handle_authentication(self, client):
        self.status_changed.emit("در حال ارسال کد تأیید...")
        
        try:
            await client.send_code_request(self.phone)
            self.status_changed.emit("کد تأیید ارسال شد")
            self.show_code_dialog.emit(self.phone)  # ارسال سیگنال برای نمایش دیالوگ
            
            # دریافت کد تأیید
            code = await self.get_verification_code()
            if not code:
                self.status_changed.emit("دریافت کد لغو شد")
                return False
            try:
                # تلاش برای ورود با کد
                await client.sign_in(self.phone, code)
                self.status_changed.emit("احراز هویت موفق")
                return True
            except Exception as e:
                if "two-steps" in str(e):
                    # درخواست رمز دو مرحله‌ای
                    self.status_changed.emit("نیاز به رمز دو مرحله‌ای")
                    self.request_password.emit()

                    # منتظر دریافت رمز
                    while not hasattr(self, '_password'):
                        await asyncio.sleep(0.5)
                    
                    password = self._password
                    del self._password
                    
                    if not password:
                        self.status_changed.emit("دریافت رمز لغو شد")
                        return False
                    
                    # تکمیل احراز هویت با رمز دو مرحله‌ای
                    await client.sign_in(password=password)
                    self.status_changed.emit("احراز هویت موفق")
                    
                    # منتظر دریافت کد بمانید
                    while not hasattr(self, '_verification_code'):
                        await asyncio.sleep(0.5)
                    
                    code = self._verification_code
                    del self._verification_code
                    
                    if not code:
                        self.status_changed.emit("دریافت کد لغو شد")
                        return False
                        
                    await client.sign_in(self.phone, code)
                    self.status_changed.emit("احراز هویت موفق")
                    return True
                raise
    
        except Exception as e:
            self.status_changed.emit(f"خطا: {str(e)}")
            logger.error(f"Authentication error: {e}")
            return False
    '''
    '''
    async def handle_authentication(self, client):
        self.status_changed.emit("در حال ارسال کد تأیید...") 
        try:
            await client.send_code_request(self.phone)
            self.status_changed.emit("کد تأیید ارسال شد")
            
            # دریافت کد تأیید
            code = await self.get_verification_code()
            if not code:
                self.status_changed.emit("دریافت کد لغو شد")
                return False
            
            try:
                # تلاش برای ورود با کد
                await client.sign_in(self.phone, code)
                self.status_changed.emit("احراز هویت موفق")
                return True
            except Exception as e:
                if "two-steps" in str(e):
                    # درخواست رمز دو مرحله‌ای
                    self.status_changed.emit("نیاز به رمز دو مرحله‌ای")
                    self.request_password.emit()
                    
                    # منتظر دریافت رمز
                    while not hasattr(self, '_password'):
                        await asyncio.sleep(0.5)
                    
                    password = self._password
                    del self._password
                    
                    if not password:
                        self.status_changed.emit("دریافت رمز لغو شد")
                        return False
                    
                    # تکمیل احراز هویت با رمز دو مرحله‌ای
                    await client.sign_in(password=password)
                    self.status_changed.emit("احراز هویت موفق")
                    return True
                raise
        except Exception as e:
            self.status_changed.emit(f"خطا: {str(e)}")
            logger.error(f"Authentication error: {e}")
            return False
    '''
    '''
    async def handle_authentication(self, client):
        self.status_changed.emit("در حال ارسال کد تأیید...")
        
        try:            
            # مرحله 1: ارسال و دریافت کد تأیید
            await client.send_code_request(self.phone)
            self.status_changed.emit("کد تأیید ارسال شد")
            self.show_code_dialog.emit(self.phone)  # ارسال سیگنال برای نمایش دیالوگ
            
            # دریافت کد تأیید از کاربر
            code = await self.get_verification_code()
            if not code:
                self.status_changed.emit("دریافت کد لغو شد")
                return False

            try:
                # مرحله 2: تلاش برای ورود با کد تأیید
                await client.sign_in(self.phone, code)
                self.status_changed.emit("احراز هویت موفق")
                return True
                
            except Exception as e:
                if "two-steps" in str(e):
                    # مرحله 3: اگر نیاز به رمز دو مرحله‌ای بود
                    return await self.handle_two_step_verification(client)
                raise
                
        except Exception as e:
            self.status_changed.emit(f"خطا: {str(e)}")
            logger.error(f"Authentication error: {e}")
            return False
    '''

    async def handle_authentication(self, client):
        try:
            self.status_changed.emit("در حال ارسال کد تأیید...")
            await client.send_code_request(self.phone)
            #self.show_code_dialog.emit(self.phone)
            
            # درخواست کد از کاربر
            self.code_requested.emit(self.phone)
            
            # منتظر دریافت کد بمانیم
            while self._code is None:
                await asyncio.sleep(0.1)
                
            code = self._code
            self._code = None
            
            if not code:
                self.auth_complete.emit(False)
                return

            try:
                await client.sign_in(self.phone, code)
                self.auth_complete.emit(True)
                
            except Exception as e:
                if "two-step" in str(e).lower():
                    # درخواست رمز 2FA
                    self.password_requested.emit()
                    
                    # منتظر دریافت رمز
                    while self._password is None:
                        await asyncio.sleep(0.1)
                        
                    password = self._password
                    self._password = None
                    
                    if password:
                        await client.sign_in(password=password)
                        self.auth_complete.emit(True)
                    else:
                        self.auth_complete.emit(False)
                else:
                    raise
                    
        except Exception as e:
            self.status_changed.emit(f"خطا: {str(e)}")
            self.auth_complete.emit(False)

    def submit_code(self, code):
        self._code = code

    def submit_password(self, password):
        self._password = password

    

    async def handle_two_step_verification(self, client):
        attempt = 0
        max_attempts = 3
        
        while attempt < max_attempts:
            attempt += 1
            self.status_changed.emit(f"نیاز به رمز دو مرحله‌ای (تلاش {attempt}/{max_attempts})")
            
            # دریافت رمز از کاربر
            password = await self.get_two_step_password()
            if not password:
                self.status_changed.emit("دریافت رمز لغو شد")
                return False
            
            try:
                # تلاش برای ورود با رمز دو مرحله‌ای
                await client.sign_in(password=password)
                self.status_changed.emit("احراز هویت موفق")
                return True
                
            except Exception as e:
                if attempt == max_attempts:
                    self.status_changed.emit("تعداد تلاش‌ها بیش از حد مجاز")
                    logger.error(f"2FA max attempts reached: {e}")
                    return False
                    
                if "password" in str(e).lower():
                    self.status_changed.emit("رمز نامعتبر، لطفاً مجدداً وارد کنید")
                    continue
                    
                self.status_changed.emit(f"خطا: {str(e)}")
                logger.error(f"2FA error: {e}")


    async def get_two_step_password(self):
        future = asyncio.Future()
        
        def show_dialog():
            dialog = QtWidgets.QDialog()
            dialog.setWindowTitle("رمز دو مرحله‌ای")
            dialog.setFixedSize(400, 200)
            
            layout = QtWidgets.QVBoxLayout()
            
            # متن راهنما
            label = QtWidgets.QLabel(
                "حساب شما دارای تأیید دو مرحله‌ای است.\n"
                "لطفاً رمز امنیتی خود را وارد کنید:"
            )
            layout.addWidget(label)
            
            # کادر ورود رمز
            password_input = QtWidgets.QLineEdit()
            password_input.setPlaceholderText("رمز دو مرحله‌ای")
            password_input.setEchoMode(QtWidgets.QLineEdit.Password)
            layout.addWidget(password_input)
            
            # دکمه‌ها
            btn_box = QtWidgets.QDialogButtonBox(
                QtWidgets.QDialogButtonBox.Ok | QtWidgets.QDialogButtonBox.Cancel
            )
            btn_box.accepted.connect(dialog.accept)
            btn_box.rejected.connect(dialog.reject)
            layout.addWidget(btn_box)
            
            dialog.setLayout(layout)
            
            if dialog.exec_() == QtWidgets.QDialog.Accepted:
                future.set_result(password_input.text().strip())
            else:
                future.set_result(None)
        
        QtCore.QTimer.singleShot(0, show_dialog)
        return await future


    async def handle_2fa(self, client):
        """مدیریت احراز هویت دو مرحله‌ای"""
        self.status_changed.emit("نیاز به رمز دو مرحله‌ای")
        
        # درخواست رمز دو مرحله‌ای از کاربر
        password = await self.get_2fa_password()
        if not password:
            self.status_changed.emit("دریافت رمز لغو شد")
            return False
        
        try:
            # تکمیل احراز هویت با رمز دو مرحله‌ای
            await client.sign_in(password=password)
            self.status_changed.emit("احراز هویت موفق")
            return True
            
        except Exception as e:
            self.status_changed.emit(f"خطا در رمز دو مرحله‌ای: {str(e)}")
            logger.error(f"2FA error: {e}")
            return await self.handle_2fa(client)  # تلاش مجدد

    async def get_2fa_password(self):
        """دریافت رمز دو مرحله‌ای از کاربر"""
        future = asyncio.Future()
        
        def show_dialog():
            dialog = QtWidgets.QInputDialog()
            dialog.setWindowTitle("رمز دو مرحله‌ای")
            dialog.setLabelText("لطفاً رمز دو مرحله‌ای حساب خود را وارد کنید:")
            dialog.setInputMode(QtWidgets.QInputDialog.TextInput)
            dialog.setTextEchoMode(QtWidgets.QLineEdit.Password)  # حالت رمز
            
            if dialog.exec_():
                future.set_result(dialog.textValue())
            else:
                future.set_result(None)
        
        QtCore.QTimer.singleShot(0, show_dialog)
        return await future



    async def get_verification_code(self):
        future = asyncio.Future()
            
        def show_dialog():
            dialog = QtWidgets.QInputDialog()
            dialog.setWindowTitle("کد تأیید تلگرام")
            dialog.setLabelText(f"کد 5 رقمی به شماره {self.phone} ارسال شد:")
            dialog.setInputMode(QtWidgets.QInputDialog.TextInput)
                
                # تنظیمات ظاهری
            dialog.setStyleSheet("""
                    QLabel {
                        font-size: 14px;
                        margin-bottom: 15px;
                    }
                    QLineEdit {
                        font-size: 16px;
                        padding: 5px;
                        min-width: 200px;
                    }
                """)
                
            if dialog.exec_():
                future.set_result(dialog.textValue())
            else:
                future.set_result(None)
            
            # اجرای دیالوگ در Thread اصلی GUI
        QtCore.QTimer.singleShot(0, show_dialog)
        return await future        

    async def start_monitoring(self, client):
        """شروع مانیتورینگ پیام‌ها"""
        self.status_changed.emit("در حال راه‌اندازی مانیتورینگ...")
        
        @client.on(events.NewMessage)
        async def handler(event):
            if not self.is_running:
                return
                
            message_text = event.message.text or ''
            for keyword in self.keywords:
                if keyword.lower() in message_text.lower():
                    chat = await event.get_chat()
                    self.new_message.emit({
                        'keyword': keyword,
                        'source': chat.title if hasattr(chat, 'title') else 'خصوصی',
                        'message': message_text,
                        'link': self._generate_message_link(event)
                    })
        
        self.status_changed.emit("مانیتورینگ فعال")
        await client.run_until_disconnected()

    def _generate_message_link(self, event):
        """تابع تولید لینک پیام"""
        try:
            chat_id = event.chat_id
            if isinstance(event.peer_id, PeerChannel):
                return f'https://t.me/c/{chat_id}/{event.message.id}'
            elif isinstance(event.peer_id, PeerChat):
                return f'https://t.me/c/{abs(chat_id)}/{event.message.id}'
            return 'Link not available'
        except Exception as e:
            print(f"Error generating link: {e}")
            return 'Error generating link'

    def run(self):
        self.is_running = True
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        loop.run_until_complete(self.main())
        loop.close()
        
    def stop(self):
        self.is_running = False
    
class MainWindow(QtWidgets.QMainWindow):
    def __init__(self):
        super().__init__()
        self.db = KeywordDB()  # اول ایجاد شود
        self.setup_ui()
        self.setup_menu()
        self.load_config()
        self.setup_telegram()
        self.telegram_thread = TelegramThread(self.api_id, self.api_hash, self.phone)
        self.setup_connections()


    def setup_connections(self):
        self.telegram_thread.code_requested.connect(self.show_code_dialog)
        self.telegram_thread.password_requested.connect(self.show_password_dialog)
        self.telegram_thread.status_changed.connect(self.update_status)
        self.telegram_thread.auth_complete.connect(self.handle_auth_result)

    def show_code_dialog(self, phone):
        code, ok = QtWidgets.QInputDialog.getText(
            self,
            "کد تأیید",
            f"کد به شماره {phone} ارسال شد:",
            QtWidgets.QLineEdit.Normal
        )
        self.telegram_thread.submit_code(code if ok else None)

    def show_password_dialog(self):
        password, ok = QtWidgets.QInputDialog.getText(
            self,
            "رمز دو مرحله‌ای",
            "لطفاً رمز امنیتی خود را وارد کنید:",
            QtWidgets.QLineEdit.Password
        )
        self.telegram_thread.submit_password(password if ok else None)

    def handle_auth_result(self, success):
        if success:
            # احراز هویت موفقیت‌آمیز بود
            self.status_changed.emit("احراز هویت موفق")
            
            # فعال کردن بخش‌های مختلف برنامه
            self.enable_ui_components(True)
            
            # نمایش پیام موفقیت
            QtWidgets.QMessageBox.information(
                self,
                "موفقیت",
                "احراز هویت با موفقیت انجام شد",
                QtWidgets.QMessageBox.Ok
            )
        else:
            # احراز هویت ناموفق بود
            self.status_changed.emit("احراز هویت ناموفق")
            
            # غیرفعال کردن بخش‌های حساس برنامه
            self.enable_ui_components(False)
            
            # نمایش پیام خطا
            QtWidgets.QMessageBox.warning(
                self,
                "خطا",
                "احراز هویت انجام نشد. لطفاً مجدداً تلاش کنید",
                QtWidgets.QMessageBox.Ok
            )
            
            # بازنشانی وضعیت اتصال
            self.reset_connection()

    def enable_ui_components(self, enabled):
        self.monitor_button.setEnabled(enabled)
        self.keyword_input.setEnabled(enabled)
        self.add_button.setEnabled(enabled)
        self.export_button.setEnabled(enabled)

    def reset_connection(self):
        if hasattr(self, 'telegram_thread'):
            self.telegram_thread.stop()
            self.telegram_thread.quit()
            self.telegram_thread.wait()
        
        # آماده‌سازی برای اتصال مجدد
        self.setup_telegram()
        self.status_changed.emit("آماده برای اتصال مجدد")    

    def setup_ui(self):
        self.setWindowTitle("مانیتورینگ تلگرام")
        self.resize(1000, 700)
        
        central = QtWidgets.QWidget()
        self.setCentralWidget(central)
        layout = QtWidgets.QVBoxLayout()
        central.setLayout(layout)
        
        # بخش مدیریت کلمات کلیدی
        keyword_box = QtWidgets.QGroupBox("مدیریت کلمات کلیدی")
        keyword_layout = QtWidgets.QHBoxLayout()
        
        self.keyword_input = QtWidgets.QLineEdit()
        self.keyword_input.setPlaceholderText("کلمه کلیدی جدید...")
        keyword_layout.addWidget(self.keyword_input)
        
        self.add_btn = QtWidgets.QPushButton("اضافه کردن")
        self.add_btn.clicked.connect(self.add_keyword)
        keyword_layout.addWidget(self.add_btn)
        
        self.remove_btn = QtWidgets.QPushButton("حذف")
        self.remove_btn.clicked.connect(self.remove_keyword)
        keyword_layout.addWidget(self.remove_btn)
        
        keyword_box.setLayout(keyword_layout)
        layout.addWidget(keyword_box)
        
        # لیست کلمات کلیدی
        self.keyword_list = QtWidgets.QListWidget()
        layout.addWidget(self.keyword_list)
        
        # نتایج
        result_box = QtWidgets.QGroupBox("نتایج مانیتورینگ")
        result_layout = QtWidgets.QVBoxLayout()
        
        toolbar = QtWidgets.QToolBar()
        self.clear_btn = QtWidgets.QPushButton("پاک کردن نتایج")
        self.clear_btn.clicked.connect(self.clear_results)
        toolbar.addWidget(self.clear_btn)
        
        self.export_btn = QtWidgets.QPushButton("خروجی CSV")
        self.export_btn.clicked.connect(self.export_results)
        toolbar.addWidget(self.export_btn)
        
        result_layout.addWidget(toolbar)
        
        self.result_table = QtWidgets.QTableWidget()
        self.result_table.setColumnCount(4)
        self.result_table.setHorizontalHeaderLabels(["کلمه کلیدی", "منبع", "پیام", "لینک"])
        self.result_table.horizontalHeader().setSectionResizeMode(0, QtWidgets.QHeaderView.ResizeToContents)
        self.result_table.horizontalHeader().setSectionResizeMode(1, QtWidgets.QHeaderView.ResizeToContents)
        self.result_table.horizontalHeader().setSectionResizeMode(2, QtWidgets.QHeaderView.Stretch)
        self.result_table.horizontalHeader().setSectionResizeMode(3, QtWidgets.QHeaderView.ResizeToContents)
        self.result_table.setEditTriggers(QtWidgets.QAbstractItemView.NoEditTriggers)
        self.result_table.doubleClicked.connect(self.open_link)
        
        result_layout.addWidget(self.result_table)
        result_box.setLayout(result_layout)
        layout.addWidget(result_box)
        
        # نوار وضعیت
        self.status_bar = QtWidgets.QStatusBar()
        self.status_label = QtWidgets.QLabel("آماده")
        self.status_bar.addPermanentWidget(self.status_label)
        self.setStatusBar(self.status_bar)
        
        self.load_keywords()
        
    def setup_menu(self):
        menubar = self.menuBar()
        
        settings_menu = menubar.addMenu("تنظیمات")
        proxy_action = QtWidgets.QAction("تنظیمات پروکسی", self)
        proxy_action.triggered.connect(self.show_proxy_settings)
        settings_menu.addAction(proxy_action)
        
    def show_proxy_settings(self):
        dialog = ProxySettingsDialog(self)
        if dialog.exec_() == QtWidgets.QDialog.Accepted:
            settings = dialog.get_settings()
            self.save_proxy_settings(settings)
            self.telegram_thread.update_proxy(settings)
            self.restart_telegram()
            
    def save_proxy_settings(self, settings):
        try:
            with open('config.json', 'r') as f:
                config = json.load(f)
        except:
            config = {}
            
        config['proxy'] = settings
        
        with open('config.json', 'w') as f:
            json.dump(config, f, indent=4)
            
    def restart_telegram(self):
        self.telegram.stop()
        self.telegram.quit()
        self.telegram.wait()
        self.setup_telegram()
        
    def load_config(self):
        try:
            with open('config.json') as f:
                config = json.load(f)
                self.api_id = config['api_id']
                self.api_hash = config['api_hash']
                self.phone = config.get('phone', '+56927915860')
                self.proxy_settings = config.get('proxy', {
                    'enabled': True,
                    'host': 'proxy.digitalresistance.dog',
                    'port': 443,
                    'secret': 'd41d8cd98f00b204e9800998ecf8427e'
                })
        except Exception as e:
            QtWidgets.QMessageBox.critical(self, "خطا", f"خطا در بارگذاری تنظیمات: {str(e)}")
            sys.exit(1)
            
    def setup_telegram(self):
        #self.db = KeywordDatabase()
        self.telegram = TelegramThread(self.api_id, self.api_hash, self.phone)
        self.telegram.update_proxy(self.proxy_settings)
        self.telegram.keywords = self.db.get_keywords()
        self.telegram.new_message.connect(self.add_result)
        self.telegram.status_changed.connect(self.update_status)
        self.telegram.start()
        
    def update_status(self, status):
        self.status_label.setText(status)
        
        if "کد نادرست" in status or "خطا" in status or "قطع" in status:
            self.status_label.setStyleSheet("color: red; font-weight: bold;")
        elif "کد نامعتبر" in status:
            self.status_label.setStyleSheet("color: orange; font-weight: bold;")
        elif "اتصال" in status or "مانیتورینگ" in status:
            self.status_label.setStyleSheet("color: green; font-weight: bold;")
        elif "کد" in status:  # برای پیام‌های مربوط به کد تأیید
            self.status_label.setStyleSheet("color: blue; font-weight: bold;")
        else:
            self.status_label.setStyleSheet("")
            
    def load_keywords(self):
        self.keyword_list.clear()
        self.keyword_list.addItems(self.db.get_keywords())
        
    def add_keyword(self):
        keyword = self.keyword_input.text().strip()
        if keyword and self.db.add_keyword(keyword):
            self.keyword_input.clear()
            self.load_keywords()
            self.telegram.keywords = self.db.get_keywords()
            
    def remove_keyword(self):
        item = self.keyword_list.currentItem()
        if item:
            self.db.remove_keyword(item.text())
            self.load_keywords()
            self.telegram.keywords = self.db.get_keywords()
            
    def add_result(self, result):
        row = self.result_table.rowCount()
        self.result_table.insertRow(row)
        
        self.result_table.setItem(row, 0, QtWidgets.QTableWidgetItem(result['keyword']))
        self.result_table.setItem(row, 1, QtWidgets.QTableWidgetItem(result['source']))
        self.result_table.setItem(row, 2, QtWidgets.QTableWidgetItem(result['message']))
        
        link = QtWidgets.QTableWidgetItem(result['link'])
        link.setForeground(QtGui.QColor(0, 0, 255))
        self.result_table.setItem(row, 3, link)
        
    def clear_results(self):
        self.result_table.setRowCount(0)
        
    def export_results(self):
        path, _ = QtWidgets.QFileDialog.getSaveFileName(
            self,
            "ذخیره نتایج",
            "",
            "CSV Files (*.csv)"
        )
        
        if path:
            try:
                with open(path, 'w', encoding='utf-8') as f:
                    headers = []
                    for col in range(self.result_table.columnCount()):
                        headers.append(self.result_table.horizontalHeaderItem(col).text())
                    f.write(",".join(headers) + "\n")
                    
                    for row in range(self.result_table.rowCount()):
                        row_data = []
                        for col in range(self.result_table.columnCount()):
                            item = self.result_table.item(row, col)
                            row_data.append(item.text() if item else "")
                        f.write(",".join(row_data) + "\n")
                        
                QtWidgets.QMessageBox.information(self, "موفق", "نتایج با موفقیت ذخیره شد")
            except Exception as e:
                QtWidgets.QMessageBox.critical(self, "خطا", f"خطا در ذخیره نتایج: {str(e)}")
                
    def open_link(self, item):
        if item.column() == 3:
            link = self.result_table.item(item.row(), 3).text()
            if link.startswith("http"):
                QtGui.QDesktopServices.openUrl(QtCore.QUrl(link))
                
    def closeEvent(self, event):
        self.telegram.stop()
        self.telegram.quit()
        self.telegram.wait()
        super().closeEvent(event)


if __name__ == '__main__':
    app = QtWidgets.QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec_())