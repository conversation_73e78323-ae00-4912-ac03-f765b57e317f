import asyncio
import sys
import json
import sqlite3
import logging
from PyQt5 import QtWidgets, <PERSON>t<PERSON><PERSON>, QtGui
from telethon import Telegram<PERSON>lient, events
from telethon.tl.types import P<PERSON><PERSON>han<PERSON>, PeerChat
from telethon.sessions import StringSession
from telethon.network import ConnectionTcpMTProxyRandomizedIntermediate

# تنظیمات پایه
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ProxySettingsDialog(QtWidgets.QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        
    def setup_ui(self):
        self.setWindowTitle("تنظیمات پروکسی")
        self.resize(450, 300)
        
        layout = QtWidgets.QVBoxLayout()
        
        # انتخاب نوع اتصال
        self.connection_group = QtWidgets.QGroupBox("نوع اتصال")
        self.direct_radio = QtWidgets.QRadioButton("اتصال مستقیم (بدون پروکسی)")
        self.proxy_radio = QtWidgets.QRadioButton("استفاده از پروکسی MTProto")
        self.proxy_radio.setChecked(True)
        
        connection_layout = QtWidgets.QVBoxLayout()
        connection_layout.addWidget(self.direct_radio)
        connection_layout.addWidget(self.proxy_radio)
        self.connection_group.setLayout(connection_layout)
        layout.addWidget(self.connection_group)
        
        # تنظیمات پروکسی
        self.proxy_group = QtWidgets.QGroupBox("تنظیمات پروکسی")
        form_layout = QtWidgets.QFormLayout()
        
        self.host_input = QtWidgets.QLineEdit()
        self.host_input.setPlaceholderText("مثال: proxy.example.com")
        form_layout.addRow("آدرس سرور:", self.host_input)
        
        self.port_input = QtWidgets.QLineEdit()
        self.port_input.setPlaceholderText("مثال: 443")
        form_layout.addRow("پورت:", self.port_input)
        
        self.secret_input = QtWidgets.QLineEdit()
        self.secret_input.setPlaceholderText("مثال: d41d8cd98f00b204e9800998ecf8427e")
        form_layout.addRow("کد امنیتی:", self.secret_input)
        
        self.proxy_group.setLayout(form_layout)
        layout.addWidget(self.proxy_group)
        
        # نمونه پروکسی
        sample_label = QtWidgets.QLabel(
            "پروکسی‌های نمونه:\n"
            "1. proxy.digitalresistance.dog - پورت: 443\n"
            "2. nl.mtproto.pro - پورت: 443\n"
            "کد امنیتی: d41d8cd98f00b204e9800998ecf8427e"
        )
        sample_label.setStyleSheet("color: #666; font-style: italic;")
        layout.addWidget(sample_label)
        
        # دکمه‌ها
        button_box = QtWidgets.QDialogButtonBox(
            QtWidgets.QDialogButtonBox.Ok | QtWidgets.QDialogButtonBox.Cancel
        )
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)
        
        self.setLayout(layout)
        self.load_settings()
        self.direct_radio.toggled.connect(self.toggle_proxy_settings)
        
    def toggle_proxy_settings(self, checked):
        self.proxy_group.setEnabled(not checked)
        
    def load_settings(self):
        try:
            with open('config.json') as f:
                config = json.load(f)
                proxy = config.get('proxy', {})
                
                if proxy.get('enabled', True):
                    self.proxy_radio.setChecked(True)
                    self.host_input.setText(proxy.get('host', ''))
                    self.port_input.setText(str(proxy.get('port', '')))
                    self.secret_input.setText(proxy.get('secret', ''))
                else:
                    self.direct_radio.setChecked(True)
        except:
            self.proxy_radio.setChecked(True)
            
    def get_settings(self):
        return {
            'enabled': not self.direct_radio.isChecked(),
            'host': self.host_input.text().strip(),
            'port': int(self.port_input.text()) if self.port_input.text().strip() else 443,
            'secret': self.secret_input.text().strip()
        }

class KeywordDatabase:
    def __init__(self, db_file='keywords.db'):
        self.conn = sqlite3.connect(db_file)
        self.create_table()
        
    def create_table(self):
        self.conn.execute('''
            CREATE TABLE IF NOT EXISTS keywords (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                keyword TEXT UNIQUE
            )
        ''')
        self.conn.commit()
        
    def add_keyword(self, keyword):
        try:
            self.conn.execute('INSERT INTO keywords (keyword) VALUES (?)', (keyword,))
            self.conn.commit()
            return True
        except sqlite3.IntegrityError:
            return False
            
    def remove_keyword(self, keyword):
        self.conn.execute('DELETE FROM keywords WHERE keyword = ?', (keyword,))
        self.conn.commit()
        
    def get_keywords(self):
        cursor = self.conn.execute('SELECT keyword FROM keywords')
        return [row[0] for row in cursor]

class TelegramMonitor(QtCore.QThread):
    new_message = QtCore.pyqtSignal(dict)
    status_changed = QtCore.pyqtSignal(str)
    
    def __init__(self, api_id, api_hash, phone):
        super().__init__()
        self.api_id = api_id
        self.api_hash = api_hash
        self.phone = phone
        self.keywords = []
        self.proxy = None
        self.is_running = True
        
    def update_proxy(self, proxy_settings):
        if proxy_settings.get('enabled', True):
            self.proxy = (
                proxy_settings.get('host'),
                proxy_settings.get('port'),
                proxy_settings.get('secret')
            )
        else:
            self.proxy = None
            
    async def create_client(self):
        if self.proxy:
            return TelegramClient(
                StringSession(),
                self.api_id,
                self.api_hash,
                proxy=self.proxy,
                connection=ConnectionTcpMTProxyRandomizedIntermediate,
                connection_retries=3,
                timeout=30
            )
        return TelegramClient(
            StringSession(),
            self.api_id,
            self.api_hash,
            connection_retries=3,
            timeout=30
        )
        
    async def main(self):
        try:
            self.status_changed.emit("در حال اتصال...")
            client = await self.create_client()
            
            await client.connect()
            self.status_changed.emit("اتصال برقرار شد")
            
            if not await client.is_user_authorized():
                self.status_changed.emit("در حال دریافت کد تأیید...")
                await client.send_code_request(self.phone)
                
                code, ok = await self.get_verification_code()
                if not ok:
                    self.status_changed.emit("لغو شد")
                    return
                    
                await client.sign_in(self.phone, code)
                self.status_changed.emit("احراز هویت موفق")
                
            @client.on(events.NewMessage)
            async def handler(event):
                if not self.is_running:
                    return
                    
                message = event.message.text or ''
                for keyword in self.keywords:
                    if keyword.lower() in message.lower():
                        chat = await event.get_chat()
                        self.new_message.emit({
                            'keyword': keyword,
                            'source': chat.title if hasattr(chat, 'title') else 'خصوصی',
                            'message': message,
                            'link': self.get_message_link(event)
                        })
                        
            self.status_changed.emit("در حال مانیتورینگ...")
            await client.run_until_disconnected()
            
        except Exception as e:
            self.status_changed.emit(f"خطا: {str(e)}")
            logger.error(f"Error: {e}")
        finally:
            self.status_changed.emit("قطع ارتباط")
            
    async def get_verification_code(self):
        future = asyncio.Future()
        
        def show_dialog():
            code, ok = QtWidgets.QInputDialog.getText(
                None,
                "کد تأیید",
                "کد ارسال شده به تلفن خود را وارد کنید:",
                QtWidgets.QLineEdit.Normal
            )
            future.set_result((code, ok))
            
        QtCore.QTimer.singleShot(0, show_dialog)
        return await future
        
    def get_message_link(self, event):
        try:
            if isinstance(event.peer_id, PeerChannel):
                return f"https://t.me/c/{event.chat_id}/{event.message.id}"
            return f"https://t.me/c/{abs(event.chat_id)}/{event.message.id}"
        except:
            return "#"
            
    def run(self):
        self.is_running = True
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        loop.run_until_complete(self.main())
        loop.close()
        
    def stop(self):
        self.is_running = False

class MainWindow(QtWidgets.QMainWindow):
    def __init__(self):
        super().__init__()
        self.db = KeywordDatabase()  # اول ایجاد شود
        self.setup_ui()
        self.setup_menu()
        self.load_config()
        self.setup_telegram()
        
    def setup_ui(self):
        self.setWindowTitle("مانیتورینگ تلگرام")
        self.resize(1000, 700)
        
        central = QtWidgets.QWidget()
        self.setCentralWidget(central)
        layout = QtWidgets.QVBoxLayout()
        central.setLayout(layout)
        
        # بخش مدیریت کلمات کلیدی
        keyword_box = QtWidgets.QGroupBox("مدیریت کلمات کلیدی")
        keyword_layout = QtWidgets.QHBoxLayout()
        
        self.keyword_input = QtWidgets.QLineEdit()
        self.keyword_input.setPlaceholderText("کلمه کلیدی جدید...")
        keyword_layout.addWidget(self.keyword_input)
        
        self.add_btn = QtWidgets.QPushButton("اضافه کردن")
        self.add_btn.clicked.connect(self.add_keyword)
        keyword_layout.addWidget(self.add_btn)
        
        self.remove_btn = QtWidgets.QPushButton("حذف")
        self.remove_btn.clicked.connect(self.remove_keyword)
        keyword_layout.addWidget(self.remove_btn)
        
        keyword_box.setLayout(keyword_layout)
        layout.addWidget(keyword_box)
        
        # لیست کلمات کلیدی
        self.keyword_list = QtWidgets.QListWidget()
        layout.addWidget(self.keyword_list)
        
        # نتایج
        result_box = QtWidgets.QGroupBox("نتایج مانیتورینگ")
        result_layout = QtWidgets.QVBoxLayout()
        
        toolbar = QtWidgets.QToolBar()
        self.clear_btn = QtWidgets.QPushButton("پاک کردن نتایج")
        self.clear_btn.clicked.connect(self.clear_results)
        toolbar.addWidget(self.clear_btn)
        
        self.export_btn = QtWidgets.QPushButton("خروجی CSV")
        self.export_btn.clicked.connect(self.export_results)
        toolbar.addWidget(self.export_btn)
        
        result_layout.addWidget(toolbar)
        
        self.result_table = QtWidgets.QTableWidget()
        self.result_table.setColumnCount(4)
        self.result_table.setHorizontalHeaderLabels(["کلمه کلیدی", "منبع", "پیام", "لینک"])
        self.result_table.horizontalHeader().setSectionResizeMode(0, QtWidgets.QHeaderView.ResizeToContents)
        self.result_table.horizontalHeader().setSectionResizeMode(1, QtWidgets.QHeaderView.ResizeToContents)
        self.result_table.horizontalHeader().setSectionResizeMode(2, QtWidgets.QHeaderView.Stretch)
        self.result_table.horizontalHeader().setSectionResizeMode(3, QtWidgets.QHeaderView.ResizeToContents)
        self.result_table.setEditTriggers(QtWidgets.QAbstractItemView.NoEditTriggers)
        self.result_table.doubleClicked.connect(self.open_link)
        
        result_layout.addWidget(self.result_table)
        result_box.setLayout(result_layout)
        layout.addWidget(result_box)
        
        # نوار وضعیت
        self.status_bar = QtWidgets.QStatusBar()
        self.status_label = QtWidgets.QLabel("آماده")
        self.status_bar.addPermanentWidget(self.status_label)
        self.setStatusBar(self.status_bar)
        
        self.load_keywords()
        
    def setup_menu(self):
        menubar = self.menuBar()
        
        settings_menu = menubar.addMenu("تنظیمات")
        proxy_action = QtWidgets.QAction("تنظیمات پروکسی", self)
        proxy_action.triggered.connect(self.show_proxy_settings)
        settings_menu.addAction(proxy_action)
        
    def show_proxy_settings(self):
        dialog = ProxySettingsDialog(self)
        if dialog.exec_() == QtWidgets.QDialog.Accepted:
            settings = dialog.get_settings()
            self.save_proxy_settings(settings)
            self.telegram.update_proxy(settings)
            self.restart_telegram()
            
    def save_proxy_settings(self, settings):
        try:
            with open('config.json', 'r') as f:
                config = json.load(f)
        except:
            config = {}
            
        config['proxy'] = settings
        
        with open('config.json', 'w') as f:
            json.dump(config, f, indent=4)
            
    def restart_telegram(self):
        self.telegram.stop()
        self.telegram.quit()
        self.telegram.wait()
        self.setup_telegram()
        
    def load_config(self):
        try:
            with open('config.json') as f:
                config = json.load(f)
                self.api_id = config['api_id']
                self.api_hash = config['api_hash']
                self.phone = config.get('phone', '+989143057625')
                self.proxy_settings = config.get('proxy', {
                    'enabled': True,
                    'host': 'proxy.digitalresistance.dog',
                    'port': 443,
                    'secret': 'd41d8cd98f00b204e9800998ecf8427e'
                })
        except Exception as e:
            QtWidgets.QMessageBox.critical(self, "خطا", f"خطا در بارگذاری تنظیمات: {str(e)}")
            sys.exit(1)
            
    def setup_telegram(self):
        #self.db = KeywordDatabase()
        self.telegram = TelegramMonitor(self.api_id, self.api_hash, self.phone)
        self.telegram.update_proxy(self.proxy_settings)
        self.telegram.keywords = self.db.get_keywords()
        self.telegram.new_message.connect(self.add_result)
        self.telegram.status_changed.connect(self.update_status)
        self.telegram.start()
        
    def update_status(self, status):
        self.status_label.setText(status)
        
        if "خطا" in status or "قطع" in status:
            self.status_label.setStyleSheet("color: red;")
        elif "اتصال" in status or "مانیتورینگ" in status:
            self.status_label.setStyleSheet("color: green;")
        else:
            self.status_label.setStyleSheet("color: blue;")
            
    def load_keywords(self):
        self.keyword_list.clear()
        self.keyword_list.addItems(self.db.get_keywords())
        
    def add_keyword(self):
        keyword = self.keyword_input.text().strip()
        if keyword and self.db.add_keyword(keyword):
            self.keyword_input.clear()
            self.load_keywords()
            self.telegram.keywords = self.db.get_keywords()
            
    def remove_keyword(self):
        item = self.keyword_list.currentItem()
        if item:
            self.db.remove_keyword(item.text())
            self.load_keywords()
            self.telegram.keywords = self.db.get_keywords()
            
    def add_result(self, result):
        row = self.result_table.rowCount()
        self.result_table.insertRow(row)
        
        self.result_table.setItem(row, 0, QtWidgets.QTableWidgetItem(result['keyword']))
        self.result_table.setItem(row, 1, QtWidgets.QTableWidgetItem(result['source']))
        self.result_table.setItem(row, 2, QtWidgets.QTableWidgetItem(result['message']))
        
        link = QtWidgets.QTableWidgetItem(result['link'])
        link.setForeground(QtGui.QColor(0, 0, 255))
        self.result_table.setItem(row, 3, link)
        
    def clear_results(self):
        self.result_table.setRowCount(0)
        
    def export_results(self):
        path, _ = QtWidgets.QFileDialog.getSaveFileName(
            self,
            "ذخیره نتایج",
            "",
            "CSV Files (*.csv)"
        )
        
        if path:
            try:
                with open(path, 'w', encoding='utf-8') as f:
                    headers = []
                    for col in range(self.result_table.columnCount()):
                        headers.append(self.result_table.horizontalHeaderItem(col).text())
                    f.write(",".join(headers) + "\n")
                    
                    for row in range(self.result_table.rowCount()):
                        row_data = []
                        for col in range(self.result_table.columnCount()):
                            item = self.result_table.item(row, col)
                            row_data.append(item.text() if item else "")
                        f.write(",".join(row_data) + "\n")
                        
                QtWidgets.QMessageBox.information(self, "موفق", "نتایج با موفقیت ذخیره شد")
            except Exception as e:
                QtWidgets.QMessageBox.critical(self, "خطا", f"خطا در ذخیره نتایج: {str(e)}")
                
    def open_link(self, item):
        if item.column() == 3:
            link = self.result_table.item(item.row(), 3).text()
            if link.startswith("http"):
                QtGui.QDesktopServices.openUrl(QtCore.QUrl(link))
                
    def closeEvent(self, event):
        self.telegram.stop()
        self.telegram.quit()
        self.telegram.wait()
        super().closeEvent(event)

if __name__ == "__main__":
    # ایجاد فایل کانفیگ اگر وجود ندارد
    try:
        with open('config.json') as f:
            pass
    except FileNotFoundError:
        with open('config.json', 'w') as f:
            json.dump({
                "api_id": "YOUR_API_ID",
                "api_hash": "YOUR_API_HASH",
                "phone": "+989123456789",
                "proxy": {
                    "enabled": True,
                    "host": "proxy.digitalresistance.dog",
                    "port": 443,
                    "secret": "d41d8cd98f00b204e9800998ecf8427e"
                }
            }, f, indent=4)
            
        QtWidgets.QMessageBox.information(
            None,
            "تنظیمات",
            "لطفاً فایل config.json را با اطلاعات خود پر کنید"
        )
    
    app = QtWidgets.QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec_())