import asyncio
import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
from pyrogram import Client, filters
from pyrogram.types import Message
import datetime
import config
import customtkinter as ctk
import tkinter as tk
from tkinter import scrolledtext
import asyncio
import threading
import customtkinter as ctk
from pyrogram import Client, filters

class TelegramMonitorApp:
    def __init__(self, root):
        self.root = root
        self.setup_ui()
        self.client = None
        self.loop = None
        self.monitoring_thread = None
        # تنظیمات ظاهری
        ctk.set_appearance_mode("Dark")  # حالت تاریک
        ctk.set_default_color_theme("green")  # تم سبز
        
        
        self.root.title("ربات مانیتورینگ تلگرام - نسخه پیشرفته")
        self.root.geometry("1000x700")
        
        # فونت فارسی
        self.farsi_font = ("Tahoma", 12)
        
        
        self.keywords = []
        
        self.monitoring = False
        self.target_channel = "https://t.me/+Vc4RVcLJPGQ4NzY5"
        
    def setup_ui(self):
        # فریم اصلی
        main_frame = ctk.CTkFrame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # بخش اضافه کردن کلمات کلیدی
        keyword_frame = ttk.LabelFrame(main_frame, text="مدیریت کلمات کلیدی", padding="10")
        keyword_frame.pack(fill=tk.X, pady=5)
        
        self.keyword_entry = ttk.Entry(keyword_frame, width=30)
        self.keyword_entry.pack(side=tk.LEFT, padx=5)
        
        add_btn = ttk.Button(keyword_frame, text="اضافه کردن", command=self.add_keyword)
        add_btn.pack(side=tk.LEFT, padx=5)
        
        remove_btn = ttk.Button(keyword_frame, text="حذف انتخاب شده", command=self.remove_keyword)
        remove_btn.pack(side=tk.RIGHT, padx=5)
        
        # لیست کلمات کلیدی
        self.keyword_listbox = tk.Listbox(main_frame, height=8, selectmode=tk.SINGLE)
        self.keyword_listbox.pack(fill=tk.X, pady=5)
        
        # بخش تنظیمات مانیتورینگ
        monitor_frame = ttk.LabelFrame(main_frame, text="تنظیمات مانیتورینگ", padding="10")
        monitor_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(monitor_frame, text="کانال/گروه هدف:").grid(row=0, column=0, sticky=tk.W)
        self.target_entry = ttk.Entry(monitor_frame, width=40)
        self.target_entry.grid(row=0, column=1, padx=5)
        self.target_entry.insert(0, "@example")  # مثال
        
        ttk.Label(monitor_frame, text="کانال مقصد:").grid(row=1, column=0, sticky=tk.W)
        self.dest_entry = ttk.Entry(monitor_frame, width=40)
        self.dest_entry.grid(row=1, column=1, padx=5)
        self.dest_entry.insert(0, "https://t.me/+Vc4RVcLJPGQ4NzY5")
        
        # دکمه‌های کنترل
        btn_frame = ttk.Frame(main_frame)
        btn_frame.pack(fill=tk.X, pady=10)
        
        self.start_btn = ttk.Button(btn_frame, text="شروع مانیتورینگ", command=self.start_monitoring)
        self.start_btn.pack(side=tk.LEFT, padx=5)
        
        self.stop_btn = ttk.Button(btn_frame, text="توقف مانیتورینگ", command=self.stop_monitoring, state=tk.DISABLED)
        self.stop_btn.pack(side=tk.LEFT, padx=5)
        
        # لاگ فعالیت‌ها
        log_frame = ttk.LabelFrame(main_frame, text="لاگ فعالیت‌ها", padding="10")
        log_frame.pack(fill=tk.BOTH, expand=True)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=15, wrap=tk.WORD)
        self.log_text.pack(fill=tk.BOTH, expand=True)
        
    def add_keyword(self):
        keyword = self.keyword_entry.get().strip()
        if keyword and keyword not in self.keywords:
            self.keywords.append(keyword)
            self.keyword_listbox.insert(tk.END, keyword)
            self.keyword_entry.delete(0, tk.END)
            self.log(f"کلمه کلیدی '{keyword}' اضافه شد")
    
    def remove_keyword(self):
        selection = self.keyword_listbox.curselection()
        if selection:
            keyword = self.keyword_listbox.get(selection)
            self.keyword_listbox.delete(selection)
            self.keywords.remove(keyword)
            self.log(f"کلمه کلیدی '{keyword}' حذف شد")
    
    def log(self, message):
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.log_text.see(tk.END)
    
    def start_monitoring(self):
        if not self.keywords:
            messagebox.showerror("خطا", "لطفا حداقل یک کلمه کلیدی اضافه کنید")
            return

        # ایجاد یک thread جدید برای اجرای asyncio loop
        self.monitoring_thread = threading.Thread(
            target=self.run_async_tasks,
            daemon=True
        )
        self.monitoring_thread.start()

        target = self.target_entry.get().strip()
        if not target:
            messagebox.showerror("خطا", "لطفا کانال/گروه هدف را مشخص کنید")
            return
            
        self.dest_channel = self.dest_entry.get().strip()
        if not self.dest_channel:
            messagebox.showerror("خطا", "لطفا کانال مقصد را مشخص کنید")
            return
            
        self.monitoring = True
        self.start_btn.config(state=tk.DISABLED)
        self.stop_btn.config(state=tk.NORMAL)
        
        self.log(f"شروع مانیتورینگ کانال {target} برای کلمات کلیدی: {', '.join(self.keywords)}")
        
        # راه‌اندازی کلاینت تلگرام
        asyncio.create_task(self.run_telegram_client(target))
    
    def run_async_tasks(self):
        # ایجاد یک event loop جدید
        self.loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self.loop)
        
        # اجرای همزمان کلاینت تلگرام
        self.loop.run_until_complete(self.run_telegram_client())

    def stop_monitoring(self):
        self.monitoring = False
        self.start_btn.config(state=tk.NORMAL)
        self.stop_btn.config(state=tk.DISABLED)
        self.log("مانیتورینگ متوقف شد")
    
    async def run_telegram_client(self, target):
        self.client = Client(
            "https://t.me/tabrizcrowtbzbot",
            api_id=config.API_ID,
            api_hash=config.API_HASH,
            bot_token=config.BOT_TOKEN
        )
        
        @self.client.on_message(filters.chat(target))
        async def handler(client, message):
            if message.text:
                for keyword in self.keywords:
                    if keyword.lower() in message.text.lower():
                        await self.process_matched_message(keyword, message)
                        break
    
        await self.client.start()
        self.update_ui("ربات شروع به کار کرد")
        await asyncio.Event().wait()  # اجرای نامحدود
        await self.client.stop()
        
    async def process_matched_message(self, keyword, message):
        try:
            # ارسال به کانال مقصد
            text = f"{message.text}\n\n#{keyword.replace(' ', '_')}"
            await self.client.send_message(
                chat_id=self.destination_channel,
                text=text
            )
            self.update_ui(f"پیام با کلمه کلیدی {keyword} ارسال شد")
        except Exception as e:
            self.update_ui(f"خطا: {str(e)}")

    def update_ui(self, message):
        # برای به روزرسانی UI از thread اصلی استفاده می‌کنیم
        self.root.after(0, lambda: self.log_area.insert("end", message + "\n"))

    def show_error(self, message):
        self.root.after(0, lambda: ctk.CTkMessagebox(
            title="خطا",
            message=message,
            icon="cancel"
        ))    
    
    async def handle_keyword_match(self, keyword, message):
        try:
            # ارسال پیام به کانال مقصد
            caption = f"پیام حاوی کلمه کلیدی:\n\n{message.text}\n\n#{keyword.replace(' ', '_')}"
            
            if message.photo:
                await self.client.send_photo(
                    chat_id=self.dest_channel,
                    photo=message.photo.file_id,
                    caption=caption
                )
            elif message.video:
                await self.client.send_video(
                    chat_id=self.dest_channel,
                    video=message.video.file_id,
                    caption=caption
                )
            elif message.document:
                await self.client.send_document(
                    chat_id=self.dest_channel,
                    document=message.document.file_id,
                    caption=caption
                )
            else:
                await self.client.send_message(
                    chat_id=self.dest_channel,
                    text=caption
                )
            
            self.log(f"کلمه کلیدی '{keyword}' در پیام {message.id} یافت شد و به کانال ارسال شد")
            
        except Exception as e:
            self.log(f"خطا در ارسال پیام: {str(e)}")

if __name__ == "__main__":
    root = tk.Tk()
    app = TelegramMonitorApp(root)
    
    # تنظیم تم زیبا
    #root.tk.call('source', 'azure.tcl')
    #root.tk.call('set_theme', 'dark')
    #root.tk.call("set", "::tk::theme::variant", "dark")  # برای حالت تاریک
# یا
    #root.tk.call("set", "::tk::theme::variant", "light")  # برای حالت روشن
    
    root.mainloop()