import pandas as pd  
import tkinter as tk  
from tkinter import filedialog, messagebox  

class DuplicateFinderApp:  
    def __init__(self, master):  
        self.master = master  
        master.title("Duplicate Finder")  

        self.label = tk.Label(master, text="لطفاً نام ستون‌های مورد نظر را وارد کنید (جداشده با ویرگول):")  
        self.label.pack()  

        self.column_entry = tk.Entry(master, width=50)  
        self.column_entry.pack()  

        self.upload_button = tk.Button(master, text="بارگذاری فایل‌ها", command=self.upload_files)  
        self.upload_button.pack()  

        self.find_button = tk.Button(master, text="یافتن رکوردهای تکراری", command=self.find_duplicates)  
        self.find_button.pack()  

        self.files = []  

    def upload_files(self):  
        file_paths = filedialog.askopenfilenames(title="انتخاب فایل‌های اکسل", filetypes=[("Excel files", "*.xlsx;*.xls")])  
        self.files = file_paths  

    def find_duplicates(self):  
        column_names = [col.strip() for col in self.column_entry.get().split(',')]  
        
        if not self.files:  
            messagebox.showerror("خطا", "لطفاً ابتدا فایل‌ها را بارگذاری کنید.")  
            return  
        
        combined_data = pd.DataFrame()  

        for file in self.files:  
            data = pd.read_excel(file)  
            combined_data = pd.concat([combined_data, data], ignore_index=True)  

        missing_columns = [col for col in column_names if col not in combined_data.columns]  
        if missing_columns:  
            messagebox.showerror("خطا", f"ستون‌های زیر در فایل‌ها یافت نشدند: {', '.join(missing_columns)}")  
            return  

        duplicates = combined_data[combined_data.duplicated(subset=column_names, keep=False)]  
        
        if not duplicates.empty:  
            result = duplicates[column_names].value_counts()  
            result_str = result.to_string()  
            messagebox.showinfo("رکوردهای تکراری", result_str)  
        else:  
            messagebox.showinfo("نتیجه", "هیچ رکورد تکراری یافت نشد.")  

if __name__ == "__main__":  
    root = tk.Tk()  
    app = DuplicateFinderApp(root)  
    root.mainloop()  