global idi
idi=0
f=open("book.txt", "r")
li=f.readlines()
for (i, item) in enumerate(li, start=1):
        parts=item.split(',')
        idiu=parts[0]
        print(i)
print("dfddddddddddddddddddddddddddddd")        
print(i)
#idi=idi+i
#print(idi)
with open("book.txt", "a+")  as file:
        idi=idi+i
        name = input("Enter the name : ") 
        family = input("Enter the family : ")
        phone = input("Enter the phone : ")
        file.write(str(idi)+","+name+","+family+","+phone) 
        file.write("\n") 
        file.close() 
print("Data is written into the file.")









"""
ADD 
idi=0
with open("book.txt", "a+")  as file:
        idi+=1
        name = input("Enter the name : ") 
        family = input("Enter the family : ")
        phone = input("Enter the phone : ")
        file.write(str(idi)+","+name+","+family+","+phone) 
        file.write("\n") 
    
file.close() 

print("Data is written into the file.") 
"""





"""
Deleted
fr=open("book.txt", "r")
print(fr.read())

with open("book.txt", "r+") as f:
    d = f.readlines()
    f.seek(0)
    hazf=input("Enter The name or family for Delete :")
    for i in d:
        if hazf not in i:
            f.write(i)          
    f.truncate()

print(fr.read())
fr.close()
"""
"""
SERCH
fr=open("book.txt", "r")
li=fr.readlines()
serchnf=input("Enter name or family for search :  ")
found = False
for (i, item) in enumerate(li, start=1):
    if serchnf in item:
        parts=item.split(',')
        name=parts[1]
        family=parts[2]
        phone=parts[3]
        print(name , " : " , family  , " : ",phone , end="\n" ) 
        found = True   
if not found:
    print("Not found")

fr.close()
"""
"""
    if serchnf in item:
        print("Item #%d is %s" % (i, str(item)))
        found=True
if not found:
     print("Not Found")
fr.close()
"""
"""
    for l in range(len(li)):
        parts=l.split(',')
        name=parts[1]
        family=parts[2]
        print(name , ": " , family , end="\n" )
"""       


"""


for (i, item) in enumerate(li):     
        if serchnf in item :
           print("Item #%d is %s" % (i, str(item))) 
           fr.close()
           break            
        else:
              print("not found")
              break
"""              
         
"""
fr=open("book.txt", "r")
li=fr.readlines()
#data = ['itemA', 'itemB', 'itemC', 'itemD']
for (i, item) in enumerate(li):
    print("Item #%d is %s" % (i, str(item)))
"""