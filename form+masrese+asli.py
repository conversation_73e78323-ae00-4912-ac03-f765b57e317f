import tkinter as tk  
from tkinter import ttk  
import ttkbootstrap as tb  
import mysql.connector  
from mysql.connector import Error  
import re  
from tkinter import messagebox  
from datetime import datetime  

# اطلاعات اتصال به پایگاه داده (جایگزین کنید)  
db_config = {  
    'user': 'root',  
    'password': '123456',  
    'host': 'localhost',  
    'database': 'school_management'  
}  




def validate_name(name):  
    """  
    اعتبارسنجی نام و نام خانوادگی.  
    """  
    if not name:  
        return False, "نام نمی تواند خالی باشد."  
    if not re.match("^[a-zA-Z\u0600-\u06FF]+$", name):  # تطبیق با حروف فارسی و انگلیسی  
        return False, "نام باید فقط شامل حروف باشد."  
    if len(name) < 2:  
        return False, "نام باید حداقل 2 حرف داشته باشد."  
    return True, ""  

def validate_phone_number(phone):  
    """  
    اعتبارسنجی شماره تلفن.  
    """  
    if not phone:  
        return False, "شماره تلفن نمی تواند خالی باشد."  
    if not re.match("^09\d{9}$", phone):  
        return False, "شماره تلفن باید 11 رقم باشد و با 09 شروع شود."  
    return True, ""  

def connect_to_database():  
    """  
    اتصال به پایگاه داده MySQL.  
    """  
    try:  
        connection = mysql.connector.connect(**db_config)  
        print("Connected to database successfully!")  
        return connection  
    except Error as err:  
        print(f"Error connecting to database: {err}")  
        return None  

def add_student():  
    """  
    تابعی برای اضافه کردن دانش آموز به پایگاه داده.  
    """  
    first_name = first_name_entry.get()  
    last_name = last_name_entry.get()  
    birth_date_str = birth_date_entry.get()  
    gender = gender_var.get()  
    address = address_entry.get("1.0", tk.END)  
    phone_number = phone_number_entry.get()  

    # اعتبارسنجی نام  
    is_valid, message = validate_name(first_name)  
    if not is_valid:  
        messagebox.showerror("Error", message)  
        return  

    # اعتبارسنجی نام خانوادگی  
    is_valid, message = validate_name(last_name)  
    if not is_valid:  
        messagebox.showerror("Error", message)  
        return  

    # اعتبارسنجی شماره تلفن  
    is_valid, message = validate_phone_number(phone_number)  
    if not is_valid:  
        messagebox.showerror("Error", message)  
        return  

    try:  
        connection = connect_to_database()  
        if connection:  
            cursor = connection.cursor()  

            # تبدیل رشته تاریخ به فرمت مناسب  
            try:  
                birth_date = datetime.strptime(birth_date_str, "%Y-%m-%d").date()  
                birth_date = birth_date.strftime("%Y-%m-%d")  
            except ValueError:  
                messagebox.showerror("Error", "فرمت تاریخ نامعتبر است. لطفا از فرمت YYYY-MM-DD استفاده کنید.")  
                return  

            query = "INSERT INTO students (first_name, last_name, date_of_birth, gender, address, phone_number) VALUES (%s, %s, %s, %s, %s, %s)"  
            values = (first_name, last_name, birth_date, gender, address, phone_number)  
            cursor.execute(query, values)  
            connection.commit()  
            print("Student added successfully!")  
            messagebox.showinfo("Success", "Student added successfully!")  
            connection.close()  

            # پاک کردن فرم بعد از ثبت  
            first_name_entry.delete(0, tk.END)  
            last_name_entry.delete(0, tk.END)  
            birth_date_entry.delete(0, tk.END)  
            address_entry.delete("1.0", tk.END)  
            phone_number_entry.delete(0, tk.END)  

            # بروزرسانی Treeview  
            update_student_list()  

    except Error as err:  
        print(f"Error adding student: {err}")  
        messagebox.showerror("Error", f"Error adding student: {err}")  

def fetch_students():  
    """  
    واکشی اطلاعات دانش آموزان از پایگاه داده.  
    """  
    try:  
        connection = connect_to_database()  
        if connection:  
            cursor = connection.cursor()  
            query = "SELECT * FROM students"  
            cursor.execute(query)  
            students = cursor.fetchall()  
            connection.close()  
            return students  
        else:  
            return []  
    except Error as e:  
        print(f"Error fetching students: {e}")  
        messagebox.showerror("Error", f"Error fetching students: {e}")  
        return []  

def update_student_list():  
    """  
    بروزرسانی لیست دانش آموزان در Treeview.  
    """  
    # پاک کردن اطلاعات قبلی Treeview  
    for item in students_tree.get_children():  
        students_tree.delete(item)  

    students = fetch_students()  
    for student in students:  
        students_tree.insert("", tk.END, values=(student[0], student[1], student[2], student[3], student[4], student[5], student[6]))  

def select_student(event):  
    """  
    نمایش اطلاعات دانش آموز انتخاب شده در فرم.  
    """  
    selected_item = students_tree.selection()[0]  
    student = students_tree.item(selected_item)['values']  

    # پر کردن فرم با اطلاعات دانش آموز انتخاب شده  
    first_name_entry.delete(0, tk.END)  
    first_name_entry.insert(0, student[1])  

    last_name_entry.delete(0, tk.END)  
    last_name_entry.insert(0, student[2])  

    birth_date_entry.delete(0, tk.END)  
    birth_date_entry.insert(0, student[3])  

    gender_var.set(student[4])  

    address_entry.delete("1.0", tk.END)  
    address_entry.insert("1.0", student[5])  

    phone_number_entry.delete(0, tk.END)  
    phone_number_entry.insert(0, student[6])  

def update_student():  
    """  
    به روز رسانی اطلاعات دانش آموز در پایگاه داده.  
    """  
    selected_item = students_tree.selection()[0]  
    student_id = students_tree.item(selected_item)['values'][0]  

    first_name = first_name_entry.get()  
    last_name = last_name_entry.get()  
    birth_date_str = birth_date_entry.get()  
    gender = gender_var.get()  
    address = address_entry.get("1.0", tk.END)  
    phone_number = phone_number_entry.get()  

    # اعتبارسنجی نام  
    is_valid, message = validate_name(first_name)  
    if not is_valid:  
        messagebox.showerror("Error", message)  
        return  

    # اعتبارسنجی نام خانوادگی  
    is_valid, message = validate_name(last_name)  
    if not is_valid:  
        messagebox.showerror("Error", message)  
        return  

    # اعتبارسنجی شماره تلفن  
    is_valid, message = validate_phone_number(phone_number)  
    if not is_valid:  
        messagebox.showerror("Error", message)  
        return  

    try:  
        connection = connect_to_database()  
        if connection:  
            cursor = connection.cursor()  

            # تبدیل رشته تاریخ به فرمت مناسب  
            try:  
                birth_date = datetime.strptime(birth_date_str, "%Y-%m-%d").date()  
                birth_date = birth_date.strftime("%Y-%m-%d")  
            except ValueError:  
                messagebox.showerror("Error", "فرمت تاریخ نامعتبر است. لطفا از فرمت YYYY-MM-DD استفاده کنید.")  
                return  

            query = "UPDATE students SET first_name = %s, last_name = %s, date_of_birth = %s, gender = %s, address = %s, phone_number = %s WHERE student_id = %s"  
            values = (first_name, last_name, birth_date, gender, address, phone_number, student_id)  
            cursor.execute(query, values)  
            connection.commit()  
            print("Student updated successfully!")  
            messagebox.showinfo("Success", "Student updated successfully!")  
            connection.close()  

            # بروزرسانی Treeview  
            update_student_list()  

    except Error as err:  
        print(f"Error updating student: {err}")  
        messagebox.showerror("Error", f"Error updating student: {err}")  

def delete_student():  
    """  
    حذف دانش آموز از پایگاه داده.  
    """  
    selected_item = students_tree.selection()[0]  
    student_id = students_tree.item(selected_item)['values'][0]  

    if messagebox.askyesno("Confirm", "آیا مطمئن هستید که می خواهید این دانش آموز را حذف کنید؟"):  
        try:  
            connection = connect_to_database()  
            if connection:  
                cursor = connection.cursor()  
                query = "DELETE FROM students WHERE student_id = %s"  
                values = (student_id,)  
                cursor.execute(query, values)  
                connection.commit()  
                print("Student deleted successfully!")  
                messagebox.showinfo("Success", "Student deleted successfully!")  
                connection.close()  

                # بروزرسانی Treeview  
                update_student_list()  

                # پاک کردن فرم  
                first_name_entry.delete(0, tk.END)  
                last_name_entry.delete(0, tk.END)  
                birth_date_entry.delete(0, tk.END)  
                address_entry.delete("1.0", tk.END)  
                phone_number_entry.delete(0, tk.END)  

        except Error as err:  
            print(f"Error deleting student: {err}")  
            messagebox.showerror("Error", f"Error deleting student: {err}")  

def main():  
    """  
    تابع اصلی برنامه.  
    """  
    global root, first_name_entry, last_name_entry, birth_date_entry, address_entry, phone_number_entry, gender_var, students_tree  
    root = tb.Window(themename="darkly")  
    root.title("School Management System")  
    root.geometry("900x600")  

    # --- فرم ثبت دانش آموز ---  
    student_form_frame = tb.Frame(root, padding=20)  
    student_form_frame.pack(fill="x")  

    # برچسب و ورودی نام  
    first_name_label = tb.Label(student_form_frame, text="First Name:")  
    first_name_label.grid(row=0, column=0, padx=5, pady=5, sticky="w")  
    first_name_entry = tb.Entry(student_form_frame)  
    first_name_entry.grid(row=0, column=1, padx=5, pady=5, sticky="e")  

    # برچسب و ورودی نام خانوادگی  
    last_name_label = tb.Label(student_form_frame, text="Last Name:")  
    last_name_label.grid(row=1, column=0, padx=5, pady=5, sticky="w")  
    last_name_entry = tb.Entry(student_form_frame)  
    last_name_entry.grid(row=1, column=1, padx=5, pady=5, sticky="e")  

    # برچسب و ورودی تاریخ تولد  
    birth_date_label = tb.Label(student_form_frame, text="Birth Date (YYYY-MM-DD):")  
    birth_date_label.grid(row=2, column=0, padx=5, pady=5, sticky="w")  
    birth_date_entry = tb.Entry(student_form_frame)  
    birth_date_entry.grid(row=2, column=1, padx=5, pady=5, sticky="e")  

    # برچسب و انتخاب جنسیت  
    gender_label = tb.Label(student_form_frame, text="Gender:")  
    gender_label.grid(row=3, column=0, padx=5, pady=5, sticky="w")  
    gender_var = tk.StringVar(value="Male")  
    male_radio = tb.Radiobutton(student_form_frame, text="Male", variable=gender_var, value="Male")  
    male_radio.grid(row=3, column=1, padx=5, pady=5, sticky="w")  
    female_radio = tb.Radiobutton(student_form_frame, text="Female", variable=gender_var, value="Female")  
    female_radio.grid(row=3, column=2, padx=5, pady=5, sticky="w")  

    # برچسب و ورودی آدرس  
    address_label = tb.Label(student_form_frame, text="Address:")  
    address_label.grid(row=4, column=0, padx=5, pady=5, sticky="w")  
    address_entry = tb.Text(student_form_frame, height=3, width=25)  
    address_entry.grid(row=4, column=1, padx=5, pady=5, sticky="e")  

    # برچسب و ورودی شماره تلفن  
    phone_number_label = tb.Label(student_form_frame, text="Phone Number:")  
    phone_number_label.grid(row=5, column=0, padx=5, pady=5, sticky="w")  
    phone_number_entry = tb.Entry(student_form_frame)  
    phone_number_entry.grid(row=5, column=1, padx=5, pady=5, sticky="e")  

    # دکمه ها  
    add_button = tb.Button(student_form_frame, text="Add Student", command=add_student)  
    add_button.grid(row=6, column=0, padx=5, pady=10, sticky="w")  

    update_button = tb.Button(student_form_frame, text="Update Student", command=update_student)  
    update_button.grid(row=6, column=1, padx=5, pady=10, sticky="w")  

    delete_button = tb.Button(student_form_frame, text="Delete Student", command=delete_student)  
    delete_button.grid(row=6, column=2, padx=5, pady=10, sticky="w")  

    # --- Treeview برای نمایش لیست دانش آموزان ---  
    columns = ("ID", "First Name", "Last Name", "Birth Date", "Gender", "Address", "Phone Number")  
    students_tree = tb.Treeview(root, columns=columns, show="headings")  

    for col in columns:  
        students_tree.heading(col, text=col)  
        students_tree.column(col, width=120, anchor="center")  # تنظیم عرض ستون ها  

    students_tree.pack(fill="both", expand=True, padx=20, pady=10)  

    # اتصال تابع select_student به رویداد انتخاب در Treeview  
    students_tree.bind("<ButtonRelease-1>", select_student)  

    # نمایش لیست اولیه دانش آموزان  
    update_student_list()  

    root.mainloop()  

if __name__ == "__main__":  
    main()  