import tkinter as tk
from tkinter import*
import mysql.connector
from tkinter import messagebox
 

def connect_db():  
   return mysql.connector.connect(
    host="localhost",
    user="root", 
    password="123456",
    database="Clinicmaster",
    auth_plugin="mysql_native_password"
    )

 
def clinic():
    conn = connect_db()  
    cursor = conn.cursor()  
    clinic_id=ent1.get()
    clinic_name=ent2.get()
    clinic_address=ent3.get()
    clinic_city=ent4.get()
    clinic_state=ent5.get()
    clinic_phone=ent6.get()
    clinic_email=ent7.get()
    clinic_website=ent8.get()
    
    sql=("INSERT INTO dentalclinic(clinic_id,clinic_name,clinic_address,clinic_city,clinic_state,clinic_phone,clinic_email,clinic_website)"  "VALUES(%s,%s,%s,%s,%s,%s,%s,%s)" ) 
    cursor.execute(sql,(clinic_id,clinic_name,clinic_address,clinic_city,clinic_state,clinic_phone,clinic_email,clinic_website))
    conn.commit()
    messagebox.showinfo("Confirmation","Clinic Added")
    print("DONE")
    cursor.close()  
    conn.close() 
  
    return True
 
def clear_text():
    conn = connect_db()  
    cursor = conn.cursor()  
    cursor.execute('SELECT MAX(clinic_id) FROM dentalclinic')  
    next_num = cursor.fetchall()[0][0]
    clinic_id.set(next_num+1)
    clinic_name.set(" ")
    clinic_address.set(" ")   
    clinic_city.set(" ") 
    clinic_state.set(" ") 
    clinic_phone.set(" ") 
    clinic_email.set(" ") 
    clinic_website.set(" ") 

def nplus():
    conn = connect_db()  
    cursor = conn.cursor()  
    cursor.execute('SELECT MAX(clinic_id) FROM dentalclinic')  
    next_num = cursor.fetchone()
    #next_num+=1
    #print(next_num)
    conn.commit()
    cursor.close()  
    conn.close() 
    return next_num
    
def g_text():

    endnum=nplus()[0]
    print(endnum)
    

    conn = connect_db()  
    cursor = conn.cursor() 
    cursor.execute('SELECT * FROM dentalclinic ')  
    print(cursor)
    next_num = cursor.fetchall()
    print(next_num[endnum-1])
    clinic_id.set(next_num[endnum-1][0])
    clinic_name.set(next_num[endnum-1][1])
    clinic_address.set(next_num[endnum-1][2])
    clinic_city.set(next_num[endnum-1][3])
    clinic_state.set(next_num[endnum-1][4]) 
    clinic_phone.set(next_num[endnum-1][5])
    clinic_email.set(next_num[endnum-1][6]) 
    clinic_website.set(next_num[endnum-1][7])


"""    
    cursor.execute('SELECT MAX(clinic_id) FROM dentalclinic')  
    next_num = cursor.fetchall()[0][0]
    clinic_id.set(next_num-1)
    cursor.execute('SELECT * FROM dentalclinic') 
    name=cursor.fetchall()[next_num-1][1]
    print("hggggggggggggggggggggggggggggj",name)
    clinic_name.set(name)
    cursor.execute('SELECT * FROM dentalclinic') 
    addr=cursor.fetchall()[next_num-1][2]
    clinic_address.set(addr)  
    cursor.execute('SELECT * FROM dentalclinic') 
    cit=cursor.fetchall()[next_num-1][3] 
    clinic_city.set(cit)
    cursor.execute('SELECT * FROM dentalclinic')  
    sta=cursor.fetchall()[next_num-1][4] 
    clinic_state.set(sta) 
    cursor.execute('SELECT * FROM dentalclinic') 
    pho=cursor.fetchall()[next_num-1][5]
    clinic_phone.set(pho)
    cursor.execute('SELECT * FROM dentalclinic')  
    emi=cursor.fetchall()[next_num-1][6]
    clinic_email.set(emi) 
    cursor.execute('SELECT * FROM dentalclinic') 
    webi=cursor.fetchall()[next_num-1][7]
    clinic_website.set(webi)
"""








def f_text():
    conn = connect_db()  
    cursor = conn.cursor() 
    cursor.execute('SELECT * FROM dentalclinic')  
    next_num = cursor.fetchone()
    clinic_id.set(next_num[0])
    clinic_name.set(next_num[1])
    clinic_address.set(next_num[2])
    clinic_city.set(next_num[3])
    clinic_state.set(next_num[4]) 
    clinic_phone.set(next_num[5])
    clinic_email.set(next_num[6]) 
    clinic_website.set(next_num[7])
    conn.commit()
    cursor.close()  
    conn.close() 
    
def e_text():
    endnum=nplus()[0]
    print(endnum)
    

    conn = connect_db()  
    cursor = conn.cursor() 
    cursor.execute('SELECT * FROM dentalclinic ')  
    next_num = cursor.fetchall()
    clinic_id.set(next_num[endnum-1][0])
    clinic_name.set(next_num[endnum-1][1])
    clinic_address.set(next_num[endnum-1][2])
    clinic_city.set(next_num[endnum-1][3])
    clinic_state.set(next_num[endnum-1][4]) 
    clinic_phone.set(next_num[endnum-1][5])
    clinic_email.set(next_num[endnum-1][6]) 
    clinic_website.set(next_num[endnum-1][7])    
    conn.commit()
    cursor.close()  
    conn.close() 
 
win=Tk()
win.title("ADD Clinic")
win.geometry("500x600")
win.configure(background='light blue')
win.resizable(False,False)
 
#frm1=Frame(win,bg="light blue")
#frm1.pack(side=tk.LEFT,padx=20)
 
var1=StringVar()
clinic_id=StringVar()
var2=StringVar()
clinic_name=StringVar()
var3=StringVar()
clinic_address=StringVar()
var4=StringVar()
clinic_city=StringVar()
var5=StringVar()
clinic_state=StringVar()
var6=StringVar()
clinic_phone=StringVar()
var7=StringVar()
clinic_email=StringVar()
var8=StringVar()
clinic_website=StringVar()
 
 
##cursor.execute('select (clinic_name) from dentalclinic')
##cname=cursor.fetchall()
#cname=StringVar()
#cname.set(sql1)
label= Label(win,text= "Clinic Details",font=('Helvetica 20 italic'),bg="light blue")
label.grid(row=0,column=2,padx=10,pady=10)
#var1['value']=c_name
 
label1=Label(win,textvariable=var1,bg="light blue")
var1.set("Clinic ID. ")
label1.grid(row=2,column=1,padx=10,pady=10)
ent1=Entry(win,textvariable=clinic_id,width=10)
#print(nplus()[0])
clinic_id.set(nplus()[0]+1)





ent1.grid(row=2,column=2,sticky=tk.W,padx=10,pady=10)
 
label2=Label(win,textvariable=var2,bg="light blue")
var2.set("Clinic Name")
label2.grid(row=3,column=1,padx=10,pady=10)
 
ent2=Entry(win,textvariable=clinic_name,width=40)
clinic_name.set(" ")
ent2.grid(row=3,column=2,padx=10,pady=10)
 
label3=Label(win,textvariable=var3,bg="light blue")
var3.set("Address")
label3.grid(row=4,column=1,padx=10,pady=10)
 
ent3=Entry(win,textvariable=clinic_address,width=40)
clinic_address.set(" ")
ent3.grid(row=4,column=2,padx=10,pady=10)
 
label4=Label(win,textvariable=var4,bg="light blue")
var4.set("City")
label4.grid(row=5,column=1,padx=10,pady=10)
 
ent4=Entry(win,textvariable=clinic_city,width=40)
clinic_city.set(" ")
ent4.grid(row=5,column=2,padx=10,pady=10)
 
label5=Label(win,textvariable=var5,bg="light blue")
var5.set("State")
label5.grid(row=6,column=1,padx=10,pady=10)
 
ent5=Entry(win,textvariable=clinic_state,width=40)
clinic_state.set(" ")
ent5.grid(row=6,column=2,padx=10,pady=10)
 
label6=Label(win,textvariable=var6,bg="light blue")
var6.set("Phone No.")
label6.grid(row=7,column=1,padx=10,pady=10)
 
ent6=Entry(win,textvariable=clinic_phone,width=40)
clinic_phone.set(" ")
ent6.grid(row=7,column=2,padx=10,pady=10)
 
label7=Label(win,textvariable=var7,bg="light blue")
var7.set("Email")
label7.grid(row=8,column=1,padx=10,pady=10)
 
ent7=Entry(win,textvariable=clinic_email,width=40)
clinic_email.set(" ")
ent7.grid(row=8,column=2,padx=10,pady=10)
 
label8=Label(win,textvariable=var8,bg="light blue")
var8.set("Website")
label8.grid(row=9,column=1,padx=10,pady=10)
 
ent8=Entry(win,textvariable=clinic_website,width=40)
clinic_website.set(" ")
ent8.grid(row=9,column=2,padx=10,pady=10)
 
##def clear_text():
##    workrequired.set(' ')
##    cursor.execute('select count(*) from workrequired')
##    next_num = cursor.fetchall()[0][0]
##    srno.set(next_num+1)
     
    
 
 
btn=Button(win, text="ADD",command= lambda:[clinic(), clear_text()])
btn.grid(row=10,column=2,padx=10,pady=10)
 
btnp=Button(win, text="قبلی",command= lambda:[g_text()])
btnp.grid(row=10,column=1,padx=10,pady=10)

btnn=Button(win, text="بعدی",command= lambda:[clinic(), clear_text()])
btnn.grid(row=10,column=3,padx=10,pady=10)

btnf=Button(win, text="اولین",command= lambda:[f_text()])
btnf.grid(row=11,column=1,padx=10,pady=10)

btne=Button(win, text="آخرین",command= lambda:[e_text()])
btne.grid(row=11,column=3,padx=10,pady=10)


win.mainloop()