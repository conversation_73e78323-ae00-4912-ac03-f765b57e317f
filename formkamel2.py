from tkinter import *
import tkinter as tk
import datetime as dt
from tkinter import ttk
from tkcalendar import DateEntry
 
 
root = Tk()
root.title("Lab Order Form")
#photo=PhotoImage(file="C:\\Users\\<USER>\\Desktop\\python exercises\\order form\\crown2.png")
#label = Label(root,image = photo,bg="light blue")
#label.image = photo # keep a reference!
#label.grid(row=7,column=3,columnspan=20,sticky=tk.E,rowspan=30)
d = f'{dt.datetime.now():%a, %b %d %Y}'
 
cal = DateEntry(root, width=12, day=20, month=6, year=22, 
background='darkblue', foreground='white', borderwidth=2)
#,cal.pack(padx=10, pady=10)
 
def getvals():
    print("Submitting form")
 
    print(f"{labnamevalue.get(),patientnamevalue.get(),patientagevalue.get(),patientsexvalue.get(), workvalue.get(), reqdatevalue.get(),toothnumbervalue.get(), materialvalue.get(), incisalvalue.get(),middlevalue.get(),cervicalvalue.get(), labservicevalue.get(),toothreductionvalue.get(),reductioncopingvalue.get(),sendbackvalue.get()} ",d)
     
 
 
 
    with open("records.txt", "a") as f:
        f.write(f"{labnamevalue.get(),patientnamevalue.get(),patientagevalue.get(),patientsexvalue.get(), workvalue.get(),reqdatevalue.get(), toothnumbervalue.get(), materialvalue.get(), incisalvalue.get(),middlevalue.get(),cervicalvalue.get(), labservicevalue.get(),toothreductionvalue.get(),reductioncopingvalue.get(),sendbackvalue.get()}\n")
 
 
root.geometry("500x300")
root.configure(background='light blue')
#root.resizable(False, False)
#Heading
Label(root, text="Arora Dental Care\n Lab Work Order\n Crown ", font="comicsansms 13 bold",bg="light blue", pady=15).grid(row=0, column=2)
 
#Text for our form
labname = Label(root, text=" Lab. Name",bg="light blue")
patientname=Label(root, text=" Patient.Name",bg="light blue")
patientage=Label(root, text="Patient Age",bg="light blue")
patientsex=Label(root,text="Sex",bg="light blue")
orderdate=Label(root, text="Order Date",bg="light blue")
date=Label(root,text=d,fg="black",bg="light blue",font=("Helvetica", 11))
reqdate=Label(root,text="Deliver By",bg="light blue")
work = Label(root, text="Work Required",bg="light blue")
toothnumber = Label(root, text="Tooth Number",bg="light blue")
material = Label(root, text="Material",bg="light blue")
shade = Label(root, text="Shade",bg="light blue")
incisal=Label(root,text="Incisal/occlusal 1/3rd",bg="light blue")
middle=Label(root,text="Middle 1/3rd",bg="light blue")
cervical=Label(root,text="Cervical 1/3rd",bg="light blue")
lessspace=Label(root,text=" In Case of inadequate occlussal clearance",font='Helvetica 18 bold', bg="light blue")
 
#Pack text for our form
labname.grid(row=1, column=0,padx=5,pady=5)
patientname.grid(row=1, column=2,padx=5,pady=5)
patientage.grid(row=2,column=2, padx=5,pady=5)
patientsex.grid(row=3,column=2,padx=5,pady=5)
orderdate.grid(row=4, column=2,padx=5,pady=5)
reqdate.grid(row=5, column=2,padx=5,pady=5)
date.grid(row=4,column=3,sticky=tk.W,padx=5,pady=5)
work.grid(row=2, column=0,padx=5,pady=5)
toothnumber.grid(row=3, column=0,padx=5,pady=5)
material.grid(row=4, column=0,padx=5,pady=5)
shade.grid(row=5, column=0,padx=5,pady=5)
incisal.grid(row=6,column=0,padx=5,pady=5)
middle.grid(row=7,column=0,padx=5,pady=5)
cervical.grid(row=8,column=0,padx=5,pady=5)
lessspace.grid(row=10,column=2)
 
# Tkinter variable for storing entries
labnamevalue = StringVar()
patientnamevalue = StringVar()
patientagevalue=StringVar()
patientsexvalue=StringVar()
orderdatevalue= StringVar()
reqdatevalue=StringVar()
workvalue = StringVar()
toothnumbervalue = StringVar()
materialvalue = StringVar()
#shadevalue = StringVar()
incisalvalue=StringVar()
middlevalue=StringVar()
cervicalvalue=StringVar()
labservicevalue = IntVar()
toothreductionvalue=IntVar()
reductioncopingvalue=IntVar()
sendbackvalue=IntVar()
 
 
 
#Entries for our form
labnameentry = Entry(root, width=23,textvariable=labnamevalue)
patientnameentry = Entry(root,width=23, textvariable=patientnamevalue)
patientageentry=Entry(root,width=6, textvariable=patientagevalue)
patientsexentry=ttk.Combobox(root,width=6,textvariable=patientsexvalue)
patientsexentry['values']=("M","F")
patientsexentry.current()
DateEntry=cal
#orderdateentry=Entry(root,textvariable=orderdatevalue)
workentry =ttk.Combobox(root,width=20,textvariable=workvalue)
workentry['values']=("Crown","Bridge","Onlay","Inlay","Veneer")
workentry.current()
#workentry = Entry(root, textvariable=workvalue)
toothnumberentry =ttk.Combobox(root,width=20,textvariable=toothnumbervalue)
toothnumberentry['values']=("18","17","16","15","14","13","12","11","21","22","23","24","25","26","27","28","38","37","36","35","34","33","32","31","41","42","43","44","45","46","47","48")
toothnumberentry.current()
materialentry =ttk.Combobox(root,width=20,textvariable=materialvalue)
materialentry['values']=("PFM","Zirconia","NiCr","NiCr+Ceramic facing","EMax")
materialentry.current()
 
#materialentry = Entry(root, textvariable=materialvalue)
#shadeentry =ttk.Combobox(root,width=20,textvariable=shadevalue)
#shadeentry['values']=("A1","A2","A3","A3.5","A4","B1","B2","B3","B4","C1","C2","C3","C4","D2","D3","D4")
#shadeentry.current()
#shadeentry = Entry(root, textvariable=shadevalue)
incisalentry =ttk.Combobox(root,width=6,textvariable=incisalvalue)
incisalentry['values']=("A1","A2","A3","A3.5","A4","B1","B2","B3","B4","C1","C2","C3","C4","D2","D3","D4")
incisalentry.current()
middleentry =ttk.Combobox(root,width=6,textvariable=middlevalue)
middleentry['values']=("A1","A2","A3","A3.5","A4","B1","B2","B3","B4","C1","C2","C3","C4","D2","D3","D4")
middleentry.current()
cervicalentry =ttk.Combobox(root,width=6,textvariable=cervicalvalue)
cervicalentry['values']=("A1","A2","A3","A3.5","A4","B1","B2","B3","B4","C1","C2","C3","C4","D2","D3","D4")
cervicalentry.current()
# Packing the Entries
labnameentry.grid(row=1, column=1,padx=5,pady=5)
patientnameentry.grid(row=1, column=3,sticky=tk.W,padx=5,pady=5)
patientageentry.grid(row=2, column=3,sticky=tk.W,padx=5,pady=5)
patientsexentry.grid(row=3, column=3,sticky=tk.W,padx=5,pady=5)
DateEntry.grid(row=5,column=3,sticky=tk.W,padx=5,pady=5)
#orderdateentry.grid(row=2, column=7)
workentry.grid(row=2, column=1,padx=5,pady=5)
toothnumberentry.grid(row=3, column=1,padx=5,pady=5)
materialentry.grid(row=4, column=1,padx=5,pady=5)
#shadeentry.grid(row=5, column=3,padx=5,pady=5)
incisalentry.grid(row=6, column=1,sticky=tk.W,padx=5,pady=5)
middleentry.grid(row=7, column=1,sticky=tk.W,padx=5,pady=5)
cervicalentry.grid(row=8, column=1,sticky=tk.W,padx=5,pady=5)
 
#Checkbox & Packing it
labservice = Checkbutton(text="Want metal coping trial",bg="light blue", variable = labservicevalue)
labservice.grid(row=9, column=2,sticky=tk.W)
toothreduction = Checkbutton(text="Do opposite tooth reduction",bg="light blue", variable = toothreductionvalue)
toothreduction.grid(row=11, column=2,sticky=tk.W)
reductioncoping = Checkbutton(text="Make reduction coping",bg="light blue", variable = reductioncopingvalue)
reductioncoping.grid(row=12, column=2,sticky=tk.W)
sendback = Checkbutton(text="Send the Case Back for correction",bg="light blue", variable = sendbackvalue)
sendback.grid(row=13, column=2,sticky=tk.W)
 
 
#Button & packing it and assigning it a command
Button(text=" submit ", command=getvals).grid(row=15, column=2)
 
 
 
root.mainloop()