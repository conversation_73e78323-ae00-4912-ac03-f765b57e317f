
import tkinter as tk  
from tkinter import ttk  
import ttkbootstrap as tb  
import mysql.connector  
from mysql.connector import Error  
from tkcalendar import DateEntry  # اضافه کردن DateEntry  
# اطلاعات اتصال به پایگاه داده (جایگزین کنید)  
db_config = {  
    'user': 'root',  
    'password': '123456',  
    'host': 'localhost',  
    'database': 'school_management'  
} 
def connect_to_database():  
    """  
    اتصال به پایگاه داده MySQL.  
    """  
    try:  
        connection = mysql.connector.connect(**db_config)  
        print("Connected to database successfully!")  
        return connection  
    except Error as err:  
        print(f"Error connecting to database: {err}")  
        return None  

def add_student():  
    """  
    تابعی برای اضافه کردن دانش آموز به پایگاه داده.  
    """  
    first_name = first_name_entry.get()  
   
    last_name = last_name_entry.get()  
    #birth_date = birth_date_entry.get()  # گرفتن تاریخ تولد  
    birth_date_str = birth_date_var.get()
    print("dbirth_date_str",birth_date_var.get())
    gender = gender_var.get()  # گرفتن جنسیت  
    address = address_entry.get("1.0", tk.END)  # گرفتن آدرس از Text widget  
    phone_number = phone_number_entry.get()  # گرفتن شماره تلفن  

    try:  
        connection = connect_to_database()  
        if connection:  
            cursor = connection.cursor()  
            query = "INSERT INTO students (first_name, last_name, date_of_birth, gender, address, phone_number) VALUES (%s, %s, %s, %s, %s, %s)"  
            values = (first_name, last_name,birth_date_str, gender, address, phone_number)  
            #print("payiiiiiiiiiiiiiiiiiiiiiiiii",birth_date_str)
            cursor.execute(query, values)  
            connection.commit()  
            print("Student added successfully!")  
            connection.close()  
    except Error as err:  
        print(f"Error adding student: {err}")  

def main():  
    """  
    تابع اصلی برنامه.  
    """  
    root = tb.Window(themename="darkly")  
    root.title("School Management System")  
    root.geometry("800x600")  

    # --- فرم ثبت دانش آموز ---  
    student_form_frame = tb.Frame(root, padding=20)  
    student_form_frame.pack(fill="x")  

    # برچسب و ورودی نام  
    first_name_label = tb.Label(student_form_frame, text="First Name:")  
    first_name_label.grid(row=0, column=0, padx=5, pady=5, sticky="w")  
    global first_name_entry  
    first_name_entry = tb.Entry(student_form_frame)  
    first_name_entry.grid(row=0, column=1, padx=5, pady=5, sticky="e")  

    # برچسب و ورودی نام خانوادگی  
    last_name_label = tb.Label(student_form_frame, text="Last Name:")  
    last_name_label.grid(row=1, column=0, padx=5, pady=5, sticky="w")  
    global last_name_entry 
    
    last_name_entry = tb.Entry(student_form_frame)  
    last_name_entry.grid(row=1, column=1, padx=5, pady=5, sticky="e")  
    #print("last name:",last_name_entry,last_name_label) 
    # برچسب و ورودی تاریخ تولد  
    birth_date_label = tb.Label(student_form_frame, text="Birth Date:")  
    birth_date_label.grid(row=2, column=0, padx=5, pady=5, sticky="w")  
    global birth_date_var  
    birth_date_var = tb.StringVar() 
    #global birth_date_entry  
    birth_date_entry = tb.DateEntry(student_form_frame, bootstyle="primary",dateformat="%Y-%m-%d")  # استفاده از DateEntry  
    birth_date_entry.grid(row=2, column=1, padx=5, pady=5, sticky="e")  
    #print("bitrh birth:",birth_date_var,birth_date_entry) 
    # برچسب و انتخاب جنسیت  
    gender_label = tb.Label(student_form_frame, text="Gender:")  
    gender_label.grid(row=3, column=0, padx=5, pady=5, sticky="w")  
    global gender_var  
    gender_var = tk.StringVar(value="Male")  # مقدار پیش فرض  
    male_radio = tb.Radiobutton(student_form_frame, text="Male", value="Male", variable=gender_var, bootstyle="primary")  
    male_radio.grid(row=3, column=1, padx=5, pady=5, sticky="w")  
    female_radio = tb.Radiobutton(student_form_frame, text="Female", value="Female", variable=gender_var, bootstyle="primary")  
    female_radio.grid(row=3, column=2, padx=5, pady=5, sticky="w")  

    # برچسب و ورودی آدرس (Text widget)  
    address_label = tb.Label(student_form_frame, text="Address:")  
    address_label.grid(row=4, column=0, padx=5, pady=5, sticky="w")  
    global address_entry  
    address_entry = tk.Text(student_form_frame, height=3, width=30)  # استفاده از Text  
    address_entry.grid(row=4, column=1, padx=5, pady=5, sticky="e")  

    # برچسب و ورودی شماره تلفن  
    phone_number_label = tb.Label(student_form_frame, text="Phone Number:")  
    phone_number_label.grid(row=5, column=0, padx=5, pady=5, sticky="w")  
    global phone_number_entry  
    phone_number_entry = tb.Entry(student_form_frame)  
    phone_number_entry.grid(row=5, column=1, padx=5, pady=5, sticky="e")  

    # دکمه ثبت  
    add_button = tb.Button(student_form_frame, text="Add Student", command=add_student, bootstyle="success")  
    add_button.grid(row=6, column=0, columnspan=2, padx=5, pady=10)  

    # --- پایان فرم ثبت دانش آموز ---  

    root.mainloop()  

if __name__ == "__main__":  
    main()  