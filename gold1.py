
# libraries Import
from tkinter import *
import customtkinter

# Main Window Properties

window = Tk()
window.title("Tkinter")
window.geometry("800x350")
window.configure(bg="#c9bcfb")


Button_id1 = customtkinter.CTkButton(
    master=window,
    text="دکمه ثبت",
    font=("undefined", 14),
    text_color="#000000",
    hover=True,
    hover_color="#949494",
    height=30,
    width=95,
    border_width=2,
    corner_radius=6,
    border_color="#000000",
    bg_color="#c9bcfb",
    fg_color="#F0F0F0",
    )
Button_id1.place(x=230, y=170)
Label_id3 = customtkinter.CTkLabel(
    master=window,
    text="به سیستم اینترنی تستی خوش آمدید",
    font=("Arial", 14),
    text_color="#000000",
    height=30,
    anchor="e",
    width=250,
    corner_radius=2,
    bg_color="#e81111",
    fg_color="#c9bcfb",
    )
Label_id3.grid(row=0, column=1, padx=20, pady=20)


products_label = customtkinter.CTkLabel(
    master=window, 
    text="مدیریت محصولات",
    height=30,
    anchor="e",
    font=("Arial", 14),
    width=250,
    corner_radius=2,
    bg_color="#e81111",
    fg_color="#c9bcfb",
    )
products_label.grid(row=0, column=0, padx=20, pady=20)


#run the main loop
window.mainloop()