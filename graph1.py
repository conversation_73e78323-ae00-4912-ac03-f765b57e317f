from turtle import *
"""
forward(250)
left(120)
forward(100)
home()
pos()
clearscreen()
"""
"""
for steps in range(25):
    for c in ('blue', 'red', 'green'):
        color(c)
        forward(steps)
        right(30)

begin_fill()        
"""
while True:
    for c in ('blue', 'red', 'green'):
        color(c)
        left(100)
        forward(150)
        #left(30)
        #if abs(pos()) < 1:
        #    break

end_fill()