import instaloader
import tkinter as tk
from tkinter import scrolledtext, messagebox, simpledialog
from threading import Thread
import json
import time
import random

class InstagramAPIViewer:
    def __init__(self, root):
        self.root = root
        self.setup_ui()
        self.L = instaloader.Instaloader()
        self.L.context.raise_all_errors = True
        self.request_log = []
        self.logged_in = False
        
    def setup_ui(self):
        self.root.title("Instagram API Request Viewer")
        self.root.geometry("800x600")
        
        # Frame برای ورودی کاربر
        top_frame = tk.Frame(self.root)
        top_frame.pack(pady=10)
        
        # بخش ورود اطلاعات
        input_frame = tk.Frame(top_frame)
        input_frame.pack(side=tk.LEFT, padx=10)
        
        tk.Label(input_frame, text="Target Username:").pack(anchor='w')
        self.username_entry = tk.Entry(input_frame, width=25)
        self.username_entry.pack(pady=5)
        
        # بخش دکمه‌ها
        button_frame = tk.Frame(top_frame)
        button_frame.pack(side=tk.LEFT, padx=10)
        
        self.login_btn = tk.Button(button_frame, text="Login", command=self.login)
        self.login_btn.pack(pady=5)
        
        self.fetch_btn = tk.Button(button_frame, text="Fetch Data", command=self.start_fetch_thread, state=tk.DISABLED)
        self.fetch_btn.pack(pady=5)
        
        # نمایشگر API Requests
        tk.Label(self.root, text="API Requests:").pack()
        self.api_request_viewer = scrolledtext.ScrolledText(self.root, width=100, height=25)
        self.api_request_viewer.pack(pady=5)
        
        # وضعیت
        self.status_label = tk.Label(self.root, text="Status: Ready", fg="blue")
        self.status_label.pack()
    
    def login(self):
        username = simpledialog.askstring("Login", "Enter your Instagram username:")
        if not username:
            return
            
        password = simpledialog.askstring("Login", "Enter your Instagram password:", show='*')
        if not password:
            return
            
        try:
            self.L.login(username, password)
            self.logged_in = True
            self.login_btn.config(text="Logged In", state=tk.DISABLED)
            self.fetch_btn.config(state=tk.NORMAL)
            self.status_label.config(text="Status: Logged in successfully!", fg="green")
        except Exception as e:
            messagebox.showerror("Login Error", f"Failed to login: {str(e)}")
            self.status_label.config(text="Status: Login failed", fg="red")
    
    def start_fetch_thread(self):
        target_username = self.username_entry.get().strip()
        if not target_username:
            messagebox.showerror("Error", "Please enter a username to fetch")
            return
        
        self.fetch_btn.config(state=tk.DISABLED, text="Fetching...")
        self.status_label.config(text="Status: Fetching data...", fg="orange")
        self.api_request_viewer.delete(1.0, tk.END)
        self.request_log = []
        
        Thread(target=self.fetch_data, args=(target_username,), daemon=True).start()
    
    def fetch_data(self, target_username):
        old_get = self.L.context.get_json
        
        try:
            def wrapped_get(url, params=None, **kwargs):
                try:
                    # ثبت درخواست
                    request_data = {
                        "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
                        "method": "GET",
                        "url": url,
                        "params": params,
                        "headers": kwargs.get('headers', {}),
                        "response_headers": kwargs.get('response_headers', {})
                    }
                    
                    # نمایش در GUI
                    self.display_request(request_data)
                    
                    # تاخیر تصادفی برای جلوگیری از مسدودی
                    time.sleep(random.uniform(1, 3))
                    
                    # اجرای درخواست اصلی
                    response = old_get(url, params=params, **kwargs)
                    
                    # ثبت پاسخ
                    request_data["response_status"] = "success"
                    self.request_log.append(request_data)
                    
                    return response
                except Exception as e:
                    request_data["response_status"] = f"error: {str(e)}"
                    self.show_error(f"API Request Error: {str(e)}")
                    raise
                
            # جایگزینی متد
            self.L.context.get_json = wrapped_get
            
            # دریافت اطلاعات پروفایل
            profile = instaloader.Profile.from_username(self.L.context, target_username)
            
            # نمایش اطلاعات
            profile_data = {
                "username": profile.username,
                "user_id": profile.userid,
                "full_name": profile.full_name,
                "followers": profile.followers,
                "following": profile.followees,
                "bio": profile.biography,
                "is_private": profile.is_private
            }
            
            self.display_api_data(profile_data)
            self.status_label.config(text="Status: Data fetched successfully!", fg="green")
            
        except instaloader.exceptions.ProfileNotExistsException:
            self.show_error("Profile does not exist!")
        except instaloader.exceptions.PrivateProfileNotFollowedException:
            self.show_error("Cannot access private profile")
        except Exception as e:
            self.show_error(f"Error: {str(e)}")
        finally:
            # بازگرداندن متد اصلی
            self.L.context.get_json = old_get
            self.fetch_btn.config(state=tk.NORMAL, text="Fetch Data")
    
    def display_request(self, request_data):
        """نمایش درخواست در GUI"""
        self.root.after(0, lambda: self._update_display(request_data))
    
    def _update_display(self, request_data):
        """به روزرسانی رابط کاربری از طریق thread اصلی"""
        self.api_request_viewer.insert(tk.END, json.dumps(request_data, indent=2) + "\n")
        self.api_request_viewer.see(tk.END)
    
    def display_api_data(self, profile_data):
        """نمایش اطلاعات پروفایل"""
        self.root.after(0, lambda: self._update_api_display(profile_data))
    
    def _update_api_display(self, profile_data):
        """به روزرسانی نمایش اطلاعات پروفایل"""
        self.api_request_viewer.insert(tk.END, "\n=== Profile Data ===\n")
        self.api_request_viewer.insert(tk.END, json.dumps(profile_data, indent=2) + "\n\n")
        self.api_request_viewer.see(tk.END)
    
    def show_error(self, message):
        """نمایش خطا در رابط کاربری"""
        self.root.after(0, lambda: self._show_error_message(message))
    
    def _show_error_message(self, message):
        """نمایش پیام خطا در thread اصلی"""
        self.status_label.config(text=f"Status: {message}", fg="red")
        messagebox.showerror("Error", message)

if __name__ == "__main__":
    root = tk.Tk()
    app = InstagramAPIViewer(root)
    root.mainloop()