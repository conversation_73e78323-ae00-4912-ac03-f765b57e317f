import requests
import json
import os
from tkinter import messagebox



def load_cookies(self, cookie_file):
    """Load cookies from a JSON file."""
    try:
        if not os.path.exists(cookie_file):
            messagebox.showerror("Error", f"Cookies file not found at: {cookie_file}\nPlease select a valid cookies file.")
            return None
            
        with open(cookie_file, "r") as file:
            cookies_list = json.load(file)
        return {cookie["name"]: cookie["value"] for cookie in cookies_list}
    except json.JSONDecodeError:
        messagebox.showerror("Error", "Invalid cookies file format. Please provide a valid JSON file.")
        return None
    except Exception as e:
        messagebox.showerror("Error", f"Error loading cookies: {e}")
        return None

def download_instagram_profile(username, cookies_file, output_dir="output"):
    """
    Downloads the Instagram profile picture and saves user details.
    :param username: Instagram username to scrape.
    :param cookies_file: Path to cookies JSON file.
    :param output_dir: Directory to save the profile data.
    """
    url = f"https://i.instagram.com/api/v1/users/web_profile_info/?username={username}"

    # Load cookies
    try:
        cookies = load_cookies(cookies_file)
    except Exception as e:
        print(f"Error loading cookies: {e}")
        return

    headers = {
        "User-Agent": "Instagram 219.0.0.12.117 Android (30/3.0; 320dpi; 720x1280; Xiaomi; Redmi Note 8; ginkgo; ginkgo; en_US)",
        "Accept": "*/*",
        "Accept-Language": "en-US",
        "X-IG-App-ID": "936619743392459",  # This header is essential for accessing Instagram APIs
    }

    try:
        response = requests.get(url, headers=headers, cookies=cookies)
    except Exception as e:
        print(f"Error making request to Instagram: {e}")
        return

    print(f"Response status: {response.status_code}")
    if response.status_code != 200:
        print(f"Error fetching profile for {username}: {response.text}")
        return

    try:
        data = response.json()
        user_data = data.get("data", {}).get("user", {})

        if not user_data:
            print(f"Error: Could not find user data for {username}")
            return

        profile_pic_url = user_data.get("profile_pic_url_hd")
        full_name = user_data.get("full_name", "N/A")
        bio = user_data.get("biography", "N/A")
        followers = user_data.get("edge_followed_by", {}).get("count", 0)
        following = user_data.get("edge_follow", {}).get("count", 0)

        os.makedirs(output_dir, exist_ok=True)

        # Save user details
        user_details = {
            "username": username,
            "full_name": full_name,
            "bio": bio,
            "followers": followers,
            "following": following,
        }
        details_file = os.path.join(output_dir, f"{username}_details.json")
        with open(details_file, "w") as file:
            json.dump(user_details, file, indent=4)
        print(f"User details saved to {details_file}")

        if profile_pic_url:
            profile_pic_response = requests.get(profile_pic_url, headers=headers, cookies=cookies)
            if profile_pic_response.status_code == 200:
                profile_pic_path = os.path.join(output_dir, f"{username}_profile_pic.jpg")
                with open(profile_pic_path, "wb") as file:
                    file.write(profile_pic_response.content)
                print(f"Profile picture saved to {profile_pic_path}")
            else:
                print(f"Failed to download profile picture for {username}")
        else:
            print(f"No profile picture found for {username}")
    except Exception as e:
        print(f"An unexpected error occurred while processing data: {e}")


if __name__ == "__main__":
    username = input("Enter the Instagram username: ").strip()
    cookies_file_path = "cookies.json"
    download_instagram_profile(username, cookies_file_path)