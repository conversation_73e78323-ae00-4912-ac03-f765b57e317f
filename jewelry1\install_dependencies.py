#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اسکریپت نصب وابستگی‌های سیستم مدیریت طلا و جواهرات
"""

import subprocess
import sys
import os

def install_package(package):
    """نصب یک پکیج با pip"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✓ {package} با موفقیت نصب شد")
        return True
    except subprocess.CalledProcessError:
        print(f"✗ خطا در نصب {package}")
        return False

def main():
    """تابع اصلی"""
    print("=== نصب وابستگی‌های سیستم مدیریت طلا و جواهرات ===\n")
    
    # لیست پکیج‌های مورد نیاز
    required_packages = [
        "customtkinter>=5.0.0",
        "Pillow>=9.0.0",
        "PyMuPDF>=1.20.0"
    ]
    
    success_count = 0
    total_count = len(required_packages)
    
    for package in required_packages:
        print(f"در حال نصب {package}...")
        if install_package(package):
            success_count += 1
        print()
    
    print("=== خلاصه نتایج ===")
    print(f"تعداد کل پکیج‌ها: {total_count}")
    print(f"نصب موفق: {success_count}")
    print(f"نصب ناموفق: {total_count - success_count}")
    
    if success_count == total_count:
        print("\n✓ همه وابستگی‌ها با موفقیت نصب شدند!")
        print("حالا می‌توانید برنامه را با دستور زیر اجرا کنید:")
        print("python jewelry_app.py")
    else:
        print("\n⚠ برخی وابستگی‌ها نصب نشدند.")
        print("برنامه ممکن است بدون PyMuPDF کار کند، اما پیش‌نمایش PDF در دسترس نخواهد بود.")
    
    input("\nبرای خروج Enter را فشار دهید...")

if __name__ == "__main__":
    main()
