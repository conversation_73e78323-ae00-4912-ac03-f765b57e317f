#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اسکریپت مهاجرت پایگاه داده برای سیستم مدیریت طرف حساب ها
Database migration script for enhanced customer management system
"""

import sqlite3
import os
import shutil
from datetime import datetime

def backup_database():
    """پشتیبان‌گیری از پایگاه داده فعلی"""
    db_file = "jewelry_store.db"
    if os.path.exists(db_file):
        backup_file = f"jewelry_store_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
        shutil.copy2(db_file, backup_file)
        print(f"پشتیبان‌گیری انجام شد: {backup_file}")
        return backup_file
    return None

def check_table_structure():
    """بررسی ساختار جدول مشتریان"""
    conn = sqlite3.connect("jewelry_store.db")
    cursor = conn.cursor()
    
    try:
        # بررسی وجود جدول customers
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='customers'")
        if not cursor.fetchone():
            print("جدول customers وجود ندارد")
            conn.close()
            return False
        
        # بررسی ستون‌های جدول
        cursor.execute("PRAGMA table_info(customers)")
        columns = [row[1] for row in cursor.fetchall()]
        
        print("ستون‌های موجود در جدول customers:")
        for col in columns:
            print(f"  - {col}")
        
        # ستون‌های مورد نیاز
        required_columns = [
            'account_type', 'entity_type', 'main_group_code', 'sub_group_code', 'dimension_code',
            'national_id', 'birth_date', 'mobile', 'phone', 'referrer',
            'company_name', 'economic_code', 'ceo_name', 'registration_number',
            'contact_name', 'contact_national_id', 'contact_birth_date', 'contact_mobile', 'contact_phone',
            'address1', 'postal_code1', 'address2', 'postal_code2'
        ]
        
        missing_columns = [col for col in required_columns if col not in columns]
        
        if missing_columns:
            print(f"\nستون‌های گمشده: {missing_columns}")
            return False
        else:
            print("\nهمه ستون‌های مورد نیاز موجود هستند")
            return True
            
    except sqlite3.Error as e:
        print(f"خطا در بررسی ساختار جدول: {e}")
        return False
    finally:
        conn.close()

def migrate_customers_table():
    """مهاجرت جدول مشتریان"""
    conn = sqlite3.connect("jewelry_store.db")
    cursor = conn.cursor()
    
    try:
        # بررسی وجود جدول قدیمی
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='customers'")
        if not cursor.fetchone():
            print("جدول customers وجود ندارد، ایجاد جدول جدید...")
            create_new_customers_table(cursor)
            conn.commit()
            return True
        
        # بررسی ساختار فعلی
        cursor.execute("PRAGMA table_info(customers)")
        columns = [row[1] for row in cursor.fetchall()]
        
        if 'account_type' not in columns:
            print("شروع مهاجرت جدول customers...")
            
            # تغییر نام جدول قدیمی
            cursor.execute("ALTER TABLE customers RENAME TO customers_old")
            
            # ایجاد جدول جدید
            create_new_customers_table(cursor)
            
            # کپی کردن داده‌های قدیمی
            cursor.execute("SELECT * FROM customers_old")
            old_customers = cursor.fetchall()
            
            # دریافت ساختار جدول قدیمی
            cursor.execute("PRAGMA table_info(customers_old)")
            old_columns = [row[1] for row in cursor.fetchall()]
            
            for customer in old_customers:
                # تبدیل داده‌های قدیمی به فرمت جدید
                customer_dict = dict(zip(old_columns, customer))
                
                # مقادیر پیش‌فرض برای ستون‌های جدید
                new_data = {
                    'name': customer_dict.get('name', ''),
                    'account_type': 'customer',  # پیش‌فرض
                    'entity_type': 'individual',  # پیش‌فرض
                    'main_group_code': None,
                    'sub_group_code': None,
                    'dimension_code': None,
                    'national_id': None,
                    'birth_date': None,
                    'mobile': customer_dict.get('phone', None),  # انتقال phone به mobile
                    'phone': None,
                    'referrer': None,
                    'company_name': None,
                    'economic_code': None,
                    'ceo_name': None,
                    'registration_number': None,
                    'contact_name': None,
                    'contact_national_id': None,
                    'contact_birth_date': None,
                    'contact_mobile': None,
                    'contact_phone': None,
                    'email': customer_dict.get('email', None),
                    'address1': customer_dict.get('address', None),
                    'postal_code1': None,
                    'address2': None,
                    'postal_code2': None,
                }
                
                # درج در جدول جدید
                cursor.execute('''
                INSERT INTO customers (
                    name, account_type, entity_type, main_group_code, sub_group_code, dimension_code,
                    national_id, birth_date, mobile, phone, referrer,
                    company_name, economic_code, ceo_name, registration_number,
                    contact_name, contact_national_id, contact_birth_date, contact_mobile, contact_phone,
                    email, address1, postal_code1, address2, postal_code2
                )
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    new_data['name'], new_data['account_type'], new_data['entity_type'],
                    new_data['main_group_code'], new_data['sub_group_code'], new_data['dimension_code'],
                    new_data['national_id'], new_data['birth_date'], new_data['mobile'], new_data['phone'], new_data['referrer'],
                    new_data['company_name'], new_data['economic_code'], new_data['ceo_name'], new_data['registration_number'],
                    new_data['contact_name'], new_data['contact_national_id'], new_data['contact_birth_date'],
                    new_data['contact_mobile'], new_data['contact_phone'],
                    new_data['email'], new_data['address1'], new_data['postal_code1'], new_data['address2'], new_data['postal_code2']
                ))
            
            # حذف جدول قدیمی
            cursor.execute("DROP TABLE customers_old")
            
            print(f"مهاجرت {len(old_customers)} مشتری انجام شد")
        else:
            print("جدول customers قبلاً به‌روزرسانی شده است")
        
        conn.commit()
        return True
        
    except sqlite3.Error as e:
        print(f"خطا در مهاجرت جدول: {e}")
        conn.rollback()
        return False
    finally:
        conn.close()

def create_new_customers_table(cursor):
    """ایجاد جدول جدید مشتریان"""
    cursor.execute('''
    CREATE TABLE customers (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        account_type TEXT NOT NULL DEFAULT 'customer',
        entity_type TEXT NOT NULL DEFAULT 'individual',
        main_group_code TEXT,
        sub_group_code TEXT,
        dimension_code TEXT,

        -- Individual fields
        national_id TEXT,
        birth_date TEXT,
        mobile TEXT,
        phone TEXT,
        referrer TEXT,

        -- Legal entity fields
        company_name TEXT,
        economic_code TEXT,
        ceo_name TEXT,
        registration_number TEXT,
        contact_name TEXT,
        contact_national_id TEXT,
        contact_birth_date TEXT,
        contact_mobile TEXT,
        contact_phone TEXT,

        email TEXT,
        address1 TEXT,
        postal_code1 TEXT,
        address2 TEXT,
        postal_code2 TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
    ''')

def main():
    """تابع اصلی مهاجرت"""
    print("=== شروع مهاجرت پایگاه داده ===")
    
    # پشتیبان‌گیری
    backup_file = backup_database()
    
    # بررسی ساختار فعلی
    if check_table_structure():
        print("ساختار پایگاه داده صحیح است، نیازی به مهاجرت نیست")
        return
    
    # مهاجرت جدول مشتریان
    if migrate_customers_table():
        print("✓ مهاجرت با موفقیت انجام شد")
        
        # بررسی نهایی
        if check_table_structure():
            print("✓ ساختار جدید تأیید شد")
        else:
            print("✗ خطا در تأیید ساختار جدید")
    else:
        print("✗ خطا در مهاجرت")
        if backup_file:
            print(f"می‌توانید از فایل پشتیبان استفاده کنید: {backup_file}")
    
    print("=== پایان مهاجرت ===")

if __name__ == "__main__":
    main()
