#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تست سیستم مدیریت طرف حساب ها
Test script for the enhanced customer management system
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import Database

def test_customer_management():
    """تست عملکرد سیستم مدیریت طرف حساب ها"""
    print("=== تست سیستم مدیریت طرف حساب ها ===")
    
    # ایجاد اتصال به پایگاه داده
    db = Database()
    
    # تست 1: افزودن طرف حساب حقیقی (مشتری)
    print("\n1. افزودن طرف حساب حقیقی...")
    individual_data = {
        'name': 'احمد محمدی',
        'account_type': 'customer',
        'entity_type': 'individual',
        'main_group_code': '1001',
        'sub_group_code': '2001',
        'dimension_code': '3001',
        'national_id': '*********0',
        'birth_date': '1370/01/01',
        'mobile': '09*********',
        'phone': '***********',
        'referrer': 'علی احمدی',
        'email': '<EMAIL>',
        'address1': 'تهران، خیابان ولیعصر، پلاک 123',
        'postal_code1': '*********0',
        'address2': 'تهران، خیابان انقلاب، پلاک 456',
        'postal_code2': '0*********',
        # فیلدهای حقوقی null
        'company_name': None,
        'economic_code': None,
        'ceo_name': None,
        'registration_number': None,
        'contact_name': None,
        'contact_national_id': None,
        'contact_birth_date': None,
        'contact_mobile': None,
        'contact_phone': None,
    }
    
    customer_id = db.add_customer(individual_data)
    if customer_id:
        print(f"✓ طرف حساب حقیقی با شناسه {customer_id} اضافه شد")
    else:
        print("✗ خطا در افزودن طرف حساب حقیقی")
    
    # تست 2: افزودن طرف حساب حقوقی (فروشنده)
    print("\n2. افزودن طرف حساب حقوقی...")
    legal_data = {
        'name': 'شرکت طلای پارس',
        'account_type': 'seller',
        'entity_type': 'legal',
        'main_group_code': '2001',
        'sub_group_code': '3001',
        'dimension_code': '4001',
        'company_name': 'شرکت طلای پارس',
        'economic_code': '*********',
        'ceo_name': 'محمد رضایی',
        'registration_number': '*********',
        'contact_name': 'سارا احمدی',
        'contact_national_id': '0*********',
        'contact_birth_date': '1365/05/15',
        'contact_mobile': '09*********',
        'contact_phone': '***********',
        'email': '<EMAIL>',
        'address1': 'تهران، بازار طلا، طبقه 2، واحد 15',
        'postal_code1': '**********',
        'address2': None,
        'postal_code2': None,
        # فیلدهای حقیقی null
        'national_id': None,
        'birth_date': None,
        'mobile': None,
        'phone': None,
        'referrer': None,
    }
    
    seller_id = db.add_customer(legal_data)
    if seller_id:
        print(f"✓ طرف حساب حقوقی با شناسه {seller_id} اضافه شد")
    else:
        print("✗ خطا در افزودن طرف حساب حقوقی")
    
    # تست 3: دریافت همه طرف حساب ها
    print("\n3. دریافت لیست طرف حساب ها...")
    customers = db.get_all_customers()
    print(f"تعداد کل طرف حساب ها: {len(customers)}")
    
    for customer in customers:
        account_type_text = {
            'customer': 'مشتری',
            'seller': 'فروشنده',
            'personnel': 'پرسنل',
            'organization': 'سازمان'
        }.get(customer.get('account_type', 'customer'), 'مشتری')
        
        entity_type_text = 'حقیقی' if customer.get('entity_type') == 'individual' else 'حقوقی'
        
        print(f"  - {customer['name']} ({account_type_text} - {entity_type_text})")
        if customer.get('main_group_code'):
            print(f"    کدهای گروه‌بندی: {customer['main_group_code']}-{customer['sub_group_code']}-{customer['dimension_code']}")
    
    # تست 4: به‌روزرسانی طرف حساب
    if customer_id:
        print(f"\n4. به‌روزرسانی طرف حساب {customer_id}...")
        updated_data = individual_data.copy()
        updated_data['mobile'] = '***********'
        updated_data['main_group_code'] = '1002'
        
        success = db.update_customer(customer_id, updated_data)
        if success:
            print("✓ طرف حساب با موفقیت به‌روزرسانی شد")
        else:
            print("✗ خطا در به‌روزرسانی طرف حساب")
    
    # تست 5: دریافت اطلاعات یک طرف حساب
    if customer_id:
        print(f"\n5. دریافت اطلاعات طرف حساب {customer_id}...")
        customer = db.get_customer(customer_id)
        if customer:
            print(f"✓ نام: {customer['name']}")
            print(f"  نوع: {customer['account_type']}")
            print(f"  شخصیت: {customer['entity_type']}")
            print(f"  موبایل: {customer.get('mobile', 'ندارد')}")
        else:
            print("✗ خطا در دریافت اطلاعات طرف حساب")
    
    print("\n=== پایان تست ===")

if __name__ == "__main__":
    test_customer_management()
