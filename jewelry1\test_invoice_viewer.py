#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
تست کننده برای نمایش‌دهنده فاکتور
"""

import customtkinter as ctk
from invoice_viewer import InvoiceViewer
import os

def test_invoice_viewer():
    """تست نمایش‌دهنده فاکتور"""
    
    # ایجاد پنجره اصلی
    root = ctk.CTk()
    root.title("تست نمایش‌دهنده فاکتور")
    root.geometry("400x300")
    
    def open_invoice_viewer():
        """باز کردن نمایش‌دهنده فاکتور"""
        # مسیر فرضی برای فاکتور (حتی اگر وجود نداشته باشد)
        invoice_path = "test_invoice.pdf"
        
        # ایجاد یک فایل PDF ساده برای تست
        if not os.path.exists(invoice_path):
            # ایجاد یک فایل متنی به عنوان جایگزین
            with open("test_invoice.txt", "w", encoding="utf-8") as f:
                f.write("این یک فاکتور تست است")
            invoice_path = "test_invoice.txt"
        
        # باز کردن نمایش‌دهنده فاکتور
        viewer = InvoiceViewer(root, invoice_path)
    
    # دکمه تست
    test_button = ctk.CTkButton(
        root,
        text="تست نمایش‌دهنده فاکتور",
        command=open_invoice_viewer,
        width=200,
        height=50
    )
    test_button.pack(expand=True)
    
    # اجرای برنامه
    root.mainloop()

if __name__ == "__main__":
    test_invoice_viewer()
