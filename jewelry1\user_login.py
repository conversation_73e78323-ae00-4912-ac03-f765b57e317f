import customtkinter as ctk
from database import Database
import os
import hashlib

class LoginWindow(ctk.CTkToplevel):
    """پنجره ورود به سیستم"""

    def __init__(self, parent, on_login_success=None):
        super().__init__(parent)

        # تنظیمات پنجره
        self.title("ورود به سیستم")
        self.geometry("400x500")
        self.resizable(False, False)
        self.grab_set()  # مدال کردن پنجره

        # تنظیم حالت ظاهری
        ctk.set_appearance_mode("Light")
        ctk.set_default_color_theme("blue")

        # اتصال به پایگاه داده
        self.db = Database()

        # تابع فراخوانی شده پس از ورود موفق
        self.on_login_success = on_login_success

        # متغیرهای مورد نیاز
        self.current_user = None
        self.login_successful = False

        # ایجاد رابط کاربری
        self.create_widgets()

        # تنظیم فوکوس روی فیلد نام کاربری
        self.username_entry.focus()

        # تنظیم کلید Enter برای ورود
        self.bind("<Return>", lambda event: self.login())

        # تنظیم رویداد بستن پنجره
        self.protocol("WM_DELETE_WINDOW", self.on_close)

    def create_widgets(self):
        """ایجاد عناصر رابط کاربری"""
        # فریم اصلی
        self.main_frame = ctk.CTkFrame(self)
        self.main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # لوگو و عنوان
        self.logo_label = ctk.CTkLabel(
            self.main_frame,
            text="سیستم مدیریت فروشگاه طلا و جواهرات",
            font=("Arial", 18, "bold")
        )
        self.logo_label.pack(pady=(20, 10))

        # توضیحات
        self.description_label = ctk.CTkLabel(
            self.main_frame,
            text="لطفاً نام کاربری و رمز عبور خود را وارد کنید",
            font=("Arial", 12)
        )
        self.description_label.pack(pady=(0, 20))

        # فرم ورود
        self.login_frame = ctk.CTkFrame(self.main_frame)
        self.login_frame.pack(fill="x", padx=20, pady=10)

        # نام کاربری
        self.username_label = ctk.CTkLabel(
            self.login_frame,
            text="نام کاربری",
            font=("Arial", 12, "bold")
        )
        self.username_label.pack(anchor="e", padx=10, pady=(10, 5))

        self.username_entry = ctk.CTkEntry(
            self.login_frame,
            width=300,
            font=("Arial", 12)
        )
        self.username_entry.pack(padx=10, pady=(0, 10))

        # رمز عبور
        self.password_label = ctk.CTkLabel(
            self.login_frame,
            text="رمز عبور",
            font=("Arial", 12, "bold")
        )
        self.password_label.pack(anchor="e", padx=10, pady=(10, 5))

        self.password_entry = ctk.CTkEntry(
            self.login_frame,
            width=300,
            font=("Arial", 12),
            show="•"
        )
        self.password_entry.pack(padx=10, pady=(0, 20))

        # دکمه ورود
        self.login_button = ctk.CTkButton(
            self.login_frame,
            text="ورود به سیستم",
            font=("Arial", 14, "bold"),
            command=self.login
        )
        self.login_button.pack(padx=10, pady=(0, 20))

        # پیام خطا
        self.error_label = ctk.CTkLabel(
            self.login_frame,
            text="",
            font=("Arial", 12),
            text_color="#dc3545"
        )
        self.error_label.pack(padx=10, pady=(0, 10))

        # اطلاعات نسخه
        self.version_label = ctk.CTkLabel(
            self.main_frame,
            text="نسخه 1.0.0",
            font=("Arial", 10)
        )
        self.version_label.pack(side="bottom", pady=10)

    def login(self):
        """ورود به سیستم"""
        # دریافت نام کاربری و رمز عبور
        username = self.username_entry.get().strip()
        password = self.password_entry.get()

        # بررسی خالی نبودن فیلدها
        if not username or not password:
            self.show_error("لطفاً نام کاربری و رمز عبور را وارد کنید")
            return

        # احراز هویت کاربر
        user = self.db.authenticate_user(username, password)

        if user:
            # ذخیره اطلاعات کاربر
            self.current_user = user
            self.login_successful = True

            # نمایش پیام موفقیت
            self.show_success("ورود موفقیت‌آمیز")

            # فراخوانی تابع موفقیت
            if self.on_login_success:
                self.on_login_success(user)

            # بستن پنجره ورود
            self.destroy()
        else:
            # نمایش پیام خطا
            self.show_error("نام کاربری یا رمز عبور اشتباه است")

    def show_error(self, message):
        """نمایش پیام خطا"""
        self.error_label.configure(text=message, text_color="#dc3545")

    def show_success(self, message):
        """نمایش پیام موفقیت"""
        self.error_label.configure(text=message, text_color="#28a745")

    def on_close(self):
        """رویداد بستن پنجره"""
        if not self.login_successful:
            # اگر کاربر بدون ورود موفق پنجره را ببندد، برنامه را ببند
            self.master.destroy()
        else:
            # در غیر این صورت فقط پنجره ورود را ببند
            self.destroy()

    def reset_password(self, username):
        """بازنشانی رمز عبور"""
        # این متد در نسخه‌های بعدی پیاده‌سازی خواهد شد
        pass

if __name__ == "__main__":
    # تست پنجره ورود
    root = ctk.CTk()
    root.withdraw()

    def on_login(user):
        print(f"ورود موفق: {user['username']} ({user['full_name']})")
        root.deiconify()

    login_window = LoginWindow(root, on_login)
    root.mainloop()
