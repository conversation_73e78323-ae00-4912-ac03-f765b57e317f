import os
import datetime
from reportlab.lib.pagesizes import A4
from reportlab.lib import colors
from reportlab.lib.units import cm
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, Image
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
import arabic_reshaper
from bidi.algorithm import get_display

class InvoiceManager:
    """کلاس مدیریت فاکتورها"""
    
    def __init__(self, db):
        """مقداردهی اولیه"""
        self.db = db
        
        # ایجاد پوشه فاکتورها اگر وجود ندارد
        self.invoices_dir = "invoices"
        if not os.path.exists(self.invoices_dir):
            os.makedirs(self.invoices_dir)
            
        # ثبت فونت‌های فارسی
        self.register_fonts()
        
    def register_fonts(self):
        """ثبت فونت‌های فارسی برای استفاده در فاکتور"""
        try:
            # ثبت فونت Vazir برای متون فارسی
            vazir_path = os.path.join("assets", "fonts", "Vazir.ttf")
            vazir_bold_path = os.path.join("assets", "fonts", "Vazir-Bold.ttf")
            
            # اگر فونت‌ها وجود ندارند، از فونت‌های پیش‌فرض استفاده می‌کنیم
            if not os.path.exists(vazir_path) or not os.path.exists(vazir_bold_path):
                return
                
            pdfmetrics.registerFont(TTFont('Vazir', vazir_path))
            pdfmetrics.registerFont(TTFont('Vazir-Bold', vazir_bold_path))
        except Exception as e:
            print(f"خطا در ثبت فونت‌ها: {e}")
            
    def prepare_arabic_text(self, text):
        """آماده‌سازی متن فارسی برای نمایش صحیح در PDF"""
        try:
            # بازسازی متن فارسی و تبدیل آن به حالت قابل نمایش
            reshaped_text = arabic_reshaper.reshape(text)
            bidi_text = get_display(reshaped_text)
            return bidi_text
        except Exception as e:
            print(f"خطا در آماده‌سازی متن فارسی: {e}")
            return text
            
    def generate_invoice(self, sale_id):
        """ایجاد فاکتور برای یک فروش"""
        try:
            # دریافت اطلاعات فروش
            sale_details = self.db.get_sale_details(sale_id)
            if not sale_details:
                return None
                
            sale = sale_details["sale"]
            items = sale_details["items"]
            
            # ایجاد نام فایل فاکتور
            invoice_number = sale["invoice_number"]
            file_name = f"invoice_{invoice_number}.pdf"
            file_path = os.path.join(self.invoices_dir, file_name)
            
            # ایجاد سند PDF
            doc = SimpleDocTemplate(
                file_path,
                pagesize=A4,
                rightMargin=1.5*cm,
                leftMargin=1.5*cm,
                topMargin=1.5*cm,
                bottomMargin=1.5*cm
            )
            
            # ایجاد استایل‌های متن
            styles = getSampleStyleSheet()
            styles.add(ParagraphStyle(
                name='RTL',
                fontName='Vazir',
                alignment=2,  # راست به چپ
                fontSize=10
            ))
            styles.add(ParagraphStyle(
                name='RTL-Bold',
                fontName='Vazir-Bold',
                alignment=2,  # راست به چپ
                fontSize=12
            ))
            styles.add(ParagraphStyle(
                name='RTL-Title',
                fontName='Vazir-Bold',
                alignment=1,  # وسط چین
                fontSize=16
            ))
            
            # لیست عناصر فاکتور
            elements = []
            
            # عنوان فاکتور
            title = self.prepare_arabic_text("فاکتور فروش")
            elements.append(Paragraph(title, styles["RTL-Title"]))
            elements.append(Spacer(1, 0.5*cm))
            
            # اطلاعات فروشگاه
            shop_info = [
                [self.prepare_arabic_text("نام فروشگاه"), self.prepare_arabic_text("فروشگاه طلا و جواهرات")],
                [self.prepare_arabic_text("نشانی"), self.prepare_arabic_text("تبریز  ")],
                [self.prepare_arabic_text("تلفن"), self.prepare_arabic_text("021-12345678")]
            ]
            shop_table = Table(shop_info, colWidths=[3*cm, 12*cm])
            shop_table.setStyle(TableStyle([
                ('FONTNAME', (0, 0), (-1, -1), 'Vazir'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('ALIGN', (0, 0), (0, -1), 'RIGHT'),
                ('ALIGN', (1, 0), (1, -1), 'RIGHT'),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
            ]))
            elements.append(shop_table)
            elements.append(Spacer(1, 0.5*cm))
            
            # اطلاعات فاکتور
            invoice_date = datetime.datetime.strptime(sale["sale_date"], "%Y-%m-%d %H:%M:%S").strftime("%Y/%m/%d")
            invoice_info = [
                [self.prepare_arabic_text("شماره فاکتور"), self.prepare_arabic_text(invoice_number)],
                [self.prepare_arabic_text("تاریخ"), self.prepare_arabic_text(invoice_date)],
                [self.prepare_arabic_text("نام مشتری"), self.prepare_arabic_text(sale["customer_name"] if sale["customer_name"] else "مشتری آزاد")],
                [self.prepare_arabic_text("تلفن مشتری"), self.prepare_arabic_text(sale["customer_phone"] if sale["customer_phone"] else "-")]
            ]
            invoice_table = Table(invoice_info, colWidths=[3*cm, 12*cm])
            invoice_table.setStyle(TableStyle([
                ('FONTNAME', (0, 0), (-1, -1), 'Vazir'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('ALIGN', (0, 0), (0, -1), 'RIGHT'),
                ('ALIGN', (1, 0), (1, -1), 'RIGHT'),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
            ]))
            elements.append(invoice_table)
            elements.append(Spacer(1, 0.5*cm))
            
            # جدول اقلام فاکتور
            items_data = [
                [
                    self.prepare_arabic_text("ردیف"),
                    self.prepare_arabic_text("کد محصول"),
                    self.prepare_arabic_text("نام محصول"),
                    self.prepare_arabic_text("تعداد"),
                    self.prepare_arabic_text("قیمت واحد (تومان)"),
                    self.prepare_arabic_text("قیمت کل (تومان)")
                ]
            ]
            
            for i, item in enumerate(items):
                row = [
                    self.prepare_arabic_text(str(i+1)),
                    self.prepare_arabic_text(item["product_code"]),
                    self.prepare_arabic_text(item["product_name"]),
                    self.prepare_arabic_text(str(item["quantity"])),
                    self.prepare_arabic_text(f"{item['price']:,}"),
                    self.prepare_arabic_text(f"{item['quantity'] * item['price']:,}")
                ]
                items_data.append(row)
                
            items_table = Table(items_data, colWidths=[1*cm, 2.5*cm, 6*cm, 1.5*cm, 3.5*cm, 3.5*cm])
            items_table.setStyle(TableStyle([
                ('FONTNAME', (0, 0), (-1, 0), 'Vazir-Bold'),
                ('FONTNAME', (0, 1), (-1, -1), 'Vazir'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ('GRID', (0, 0), (-1, -1), 0.5, colors.black),
                ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
                ('TOPPADDING', (0, 0), (-1, -1), 6),
            ]))
            elements.append(items_table)
            elements.append(Spacer(1, 0.5*cm))
            
            # جمع کل
            total_info = [
                [self.prepare_arabic_text("روش پرداخت:"), self.prepare_arabic_text(sale["payment_method"] if sale["payment_method"] else "-")],
                [self.prepare_arabic_text("توضیحات:"), self.prepare_arabic_text(sale["notes"] if sale["notes"] else "-")],
                [self.prepare_arabic_text("جمع کل:"), self.prepare_arabic_text(f"{sale['total_amount']:,} تومان")]
            ]
            total_table = Table(total_info, colWidths=[3*cm, 12*cm])
            total_table.setStyle(TableStyle([
                ('FONTNAME', (0, 0), (0, -1), 'Vazir'),
                ('FONTNAME', (1, -1), (1, -1), 'Vazir-Bold'),
                ('FONTNAME', (0, -1), (0, -1), 'Vazir-Bold'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('FONTSIZE', (0, -1), (1, -1), 12),
                ('ALIGN', (0, 0), (0, -1), 'RIGHT'),
                ('ALIGN', (1, 0), (1, -1), 'RIGHT'),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
            ]))
            elements.append(total_table)
            elements.append(Spacer(1, 1*cm))
            
            # پاورقی
            footer_text = self.prepare_arabic_text("با تشکر از خرید شما - این فاکتور بدون مهر و امضا فاقد اعتبار است")
            elements.append(Paragraph(footer_text, styles["RTL"]))
            
            # ایجاد فایل PDF
            doc.build(elements)
            
            return file_path
            
        except Exception as e:
            print(f"خطا در ایجاد فاکتور: {e}")
            return None
            
    def print_invoice(self, file_path):
        """چاپ فاکتور"""
        try:
            import os
            if os.name == 'nt':  # ویندوز
                os.startfile(file_path, "print")
            else:  # لینوکس و مک
                os.system(f"lpr {file_path}")
            return True
        except Exception as e:
            print(f"خطا در چاپ فاکتور: {e}")
            return False
