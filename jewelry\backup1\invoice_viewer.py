import os
import customtkinter as ctk
from PIL import Image, ImageTk
import fitz  # PyMuPDF

class InvoiceViewer(ctk.CTkToplevel):
    """کلاس نمایش پیش‌نمایش فاکتور"""
    
    def __init__(self, parent, invoice_path, sale_id=None, on_print=None):
        """مقداردهی اولیه"""
        super().__init__(parent)
        
        # تنظیمات پنجره
        self.title("پیش‌نمایش فاکتور")
        self.geometry("1000x800")
        self.minsize(600, 600)
        
        # مسیر فایل فاکتور
        self.invoice_path = invoice_path
        
        # شناسه فروش
        self.sale_id = sale_id
        
        # تابع چاپ
        self.on_print = on_print
        
        # ایجاد رابط کاربری
        self.create_widgets()
        
        # بارگذاری فاکتور
        self.load_invoice()
        
    def create_widgets(self):
        """ایجاد عناصر رابط کاربری"""
        # فریم اصلی
        self.main_frame = ctk.CTkFrame(self)
        self.main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # عنوان
        self.title_label = ctk.CTkLabel(
            self.main_frame,
            text="پیش‌نمایش فاکتور",
            font=("Arial", 18, "bold")
        )
        self.title_label.pack(pady=(0, 20))
        
        # فریم نمایش فاکتور
        self.invoice_frame = ctk.CTkScrollableFrame(
            self.main_frame,
            width=700,
            height=400
        )
        self.invoice_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # برچسب نمایش فاکتور
        self.invoice_label = ctk.CTkLabel(
            self.invoice_frame,
            text="",
            image=None
        )
        self.invoice_label.pack(fill="both", expand=True)
        
        # فریم دکمه‌ها
        self.buttons_frame = ctk.CTkFrame(self.main_frame, fg_color="transparent")
        self.buttons_frame.pack(fill="x", padx=10, pady=10)
        
        # دکمه چاپ
        self.print_button = ctk.CTkButton(
            self.buttons_frame,
            text="چاپ فاکتور",
            font=("Arial", 14, "bold"),
            fg_color="#28a745",
            hover_color="#218838",
            width=150,
            height=40,
            command=self.print_invoice
        )
        self.print_button.pack(side="right", padx=10)
        
        # دکمه بستن
        self.close_button = ctk.CTkButton(
            self.buttons_frame,
            text="بستن",
            font=("Arial", 14),
            fg_color="#6c757d",
            hover_color="#5a6268",
            width=150,
            height=40,
            command=self.destroy
        )
        self.close_button.pack(side="right", padx=10)
        
    def load_invoice(self):
        """بارگذاری فاکتور"""
        try:
            if not os.path.exists(self.invoice_path):
                self.show_error("فایل فاکتور یافت نشد")
                return
                
            # باز کردن فایل PDF
            doc = fitz.open(self.invoice_path)
            
            # دریافت صفحه اول
            page = doc.load_page(0)
            
            # تبدیل به تصویر با کیفیت بالا
            pix = page.get_pixmap(matrix=fitz.Matrix(2, 2))
            
            # تبدیل به تصویر PIL
            img = Image.frombytes("RGB", [pix.width, pix.height], pix.samples)
            
            # تبدیل به تصویر CTk
            ctk_img = ctk.CTkImage(light_image=img, dark_image=img, size=(pix.width, pix.height))
            
            # نمایش تصویر
            self.invoice_label.configure(image=ctk_img, text="")
            
            # ذخیره مرجع تصویر
            self.invoice_image = ctk_img
            
            # بستن فایل PDF
            doc.close()
            
        except Exception as e:
            self.show_error(f"خطا در بارگذاری فاکتور: {e}")
            
    def print_invoice(self):
        """چاپ فاکتور"""
        if self.on_print:
            success = self.on_print(self.invoice_path)
            if success:
                self.show_success("فاکتور با موفقیت به چاپگر ارسال شد")
            else:
                self.show_error("خطا در ارسال فاکتور به چاپگر")
        else:
            self.show_error("امکان چاپ فاکتور وجود ندارد")
            
    def show_error(self, message):
        """نمایش پیام خطا"""
        from tkinter import messagebox
        messagebox.showerror("خطا", message)
        
    def show_success(self, message):
        """نمایش پیام موفقیت"""
        from tkinter import messagebox
        messagebox.showinfo("موفقیت", message)
