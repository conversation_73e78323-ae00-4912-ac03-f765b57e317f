import os
import sys
import customtkinter as ctk
from PIL import Image, ImageTk
from database import Database

# Set appearance mode and default color theme
ctk.set_appearance_mode("System")  # Modes: "System" (standard), "Dark", "Light"
ctk.set_default_color_theme("blue")  # Themes: "blue" (standard), "green", "dark-blue"

class JewelryStoreApp(ctk.CTk):
    def __init__(self):
        super().__init__()
        
        # Initialize database
        self.db = Database()
        
        # Configure window
        self.title("Jewelry Store Management System")
        self.geometry("1200x700")
        self.minsize(1000, 600)
        
        # Create assets directory if it doesn't exist
        if not os.path.exists("assets"):
            os.makedirs("assets")
        
        # Load images
        self.load_images()
        
        # Create main layout
        self.create_layout()
        
        # Create navigation frame
        self.create_navigation()
        
        # Create home frame (default)
        self.create_home_frame()
        
        # Select default frame
        self.select_frame_by_name("home")
        
    def load_images(self):
        """Load images for the application"""
        self.logo_image = self.load_image("assets/logo.png", (50, 50))
        self.home_image = self.load_image("assets/home.png", (20, 20))
        self.products_image = self.load_image("assets/products.png", (20, 20))
        self.customers_image = self.load_image("assets/customers.png", (20, 20))
        self.sales_image = self.load_image("assets/sales.png", (20, 20))
        self.inventory_image = self.load_image("assets/inventory.png", (20, 20))
        self.reports_image = self.load_image("assets/reports.png", (20, 20))
        self.settings_image = self.load_image("assets/settings.png", (20, 20))
        
    def load_image(self, path, size):
        """Load an image with error handling"""
        try:
            return ctk.CTkImage(
                light_image=Image.open(path),
                dark_image=Image.open(path),
                size=size
            )
        except Exception as e:
            print(f"Error loading image {path}: {e}")
            return None
        
    def create_layout(self):
        """Create the main application layout"""
        # Configure grid layout (2x1)
        self.grid_columnconfigure(1, weight=1)
        self.grid_rowconfigure(0, weight=1)
        
    def create_navigation(self):
        """Create the navigation sidebar"""
        # Create navigation frame
        self.navigation_frame = ctk.CTkFrame(self, corner_radius=0)
        self.navigation_frame.grid(row=0, column=0, sticky="nsew")
        self.navigation_frame.grid_rowconfigure(7, weight=1)
        
        # Navigation frame label
        self.navigation_frame_label = ctk.CTkLabel(
            self.navigation_frame, text="  Jewelry Store", 
            image=self.logo_image,
            compound="left", 
            font=ctk.CTkFont(size=15, weight="bold")
        )
        self.navigation_frame_label.grid(row=0, column=0, padx=20, pady=20)
        
        # Home button
        self.home_button = ctk.CTkButton(
            self.navigation_frame, corner_radius=0, height=40, border_spacing=10, 
            text="Home",
            fg_color="transparent", text_color=("gray10", "gray90"), 
            hover_color=("gray70", "gray30"),
            image=self.home_image, anchor="w",
            command=self.home_button_event
        )
        self.home_button.grid(row=1, column=0, sticky="ew")
        
        # Products button
        self.products_button = ctk.CTkButton(
            self.navigation_frame, corner_radius=0, height=40, border_spacing=10, 
            text="Products",
            fg_color="transparent", text_color=("gray10", "gray90"), 
            hover_color=("gray70", "gray30"),
            image=self.products_image, anchor="w",
            command=self.products_button_event
        )
        self.products_button.grid(row=2, column=0, sticky="ew")
        
        # Customers button
        self.customers_button = ctk.CTkButton(
            self.navigation_frame, corner_radius=0, height=40, border_spacing=10, 
            text="Customers",
            fg_color="transparent", text_color=("gray10", "gray90"), 
            hover_color=("gray70", "gray30"),
            image=self.customers_image, anchor="w",
            command=self.customers_button_event
        )
        self.customers_button.grid(row=3, column=0, sticky="ew")
        
        # Sales button
        self.sales_button = ctk.CTkButton(
            self.navigation_frame, corner_radius=0, height=40, border_spacing=10, 
            text="Sales",
            fg_color="transparent", text_color=("gray10", "gray90"), 
            hover_color=("gray70", "gray30"),
            image=self.sales_image, anchor="w",
            command=self.sales_button_event
        )
        self.sales_button.grid(row=4, column=0, sticky="ew")
        
        # Inventory button
        self.inventory_button = ctk.CTkButton(
            self.navigation_frame, corner_radius=0, height=40, border_spacing=10, 
            text="Inventory",
            fg_color="transparent", text_color=("gray10", "gray90"), 
            hover_color=("gray70", "gray30"),
            image=self.inventory_image, anchor="w",
            command=self.inventory_button_event
        )
        self.inventory_button.grid(row=5, column=0, sticky="ew")
        
        # Reports button
        self.reports_button = ctk.CTkButton(
            self.navigation_frame, corner_radius=0, height=40, border_spacing=10, 
            text="Reports",
            fg_color="transparent", text_color=("gray10", "gray90"), 
            hover_color=("gray70", "gray30"),
            image=self.reports_image, anchor="w",
            command=self.reports_button_event
        )
        self.reports_button.grid(row=6, column=0, sticky="ew")
        
        # Appearance mode menu
        self.appearance_mode_menu = ctk.CTkOptionMenu(
            self.navigation_frame, values=["Light", "Dark", "System"],
            command=self.change_appearance_mode_event
        )
        self.appearance_mode_menu.grid(row=8, column=0, padx=20, pady=20, sticky="s")
        
    def select_frame_by_name(self, name):
        """Select a frame by name and update button colors"""
        # Set button color for selected button
        self.home_button.configure(fg_color=("gray75", "gray25") if name == "home" else "transparent")
        self.products_button.configure(fg_color=("gray75", "gray25") if name == "products" else "transparent")
        self.customers_button.configure(fg_color=("gray75", "gray25") if name == "customers" else "transparent")
        self.sales_button.configure(fg_color=("gray75", "gray25") if name == "sales" else "transparent")
        self.inventory_button.configure(fg_color=("gray75", "gray25") if name == "inventory" else "transparent")
        self.reports_button.configure(fg_color=("gray75", "gray25") if name == "reports" else "transparent")
        
        # Show selected frame
        if name == "home":
            self.home_frame.grid(row=0, column=1, sticky="nsew")
        else:
            self.home_frame.grid_forget()
            
        if name == "products":
            self.create_products_frame()
            self.products_frame.grid(row=0, column=1, sticky="nsew")
        elif hasattr(self, "products_frame"):
            self.products_frame.grid_forget()
            
        if name == "customers":
            self.create_customers_frame()
            self.customers_frame.grid(row=0, column=1, sticky="nsew")
        elif hasattr(self, "customers_frame"):
            self.customers_frame.grid_forget()
            
        if name == "sales":
            self.create_sales_frame()
            self.sales_frame.grid(row=0, column=1, sticky="nsew")
        elif hasattr(self, "sales_frame"):
            self.sales_frame.grid_forget()
            
        if name == "inventory":
            self.create_inventory_frame()
            self.inventory_frame.grid(row=0, column=1, sticky="nsew")
        elif hasattr(self, "inventory_frame"):
            self.inventory_frame.grid_forget()
            
        if name == "reports":
            self.create_reports_frame()
            self.reports_frame.grid(row=0, column=1, sticky="nsew")
        elif hasattr(self, "reports_frame"):
            self.reports_frame.grid_forget()
            
    def home_button_event(self):
        self.select_frame_by_name("home")
        
    def products_button_event(self):
        self.select_frame_by_name("products")
        
    def customers_button_event(self):
        self.select_frame_by_name("customers")
        
    def sales_button_event(self):
        self.select_frame_by_name("sales")
        
    def inventory_button_event(self):
        self.select_frame_by_name("inventory")
        
    def reports_button_event(self):
        self.select_frame_by_name("reports")
        
    def change_appearance_mode_event(self, new_appearance_mode):
        ctk.set_appearance_mode(new_appearance_mode)
        
    def create_home_frame(self):
        """Create the home frame with dashboard"""
        self.home_frame = ctk.CTkFrame(self, corner_radius=0, fg_color="transparent")
        self.home_frame.grid_columnconfigure(0, weight=1)
        
        # Welcome label
        self.home_frame_large_label = ctk.CTkLabel(
            self.home_frame, text="Welcome to Jewelry Store Management System",
            font=ctk.CTkFont(size=20, weight="bold")
        )
        self.home_frame_large_label.grid(row=0, column=0, padx=20, pady=20)
        
        # Dashboard frame
        self.dashboard_frame = ctk.CTkFrame(self.home_frame)
        self.dashboard_frame.grid(row=1, column=0, padx=20, pady=20, sticky="nsew")
        self.dashboard_frame.grid_columnconfigure((0, 1, 2), weight=1)
        
        # Dashboard widgets
        # Sales summary card
        self.sales_card = ctk.CTkFrame(self.dashboard_frame)
        self.sales_card.grid(row=0, column=0, padx=10, pady=10, sticky="nsew")
        
        self.sales_title = ctk.CTkLabel(
            self.sales_card, text="Sales Today",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        self.sales_title.grid(row=0, column=0, padx=10, pady=5)
        
        self.sales_amount = ctk.CTkLabel(
            self.sales_card, text="$0",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        self.sales_amount.grid(row=1, column=0, padx=10, pady=5)
        
        # Inventory summary card
        self.inventory_card = ctk.CTkFrame(self.dashboard_frame)
        self.inventory_card.grid(row=0, column=1, padx=10, pady=10, sticky="nsew")
        
        self.inventory_title = ctk.CTkLabel(
            self.inventory_card, text="Total Products",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        self.inventory_title.grid(row=0, column=0, padx=10, pady=5)
        
        self.inventory_count = ctk.CTkLabel(
            self.inventory_card, text="0",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        self.inventory_count.grid(row=1, column=0, padx=10, pady=5)
        
        # Customers summary card
        self.customers_card = ctk.CTkFrame(self.dashboard_frame)
        self.customers_card.grid(row=0, column=2, padx=10, pady=10, sticky="nsew")
        
        self.customers_title = ctk.CTkLabel(
            self.customers_card, text="Total Customers",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        self.customers_title.grid(row=0, column=0, padx=10, pady=5)
        
        self.customers_count = ctk.CTkLabel(
            self.customers_card, text="0",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        self.customers_count.grid(row=1, column=0, padx=10, pady=5)
        
        # Recent sales section
        self.recent_sales_frame = ctk.CTkFrame(self.home_frame)
        self.recent_sales_frame.grid(row=2, column=0, padx=20, pady=20, sticky="nsew")
        
        self.recent_sales_label = ctk.CTkLabel(
            self.recent_sales_frame, text="Recent Sales",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        self.recent_sales_label.grid(row=0, column=0, padx=10, pady=10, sticky="w")
        
        # Placeholder for recent sales data
        self.recent_sales_placeholder = ctk.CTkLabel(
            self.recent_sales_frame, text="No recent sales data available",
            font=ctk.CTkFont(size=12)
        )
        self.recent_sales_placeholder.grid(row=1, column=0, padx=10, pady=10)
        
    def create_products_frame(self):
        """Create the products management frame"""
        if not hasattr(self, "products_frame"):
            self.products_frame = ctk.CTkFrame(self, corner_radius=0, fg_color="transparent")
            self.products_frame.grid_columnconfigure(0, weight=1)
            
            # Products label
            self.products_label = ctk.CTkLabel(
                self.products_frame, text="Products Management",
                font=ctk.CTkFont(size=20, weight="bold")
            )
            self.products_label.grid(row=0, column=0, padx=20, pady=20)
            
            # Products content will be implemented later
            self.products_placeholder = ctk.CTkLabel(
                self.products_frame, text="Products management interface will be implemented here",
                font=ctk.CTkFont(size=12)
            )
            self.products_placeholder.grid(row=1, column=0, padx=20, pady=20)
            
    def create_customers_frame(self):
        """Create the customers management frame"""
        if not hasattr(self, "customers_frame"):
            self.customers_frame = ctk.CTkFrame(self, corner_radius=0, fg_color="transparent")
            self.customers_frame.grid_columnconfigure(0, weight=1)
            
            # Customers label
            self.customers_label = ctk.CTkLabel(
                self.customers_frame, text="Customers Management",
                font=ctk.CTkFont(size=20, weight="bold")
            )
            self.customers_label.grid(row=0, column=0, padx=20, pady=20)
            
            # Customers content will be implemented later
            self.customers_placeholder = ctk.CTkLabel(
                self.customers_frame, text="Customers management interface will be implemented here",
                font=ctk.CTkFont(size=12)
            )
            self.customers_placeholder.grid(row=1, column=0, padx=20, pady=20)
            
    def create_sales_frame(self):
        """Create the sales management frame"""
        if not hasattr(self, "sales_frame"):
            self.sales_frame = ctk.CTkFrame(self, corner_radius=0, fg_color="transparent")
            self.sales_frame.grid_columnconfigure(0, weight=1)
            
            # Sales label
            self.sales_label = ctk.CTkLabel(
                self.sales_frame, text="Sales Management",
                font=ctk.CTkFont(size=20, weight="bold")
            )
            self.sales_label.grid(row=0, column=0, padx=20, pady=20)
            
            # Sales content will be implemented later
            self.sales_placeholder = ctk.CTkLabel(
                self.sales_frame, text="Sales management interface will be implemented here",
                font=ctk.CTkFont(size=12)
            )
            self.sales_placeholder.grid(row=1, column=0, padx=20, pady=20)
            
    def create_inventory_frame(self):
        """Create the inventory management frame"""
        if not hasattr(self, "inventory_frame"):
            self.inventory_frame = ctk.CTkFrame(self, corner_radius=0, fg_color="transparent")
            self.inventory_frame.grid_columnconfigure(0, weight=1)
            
            # Inventory label
            self.inventory_label = ctk.CTkLabel(
                self.inventory_frame, text="Inventory Management",
                font=ctk.CTkFont(size=20, weight="bold")
            )
            self.inventory_label.grid(row=0, column=0, padx=20, pady=20)
            
            # Inventory content will be implemented later
            self.inventory_placeholder = ctk.CTkLabel(
                self.inventory_frame, text="Inventory management interface will be implemented here",
                font=ctk.CTkFont(size=12)
            )
            self.inventory_placeholder.grid(row=1, column=0, padx=20, pady=20)
            
    def create_reports_frame(self):
        """Create the reports and analytics frame"""
        if not hasattr(self, "reports_frame"):
            self.reports_frame = ctk.CTkFrame(self, corner_radius=0, fg_color="transparent")
            self.reports_frame.grid_columnconfigure(0, weight=1)
            
            # Reports label
            self.reports_label = ctk.CTkLabel(
                self.reports_frame, text="Reports and Analytics",
                font=ctk.CTkFont(size=20, weight="bold")
            )
            self.reports_label.grid(row=0, column=0, padx=20, pady=20)
            
            # Reports content will be implemented later
            self.reports_placeholder = ctk.CTkLabel(
                self.reports_frame, text="Reports and analytics interface will be implemented here",
                font=ctk.CTkFont(size=12)
            )
            self.reports_placeholder.grid(row=1, column=0, padx=20, pady=20)

if __name__ == "__main__":
    app = JewelryStoreApp()
    app.mainloop()
