import tkinter as tk  
from tkinter import ttk  
import ttkbootstrap as tb  
import mysql.connector  
from mysql.connector import Error  
from tkcalendar import DateEntry
from tkcalendar import Calendar  # وارد کردن Calendar  
import re  # برای استفاده از عبارات با قاعده (Regular Expressions)  
from tkinter import messagebox  # اضافه کردن messagebox  
from  datetime import date




# اطلاعات اتصال به پایگاه داده (جایگزین کنید)  
db_config = {  
    'user': 'root',  
    'password': '123456',  
    'host': 'localhost',  
    'database': 'school_management'  
}  

def validate_name(name):  
    """  
    اعتبارسنجی نام.  
    """  
    if not name:  
        return False, "نام نمی تواند خالی باشد."  
    if not re.match("^[a-zA-Z\u0600-\u06FF]+$", name):  # تطبیق با حروف فارسی و انگلیسی  
        return False, "نام باید فقط شامل حروف باشد."  
    if len(name) < 2:  
        return False, "نام باید حداقل 2 حرف داشته باشد."  
    return True, ""  # معتبر  

def validate_phone_number(phone):  
    """  
    اعتبارسنجی شماره تلفن.  
    """  
    if not phone:  
        return False, "شماره تلفن نمی تواند خالی باشد."  
    if not re.match("^09\d{9}$", phone):  
        return False, "شماره تلفن باید 11 رقم باشد و با 09 شروع شود."  
    return True, ""  

def connect_to_database():  
    """  
    اتصال به پایگاه داده MySQL.  
    """  
    try:  
        connection = mysql.connector.connect(**db_config)  
        print("Connected to database successfully!")  
        return connection  
    except Error as err:  
        print(f"Error connecting to database: {err}")  
        return None  

def add_student():  
    """  
    تابعی برای اضافه کردن دانش آموز به پایگاه داده.  
    """  
    first_name = first_name_entry.get()  
    last_name = last_name_entry.get()  
    birth_date = birth_date_var.get()  # گرفتن تاریخ تولد از متغیر  
    gender = gender_var.get()  # گرفتن جنسیت  
    address = address_entry.get("1.0", tk.END)  # گرفتن آدرس از Text widget  
    phone_number = phone_number_entry.get()  # گرفتن شماره تلفن  

# اعتبارسنجی نام  
    is_valid, message = validate_name(first_name)  
    if not is_valid:  
        messagebox.showerror("Error", message)  
        return  

    # اعتبارسنجی نام خانوادگی  
    is_valid, message = validate_name(last_name)  
    if not is_valid:  
        messagebox.showerror("Error", message) 
        return  


    # اعتبارسنجی شماره تلفن  
    is_valid, message = validate_phone_number(phone_number)  
    if not is_valid:  
        messagebox.showerror("Error", message)  # نمایش خطا با messagebox  
        return  
    try:  
        connection = connect_to_database()  
        if connection:  
            cursor = connection.cursor()  
            query = "INSERT INTO students (first_name, last_name, date_of_birth, gender, address, phone_number) VALUES (%s, %s, %s, %s, %s, %s)"  
            values = (first_name, last_name, birth_date, gender, address, phone_number)  
            cursor.execute(query, values)  
            connection.commit()  
            print("Student added successfully!")  
            connection.close()  
    except Error as err:  
        print(f"Error adding student: {err}")  



def open_calendar():  
    """  
    باز کردن پنجره تقویم.  
    """  
    def set_date():  
        """  
        تنظیم تاریخ انتخاب شده و بستن پنجره تقویم.  
        """  
        birth_date_var.set(cal.get_date())  
        cal_window.destroy()  

  

    cal_window = tk.Toplevel(root)  
    cal = Calendar(cal_window, selectmode="day",date_pattern='yyyy-mm-dd') # سال، ماه و روز را تنظیم کنید  
    cal.pack(pady=20)  

    confirm_button = tb.Button(cal_window, text="Confirm", command=set_date, bootstyle="success")  
    confirm_button.pack(pady=10)  




def main():  
    """  
    تابع اصلی برنامه.  
    """  
    global root  
    root = tb.Window(themename="darkly")  
    root.title("School Management System")  
    root.geometry("800x600")  
 

    
    # --- فرم ثبت دانش آموز ---  
    student_form_frame = tb.Frame(root, padding=20)  
    student_form_frame.pack(fill="x")  

    # برچسب و ورودی نام  
    first_name_label = tb.Label(student_form_frame, text="First Name:")  
    first_name_label.grid(row=0, column=0, padx=5, pady=5, sticky="w")  
    global first_name_entry  
    first_name_entry = tb.Entry(student_form_frame)  
    first_name_entry.grid(row=0, column=1, padx=5, pady=5, sticky="e")  

    # برچسب و ورودی نام خانوادگی  
    last_name_label = tb.Label(student_form_frame, text="Last Name:")  
    last_name_label.grid(row=1, column=0, padx=5, pady=5, sticky="w")  
    global last_name_entry  
    last_name_entry = tb.Entry(student_form_frame)  
    last_name_entry.grid(row=1, column=1, padx=5, pady=5, sticky="e")  



    # برچسب و ورودی تاریخ تولد  
    birth_date_label = tb.Label(student_form_frame, text="Birth Date:")  
    birth_date_label.grid(row=2, column=0, padx=5, pady=5, sticky="w")  
    

    global birth_date_var  
    birth_date_var = tk.StringVar()  
    birth_date_entry_button = tb.Button(student_form_frame, text="Select Date", command=open_calendar, bootstyle="primary")  
    birth_date_entry_button.grid(row=2, column=1, padx=5, pady=5, sticky="e")  
    birth_date_var = tb.DateEntry(student_form_frame)
    birth_date_label_display = tb.Label(student_form_frame, textvariable=birth_date_var)  
    birth_date_label_display.grid(row=2, column=2, padx=5, pady=5, sticky="w")  

    
    #cal =  tb.DateEntry(student_form_frame,bootstyle="info")
    #cal.grid(row=2, column=1, padx=5, pady=5, sticky="w")  
  
    #cal.place(x=10, y=80)


    # برچسب و انتخاب جنسیت  
    gender_label = tb.Label(student_form_frame, text="Gender:")  
    gender_label.grid(row=3, column=0, padx=5, pady=5, sticky="w")  
    global gender_var  
    gender_var = tk.StringVar(value="Male")  # مقدار پیش فرض  
    male_radio = tb.Radiobutton(student_form_frame, text="Male", value="Male", variable=gender_var, bootstyle="primary")  
    male_radio.grid(row=3, column=1, padx=5, pady=5, sticky="w")  
    female_radio = tb.Radiobutton(student_form_frame, text="Female", value="Female", variable=gender_var, bootstyle="primary")  
    female_radio.grid(row=3, column=2, padx=5, pady=5, sticky="w")  

    # برچسب و ورودی آدرس (Text widget)  
    address_label = tb.Label(student_form_frame, text="Address:")  
    address_label.grid(row=4, column=0, padx=5, pady=5, sticky="w")  
    global address_entry  
    address_entry = tk.Text(student_form_frame, height=3, width=30)  # استفاده از Text  
    address_entry.grid(row=4, column=1, padx=5, pady=5, sticky="e")  

    # برچسب و ورودی شماره تلفن  
    phone_number_label = tb.Label(student_form_frame, text="Phone Number:")  
    phone_number_label.grid(row=5, column=0, padx=5, pady=5, sticky="w")  
    global phone_number_entry  
    phone_number_entry = tb.Entry(student_form_frame)  
    phone_number_entry.grid(row=5, column=1, padx=5, pady=5, sticky="e")  

    # دکمه ثبت  
    add_button = tb.Button(student_form_frame, text="Add Student", command=add_student, bootstyle="success")  
    add_button.grid(row=6, column=0, columnspan=2, padx=5, pady=10)  

    # --- پایان فرم ثبت دانش آموز ---  

    root.mainloop()  

if __name__ == "__main__":  
    main()  