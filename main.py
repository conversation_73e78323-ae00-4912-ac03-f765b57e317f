import tkinter as tk
from tkinter import ttk
import json
import importlib.util
import os
from datetime import datetime
from jdatetime import datetime as jdatetime

class Dashboard:
    def __init__(self, root):
        self.root = root
        self.root.title("داشبورد ابزارهای هوش مصنوعی")
        self.root.geometry("1000x700")
        
        # تنظیم جهت راست به چپ
        self.root.tk.call('tk', 'scaling', 1.0)
        
        # بارگذاری تنظیمات
        self.load_config()
        
        # ایجاد رابط کاربری
        self.setup_ui()
        
        # بارگذاری ماژول‌ها
        self.load_modules()

    def load_config(self):
        """بارگذاری تنظیمات از فایل configall.json"""
        with open('configall.json', encoding='utf-8') as f:
            self.config = json.load(f)

    def setup_ui(self):
        """ایجاد رابط کاربری اصلی"""
        # نوار کناری برای منوی ماژول‌ها
        self.sidebar = tk.Frame(self.root, width=200, bg='#2c3e50')
        self.sidebar.pack(side=tk.RIGHT, fill=tk.Y)  # تغییر به سمت راست
        
        # فضای اصلی برای نمایش ماژول‌ها
        self.main_area = tk.Frame(self.root, bg='#ecf0f1')
        self.main_area.pack(side=tk.LEFT, expand=True, fill=tk.BOTH)  # تغییر به سمت چپ
        
        # عنوان
        title_label = tk.Label(self.sidebar, text="ماژول‌ها", bg='#2c3e50', fg='white', font=('Tahoma', 14))
        title_label.pack(pady=20)
        
        # نمایش تاریخ شمسی
        self.date_label = tk.Label(self.sidebar, text="", bg='#2c3e50', fg='white', font=('Tahoma', 10))
        self.date_label.pack(pady=5)
        self.update_date()

    def update_date(self):
        """به‌روزرسانی تاریخ شمسی"""
        j_date = jdatetime.now().strftime("%Y/%m/%d")
        j_time = jdatetime.now().strftime("%H:%M:%S")
        self.date_label.config(text=f"تاریخ: {j_date}\nساعت: {j_time}")
        self.root.after(1000, self.update_date)  # به‌روزرسانی هر ثانیه

    def load_modules(self):
        """بارگذاری پویای ماژول‌ها از پوشه modules"""
        for module_name in self.config['modules']:
            self.add_module_button(module_name)

    def add_module_button(self, module_name):
        """افزودن دکمه ماژول به نوار کناری"""
        btn = tk.Button(self.sidebar, text=module_name, 
                       command=lambda m=module_name: self.run_module(m),
                       bg='#3498db', fg='white', width=20,
                       font=('Tahoma', 10))
        btn.pack(pady=5)

    def run_module(self, module_name):
        """اجرای ماژول انتخاب‌شده"""
        # پاک کردن محتوای قبلی
        for widget in self.main_area.winfo_children():
            widget.destroy()
        
        # بارگذاری و اجرای ماژول
        module_path = f"modules/{module_name}.py"
        if os.path.exists(module_path):
            spec = importlib.util.spec_from_file_location(module_name, module_path)
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
            module.run(self.main_area)  # فرض بر اینکه هر ماژول تابع run دارد
        else:
            error_label = tk.Label(self.main_area, 
                                 text=f"ماژول {module_name} یافت نشد!", 
                                 bg='#ecf0f1',
                                 font=('Tahoma', 12))
            error_label.pack()

if __name__ == "__main__":
    root = tk.Tk()
    app = Dashboard(root)
    root.mainloop()