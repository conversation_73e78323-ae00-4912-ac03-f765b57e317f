import requests
import json
import os
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from PIL import Image, ImageTk
import io
import webbrowser
from tkinter import Toplevel, Label, Frame, Button, Text, Scrollbar, END

def run(parent_frame):
    tool = (parent_frame)
    return tool


class InstagramCookieHelper:
    def __init__(self, parent):
        self.parent = parent
        self.window = Toplevel(parent)
        self.window.title("راهنمای دریافت کوکی اینستاگرام")
        self.window.geometry("700x600")
        
        # تنظیمات grid برای پنجره اصلی
        self.window.grid_rowconfigure(0, weight=1)
        self.window.grid_columnconfigure(0, weight=1)
        
        self.create_widgets()
        self.insert_help_text()  # اضافه کردن متد برای درج متن راهنما
    
    def create_widgets(self):
        # Frame اصلی با grid
        main_frame = Frame(self.window)
        main_frame.grid(row=0, column=0, sticky="nsew", padx=10, pady=10)
        
        # داخل main_frame از grid استفاده می‌کنیم
        text_frame = Frame(main_frame)
        text_frame.grid(row=0, column=0, sticky="nsew")
        
        # متن راهنما
        self.text_widget = Text(text_frame, wrap="word", font=("Tahoma", 10))
        self.text_widget.grid(row=0, column=0, sticky="nsew")
        
        scrollbar = Scrollbar(text_frame, command=self.text_widget.yview)
        scrollbar.grid(row=0, column=1, sticky="ns")
        self.text_widget.config(yscrollcommand=scrollbar.set)
        
        # دکمه‌ها در فریم جداگانه با grid
        button_frame = Frame(main_frame)
        button_frame.grid(row=1, column=0, pady=10)
        
        # دکمه‌ها با grid در فریم خودشان
        #Button(button_frame, text="آموزش تصویری", 
        #      command=self.open_tutorial).grid(row=0, column=0, padx=5)
        Button(button_frame, text="دانلود افزونه Chrome", 
              command=lambda: self.open_browser("https://chrome.google.com/webstore/detail/editthiscookie/fngmhnnpilhplaeedifhccceomclgfbg")).grid(row=0, column=1, padx=5)
        Button(button_frame, text="دانلود افزونه Firefox", 
              command=lambda: self.open_browser("https://addons.mozilla.org/en-US/firefox/addon/cookie-editor/")).grid(row=0, column=2, padx=5)
        Button(button_frame, text="بستن", 
              command=self.window.destroy).grid(row=0, column=3, padx=5)
        
        # تنظیمات وزن برای تغییر سایز
        main_frame.grid_rowconfigure(0, weight=1)
        main_frame.grid_columnconfigure(0, weight=1)
        text_frame.grid_rowconfigure(0, weight=1)
        text_frame.grid_columnconfigure(0, weight=1)
    
    def insert_help_text(self):
        """درج متن راهنما در ویجت Text"""
        help_text = """
مراحل دریافت کوکی اینستاگرام:

1. وارد حساب اینستاگرام خود در مرورگر شوید
   - فقط از حساب‌های مجاز برای توسعه استفاده کنید

2. نصب افزونه مدیریت کوکی:
   - برای Chrome: EditThisCookie
   - برای Firefox: Cookie-Editor

3. دریافت کوکی‌ها:
   - باز کردن افزونه
   - کلیک روی دکمه Export
   - انتخاب فرمت JSON

4. ذخیره فایل:
   - محتوا را در فایلی با نام cookies.json ذخیره کنید
   - در محل امنی که دیگران به آن دسترسی ندارند

5. بارگذاری در برنامه:
   - از دکمه 'مرور' برای انتخاب فایل استفاده کنید

نکات امنیتی مهم:
- هرگز کوکی‌ها را با دیگران به اشتراک نگذارید
- کوکی‌ها پس از چند روز منقضی می‌شوند
- از حساب تست برای توسعه استفاده کنید
"""
        self.text_widget.insert(END, help_text)
        self.text_widget.config(state="disabled")  # غیرفعال کردن ویرایش
    
    def open_tutorial(self):
        """نمایش آموزش تصویری"""
        tutorial_url = "https://www.youtube.com/watch?v=EXAMPLE"  # جایگزین کنید با لینک واقعی
        self.open_browser(tutorial_url)
    
    def open_browser(self, url):
        """باز کردن مرورگر با URL داده شده"""
        try:
            webbrowser.open_new_tab(url)
        except Exception as e:
            messagebox.showerror("خطا", f"نمیتوان مرورگر را باز کرد: {str(e)}")
  

class InstagramProfileViewer:
    def __init__(self, parent_frame):
        #self.root = root
        self.parent = parent_frame
        #self.root.title("Instagram Profile Viewer")
        #self.root.geometry("800x600")
        
        # Variables
        self.cookies_file = "cookies.json"
        self.output_dir = "output"
        self.profile_image = None
        
        # Configure root grid
        self.root.grid_rowconfigure(0, weight=1)
        self.root.grid_columnconfigure(0, weight=1)
        
        ttk.Button(root, text="راهنمای کوکی", 
                 command=self.show_cookie_help).grid(row=1, column=3, padx=5)
        # Create UI
        self.create_widgets()
    
    def show_cookie_help(self):
        """نمایش پنجره راهنمای کوکی"""
        if not hasattr(self, 'cookie_helper') or not self.cookie_helper.window.winfo_exists():
            self.cookie_helper = InstagramCookieHelper(self.root)
        else:
            try:
                self.cookie_helper.window.lift()
            except tk.TclError:
                # اگر پنجره از بین رفته باشد، یک نمونه جدید ایجاد کنید
                self.cookie_helper = InstagramCookieHelper(self.root)

    def create_widgets(self):
        # Main Frame - using grid
        main_frame = ttk.Frame(self.parent_frame, padding="10")
        main_frame.grid(row=0, column=0, sticky="nsew")
        
        # Configure main_frame grid
        main_frame.grid_rowconfigure(1, weight=1)
        main_frame.grid_columnconfigure(0, weight=1)
        
        # Input Frame - using grid
        input_frame = ttk.LabelFrame(main_frame, text="Profile Information", padding="10")
        input_frame.grid(row=0, column=0, sticky="ew", pady=5)
        
        # Input widgets using grid
        ttk.Label(input_frame, text="Username:").grid(row=0, column=0, sticky="w")
        self.username_entry = ttk.Entry(input_frame, width=30)
        self.username_entry.grid(row=0, column=1, padx=5)
        
        ttk.Label(input_frame, text="Cookies File:").grid(row=1, column=0, sticky="w")
        self.cookies_entry = ttk.Entry(input_frame, width=30)
        self.cookies_entry.insert(0, self.cookies_file)
        self.cookies_entry.grid(row=1, column=1, padx=5)
        ttk.Button(input_frame, text="Browse", command=self.browse_cookies).grid(row=1, column=2)
        
        ttk.Label(input_frame, text="Output Directory:").grid(row=2, column=0, sticky="w")
        self.output_entry = ttk.Entry(input_frame, width=30)
        self.output_entry.insert(0, self.output_dir)
        self.output_entry.grid(row=2, column=1, padx=5)
        ttk.Button(input_frame, text="Browse", command=self.browse_output).grid(row=2, column=2)
        
        # Help Button - using grid
        ttk.Button(input_frame, text="Cookie Help", command=self.show_cookie_help).grid(row=1, column=3, padx=5)
        
        # Button Frame - using grid
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=1, column=0, sticky="ew", pady=10)
        ttk.Button(button_frame, text="Download Profile", command=self.download_profile).grid(row=0, column=0, padx=5)
        
        # Results Frame - using grid
        results_frame = ttk.LabelFrame(main_frame, text="Profile Details", padding="10")
        results_frame.grid(row=2, column=0, sticky="nsew")
        
        # Configure results_frame grid
        results_frame.grid_rowconfigure(0, weight=1)
        results_frame.grid_columnconfigure(1, weight=1)
        
        # Image Frame - using grid
        self.image_frame = ttk.Frame(results_frame)
        self.image_frame.grid(row=0, column=0, sticky="nsew", padx=10)
        
        self.image_label = ttk.Label(self.image_frame, text="Profile image will appear here")
        self.image_label.grid(row=0, column=0)
        
        # Details Frame - using grid
        details_frame = ttk.Frame(results_frame)
        details_frame.grid(row=0, column=1, sticky="nsew")
        
        self.details_text = tk.Text(details_frame, height=15, wrap="word")
        self.details_text.grid(row=0, column=0, sticky="nsew")
        
        scrollbar = ttk.Scrollbar(details_frame, orient="vertical", command=self.details_text.yview)
        scrollbar.grid(row=0, column=1, sticky="ns")
        self.details_text.config(yscrollcommand=scrollbar.set)
        
        # Configure columns
        details_frame.grid_columnconfigure(0, weight=1)

    def browse_cookies(self):
        filename = filedialog.askopenfilename(title="Select Cookies File", filetypes=[("JSON files", "*.json")])
        if filename:
            self.cookies_entry.delete(0, tk.END)
            self.cookies_entry.insert(0, filename)
    
    def browse_output(self):
        dirname = filedialog.askdirectory(title="Select Output Directory")
        if dirname:
            self.output_entry.delete(0, tk.END)
            self.output_entry.insert(0, dirname)
    
    def load_cookies(self, cookie_file):
        """Load cookies from a JSON file."""
        try:
            if not os.path.exists(cookie_file):
                messagebox.showerror("Error", f"Cookies file not found at: {cookie_file}\nPlease select a valid cookies file.")
                return None
                
            with open(cookie_file, "r") as file:
                cookies_list = json.load(file)
            return {cookie["name"]: cookie["value"] for cookie in cookies_list}
        except json.JSONDecodeError:
            messagebox.showerror("Error", "Invalid cookies file format. Please provide a valid JSON file.")
            return None
        except Exception as e:
            messagebox.showerror("Error", f"Error loading cookies: {e}")
            return None
    
    def download_profile(self):
        username = self.username_entry.get().strip()
        if not username:
            messagebox.showerror("Error", "Please enter a username")
            return
        
        self.cookies_file = self.cookies_entry.get()
        self.output_dir = self.output_entry.get()
        
        # Check if cookies file exists
        if not os.path.exists(self.cookies_file):
            messagebox.showerror("Error", 
                "Cookies file not found!\n\n"
                "Please:\n"
                "1. Export cookies from your browser\n"
                "2. Save as cookies.json\n"
                "3. Select the file using the Browse button")
            return


        url = f"https://i.instagram.com/api/v1/users/web_profile_info/?username={username}"
        
        # Load cookies
        cookies = self.load_cookies(self.cookies_file)
        if not cookies:
            return
        
        headers = {
            "User-Agent": "Instagram **********.117 Android (30/3.0; 320dpi; 720x1280; Xiaomi; Redmi Note 8; ginkgo; ginkgo; en_US)",
            "Accept": "*/*",
            "Accept-Language": "en-US",
            "X-IG-App-ID": "936619743392459",
        }
        
        try:
            response = requests.get(url, headers=headers, cookies=cookies)
            
            if response.status_code != 200:
                messagebox.showerror("Error", f"Error fetching profile for {username}: {response.text}")
                return
            
            data = response.json()
            user_data = data.get("data", {}).get("user", {})
            
            if not user_data:
                messagebox.showerror("Error", f"Could not find user data for {username}")
                return
            
            # Extract all relevant data including target_id
            target_id = user_data.get("id", "N/A")
            profile_pic_url = user_data.get("profile_pic_url_hd")
            full_name = user_data.get("full_name", "N/A")
            bio = user_data.get("biography", "N/A")
            followers = user_data.get("edge_followed_by", {}).get("count", 0)
            following = user_data.get("edge_follow", {}).get("count", 0)
            
            # Create output directory
            os.makedirs(self.output_dir, exist_ok=True)
            
            # Save user details
            user_details = {
                "username": username,
                "target_id": target_id,
                "full_name": full_name,
                "bio": bio,
                "followers": followers,
                "following": following,
            }
            
            details_file = os.path.join(self.output_dir, f"{username}_details.json")
            with open(details_file, "w") as file:
                json.dump(user_details, file, indent=4)
            
            # Display details in the text box
            self.details_text.delete(1.0, tk.END)
            self.details_text.insert(tk.END, f"Username: {username}\n")
            self.details_text.insert(tk.END, f"Target ID: {target_id}\n")
            self.details_text.insert(tk.END, f"Full Name: {full_name}\n")
            self.details_text.insert(tk.END, f"Bio: {bio}\n")
            self.details_text.insert(tk.END, f"Followers: {followers}\n")
            self.details_text.insert(tk.END, f"Following: {following}\n")
            
            # Download and display profile picture
            if profile_pic_url:
                try:
                    profile_pic_response = requests.get(profile_pic_url, headers=headers, cookies=cookies)
                    if profile_pic_response.status_code == 200:
                        profile_pic_path = os.path.join(self.output_dir, f"{username}_profile_pic.jpg")
                        with open(profile_pic_path, "wb") as file:
                            file.write(profile_pic_response.content)
                        
                        # Display image
                        image_data = profile_pic_response.content
                        image = Image.open(io.BytesIO(image_data))
                        image.thumbnail((300, 300))
                        self.profile_image = ImageTk.PhotoImage(image)
                        self.image_label.config(image=self.profile_image)
                        self.image_label.image = self.profile_image
                    else:
                        self.image_label.config(text="Failed to download profile picture")
                except Exception as e:
                    self.image_label.config(text=f"Error loading image: {str(e)}")
            else:
                self.image_label.config(text="No profile picture available")
            
            messagebox.showinfo("Success", f"Profile data saved to {self.output_dir}")
            
        except Exception as e:
            messagebox.showerror("Error", f"An unexpected error occurred: {e}")

'''
if __name__ == "__main__":
    root = tk.Tk()
    app = InstagramProfileViewer(root)
    root.mainloop()
'''