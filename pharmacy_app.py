import sys
from PyQt5.QtWidgets import (QA<PERSON><PERSON>, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                             QLabel, QLineEdit, QPushButton, QTableWidget, QTableWidgetItem,
                             QMessageBox, QStackedWidget, QComboBox, QDateEdit, QHeaderView)
from PyQt5.QtCore import Qt, QDate
import sqlite3
from datetime import datetime
from PyQt5.QtWidgets import QFormLayout
import jdatetime
from PyQt5.QtCore import QDate


class LoginWindow(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent = parent
        self.setup_ui()
        
    def setup_ui(self):
        self.setWindowTitle("ورود به سیستم")
        self.setFixedSize(400, 300)
        
        layout = QVBoxLayout()
        
        # عنوان برنامه
        title = QLabel("سیستم مدیریت داروخانه")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("font-size: 20px; font-weight: bold; color: #2c3e50; margin-bottom: 30px;")
        
        # فیلدهای ورود
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("نام کاربری")
        self.username_input.setStyleSheet("padding: 10px; font-size: 16px;")
        
        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText("رمز عبور")
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setStyleSheet("padding: 10px; font-size: 16px;")
        
        # دکمه ورود
        login_btn = QPushButton("ورود")
        login_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 12px;
                font-size: 16px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        login_btn.clicked.connect(self.login)
        
        # افزودن ویجت‌ها به لایه
        layout.addWidget(title)
        layout.addWidget(self.username_input)
        layout.addWidget(self.password_input)
        layout.addWidget(login_btn)
        layout.addStretch()
        
        self.setLayout(layout)
    
    def login(self):
        username = self.username_input.text()
        password = self.password_input.text()
        
        # بررسی ساده برای مثال (در عمل باید از دیتابیس استفاده شود)
        if username == "a" and password == "1":
            self.parent.change_page("main")
        else:
            QMessageBox.warning(self, "خطا", "نام کاربری یا رمز عبور اشتباه است")

class MainWindow(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent = parent
        self.db = DatabaseManager()
        self.setup_ui()
        self.load_medicines()
        self.check_low_stock()
        
    def setup_ui(self):
        self.setWindowTitle("مدیریت داروخانه")
        self.setMinimumSize(1000, 700)
        
        main_layout = QVBoxLayout()
        
        # نوار ابزار
        toolbar = QHBoxLayout()
        
        self.add_btn = QPushButton("افزودن دارو")
        self.add_btn.setStyleSheet("background-color: #2ecc71; color: white; padding: 8px;")
        self.add_btn.clicked.connect(self.show_add_dialog)
        
        self.edit_btn = QPushButton("ویرایش دارو")
        self.edit_btn.setStyleSheet("background-color: #f39c12; color: white; padding: 8px;")
        self.edit_btn.clicked.connect(self.edit_medicine)
        
        self.delete_btn = QPushButton("حذف دارو")
        self.delete_btn.setStyleSheet("background-color: #e74c3c; color: white; padding: 8px;")
        self.delete_btn.clicked.connect(self.delete_medicine)
        
        self.report_btn = QPushButton("گزارش‌گیری")
        self.report_btn.setStyleSheet("background-color: #9b59b6; color: white; padding: 8px;")
        self.report_btn.clicked.connect(self.generate_report)
        
        self.logout_btn = QPushButton("خروج")
        self.logout_btn.setStyleSheet("background-color: #34495e; color: white; padding: 8px;")
        self.logout_btn.clicked.connect(self.logout)
        
        toolbar.addWidget(self.add_btn)
        toolbar.addWidget(self.edit_btn)
        toolbar.addWidget(self.delete_btn)
        toolbar.addWidget(self.report_btn)
        toolbar.addStretch()
        toolbar.addWidget(self.logout_btn)
        
        # جدول داروها
        self.medicines_table = QTableWidget()
        self.medicines_table.setColumnCount(6)
        self.medicines_table.setHorizontalHeaderLabels(["شناسه", "نام دارو", "تعداد", "قیمت", "تاریخ انقضا", "توضیحات"])
        self.medicines_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.medicines_table.setSelectionBehavior(QTableWidget.SelectRows)
        
        # جستجو
        search_layout = QHBoxLayout()
        search_label = QLabel("جستجو:")
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("نام دارو یا شناسه...")
        search_btn = QPushButton("جستجو")
        search_btn.setStyleSheet("background-color: #3498db; color: white; padding: 5px;")
        search_btn.clicked.connect(self.search_medicines)
        
        search_layout.addWidget(search_label)
        search_layout.addWidget(self.search_input)
        search_layout.addWidget(search_btn)
        
        main_layout.addLayout(toolbar)
        main_layout.addLayout(search_layout)
        main_layout.addWidget(self.medicines_table)
        
        self.setLayout(main_layout)
    
    def load_medicines(self):
        medicines = self.db.get_medicines()
        self.medicines_table.setRowCount(len(medicines))
        
        for row, medicine in enumerate(medicines):
            for col, data in enumerate(medicine):
                item = QTableWidgetItem(str(data))
                self.medicines_table.setItem(row, col, item)
    
    def check_low_stock(self):
        low_stock = self.db.get_low_stock_medicines()
        if low_stock:
            warning = "هشدار: داروهای زیر در حال اتمام هستند:\n"
            for med in low_stock:
                warning += f"- {med[1]} (موجودی: {med[2]})\n"
            
            QMessageBox.warning(self, "هشدار کمبود دارو", warning)
    
    def show_add_dialog(self):
        dialog = MedicineDialog(self)
        if dialog.exec_() == MedicineDialog.Accepted:
            medicine_data = dialog.get_data()
            self.db.add_medicine(*medicine_data)
            self.load_medicines()
            QMessageBox.information(self, "موفق", "دارو با موفقیت افزوده شد")
    
    def edit_medicine(self):
        selected = self.medicines_table.selectedItems()
        if not selected:
            QMessageBox.warning(self, "خطا", "لطفاً یک دارو را انتخاب کنید")
            return
            
        row = selected[0].row()
        medicine_id = self.medicines_table.item(row, 0).text()
        medicine = self.db.get_medicine_by_id(medicine_id)
        
        dialog = MedicineDialog(self)
        dialog.set_data(medicine)
        if dialog.exec_() == MedicineDialog.Accepted:
            medicine_data = dialog.get_data()
            self.db.update_medicine(medicine_id, *medicine_data)
            self.load_medicines()
            QMessageBox.information(self, "موفق", "دارو با موفقیت ویرایش شد")
    
    def delete_medicine(self):
        selected = self.medicines_table.selectedItems()
        if not selected:
            QMessageBox.warning(self, "خطا", "لطفاً یک دارو را انتخاب کنید")
            return
            
        row = selected[0].row()
        medicine_id = self.medicines_table.item(row, 0).text()
        medicine_name = self.medicines_table.item(row, 1).text()
        
        reply = QMessageBox.question(
            self, 
            "حذف دارو", 
            f"آیا از حذف داروی '{medicine_name}' مطمئن هستید؟",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.db.delete_medicine(medicine_id)
            self.load_medicines()
            QMessageBox.information(self, "موفق", "دارو با موفقیت حذف شد")
    
    def search_medicines(self):
        query = self.search_input.text()
        medicines = self.db.search_medicines(query)
        self.medicines_table.setRowCount(len(medicines))
        
        for row, medicine in enumerate(medicines):
            for col, data in enumerate(medicine):
                item = QTableWidgetItem(str(data))
                self.medicines_table.setItem(row, col, item)
    
    def generate_report(self):
        report = self.db.generate_report()
        
        dialog = QMessageBox(self)
        dialog.setWindowTitle("گزارش داروخانه")
        dialog.setText(f"""
            گزارش کلی داروخانه:
            --------------------------
            تعداد کل داروها: {report['total_medicines']}
            داروهای در حال اتمام: {report['low_stock_count']}
            داروهای منقضی شده: {report['expired_count']}
            مجموع ارزش داروها: {report['total_value']:,} تومان
        """)
        dialog.exec_()
    
    def logout(self):
        self.parent.change_page("login")

class MedicineDialog(QMessageBox):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("مدیریت دارو")
        
        content_widget = QWidget()
        form_layout = QFormLayout(content_widget)
        
        self.name_input = QLineEdit()
        self.quantity_input = QLineEdit()
        self.price_input = QLineEdit()
        self.expiry_input = QDateEdit()
        
        # تنظیم تاریخ شمسی فعلی
        today = jdatetime.date.today()
        
        qdate = QDate(today.year, today.month, today.day)
        
        self.expiry_input.setDate(qdate)
        self.expiry_input.setDisplayFormat("yyyy/MM/dd")
        
        self.description_input = QLineEdit()
        
        form_layout.addRow("نام دارو:", self.name_input)
        form_layout.addRow("تعداد:", self.quantity_input)
        form_layout.addRow("قیمت (تومان):", self.price_input)
        form_layout.addRow("تاریخ انقضا (شمسی):", self.expiry_input)
        form_layout.addRow("توضیحات:", self.description_input)
        
        self.setStandardButtons(QMessageBox.Ok | QMessageBox.Cancel)
        self.layout().addWidget(content_widget)
    
    def get_data(self):
        # تبدیل تاریخ به شمسی برای ذخیره در دیتابیس
        gregorian_date = self.expiry_input.date().toPyDate()
        jalali_date = jdatetime.date.fromgregorian(date=gregorian_date)
        expiry_date_str = f"{jalali_date.year}-{jalali_date.month}-{jalali_date.day}"
        
        return (
            self.name_input.text(),
            int(self.quantity_input.text()),
            float(self.price_input.text()),
            expiry_date_str,
            self.description_input.text()
        )
    
    def set_data(self, medicine):
        self.name_input.setText(medicine[1])
        self.quantity_input.setText(str(medicine[2]))
        self.price_input.setText(str(medicine[3]))
        
        # نمایش تاریخ شمسی هنگام ویرایش
        year, month, day = map(int, medicine[4].split('-'))
        jalali_date = jdatetime.date(year, month, day)
        gregorian_date = jalali_date.togregorian()
        qdate = QDate(gregorian_date.year, gregorian_date.month, gregorian_date.day)
        self.expiry_input.setDate(qdate)
        
        self.description_input.setText(medicine[5])



class DatabaseManager:
    def __init__(self):
        self.conn = sqlite3.connect("pharmacy.db")
        self.create_tables()
    
    def create_tables(self):
        cursor = self.conn.cursor()
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS medicines (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                quantity INTEGER NOT NULL,
                price REAL NOT NULL,
                expiry_date TEXT NOT NULL,
                description TEXT
            )
        """)
        self.conn.commit()
    
    def add_medicine(self, name, quantity, price, expiry_date, description):
        cursor = self.conn.cursor()
        cursor.execute("""
            INSERT INTO medicines (name, quantity, price, expiry_date, description)
            VALUES (?, ?, ?, ?, ?)
        """, (name, quantity, price, expiry_date, description))
        self.conn.commit()
    
    def get_medicines(self):
        cursor = self.conn.cursor()
        cursor.execute("SELECT * FROM medicines ORDER BY name")
        return cursor.fetchall()
    
    def get_medicine_by_id(self, medicine_id):
        cursor = self.conn.cursor()
        cursor.execute("SELECT * FROM medicines WHERE id = ?", (medicine_id,))
        return cursor.fetchone()
    
    def update_medicine(self, medicine_id, name, quantity, price, expiry_date, description):
        cursor = self.conn.cursor()
        cursor.execute("""
            UPDATE medicines 
            SET name = ?, quantity = ?, price = ?, expiry_date = ?, description = ?
            WHERE id = ?
        """, (name, quantity, price, expiry_date, description, medicine_id))
        self.conn.commit()
    
    def delete_medicine(self, medicine_id):
        cursor = self.conn.cursor()
        cursor.execute("DELETE FROM medicines WHERE id = ?", (medicine_id,))
        self.conn.commit()
    
    def search_medicines(self, query):
        cursor = self.conn.cursor()
        cursor.execute("""
            SELECT * FROM medicines 
            WHERE name LIKE ? OR id LIKE ?
            ORDER BY name
        """, (f"%{query}%", f"%{query}%"))
        return cursor.fetchall()
    
    def get_low_stock_medicines(self, threshold=10):
        cursor = self.conn.cursor()
        cursor.execute("""
            SELECT * FROM medicines 
            WHERE quantity <= ?
            ORDER BY quantity
        """, (threshold,))
        return cursor.fetchall()
    
    def generate_report(self):
        cursor = self.conn.cursor()
        
        # تعداد کل داروها
        cursor.execute("SELECT COUNT(*) FROM medicines")
        total_medicines = cursor.fetchone()[0]
        
        # داروهای در حال اتمام
        cursor.execute("SELECT COUNT(*) FROM medicines WHERE quantity <= 10")
        low_stock_count = cursor.fetchone()[0]
        
        # داروهای منقضی شده
        today = datetime.now().strftime("%Y-%m-%d")
        cursor.execute("SELECT COUNT(*) FROM medicines WHERE expiry_date < ?", (today,))
        expired_count = cursor.fetchone()[0]
        
        # مجموع ارزش داروها
        cursor.execute("SELECT SUM(quantity * price) FROM medicines")
        total_value = cursor.fetchone()[0] or 0
        
        return {
            "total_medicines": total_medicines,
            "low_stock_count": low_stock_count,
            "expired_count": expired_count,
            "total_value": total_value
        }

class PharmacyApp(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("سیستم مدیریت داروخانه")
        self.setMinimumSize(800, 600)
        
        self.central_widget = QStackedWidget()
        self.setCentralWidget(self.central_widget)
        
        # ایجاد صفحات
        self.login_page = LoginWindow(self)
        self.main_page = MainWindow(self)
        
        # افزودن صفحات به استک
        self.central_widget.addWidget(self.login_page)
        self.central_widget.addWidget(self.main_page)
        
        # نمایش صفحه ورود به عنوان پیش‌فرض
        self.central_widget.setCurrentWidget(self.login_page)
    
    def change_page(self, page_name):
        if page_name == "login":
            self.central_widget.setCurrentWidget(self.login_page)
        elif page_name == "main":
            self.central_widget.setCurrentWidget(self.main_page)
            self.main_page.load_medicines()
            self.main_page.check_low_stock()

if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    # تنظیم فونت برای نمایش صحیح فارسی
    font = app.font()
    font.setFamily("Arial")
    app.setFont(font)
    
    window = PharmacyApp()
    window.show()
    
    sys.exit(app.exec_())