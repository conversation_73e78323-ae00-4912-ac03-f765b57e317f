import tkinter as tk
from tkinter import ttk, messagebox
import sqlite3
import jdatetime
from datetime import datetime

class SalesManager:
    def __init__(self, db):
        self.db = db
    
    def add_sale(self, medicine_id, quantity, price, discount=0, customer_info=None):
        """ثبت فروش جدید با کاهش خودکار موجودی"""
        try:
            # محاسبه مبلغ کل با احتساب تخفیف
            total = (price * quantity) * (1 - discount)
            
            # ذخیره اطلاعات فروش
            cursor = self.db.conn.cursor()
            cursor.execute("""
                INSERT INTO sales 
                (medicine_id, quantity, sale_date, price, discount, total_price, customer_info)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (medicine_id, quantity, datetime.now().strftime("%Y-%m-%d %H:%M:%S"), 
                 price, discount, total, customer_info))
            
            # کاهش موجودی انبار
            cursor.execute("""
                UPDATE medicines 
                SET quantity = quantity - ? 
                WHERE id = ?
            """, (quantity, medicine_id))
            
            self.db.conn.commit()
            return True
        except Exception as e:
            print(f"Error in add_sale: {e}")
            return False
    
    def get_sales_report(self, start_date=None, end_date=None):
        """گزارش فروش با امکان فیلتر زمانی"""
        query = """
            SELECT s.id, m.name, s.quantity, s.price, s.discount, 
                   s.total_price, s.sale_date, s.customer_info
            FROM sales s
            JOIN medicines m ON s.medicine_id = m.id
        """
        
        params = []
        if start_date and end_date:
            query += " WHERE s.sale_date BETWEEN ? AND ?"
            params.extend([start_date, end_date])
        
        query += " ORDER BY s.sale_date DESC"
        
        cursor = self.db.conn.cursor()
        cursor.execute(query, params)
        return cursor.fetchall()

class PharmacyApp:
    def __init__(self, root):
        self.root = root
        self.root.title("سیستم مدیریت داروخانه")
        self.root.geometry("1000x700")
        
        # ایجاد دیتابیس
        self.db = DatabaseManager()
        
        # متغیرهای ورودی
        self.login_var = tk.StringVar()
        self.password_var = tk.StringVar()
        
        # صفحات برنامه
        self.create_login_page()
        
    def create_login_page(self):
        """صفحه ورود به سیستم"""
        self.clear_window()
        
        frame = ttk.Frame(self.root, padding="30")
        frame.pack(expand=True)
        
        ttk.Label(frame, text="سیستم مدیریت داروخانه", font=('Tahoma', 16)).grid(row=0, column=0, columnspan=2, pady=20)
        
        ttk.Label(frame, text="نام کاربری:").grid(row=1, column=0, sticky='e', pady=5)
        ttk.Entry(frame, textvariable=self.login_var).grid(row=1, column=1, pady=5)
        
        ttk.Label(frame, text="رمز عبور:").grid(row=2, column=0, sticky='e', pady=5)
        ttk.Entry(frame, textvariable=self.password_var, show="*").grid(row=2, column=1, pady=5)
        
        ttk.Button(frame, text="ورود", command=self.login).grid(row=3, column=0, columnspan=2, pady=20)
        
        # اطلاعات پیش‌فرض برای تست
        self.login_var.set("a")
        self.password_var.set("1")
    
    '''
    def create_main_page(self):
        self.clear_window()
        
        # ایجاد Notebook برای تب‌ها
        notebook = ttk.Notebook(self.root)
        notebook.pack(fill='both', expand=True)
        
        # تب مدیریت داروها
        med_frame = ttk.Frame(notebook)
        notebook.add(med_frame, text="مدیریت داروها")
        
        # تب تاریخچه فروش
        sales_frame = ttk.Frame(notebook)
        notebook.add(sales_frame, text="تاریخچه فروش")

        # --- بخش مدیریت داروها ---
        # نوار ابزار
        toolbar = ttk.Frame(med_frame)
        toolbar.pack(fill='x', padx=5, pady=5)
        
        ttk.Button(toolbar, text="افزودن دارو", command=self.show_add_dialog).pack(side='left', padx=5)
        ttk.Button(toolbar, text="ویرایش دارو", command=self.edit_medicine).pack(side='left', padx=5)
        ttk.Button(toolbar, text="حذف دارو", command=self.delete_medicine).pack(side='left', padx=5)
        ttk.Button(toolbar, text="ثبت فروش", command=self.show_sale_dialog).pack(side='left', padx=5)
        ttk.Button(toolbar, text="گزارش‌گیری", command=self.generate_report).pack(side='left', padx=5)
        ttk.Button(toolbar, text="مدیریت گروه‌ها", command=self.manage_categories).pack(side='left', padx=5)
        ttk.Button(toolbar, text="خروج", command=self.logout).pack(side='right', padx=5)
        
        # جستجو
        search_frame = ttk.Frame(med_frame)
        search_frame.pack(fill='x', padx=5, pady=5)
        
        ttk.Label(search_frame, text="جستجو دارو").pack(side='right')
        self.search_var = tk.StringVar()
        self.search_entry = ttk.Entry(search_frame, textvariable=self.search_var)
        self.search_entry.pack(side='left', padx=5, expand=True, fill='x')
        self.search_var.trace_add("write", self.search_medicines)
        
        # Treeview داروها
        self.tree = ttk.Treeview(med_frame, columns=('id', 'name', 'quantity', 'price', 'expiry', 'category', 'desc'), show='headings')
        self.tree.heading('id', text='شناسه')
        self.tree.heading('name', text='نام دارو')
        self.tree.heading('quantity', text='تعداد')
        self.tree.heading('price', text='قیمت')
        self.tree.heading('expiry', text='تاریخ انقضا')
        self.tree.heading('category', text='گروه')
        self.tree.heading('desc', text='توضیحات')

        self.tree.column('id', width=20)
        self.tree.column('name', width=150)
        self.tree.column('quantity', width=80)
        self.tree.column('price', width=100)
        self.tree.column('expiry', width=100)
        self.tree.column('category', width=100)
        self.tree.column('desc', width=200)
        
        scrollbar = ttk.Scrollbar(med_frame, orient='vertical', command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        self.tree.pack(fill='both', expand=True, padx=5, pady=5)
        scrollbar.pack(side='right', fill='y')
        
        # --- بخش تاریخچه فروش ---
        sales_tree = ttk.Treeview(sales_frame, columns=('id', 'medicine', 'quantity', 'total', 'date', 'customer'), show='headings')
        sales_tree.heading('id', text='شناسه')
        sales_tree.heading('medicine', text='نام دارو')
        sales_tree.heading('quantity', text='تعداد')
        sales_tree.heading('total', text='مبلغ کل')
        sales_tree.heading('date', text='تاریخ فروش')
        sales_tree.heading('customer', text='مشتری')
        
        sales_tree.column('id', width=30)
        sales_tree.column('quantity', width=30)
        sales_scroll = ttk.Scrollbar(sales_frame, orient='vertical', command=sales_tree.yview)
        sales_tree.configure(yscrollcommand=sales_scroll.set)
        
        sales_tree.pack(fill='both', expand=True, padx=5, pady=5)
        sales_scroll.pack(side='right', fill='y')
        
        # بارگذاری داده‌ها
        self.load_medicines()
        self.check_low_stock()
        self.load_sales_history(sales_tree)
    '''

    def create_main_page(self):
        self.clear_window()
        
        # ایجاد Notebook برای تب‌ها
        notebook = ttk.Notebook(self.root)
        notebook.pack(fill='both', expand=True)
        
        # --- تب مدیریت داروها ---
        med_frame = ttk.Frame(notebook)
        notebook.add(med_frame, text="مدیریت داروها")
        
        # نوار ابزار
        toolbar = ttk.Frame(med_frame)
        toolbar.pack(fill='x', padx=5, pady=5)
        
        ttk.Button(toolbar, text="افزودن دارو", command=self.show_add_dialog).pack(side='left', padx=5)
        ttk.Button(toolbar, text="ویرایش دارو", command=self.edit_medicine).pack(side='left', padx=5)
        ttk.Button(toolbar, text="حذف دارو", command=self.delete_medicine).pack(side='left', padx=5)
        ttk.Button(toolbar, text="ثبت فروش", command=self.show_sale_dialog).pack(side='left', padx=5)
        ttk.Button(toolbar, text="گزارش‌گیری", command=self.generate_report).pack(side='left', padx=5)
        ttk.Button(toolbar, text="مدیریت گروه‌ها", command=self.manage_categories).pack(side='left', padx=5)
        ttk.Button(toolbar, text="خروج", command=self.logout).pack(side='right', padx=5)
        
        # جستجو
        search_frame = ttk.Frame(med_frame)
        search_frame.pack(fill='x', padx=5, pady=5)
        
        ttk.Label(search_frame, text="جستجو دارو").pack(side='right')
        self.search_var = tk.StringVar()
        self.search_entry = ttk.Entry(search_frame, textvariable=self.search_var)
        self.search_entry.pack(side='left', padx=5, expand=True, fill='x')
        self.search_var.trace_add("write", self.search_medicines)
        
        # Treeview داروها
        self.tree = ttk.Treeview(med_frame, columns=('id', 'name', 'quantity', 'price', 'expiry', 'category', 'desc'), show='headings')
        self.tree.heading('id', text='شناسه')
        self.tree.heading('name', text='نام دارو')
        self.tree.heading('quantity', text='تعداد')
        self.tree.heading('price', text='قیمت')
        self.tree.heading('expiry', text='تاریخ انقضا')
        self.tree.heading('category', text='گروه')
        self.tree.heading('desc', text='توضیحات')

        self.tree.column('id', width=50)
        self.tree.column('name', width=150)
        self.tree.column('quantity', width=80)
        self.tree.column('price', width=100)
        self.tree.column('expiry', width=100)
        self.tree.column('category', width=100)
        self.tree.column('desc', width=200)
        
        scrollbar = ttk.Scrollbar(med_frame, orient='vertical', command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        self.tree.pack(fill='both', expand=True, padx=5, pady=5)
        scrollbar.pack(side='right', fill='y')
        
        # --- تب تاریخچه فروش ---
        sales_frame = ttk.Frame(notebook)
        notebook.add(sales_frame, text="تاریخچه فروش")

        # نوار ابزار مدیریت فاکتورها
        sales_toolbar = ttk.Frame(sales_frame)
        sales_toolbar.pack(fill='x', padx=5, pady=5)

        # دکمه‌های مدیریت
        ttk.Button(sales_toolbar, text="مشاهده جزئیات", 
                command=self.show_invoice_details).pack(side='right', padx=2)
        ttk.Button(sales_toolbar, text="حذف فاکتور", 
                command=self.delete_invoice, style='Danger.TButton').pack(side='right', padx=2)
        ttk.Button(sales_toolbar, text="بازفروش", 
                command=self.recreate_invoice, style='Success.TButton').pack(side='right', padx=2)
        ttk.Button(sales_toolbar, text="ویرایش فاکتور", 
                command=self.edit_invoice).pack(side='right', padx=2)

        # استایل‌های دکمه‌ها
        style = ttk.Style()
        style.configure('Danger.TButton', foreground='red')
        style.configure('Success.TButton', foreground='green')


        # Treeview تاریخچه فروش
        self.sales_tree = ttk.Treeview(sales_frame, 
                                    columns=('id', 'customer', 'date', 'total', 'items_count'), 
                                    show='headings')
        
        self.sales_tree.heading('id', text='شماره فاکتور')
        self.sales_tree.heading('customer', text='مشتری')
        self.sales_tree.heading('date', text='تاریخ فروش')
        self.sales_tree.heading('total', text='مبلغ کل')
        self.sales_tree.heading('items_count', text='تعداد اقلام')
        
        # تنظیم عرض ستون‌ها
        self.sales_tree.column('id', width=40, anchor='center')
        self.sales_tree.column('customer', width=150, anchor='center')
        self.sales_tree.column('date', width=120, anchor='center')
        self.sales_tree.column('total', width=120, anchor='center')
        self.sales_tree.column('items_count', width=80, anchor='center')
        
        # اسکرول بار
        sales_scroll = ttk.Scrollbar(sales_frame, orient='vertical', command=self.sales_tree.yview)
        self.sales_tree.configure(yscrollcommand=sales_scroll.set)
        
        self.sales_tree.pack(fill='both', expand=True, padx=5, pady=5)
        sales_scroll.pack(side='right', fill='y')
        
        # رویداد دوبار کلیک برای مشاهده جزئیات فاکتور
        self.sales_tree.bind('<Double-1>', self.show_invoice_details)
        
        # بارگذاری داده‌ها
        self.load_medicines()
        self.check_low_stock()
        self.load_sales_history()
     
    
    def show_sale_dialog(self):
        self.sale_dialog = tk.Toplevel(self.root)
        self.sale_dialog.transient(self.root) 
        self.sale_dialog.title("ثبت فاکتور فروش جدید")
        self.sale_dialog.geometry("900x700")
        self.sale_dialog.protocol("WM_DELETE_WINDOW", self.on_sale_dialog_close)

        # متغیرها
        self.total_var = tk.StringVar(value="0 تومان")
        self.final_total_var = tk.StringVar(value="0 تومان")
        self.customer_name_var = tk.StringVar()
        self.customer_phone_var = tk.StringVar()
        self.discount_var = tk.StringVar(value="0")
        self.cart_items = []

        # بخش اطلاعات مشتری
        customer_frame = ttk.LabelFrame(self.sale_dialog, text="اطلاعات مشتری", padding=10)
        customer_frame.pack(fill='x', padx=10, pady=5)
        customer_frame.grid_columnconfigure(1, weight=1)
        customer_frame.grid_columnconfigure(3, weight=1)

        ttk.Label(customer_frame, text="نام مشتری:").grid(row=0, column=0, padx=5, pady=5, sticky='e')
        ttk.Entry(customer_frame, textvariable=self.customer_name_var).grid(row=0, column=1, padx=5, pady=5)

        ttk.Label(customer_frame, text="تلفن:").grid(row=0, column=2, padx=5, pady=5, sticky='e')
        ttk.Entry(customer_frame, textvariable=self.customer_phone_var).grid(row=0, column=3, padx=5, pady=5)

        ttk.Label(customer_frame, text="تخفیف کل (%):").grid(row=0, column=4, padx=5, pady=5, sticky='e')
        ttk.Entry(customer_frame, textvariable=self.discount_var, width=5).grid(row=0, column=5, padx=5, pady=5)

        # بخش انتخاب دارو
        add_item_frame = ttk.LabelFrame(self.sale_dialog, text="افزودن دارو به فاکتور", padding=10)
        add_item_frame.pack(fill='x', padx=10, pady=5)

        self.medicine_var = tk.StringVar()
        self.quantity_var = tk.StringVar(value="1")

        ttk.Label(add_item_frame, text="دارو:").grid(row=0, column=0, padx=5, pady=5)
        medicine_combo = ttk.Combobox(add_item_frame, textvariable=self.medicine_var)
        medicine_combo['values'] = [med[1] for med in self.db.get_medicines()]
        medicine_combo.grid(row=0, column=1, padx=5, pady=5)
        #medicine_combo['values'] = [med[1] for med in self.db.get_medicines()]
        #medicine_combo.bind('<KeyRelease>', self.filter_medicines)
        


        ttk.Label(add_item_frame, text="تعداد:").grid(row=0, column=2, padx=5, pady=5)
        ttk.Entry(add_item_frame, textvariable=self.quantity_var, width=5).grid(row=0, column=3, padx=5, pady=5)

        ttk.Button(add_item_frame, text="افزودن به فاکتور", command=self.add_to_cart).grid(row=0, column=4, padx=5, pady=5)

        # سبد خرید با تنظیمات بهتر
        cart_frame = ttk.LabelFrame(self.sale_dialog, text="سبد خرید (برای ویرایش/حذف آیتم را انتخاب کنید)", padding=10)
        cart_frame.pack(fill='both', expand=True, padx=10, pady=5)

        columns = ('medicine', 'quantity', 'unit_price', 'total_price')
        self.cart_tree = ttk.Treeview(cart_frame, columns=columns, show='headings', selectmode='browse')
        
        # تنظیم عرض و تراز ستون‌ها
        col_widths = [200, 80, 120, 150]
        for col, width in zip(columns, col_widths):
            self.cart_tree.column(col, width=width, anchor='center')
            self.cart_tree.heading(col, text=col.replace('_', ' ').title())

        scrollbar = ttk.Scrollbar(cart_frame, orient='vertical', command=self.cart_tree.yview)
        self.cart_tree.configure(yscrollcommand=scrollbar.set)
        self.cart_tree.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')

        self.cart_tree.heading('medicine', text='نام دارو')
        self.cart_tree.heading('quantity', text='تعداد')
        self.cart_tree.heading('unit_price', text='قیمت واحد')
        self.cart_tree.heading('total_price', text='قیمت کل')


        # فریم دکمه‌های مدیریت آیتم‌ها
        item_buttons_frame = ttk.Frame(add_item_frame)
        item_buttons_frame.grid(row=1, column=0, columnspan=5, pady=5)
        
       
        # تغییر استایل دکمه‌ها
        style = ttk.Style()
        style.configure('Edit.TButton', foreground='blue')
        style.configure('Delete.TButton', foreground='red')
        
        edit_btn = ttk.Button(item_buttons_frame, text="ویرایش آیتم", 
                            command=self.edit_cart_item, style='Edit.TButton')
        delete_btn = ttk.Button(item_buttons_frame, text="حذف آیتم", 
                            command=self.remove_cart_item, style='Delete.TButton')
        
        edit_btn.pack(side='left', padx=5)
        delete_btn.pack(side='left', padx=5)
        
        # غیرفعال کردن اولیه دکمه‌ها
        edit_btn['state'] = 'disabled'
        delete_btn['state'] = 'disabled'
        
        # فعال/غیرفعال کردن دکمه‌ها بر اساس انتخاب
        def toggle_buttons(event):
            has_selection = bool(self.cart_tree.selection())
            edit_btn['state'] = 'normal' if has_selection else 'disabled'
            delete_btn['state'] = 'normal' if has_selection else 'disabled'
        
        self.cart_tree.bind('<<TreeviewSelect>>', toggle_buttons)

        summary_frame = ttk.LabelFrame(self.sale_dialog, text="جمع‌بندی فاکتور", padding=10)
        summary_frame.pack(fill='x', padx=10, pady=5)
        
        ttk.Label(summary_frame, text="جمع کل:").grid(row=0, column=0, padx=5, pady=5, sticky='e')
        ttk.Label(summary_frame, textvariable=self.total_var, font=('Tahoma', 10, 'bold')).grid(row=0, column=1, padx=5, pady=5, sticky='w')

        ttk.Label(summary_frame, text="تخفیف (%):").grid(row=1, column=0, padx=5, pady=5, sticky='e')
        discount_entry = ttk.Entry(summary_frame, textvariable=self.discount_var, width=5)
        discount_entry.grid(row=1, column=1, padx=5, pady=5, sticky='w')
        discount_entry.bind('<KeyRelease>', self.update_invoice_totals)

        ttk.Label(summary_frame, text="مبلغ قابل پرداخت:").grid(row=2, column=0, padx=5, pady=5, sticky='e')
        ttk.Label(summary_frame, textvariable=self.final_total_var, font=('Tahoma', 10, 'bold')).grid(row=2, column=1, padx=5, pady=5, sticky='w')

        scrollbar = ttk.Scrollbar(cart_frame, orient='vertical', command=self.cart_tree.yview)
        self.cart_tree.configure(yscrollcommand=scrollbar.set)

        self.cart_tree.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')

        # دکمه ثبت نهایی با استایل متمایز
        ttk.Button(self.sale_dialog, 
                text="ثبت فاکتور نهایی", 
                command=lambda: self.finalize_invoice(self.sale_dialog),
                style='Accent.TButton').pack(pady=10)

        # تنظیم استایل‌ها
        style = ttk.Style()
        style.configure('Accent.TButton', font=('Tahoma', 10, 'bold'), foreground='green')


        # دکمه ثبت نهایی
        #ttk.Button(dialog, text="ثبت فاکتور", command=lambda: self.finalize_invoice(dialog)).pack(pady=10)

    def on_sale_dialog_close(self):
        """مدیریت بسته شدن پنجره فروش"""
        self.cart_items.clear()
        if hasattr(self, 'sale_dialog'):
            self.sale_dialog.destroy()

    def edit_cart_item(self):
        """ویرایش آیتم انتخاب شده در سبد خرید"""
        selected = self.cart_tree.selection()
        if not selected:
            messagebox.showwarning("خطا", "لطفاً یک آیتم را انتخاب کنید")
            return
        
        item = self.cart_tree.item(selected[0])
        values = item['values']
        medicine_name = values[0]
        
        # پیدا کردن آیتم در لیست cart_items
        for i, cart_item in enumerate(self.cart_items):
            if cart_item['name'] == medicine_name:
                # ایجاد دیالوگ ویرایش
                edit_dialog = tk.Toplevel(self.root)
                edit_dialog.title(f"ویرایش {medicine_name}")
                
                ttk.Label(edit_dialog, text="تعداد:").pack(padx=10, pady=5)
                quantity_var = tk.StringVar(value=str(cart_item['quantity']))
                quantity_entry = ttk.Entry(edit_dialog, textvariable=quantity_var)
                quantity_entry.pack(padx=10, pady=5)
                
                def save_changes():
                    try:
                        new_quantity = int(quantity_var.get())
                        if new_quantity <= 0:
                            raise ValueError("تعداد باید بیشتر از صفر باشد")
                        
                        # آپدیت آیتم
                        self.cart_items[i]['quantity'] = new_quantity
                        self.cart_items[i]['total_price'] = cart_item['unit_price'] * new_quantity
                        
                        self.update_cart_display()
                        self.update_invoice_totals()
                        edit_dialog.destroy()
                    except ValueError as e:
                        messagebox.showerror("خطا", str(e))
                
                ttk.Button(edit_dialog, text="ذخیره تغییرات", command=save_changes).pack(pady=10)
                break

    def remove_cart_item(self):
        """حذف آیتم انتخاب شده از سبد خرید"""
        selected = self.cart_tree.selection()
        if not selected:
            messagebox.showwarning("خطا", "لطفاً یک آیتم را انتخاب کنید")
            return
        
        item = self.cart_tree.item(selected[0])
        medicine_name = item['values'][0]
        
        if messagebox.askyesno("تأیید حذف", f"آیا از حذف {medicine_name} از سبد خرید مطمئنید؟"):
            # حذف آیتم از لیست
            self.cart_items = [item for item in self.cart_items if item['name'] != medicine_name]
            self.update_cart_display()
            self.update_invoice_totals()

    def filter_medicines(self, event):
            typed = self.medicine_var.get()
            medicines = [med[1] for med in self.db.get_medicines()]
            filtered = [m for m in medicines if typed.lower() in m.lower()]
            medicine_combo['values'] = filtered

    def update_invoice_totals(self, event=None):
        """محاسبه و به‌روزرسانی جمع‌های فاکتور"""
        try:
            # محاسبه جمع کل
            total = sum(item['total_price'] for item in self.cart_items)
            self.total_var.set(f"{total:,} تومان")
            
            # محاسبه تخفیف
            discount = float(self.discount_var.get()) if self.discount_var.get() else 0
            discount = max(0, min(100, discount))  # محدود کردن تخفیف بین 0 تا 100%
            
            # محاسبه مبلغ نهایی
            final_total = total * (1 - discount / 100)
            self.final_total_var.set(f"{final_total:,} تومان")
        except:
            self.total_var.set("0 تومان")
            self.final_total_var.set("0 تومان")

    def add_to_cart(self):
        try:
            quantity = int(self.quantity_var.get())
            if quantity <= 0:
                raise ValueError("تعداد باید بیشتر از صفر باشد")

            """افزودن دارو به سبد خرید موقت"""
            medicine_name = self.medicine_var.get()
            quantity = int(self.quantity_var.get())

            if not medicine_name or quantity <= 0:
                messagebox.showwarning("خطا", "لطفاً دارو و تعداد معتبر انتخاب کنید")
                return

            medicine = next(med for med in self.db.get_medicines() if med[1] == medicine_name)
            medicine_id, price = medicine[0], medicine[3]
            total = price * quantity

            # افزودن به لیست موقت
            self.cart_items.append({
                'medicine_id': medicine_id,
                'name': medicine_name,
                'quantity': quantity,
                'unit_price': price,
                'total_price': total
            })

            # آپدیت Treeview
            self.update_cart_display()
            self.update_invoice_totals()

        except ValueError as e:
            messagebox.showerror("خطا", f"مقدار نامعتبر:\n{str(e)}")

    def update_cart_display(self):
        """به‌روزرسانی نمایش سبد خرید با قابلیت انتخاب"""
        if not hasattr(self, 'cart_tree') or not self.cart_tree.winfo_exists():
            return
        
        # ذخیره انتخاب فعلی
        selected = self.cart_tree.selection()
        selected_medicine = None
        if selected:
            selected_medicine = self.cart_tree.item(selected[0])['values'][0]
        
        # پاکسازی و پرکردن مجدد
        self.cart_tree.delete(*self.cart_tree.get_children())
        for item in self.cart_items:
            item_id = self.cart_tree.insert('', 'end', values=(
                item['name'],
                item['quantity'],
                f"{item['unit_price']:,} تومان",
                f"{item['total_price']:,} تومان"
            ))
            
            # بازگرداندن انتخاب قبلی
            if selected_medicine and item['name'] == selected_medicine:
                self.cart_tree.selection_set(item_id)
        
        # غیرفعال کردن دکمه‌ها اگر سبد خرید خالی است
        if hasattr(self, 'sale_dialog'):
            for child in self.sale_dialog.winfo_children():
                if isinstance(child, ttk.Button) and "ویرایش" in child['text']:
                    child['state'] = 'normal' if self.cart_items else 'disabled'
                if isinstance(child, ttk.Button) and "حذف" in child['text']:
                    child['state'] = 'normal' if self.cart_items else 'disabled'

    def finalize_invoice(self, dialog):
        if not self.cart_items:
            messagebox.showwarning("خطا", "سبد خرید خالی است")
            return

        try:
            # اعتبارسنجی و دریافت مقادیر
            customer_name = self.customer_name_var.get().strip()
            customer_phone = self.customer_phone_var.get().strip()
            
            # اعتبارسنجی مقادیر عددی
            try:
                total = float(self.total_var.get().replace(" تومان", "").replace(",", ""))
                discount = float(self.discount_var.get()) if self.discount_var.get() else 0
                final_price = float(self.final_total_var.get().replace(" تومان", "").replace(",", ""))
                
                # اعتبارسنجی منطقی مقادیر
                if total <= 0:
                    raise ValueError("مبلغ کل نامعتبر است")
                if discount < 0 or discount > 100:
                    raise ValueError("تخفیف باید بین 0 تا 100 باشد")
                if abs(final_price - (total * (1 - discount/100))) > 1:  # اختلاف ناشی از گرد کردن
                    raise ValueError("محاسبات مالی نادرست است")
            except ValueError as ve:
                messagebox.showerror("خطای محاسباتی", f"خطا در مقادیر مالی:\n{str(ve)}")
                return

            # شروع تراکنش دیتابیس
            cursor = self.db.conn.cursor()
            
            try:
                # 1. ثبت فاکتور اصلی
                cursor.execute("""
                    INSERT INTO invoices 
                    (customer_name, customer_phone, sale_date, total_price, discount, final_price)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (
                    customer_name if customer_name else "ناشناس",
                    customer_phone if customer_phone else None,
                    datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    total,
                    discount,
                    final_price
                ))
                invoice_id = cursor.lastrowid

                # 2. ثبت آیتم‌های فاکتور و به‌روزرسانی موجودی
                for item in self.cart_items:
                    # بررسی موجودی کافی
                    cursor.execute("SELECT quantity FROM medicines WHERE id = ?", (item['medicine_id'],))
                    current_stock = cursor.fetchone()[0]
                    
                    if current_stock < item['quantity']:
                        raise ValueError(
                            f"موجودی کافی برای داروی {item['name']} وجود ندارد\n"
                            f"موجودی فعلی: {current_stock} - تعداد درخواستی: {item['quantity']}"
                        )

                    # ثبت آیتم فاکتور
                    cursor.execute("""
                        INSERT INTO invoice_items 
                        (invoice_id, medicine_id, quantity, unit_price, total_price)
                        VALUES (?, ?, ?, ?, ?)
                    """, (
                        invoice_id,
                        item['medicine_id'],
                        item['quantity'],
                        item['unit_price'],
                        item['total_price']
                    ))

                    # کاهش موجودی انبار
                    cursor.execute("""
                        UPDATE medicines 
                        SET quantity = quantity - ? 
                        WHERE id = ?
                    """, (item['quantity'], item['medicine_id']))

                # اعمال تغییرات در دیتابیس
                self.db.conn.commit()
                
                # نمایش پیام موفقیت
                messagebox.showinfo(
                    "موفق", 
                    f"فاکتور شماره {invoice_id} با موفقیت ثبت شد\n"
                    f"مبلغ قابل پرداخت: {final_price:,} تومان"
                )
                
                # پاکسازی و به‌روزرسانی
                dialog.destroy()
                self.clear_cart()
                self.load_medicines()
                self.load_sales_history()
                
            except Exception as e:
                self.db.conn.rollback()
                raise e

        except ValueError as ve:
            messagebox.showerror("خطای اعتبارسنجی", str(ve))
        except sqlite3.Error as dbe:
            messagebox.shaterror("خطای دیتابیس", f"خطا در ثبت فاکتور:\n{str(dbe)}")
        except Exception as e:
            messagebox.showerror("خطای سیستمی", f"خطای غیرمنتظره:\n{str(e)}")

    def clear_cart(self):
        """پاک کردن سبد خرید موقت"""
        self.cart_items = []
        self.update_cart_display()

    def process_sale(self):
        medicine_name = self.medicine_var.get()
        quantity = int(self.quantity_var.get())
        discount = float(self.discount_var.get()) / 100
        customer_info = self.customer_var.get()

        # یافتن ID دارو و قیمت آن
        medicine = next(med for med in self.db.get_medicines() if med[1] == medicine_name)
        medicine_id, price = medicine[0], medicine[3]

        # ثبت فروش
        sales_manager = SalesManager(self.db)
        success = sales_manager.add_sale(medicine_id, quantity, price, discount, customer_info)

        if success:
            messagebox.showinfo("موفق", "فروش با موفقیت ثبت شد!")
            self.load_medicines()  # به‌روزرسانی لیست داروها
            
            # آپدیت تاریخچه فروش
            notebook = self.root.winfo_children()[0]  # دریافت notebook
            sales_frame = notebook.winfo_children()[1]  # دریافت فریم فروش
            sales_tree = sales_frame.winfo_children()[0]  # دریافت treeview فروش
            self.load_sales_history(sales_tree)  # آپدیت تاریخچه
        else:
            messagebox.showerror("خطا", "خطا در ثبت فروش!")

    '''
    def load_sales_history(self, tree=None):
        """بارگذاری تاریخچه فاکتورها"""
        if tree is None:
            notebook = self.root.winfo_children()[0]
            sales_frame = notebook.winfo_children()[1]
            tree = sales_frame.winfo_children()[0]
        
        if hasattr(self, 'sales_tree'):
            for item in self.sales_tree.get_children():
                self.sales_tree.delete(item)
        
            cursor = self.db.conn.cursor()
            
            cursor.execute("""
            SELECT i.id, 
                   i.customer_name, 
                   i.sale_date, 
                   i.final_price,
                   COUNT(ii.id) as items_count
            FROM invoices i
            LEFT JOIN invoice_items ii ON i.id = ii.invoice_id
            GROUP BY i.id
            ORDER BY i.sale_date DESC
        """)
            
            invoices = cursor.fetchall()
            
            for inv in invoices:
                # تبدیل تاریخ به شمسی
                sale_date = datetime.strptime(inv[2], "%Y-%m-%d %H:%M:%S")
                jalali_date = jdatetime.date.fromgregorian(date=sale_date.date())
                persian_date = f"{jalali_date.year}/{jalali_date.month:02d}/{jalali_date.day:02d}"
                
                self.sales_tree.insert('', 'end', values=(
                inv[0],  # شماره فاکتور
                inv[1] if inv[1] else "ناشناس",
                persian_date,
                f"{inv[3]:,} تومان",
                inv[4]  # تعداد اقلام
            ))
    '''
    def load_sales_history(self):
        if not hasattr(self, 'sales_tree'):
            return
        
        self.sales_tree.delete(*self.sales_tree.get_children())
        
        cursor = self.db.conn.cursor()
        cursor.execute("""
            SELECT i.id, i.customer_name, i.sale_date, 
                i.final_price, COUNT(ii.id) as items_count
            FROM invoices i
            LEFT JOIN invoice_items ii ON i.id = ii.invoice_id
            GROUP BY i.id
            ORDER BY i.sale_date DESC
        """)
        
        for row in cursor.fetchall():
            # تبدیل تاریخ به شمسی
            greg_date = datetime.strptime(row[2], "%Y-%m-%d %H:%M:%S")
            jalali_date = jdatetime.date.fromgregorian(date=greg_date.date())
            persian_date = jalali_date.strftime("%Y/%m/%d")
            
            self.sales_tree.insert('', 'end', values=(
                row[0],  # شماره فاکتور
                row[1] or "ناشناس",  # مشتری
                persian_date,  # تاریخ
                f"{row[3]:,}",  # مبلغ کل
                row[4]  # تعداد اقلام
            ))
    
    '''
    def show_invoice_details(self, event):
        """نمایش جزئیات فاکتور انتخاب شده"""
        selected = self.sales_tree.focus()
        if not selected:
            return
        
        item = self.sales_tree.item(selected)
        invoice_id = item['values'][0]
        
        # ایجاد پنجره جدید
        detail_window = tk.Toplevel(self.root)
        detail_window.title(f"جزئیات فاکتور شماره {invoice_id}")
        
        # Treeview برای نمایش آیتم‌ها
        columns = ('medicine', 'quantity', 'unit_price', 'total_price')
        detail_tree = ttk.Treeview(detail_window, columns=columns, show='headings')
        
        detail_tree.heading('medicine', text='نام دارو')
        detail_tree.heading('quantity', text='تعداد')
        detail_tree.heading('unit_price', text='قیمت واحد')
        detail_tree.heading('total_price', text='قیمت کل')
        
        # دریافت آیتم‌های فاکتور از دیتابیس
        cursor = self.db.conn.cursor()
        cursor.execute("""
            SELECT m.name, i.quantity, i.unit_price, i.total_price
            FROM invoice_items i
            JOIN medicines m ON i.medicine_id = m.id
            WHERE i.invoice_id = ?
        """, (invoice_id,))
        
        for item in cursor.fetchall():
            detail_tree.insert('', 'end', values=(
                item[0],
                item[1],
                f"{item[2]:,} تومان",
                f"{item[3]:,} تومان"
            ))
        
        detail_tree.pack(fill='both', expand=True, padx=10, pady=10)
    '''
    def show_invoice_details(self, event=None):
        selected = self.sales_tree.selection()
        if not selected:
            messagebox.showwarning("خطا", "لطفاً یک فاکتور را انتخاب کنید", parent=self.root)
            return
        
        invoice_id = self.sales_tree.item(selected[0])['values'][0]
        
        # ایجاد پنجره جزئیات
        detail_win = tk.Toplevel(self.root)
        detail_win.title(f"جزئیات فاکتور #{invoice_id}")
        
        # Treeview برای نمایش آیتم‌ها
        columns = ('id', 'medicine', 'quantity', 'unit_price', 'total_price')
        tree = ttk.Treeview(detail_win, columns=columns, show='headings')
        
        for col in columns:
            tree.heading(col, text=col.replace('_', ' ').title())
            tree.column(col, width=100, anchor='center')
        
        # دریافت آیتم‌های فاکتور از دیتابیس
        cursor = self.db.conn.cursor()
        cursor.execute("""
            SELECT m.name, ii.quantity, ii.unit_price, ii.total_price
            FROM invoice_items ii
            JOIN medicines m ON ii.medicine_id = m.id
            WHERE ii.invoice_id = ?
        """, (invoice_id,))
        
        for row in cursor.fetchall():
            tree.insert('', 'end', values=(
                invoice_id,
                row[0],  # نام دارو
                row[1],  # تعداد
                f"{row[2]:,}",  # قیمت واحد
                f"{row[3]:,}"   # قیمت کل
            ))
        
        tree.pack(fill='both', expand=True, padx=10, pady=10)

    def delete_invoice(self):
        selected = self.sales_tree.selection()
        if not selected:
            messagebox.showwarning("خطا", "لطفاً یک فاکتور را انتخاب کنید", parent=self.root)
            return
        
        invoice_id = self.sales_tree.item(selected[0])['values'][0]
        
        if not messagebox.askyesno(
            "تأیید حذف", 
            f"آیا از حذف فاکتور #{invoice_id} مطمئنید؟\nاین عمل غیرقابل بازگشت است!",
            parent=self.root
        ):
            return
        
        try:
            # بازگرداندن موجودی داروها
            cursor = self.db.conn.cursor()
            cursor.execute("""
                SELECT medicine_id, quantity FROM invoice_items 
                WHERE invoice_id = ?
            """, (invoice_id,))
            
            items = cursor.fetchall()
            
            for med_id, qty in items:
                cursor.execute("""
                    UPDATE medicines SET quantity = quantity + ? 
                    WHERE id = ?
                """, (qty, med_id))
            
            # حذف فاکتور
            cursor.execute("DELETE FROM invoices WHERE id = ?", (invoice_id,))
            cursor.execute("DELETE FROM invoice_items WHERE invoice_id = ?", (invoice_id,))
            
            self.db.conn.commit()
            
            # بروزرسانی هر دو جدول
            self.load_sales_history()  # بروزرسانی تاریخچه فروش
            self.load_medicines()      # بروزرسانی جدول داروها
            self.check_low_stock()     # بررسی موجودی کم
            
            messagebox.showinfo("موفق", "فاکتور با موفقیت حذف شد و موجودی انبار بروزرسانی گردید", parent=self.root)
            
        except Exception as e:
            self.db.conn.rollback()
            messagebox.showerror("خطا", f"خطا در حذف فاکتور:\n{str(e)}", parent=self.root)

    def recreate_invoice(self):
        selected = self.sales_tree.selection()
        if not selected:
            messagebox.showwarning("خطا", "لطفاً یک فاکتور را انتخاب کنید", parent=self.root)
            return
        
        invoice_id = self.sales_tree.item(selected[0])['values'][0]
        
        # دریافت اطلاعات فاکتور اصلی
        cursor = self.db.conn.cursor()
        cursor.execute("""
            SELECT customer_name, customer_phone, discount 
            FROM invoices WHERE id = ?
        """, (invoice_id,))
        invoice_data = cursor.fetchone()
        
        # دریافت آیتم‌های فاکتور
        cursor.execute("""
            SELECT m.name, ii.quantity, ii.unit_price
            FROM invoice_items ii
            JOIN medicines m ON ii.medicine_id = m.id
            WHERE ii.invoice_id = ?
        """, (invoice_id,))
        items = cursor.fetchall()
        
        # ایجاد فاکتور جدید
        self.show_sale_dialog()
        
        # پر کردن خودکار اطلاعات
        if hasattr(self, 'sale_dialog'):
            self.customer_name_var.set(invoice_data[0] or "")
            self.customer_phone_var.set(invoice_data[1] or "")
            self.discount_var.set(str(float(invoice_data[2] or 0) * 100))
            
            # افزودن آیتم‌ها به سبد خرید
            for name, qty, price in items:
                self.medicine_var.set(name)
                self.quantity_var.set(str(qty))
                self.add_to_cart()

    def edit_invoice(self):
        selected = self.sales_tree.selection()
        if not selected:
            messagebox.showwarning("خطا", "لطفاً یک فاکتور را انتخاب کنید", parent=self.root)
            return
        
        invoice_id = self.sales_tree.item(selected[0])['values'][0]
        
        # ایجاد پنجره ویرایش
        edit_win = tk.Toplevel(self.root)
        edit_win.title(f"ویرایش فاکتور #{invoice_id}")
        
        # دریافت اطلاعات فاکتور
        cursor = self.db.conn.cursor()
        cursor.execute("SELECT * FROM invoices WHERE id = ?", (invoice_id,))
        invoice = cursor.fetchone()
        
        # ایجاد فرم ویرایش (شبیه به فرم فروش جدید)
        # [کدهای مشابه show_sale_dialog با مقادیر پیش‌فرض]
        
        # دکمه ذخیره تغییرات
        ttk.Button(edit_win, text="ذخیره تغییرات", 
                command=lambda: self.update_invoice(invoice_id, edit_win)).pack(pady=10)









    def manage_categories(self):
        """مدیریت گروه‌های دارویی"""
        dialog = tk.Toplevel(self.root)
        dialog.transient(self.root) 
        dialog.title("مدیریت گروه‌های دارویی")
        dialog.geometry("600x400")
        
        # Treeview برای نمایش گروه‌ها
        tree = ttk.Treeview(dialog, columns=('id', 'name', 'desc'), show='headings')
        tree.heading('id', text='شناسه')
        tree.heading('name', text='نام گروه')
        tree.heading('desc', text='توضیحات')
        tree.pack(fill='both', expand=True, padx=10, pady=10)
        
        # فریم برای دکمه‌ها
        btn_frame = ttk.Frame(dialog)
        btn_frame.pack(fill='x', padx=10, pady=10)
        
        ttk.Button(btn_frame, text="افزودن گروه", command=lambda: self.add_category_dialog(dialog, tree)).pack(side='left', padx=5)
        ttk.Button(btn_frame, text="ویرایش گروه", command=lambda: self.edit_category_dialog(dialog, tree)).pack(side='left', padx=5)
        ttk.Button(btn_frame, text="حذف گروه", command=lambda: self.delete_category_dialog(tree)).pack(side='left', padx=5)
        
        # بارگذاری گروه‌ها
        self.load_categories(tree)

    def load_categories(self, tree):
        """بارگذاری گروه‌ها در Treeview"""
        for item in tree.get_children():
            tree.delete(item)
            
        categories = self.db.get_categories()
        for cat in categories:
            tree.insert('', 'end', values=(cat[0], cat[1], cat[2]))

    def add_category_dialog(self, parent, tree):
        """دیالوگ افزودن گروه جدید"""
        dialog = tk.Toplevel(parent)
        dialog.title("افزودن گروه جدید")
        dialog.geometry("300x200")
        
        ttk.Label(dialog, text="نام گروه:").pack(padx=10, pady=5)
        name_entry = ttk.Entry(dialog)
        name_entry.pack(fill='x', padx=10, pady=5)
        
        ttk.Label(dialog, text="توضیحات:").pack(padx=10, pady=5)
        desc_entry = ttk.Entry(dialog)
        desc_entry.pack(fill='x', padx=10, pady=5)
        
        def save():
            name = name_entry.get()
            desc = desc_entry.get()
            if name:
                self.db.add_category(name, desc)
                self.load_categories(tree)
                dialog.destroy()
            else:
                messagebox.showerror("خطا", "نام گروه نمی‌تواند خالی باشد")
        
        ttk.Button(dialog, text="ذخیره", command=save).pack(pady=10)

    def edit_category_dialog(self, parent, tree):
        """دیالوگ ویرایش گروه"""
        selected = tree.selection()
        if not selected:
            messagebox.showwarning("خطا", "لطفاً یک گروه را انتخاب کنید")
            return
        
        item = tree.item(selected[0])
        cat_id = item['values'][0]
        cat_name = item['values'][1]
        cat_desc = item['values'][2]
        
        dialog = tk.Toplevel(parent)
        dialog.title("ویرایش گروه")
        dialog.geometry("300x200")
        
        ttk.Label(dialog, text="نام گروه:").pack(padx=10, pady=5)
        name_entry = ttk.Entry(dialog)
        name_entry.insert(0, cat_name)
        name_entry.pack(fill='x', padx=10, pady=5)
        
        ttk.Label(dialog, text="توضیحات:").pack(padx=10, pady=5)
        desc_entry = ttk.Entry(dialog)
        desc_entry.insert(0, cat_desc)
        desc_entry.pack(fill='x', padx=10, pady=5)
        
        def update():
            name = name_entry.get()
            desc = desc_entry.get()
            if name:
                self.db.update_category(cat_id, name, desc)
                self.load_categories(tree)
                dialog.destroy()
            else:
                messagebox.showerror("خطا", "نام گروه نمی‌تواند خالی باشد")
        
        ttk.Button(dialog, text="ذخیره تغییرات", command=update).pack(pady=10)

    def delete_category_dialog(self, tree):
        """حذف گروه انتخاب شده"""
        selected = tree.selection()
        if not selected:
            messagebox.showwarning("خطا", "لطفاً یک گروه را انتخاب کنید")
            return
        
        item = tree.item(selected[0])
        cat_id = item['values'][0]
        cat_name = item['values'][1]
        
        if messagebox.askyesno("تأیید حذف", f"آیا از حذف گروه '{cat_name}' مطمئن هستید؟"):
            self.db.delete_category(cat_id)
            self.load_categories(tree)
            messagebox.showinfo("موفق", "گروه با موفقیت حذف شد")

    def clear_window(self):
        """پاک کردن تمام ویجت‌های پنجره"""
        for widget in self.root.winfo_children():
            widget.destroy()
    
    def login(self):
        """ورود به سیستم"""
        username = self.login_var.get()
        password = self.password_var.get()
        
        if username == "a" and password == "1":
            self.create_main_page()
        else:
            messagebox.showerror("خطا", "نام کاربری یا رمز عبور اشتباه است")
    
    def logout(self):
        """خروج از سیستم"""
        self.create_login_page()
    
    def load_medicines(self):
        """بارگذاری داروها از دیتابیس"""
        for item in self.tree.get_children():
            self.tree.delete(item)
            
        medicines = self.db.get_medicines_with_categories()
        
        for med in medicines:
            # تبدیل تاریخ میلادی به شمسی برای نمایش
            gregorian_date = datetime.strptime(med[4], "%Y-%m-%d").date()
            jalali_date = jdatetime.date.fromgregorian(date=gregorian_date)
            expiry_date = f"{jalali_date.year}/{jalali_date.month:02d}/{jalali_date.day:02d}"
            # تبدیل تاریخ به شمسی برای نمایش
            #year, month, day = map(int, med[4].split('-'))
            #jalali_date = jdatetime.date(year, month, day)
            #expiry_date = f"{jalali_date.year}/{jalali_date.month:02d}/{jalali_date.day:02d}"
            
            # نام گروه یا 'بدون گروه'
            category = med[6] if med[6] else "بدون گروه"
            
            self.tree.insert('', 'end', values=(
                med[0], med[1], med[2], f"{med[3]:,} تومان", 
                expiry_date, category, med[5]
            ))
    
    def get_medicines_with_categories(self):
        cursor = self.conn.cursor()
        cursor.execute("""
            SELECT m.*, c.name 
            FROM medicines m
            LEFT JOIN categories c ON m.category_id = c.id
            ORDER BY m.name
        """)
        return cursor.fetchall()

    def check_low_stock(self):
        """بررسی داروهای در حال اتمام"""
        low_stock = self.db.get_low_stock_medicines()
        if low_stock:
            warning = "هشدار: داروهای زیر در حال اتمام هستند:\n"
            for med in low_stock:
                warning += f"- {med[1]} (موجودی: {med[2]})\n"
            
            messagebox.showwarning("هشدار کمبود دارو", warning)
    
    def show_add_dialog(self):
        dialog = tk.Toplevel(self.root)
        dialog.title("افزودن داروی جدید")
        dialog.geometry("400x350")
        """نمایش دیالوگ افزودن دارو"""
        dialog.resizable(False, False)
        
        # متغیرهای فرم
        name_var = tk.StringVar()
        quantity_var = tk.StringVar()
        price_var = tk.StringVar()
        expiry_var = tk.StringVar()
        desc_var = tk.StringVar()
        
        # تنظیم تاریخ امروز به شمسی
        today = jdatetime.date.today()
        expiry_var.set(f"{today.year}/{today.month:02d}/{today.day:02d}")     
        

        # ایجاد فرم
        ttk.Label(dialog, text="نام دارو:").grid(row=0, column=0, padx=10, pady=5, sticky='e')
        ttk.Entry(dialog, textvariable=name_var).grid(row=0, column=1, padx=10, pady=5, sticky='we')
        
        ttk.Label(dialog, text="تعداد:").grid(row=1, column=0, padx=10, pady=5, sticky='e')
        ttk.Entry(dialog, textvariable=quantity_var).grid(row=1, column=1, padx=10, pady=5, sticky='we')
        
        ttk.Label(dialog, text="قیمت (تومان):").grid(row=2, column=0, padx=10, pady=5, sticky='e')
        ttk.Entry(dialog, textvariable=price_var).grid(row=2, column=1, padx=10, pady=5, sticky='we')
        
       
        # ویجت‌های تاریخ شمسی
        ttk.Label(dialog, text="تاریخ انقضا (شمسی):").grid(row=3, column=0, padx=10, pady=5, sticky='e')
        
        # فریم برای سال، ماه و روز
        date_frame = ttk.Frame(dialog)
        date_frame.grid(row=3, column=1, padx=10, pady=5, sticky='we')
        
        # سال شمسی
        self.year_var = tk.StringVar()
        ttk.Label(date_frame, text="سال:").pack(side='left')
        year_spin = ttk.Spinbox(date_frame, from_=1400, to=1500, textvariable=self.year_var, width=5)
        year_spin.pack(side='left', padx=2)
        self.year_var.set(jdatetime.date.today().year)
        
        # ماه شمسی
        self.month_var = tk.StringVar()
        ttk.Label(date_frame, text="ماه:").pack(side='left')
        month_spin = ttk.Spinbox(date_frame, from_=1, to=12, textvariable=self.month_var, width=3)
        month_spin.pack(side='left', padx=2)
        self.month_var.set(jdatetime.date.today().month)
        
        # روز شمسی
        self.day_var = tk.StringVar()
        ttk.Label(date_frame, text="روز:").pack(side='left')
        day_spin = ttk.Spinbox(date_frame, from_=1, to=31, textvariable=self.day_var, width=3)
        day_spin.pack(side='left', padx=2)
        self.day_var.set(jdatetime.date.today().day)

        ttk.Label(dialog, text="توضیحات:").grid(row=4, column=0, padx=10, pady=5, sticky='e')
        ttk.Entry(dialog, textvariable=desc_var).grid(row=4, column=1, padx=10, pady=5, sticky='we')
        
        # اضافه کردن ComboBox برای انتخاب گروه
        ttk.Label(dialog, text="گروه:").grid(row=5, column=0, padx=10, pady=5, sticky='e')
        category_combo = ttk.Combobox(dialog, state='readonly')
        category_combo.grid(row=5, column=1, padx=10, pady=5, sticky='we')
        
        # بارگذاری گروه‌ها
        categories = self.db.get_categories()
        category_combo['values'] = ["بدون گروه"] + [cat[1] for cat in categories]
        category_combo.current(0)

        def save():
            try:
                # اعتبارسنجی فیلدهای اجباری
                if not name_var.get().strip():
                    raise ValueError("نام دارو نمی‌تواند خالی باشد")
                
                # اعتبارسنجی تعداد
                try:
                    quantity = int(quantity_var.get())
                    if quantity <= 0:
                        raise ValueError("تعداد باید بیشتر از صفر باشد")
                except ValueError:
                    raise ValueError("تعداد باید یک عدد صحیح باشد")

                # اعتبارسنجی قیمت
                try:
                    price = float(price_var.get())
                    if price <= 0:
                        raise ValueError("قیمت باید بیشتر از صفر باشد")
                except ValueError:
                    raise ValueError("قیمت باید یک عدد معتبر باشد")

                # اعتبارسنجی تاریخ
                try:
                    year = int(self.year_var.get())
                    month = int(self.month_var.get())
                    day = int(self.day_var.get())
                    
                    if not validate_jalali_date(year, month, day):
                        raise ValueError("تاریخ وارد شده معتبر نیست")
                        
                    jalali_date = jdatetime.date(year, month, day)
                    gregorian_date = jalali_date.togregorian()
                    expiry_date = gregorian_date.strftime("%Y-%m-%d")
                    
                    # بررسی تاریخ انقضا (نباید در گذشته باشد)
                    today = jdatetime.date.today()
                    if jalali_date < today:
                        raise ValueError("تاریخ انقضا نمی‌تواند در گذشته باشد")
                        
                except ValueError as e:
                    raise ValueError(f"خطا در تاریخ: {str(e)}")

                # اعتبارسنجی گروه
                category_id = None
                category_name = category_combo.get()
                if category_name != "بدون گروه":
                    try:
                        category_id = next(cat[0] for cat in categories if cat[1] == category_name)
                    except StopIteration:
                        raise ValueError("گروه انتخاب شده معتبر نیست")

                # ذخیره در دیتابیس
                try:
                    success = self.db.add_medicine(
                        name_var.get().strip(),
                        quantity,
                        price,
                        expiry_date,
                        desc_var.get().strip(),
                        category_id
                    )
                    
                    if not success:
                        raise RuntimeError("خطا در ذخیره اطلاعات در دیتابیس")
                        
                except sqlite3.Error as db_error:
                    raise RuntimeError(f"خطای دیتابیس: {str(db_error)}")

                # عملیات موفقیت‌آمیز
                self.load_medicines()
                messagebox.showinfo("موفق", "دارو با موفقیت افزوده شد", parent=dialog)
                dialog.destroy()

            except ValueError as ve:
                messagebox.showerror("خطای اعتبارسنجی", str(ve), parent=dialog)
            except RuntimeError as re:
                messagebox.showerror("خطای اجرایی", str(re), parent=dialog)
            except Exception as e:
                messagebox.showerror("خطای غیرمنتظره", 
                                f"خطای ناشناخته:\n{str(e)}\n\nلطفاً با پشتیبانی تماس بگیرید",
                                parent=dialog)
        
        def validate_jalali_date(year, month, day):
            """اعتبارسنجی تاریخ شمسی"""
            try:
                jdatetime.date(year, month, day)
                return True
            except:
                return False

        ttk.Button(dialog, text="ذخیره", command=save).grid(row=6, column=0, columnspan=2, pady=10)
      
    def edit_medicine(self):
        """ویرایش داروی انتخاب شده"""
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("خطا", "لطفاً یک دارو را انتخاب کنید")
            return
        
        item = self.tree.item(selected[0])
        medicine_id = item['values'][0]
        medicine = self.db.get_medicine_by_id(medicine_id)
        
        dialog = tk.Toplevel(self.root)
        dialog.title("ویرایش دارو")
        dialog.geometry("400x350")
        dialog.resizable(False, False)
        
        # متغیرهای فرم
        name_var = tk.StringVar(value=medicine[1])
        quantity_var = tk.StringVar(value=str(medicine[2]))
        price_var = tk.StringVar(value=str(medicine[3]))
        desc_var = tk.StringVar(value=medicine[5])
        
        # تبدیل تاریخ میلادی به شمسی
        gregorian_date = datetime.strptime(medicine[4], "%Y-%m-%d").date()
        jalali_date = jdatetime.date.fromgregorian(date=gregorian_date)
        
         # ایجاد فرم
        ttk.Label(dialog, text="نام دارو:").grid(row=0, column=0, padx=10, pady=5, sticky='e')
        ttk.Entry(dialog, textvariable=name_var).grid(row=0, column=1, padx=10, pady=5, sticky='we')
        
        ttk.Label(dialog, text="تعداد:").grid(row=1, column=0, padx=10, pady=5, sticky='e')
        ttk.Entry(dialog, textvariable=quantity_var).grid(row=1, column=1, padx=10, pady=5, sticky='we')
        
        ttk.Label(dialog, text="قیمت (تومان):").grid(row=2, column=0, padx=10, pady=5, sticky='e')
        ttk.Entry(dialog, textvariable=price_var).grid(row=2, column=1, padx=10, pady=5, sticky='we')

        # ویجت‌های تاریخ شمسی
        ttk.Label(dialog, text="تاریخ انقضا (شمسی):").grid(row=3, column=0, padx=10, pady=5, sticky='e')
        
        date_frame = ttk.Frame(dialog)
        date_frame.grid(row=3, column=1, padx=10, pady=5, sticky='we')
        
        # سال شمسی
        year_var = tk.StringVar(value=jalali_date.year)
        ttk.Label(date_frame, text="سال:").pack(side='left')
        year_spin = ttk.Spinbox(date_frame, from_=1400, to=1500, textvariable=year_var, width=5)
        year_spin.pack(side='left', padx=2)
        
        # ماه شمسی
        month_var = tk.StringVar(value=jalali_date.month)
        ttk.Label(date_frame, text="ماه:").pack(side='left')
        month_spin = ttk.Spinbox(date_frame, from_=1, to=12, textvariable=month_var, width=3)
        month_spin.pack(side='left', padx=2)
        
        # روز شمسی
        day_var = tk.StringVar(value=jalali_date.day)
        ttk.Label(date_frame, text="روز:").pack(side='left')
        day_spin = ttk.Spinbox(date_frame, from_=1, to=31, textvariable=day_var, width=3)
        day_spin.pack(side='left', padx=2)

        ttk.Label(dialog, text="توضیحات:").grid(row=4, column=0, padx=10, pady=5, sticky='e')
        ttk.Entry(dialog, textvariable=desc_var).grid(row=4, column=1, padx=10, pady=5, sticky='we')

        # اضافه کردن ComboBox برای انتخاب گروه
        ttk.Label(dialog, text="گروه:").grid(row=5, column=0, padx=10, pady=5, sticky='e')
        category_combo = ttk.Combobox(dialog, state='readonly')
        category_combo.grid(row=5, column=1, padx=10, pady=5, sticky='we')
        
        # بارگذاری گروه‌ها
        categories = self.db.get_categories()
        category_combo['values'] = ["بدون گروه"] + [cat[1] for cat in categories]
        category_combo.current(0)

        def update():
            """به‌روزرسانی دارو"""
            try:
                # دریافت تاریخ شمسی از کاربر
                year = int(year_var.get())
                month = int(month_var.get())
                day = int(day_var.get())
                
                # اعتبارسنجی تاریخ
                if not validate_jalali_date(year, month, day):
                    messagebox.showerror("خطا", "تاریخ وارد شده معتبر نیست")
                    return

                # تبدیل به تاریخ میلادی برای ذخیره در دیتابیس
                jalali_date = jdatetime.date(year, month, day)
                gregorian_date = jalali_date.togregorian()
                expiry_date = gregorian_date.strftime("%Y-%m-%d")
                
                # دریافت گروه انتخاب شده
                category_name = category_combo.get()
                category_id = None
                if category_name != "بدون گروه":
                    category_id = next(cat[0] for cat in categories if cat[1] == category_name)
                
                
                self.db.update_medicine(
                    medicine_id,
                    name_var.get(),
                    int(quantity_var.get()),
                    float(price_var.get()),
                    expiry_date,
                    desc_var.get(),
                    category_id
                )
                
                self.load_medicines()  # تصحیح نام تابع
                messagebox.showinfo("موفق", "دارو با موفقیت ویرایش شد")
                dialog.destroy()
            except Exception as e:
                messagebox.showerror("خطا", f"خطا در به‌روزرسانی اطلاعات:\n{str(e)}")
        
        def validate_jalali_date(year, month, day):
            """اعتبارسنجی تاریخ شمسی"""
            try:
                jdatetime.date(year, month, day)
                return True
            except:
                return False


        ttk.Button(dialog, text="ذخیره تغییرات", command=update).grid(row=6, column=0, columnspan=2, pady=10)

    def delete_medicine(self):
        """حذف داروی انتخاب شده"""
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("خطا", "لطفاً یک دارو را انتخاب کنید")
            return
        
        item = self.tree.item(selected[0])
        medicine_id = item['values'][0]
        medicine_name = item['values'][1]
        
        if messagebox.askyesno("تأیید حذف", f"آیا از حذف داروی '{medicine_name}' مطمئن هستید؟"):
            self.db.delete_medicine(medicine_id)
            self.load_medicines()
            messagebox.showinfo("موفق", "دارو با موفقیت حذف شد")
    
    def search_medicines(self, *args):
        query = self.search_var.get()
        medicines = self.db.search_medicines(query)
        
        for item in self.tree.get_children():
            self.tree.delete(item)
            
        for med in medicines:
            # تبدیل تاریخ به شمسی برای نمایش
            year, month, day = map(int, med[4].split('-'))
            jalali_date = jdatetime.date(year, month, day)
            expiry_date = f"{jalali_date.year}/{jalali_date.month:02d}/{jalali_date.day:02d}"
            
            self.tree.insert('', 'end', values=(
                med[0], med[1], med[2], f"{med[3]:,} تومان", 
                expiry_date, med[5]
            ))

    def generate_report(self):
        """تهیه گزارش"""
        report = self.db.generate_report()
        
        messagebox.showinfo("گزارش داروخانه", 
            f"""گزارش کلی داروخانه:
            --------------------------
            تعداد کل داروها: {report['total_medicines']}
            داروهای در حال اتمام: {report['low_stock_count']}
            داروهای منقضی شده: {report['expired_count']}
            مجموع ارزش داروها: {report['total_value']:,} تومان
            """)

class DatabaseManager:
    """مدیریت ارتباط با دیتابیس"""
    def __init__(self):
        

        self.conn = sqlite3.connect("pharmacy.db")
        self.create_tables()
    
    def create_tables(self):

        cursor = self.conn.cursor()
        # جدول گروه‌ها
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS categories (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL UNIQUE,
            description TEXT
         )
         """)
        # جدول داروها با اضافه کردن فیلد category_id
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS medicines (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            quantity INTEGER NOT NULL,
            price REAL NOT NULL,
            expiry_date TEXT NOT NULL,
            description TEXT,
            category_id INTEGER,
            FOREIGN KEY (category_id) REFERENCES categories(id)
            )
            """)

        cursor.execute("""
        CREATE TABLE IF NOT EXISTS sales (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            medicine_id INTEGER NOT NULL,
            quantity INTEGER NOT NULL,
            sale_date TEXT NOT NULL,
            price REAL NOT NULL,
            discount REAL DEFAULT 0,
            total_price REAL NOT NULL,
            customer_info TEXT,
            FOREIGN KEY (medicine_id) REFERENCES medicines(id)
        )
                """)


        cursor.execute("""
            CREATE TABLE IF NOT EXISTS invoices (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                customer_name TEXT,
                customer_phone TEXT,
                sale_date TEXT NOT NULL,
                total_price REAL NOT NULL,
                discount REAL DEFAULT 0,
                final_price REAL NOT NULL
            )
            """)

        cursor.execute("""
            CREATE TABLE IF NOT EXISTS invoice_items (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            invoice_id INTEGER NOT NULL,
            medicine_id INTEGER NOT NULL,
            quantity INTEGER NOT NULL,
            unit_price REAL NOT NULL,
            total_price REAL NOT NULL,
            FOREIGN KEY (invoice_id) REFERENCES invoices(id),
            FOREIGN KEY (medicine_id) REFERENCES medicines(id)
        )
        """)


        self.conn.commit()
    
    def add_category(self, name, description=None):
        cursor = self.conn.cursor()
        cursor.execute("""
            INSERT INTO categories (name, description)
            VALUES (?, ?)
        """, (name, description))
        self.conn.commit()
        return cursor.lastrowid

    def get_categories(self):
        cursor = self.conn.cursor()
        cursor.execute("SELECT * FROM categories ORDER BY name")
        return cursor.fetchall()

    def update_category(self, category_id, name, description=None):
        cursor = self.conn.cursor()
        cursor.execute("""
            UPDATE categories 
            SET name = ?, description = ?
            WHERE id = ?
        """, (name, description, category_id))
        self.conn.commit()

    def delete_category(self, category_id):
        cursor = self.conn.cursor()
        # ابتدا داروهای این گروه را به گروه پیش‌فرض منتقل می‌کنیم
        cursor.execute("UPDATE medicines SET category_id = NULL WHERE category_id = ?", (category_id,))
        cursor.execute("DELETE FROM categories WHERE id = ?", (category_id,))
        self.conn.commit()

    def add_medicine(self, name, quantity, price, expiry_date, description, category_id=None):
        """ذخیره دارو با تاریخ آماده شده (میلادی)"""
        try:
            cursor = self.conn.cursor()
            cursor.execute("""
                INSERT INTO medicines (name, quantity, price, expiry_date, description, category_id)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (name, quantity, price, expiry_date, description, category_id))
            self.conn.commit()
            return True
        except Exception as e:
            print(f"Error in add_medicine: {e}")
            return False
    
    def get_medicines(self):
        cursor = self.conn.cursor()
        cursor.execute("SELECT * FROM medicines ORDER BY name")
        return cursor.fetchall()

    def get_medicines_with_categories(self):
        """دریافت لیست داروها همراه با اطلاعات گروه‌بندی"""
        cursor = self.conn.cursor()
        cursor.execute("""
            SELECT m.id, m.name, m.quantity, m.price, m.expiry_date, 
                   m.description, c.name as category_name
            FROM medicines m
            LEFT JOIN categories c ON m.category_id = c.id
            ORDER BY m.name
        """)
        return cursor.fetchall()

    def get_medicine_by_id(self, medicine_id):
        cursor = self.conn.cursor()
        cursor.execute("SELECT * FROM medicines WHERE id = ?", (medicine_id,))
        return cursor.fetchone()
    
    def update_medicine(self, medicine_id, name, quantity, price, expiry_date, description, category_id=None):
        try:           
            cursor = self.conn.cursor()
            cursor.execute("""
                UPDATE medicines 
                SET name = ?, quantity = ?, price = ?, expiry_date = ?, description = ?, category_id = ?
                WHERE id = ?
            """, (name, quantity, price, expiry_date, description, category_id, medicine_id))
            self.conn.commit()
            return True
        except Exception as e:
            print(f"Error in update_medicine: {e}")
            return False

    def delete_medicine(self, medicine_id):
        cursor = self.conn.cursor()
        cursor.execute("DELETE FROM medicines WHERE id = ?", (medicine_id,))
        self.conn.commit()
    
    def search_medicines(self, query):
        cursor = self.conn.cursor()
        cursor.execute("""
            SELECT * FROM medicines 
            WHERE name LIKE ? OR id LIKE ?
            ORDER BY name
        """, (f"%{query}%", f"%{query}%"))
        return cursor.fetchall()
    
    def get_low_stock_medicines(self, threshold=10):
        cursor = self.conn.cursor()
        cursor.execute("""
            SELECT * FROM medicines 
            WHERE quantity <= ?
            ORDER BY quantity
        """, (threshold,))
        return cursor.fetchall()
    
    def generate_report(self):
        cursor = self.conn.cursor()
        
        # تعداد کل داروها
        cursor.execute("SELECT COUNT(*) FROM medicines")
        total_medicines = cursor.fetchone()[0]
        
        # داروهای در حال اتمام
        cursor.execute("SELECT COUNT(*) FROM medicines WHERE quantity <= 10")
        low_stock_count = cursor.fetchone()[0]
        
        # داروهای منقضی شده
        today = datetime.now().strftime("%Y-%m-%d")
        cursor.execute("SELECT COUNT(*) FROM medicines WHERE expiry_date < ?", (today,))
        expired_count = cursor.fetchone()[0]
        
        # مجموع ارزش داروها
        cursor.execute("SELECT SUM(quantity * price) FROM medicines")
        total_value = cursor.fetchone()[0] or 0
        
        return {
            "total_medicines": total_medicines,
            "low_stock_count": low_stock_count,
            "expired_count": expired_count,
            "total_value": total_value
        }

    def get_sales_report(self, start_date=None, end_date=None):
        """دریافت گزارش فروش با امکان فیلتر زمانی"""
        cursor = self.conn.cursor()
        
        query = """
            SELECT s.id, m.name, s.quantity, s.price, s.discount, 
                   s.total_price, s.sale_date, s.customer_info
            FROM sales s
            JOIN medicines m ON s.medicine_id = m.id
        """
        
        params = []
        if start_date and end_date:
            query += " WHERE s.sale_date BETWEEN ? AND ?"
            params.extend([start_date, end_date])
        
        query += " ORDER BY s.sale_date DESC"
        
        cursor.execute(query, params)
        return cursor.fetchall()

if __name__ == "__main__":
    root = tk.Tk()
    app = PharmacyApp(root)
    root.mainloop()