import mysql.connector  


def connect_db():  
    return mysql.connector.connect(  
        host='localhost',      # یا آدرس IP سرور  
        user='root',       # نام کاربری  
        password='123456',   # کلمه عبور  
        database='phonebook'   # نام پایگاه داده  
    )  

def add_contact(id,name, phone):  
    conn = connect_db()  
    cursor = conn.cursor()  
    cursor.execute('INSERT INTO contacts (id,name, phone) VALUES (%s,%s, %s)', (id,name, phone))  
    conn.commit()  
    cursor.close()  
    conn.close()  
    print("Contact added successfully!")   

def view_contacts():  
    conn = connect_db()  
    cursor = conn.cursor()  
    cursor.execute('SELECT * FROM contacts')  
    rows = cursor.fetchall()  
    
    print("Contacts:")
    print("***********************") 
    for row in rows:  
        print(f"ID: {row[0]}, Name: {row[1]}, Phone: {row[2]}")  
    
    print("***********************")
    cursor.close()  
    conn.close()  

def update_contact(contact_id, name, phone):  
    conn = connect_db()  
    cursor = conn.cursor()  
    cursor.execute('UPDATE contacts SET name = %s, phone = %s WHERE id = %s', (name, phone, contact_id))  
    conn.commit()  
    cursor.close()  
    conn.close()  
    print("Contact updated successfully!")  

def delete_contact(contact_id):  
    conn = connect_db()  
    cursor = conn.cursor()  
    cursor.execute('DELETE FROM contacts WHERE id = %s', (contact_id,))  
    conn.commit()  
    cursor.close()  
    conn.close()  
    print("Contact deleted successfully!")  

def main():  
    while True:  
        print("\nPhonebook Menu")  
        print("1. Add Contact")  
        print("2. View Contacts")  
        print("3. Update Contact")  
        print("4. Delete Contact")  
        print("5. Exit")  
        
        choice = input("Enter your choice: ")  
        
       
        if choice == '1':  
            name = input("Enter name: ")  
            phone = input("Enter phone number: ")  
            conn = connect_db()  
            cursor = conn.cursor()  
            cursor.execute('SELECT MAX(id) FROM contacts')  
            #rows = cursor.fetchone()
            rows=cursor.fetchall()[0][0] 
            #for row in rows: 
                #print(rows[0])
            #print(cursor)
       
            
            if rows==0 or rows =="None" :
                id=0

            else:
                id=rows+1
                
            cursor.close()  
            conn.close()
            
            add_contact(id,name, phone) 
        
        elif choice == '2':  
            view_contacts()  
        
        elif choice == '3':  
            contact_id = int(input("Enter contact ID to update: "))  
            name = input("Enter new name: ")  
            phone = input("Enter new phone number: ")  
            update_contact(contact_id, name, phone)  
        
        elif choice == '4':  
            contact_id = int(input("Enter contact ID to delete: "))  
            delete_contact(contact_id)  
        
        elif choice == '5':  
            print("Exiting...")  
            break  
        
        else:  
            print("Invalid choice. Please try again.")  

if __name__ == "__main__":  
    main() 