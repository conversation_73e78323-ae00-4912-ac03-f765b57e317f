import os

"""
if os.path.exists("phone book.txt"): 
    os.remove("sample.txt") 
  
    # Print the statement once 
    # the file is deleted  
    print("File deleted !")  
  
else: 
  
    # Print if file is not present  
    print("File doesnot exist !")  
"""

def my_menu():
    print("*" *50)
    print ("1: Add new phone : ")
    print ("2: search phone : ")
    print ("3: Delete phone : ")
    print ("4: all list")
    print ("0: Exit")
    print("*" *50)  

my_menu()


#f= open("book.txt","w+")
global men
men=int(input("Enter the number menu:  "))
global idi


def addphon():
    idi=1
    f=open("book.txt", "r")
    li=f.readlines()
    for (i, item) in enumerate(li, start=1):
        parts=item.split(',')
        idiu=parts[0]
    f.close()
    with open("book.txt", "a+")  as file:
        idi=idi+i
        name = input("Enter the name : ") 
        family = input("Enter the family : ")
        phone = input("Enter the phone : ")
        file.write(str(idi)+","+name+","+family+","+phone) 
        file.write("\n") 
        file.close() 
    print("Data is written into the file.")


 



def searchlist():
    fr=open("book.txt", "r")
    li=fr.readlines()
    serchnf=input("Enter name or family for search :  ")
    found = False
    for (i, item) in enumerate(li, start=1):
        if serchnf in item:
            parts=item.split(',')
            name=parts[1]
            family=parts[2]
            phone=parts[3]
            print(name , " : " , family  , " : ",phone , end="\n" ) 
            found = True   
    if not found:
        print("Not found")
    fr.close()

def dellist():
    with open("book.txt", "r+") as f:
        d = f.readlines()
        f.seek(0)
        found = False
        hazf=input("Enter The name or family for Delete :")
        for i in d:
            if hazf not in i:
                f.write(i)
        print("found item for delete :")
        found = True                           
        f.truncate()
        if not found:
            print("not Dleted : ")



def alllist():
    fr=open("book.txt", "r")
    print(fr.read())
    fr.close()


"""
    serchnf=input("Enter name or family for search :  ")  
    f=open("book.txt", "r")
    with f as file:  
        found = False  
        for line_number, line in enumerate(file, start=1):  
            if serchnf in line:  
                print(f"Found '{serchnf}' in line {line_number}: {line.strip()}")  
                found = True  
        if not found:  
            print(f"'{serchnf}' not found in the file.")  
    file.close()
"""


"""
    addd=input("Are you add? y or n: ")
    if addd== "y":
        global idi
        idi+=1
        name=input("Enter the name:  ")
        family=input("Enter the family: ")
        phone=input("Enter the phone:   ")
        dik.append([idi,name,family,phone])
        print(dik)
        addd=input("Are you add? y or n: ")
    else:
        my_menu()
        
"""      
   

while men !=0 :
    if men==1:
      
       addphon()
       my_menu()
       men=int(input("Enter the number menu:  "))       
    elif men==2 :  
        searchlist()
        my_menu()
        men=int(input("Enter the number menu:  "))
        #print("2")
    elif men==3 :
        dellist()
        my_menu()
        men=int(input("Enter the number menu:  "))
    elif men==4 :
        alllist()
        my_menu()
        men=int(input("Enter the number menu:  "))

else:
    exit
