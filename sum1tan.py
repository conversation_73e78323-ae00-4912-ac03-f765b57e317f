import string

def fibo(n):
    if n<0 :
        print("Incorrect input")
    elif n==0:
        return 0
        
    elif n==1:
        return 1        
    else:
       return fibo(n-1)+fibo(n-2)

n = int(input("Enter the number: "))
print(fibo(n))







"""
فاکتوریل عدد N
n=int(input("Enter the number of factorils : "))
f=0
temp=1
if n==0 :
    print("factotiis :",n+1)
elif n==1:
    print("factotiis :",n)
else:
    for i in range(n):
        temp=temp*(i+1)
    print(temp)    
"""    



"""
revers string
def reverces():
    s=input("Enter the word: ")
    print(s[::-1])
reverces()   
"""
"""
عدد اول:
n=int(input("Enter number for sum: "))
if n%2==0:
    print ("Avallllllllll")
else:
    print("No aval")
"""
"""
جمع 1 تا n
n=int(input("Enter number for sum: "))
sum=0
for i in range(n+1):
    sum+=i
print("sum is:" , sum )

"""
