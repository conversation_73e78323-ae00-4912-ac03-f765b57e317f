import sys
import json
from datetime import datetime, timedelta
from persiantools.jdatetime import JalaliDate
from plyer import notification
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                             QPushButton, QLabel, QLineEdit, QTextEdit, QListWidget, 
                             QMessageBox, QCalendarWidget, QFrame, QScrollArea)
from PyQt5.QtCore import Qt, QTimer, QDate
from PyQt5.QtGui import QFont, QIcon, QPixmap

class PersianCalendarWidget(QCalendarWidget):
    def __init__(self):
        super().__init__()
        self.setLocale(Qt.Persian)
        self.setGridVisible(True)
        
    def paintCell(self, painter, rect, date):
        gregorian_date = QDate.toPyDate(date)  # تبدیل QDate به datetime.date
        jalali_date = JalaliDate(gregorian_date)
        '''
        jalali_date = JalaliDate.fromgregorian(
            year=date.year(),
            month=date.month(),
            day=date.day()
        )
        '''
        super().paintCell(painter, rect, date)
        
        painter.save()
        painter.setFont(QFont("B Nazanin", 10))
        painter.drawText(rect, Qt.AlignCenter, str(jalali_date.day))
        painter.restore()

class NoteCard(QFrame):
    def __init__(self, note, parent=None):
        super().__init__(parent)
        self.note = note
        self.parent = parent
        self.setup_ui()
        
    def setup_ui(self):
        self.setFrameShape(QFrame.StyledPanel)
        self.setFrameShadow(QFrame.Raised)
        self.setStyleSheet("""
            NoteCard {
                background-color: #f8f9fa;
                border-radius: 10px;
                padding: 15px;
                margin: 5px;
            }
            NoteCard:hover {
                background-color: #e9ecef;
                border: 1px solid #adb5bd;
            }
        """)
        
        layout = QVBoxLayout()
        
        # عنوان یادداشت
        title_label = QLabel(self.note['title'])
        title_label.setFont(QFont("B Nazanin", 12, QFont.Bold))
        title_label.setStyleSheet("color: #212529;")
        
        # تاریخ یادداشت
        date_str = f"{self.note['date']['year']}/{self.note['date']['month']:02d}/{self.note['date']['day']:02d}"
        date_label = QLabel(date_str)
        date_label.setFont(QFont("B Nazanin", 10))
        date_label.setStyleSheet("color: #6c757d;")
        
        # دکمه‌های عملیات
        btn_layout = QHBoxLayout()
        
        view_btn = QPushButton("مشاهده")
        view_btn.setFont(QFont("B Nazanin", 10))
        view_btn.setStyleSheet("""
            QPushButton {
                background-color: #0d6efd;
                color: white;
                border-radius: 5px;
                padding: 5px 10px;
            }
            QPushButton:hover {
                background-color: #0b5ed7;
            }
        """)
        view_btn.clicked.connect(self.view_note)
        
        delete_btn = QPushButton("حذف")
        delete_btn.setFont(QFont("B Nazanin", 10))
        delete_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                border-radius: 5px;
                padding: 5px 10px;
            }
            QPushButton:hover {
                background-color: #bb2d3b;
            }
        """)
        delete_btn.clicked.connect(self.delete_note)
        
        btn_layout.addWidget(view_btn)
        btn_layout.addWidget(delete_btn)
        
        layout.addWidget(title_label)
        layout.addWidget(date_label)
        layout.addLayout(btn_layout)
        
        self.setLayout(layout)
    
    def view_note(self):
        self.parent.show_note_details(self.note)
    
    def delete_note(self):
        reply = QMessageBox.question(
            self, 'حذف یادداشت',
            f"آیا مطمئن هستید که می‌خواهید یادداشت '{self.note['title']}' را حذف کنید؟",
            QMessageBox.Yes | QMessageBox.No, QMessageBox.No
        )
        if reply == QMessageBox.Yes:
            self.parent.delete_note(self.note)

class PersianNoteApp(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("یادداشت‌نویس فارسی")
        self.setWindowIcon(QIcon("note_icon.png"))  # می‌توانید یک آیکون مناسب قرار دهید
        self.setGeometry(100, 100, 800, 600)
        
        # تنظیم فونت پیش‌فرض
        font = QFont("B Nazanin", 12)
        QApplication.setFont(font)
        
        self.notes_file = 'persian_notes.json'
        self.notes = self.load_notes()
        
        self.setup_ui()
        self.setup_notification()
        
    def load_notes(self):
        try:
            with open(self.notes_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
          QMessageBox.critical(self, "خطا", f"خطا در بارگذاری یادداشتها: {str(e)}")
          return []
    
    def save_notes(self):
        with open(self.notes_file, 'w', encoding='utf-8') as f:
            json.dump(self.notes, f, ensure_ascii=False, indent=4)
    
    def setup_ui(self):
        main_widget = QWidget()
        main_layout = QHBoxLayout()
        
        # سایدبار
        sidebar = QFrame()
        sidebar.setFrameShape(QFrame.StyledPanel)
        sidebar.setStyleSheet("background-color: #212529;")
        sidebar_layout = QVBoxLayout()
        
        # عنوان برنامه در سایدبار
        app_title = QLabel("یادداشت‌نویس فارسی")
        app_title.setAlignment(Qt.AlignCenter)
        app_title.setStyleSheet("color: white; font-size: 18px; font-weight: bold;")
        app_title.setFont(QFont("B Nazanin", 14, QFont.Bold))
        
        # دکمه افزودن یادداشت جدید
        add_btn = QPushButton("یادداشت جدید")
        add_btn.setIcon(QIcon("add_icon.png"))  # می‌توانید یک آیکون مناسب قرار دهید
        add_btn.setStyleSheet("""
            QPushButton {
                background-color: #0d6efd;
                color: white;
                border-radius: 5px;
                padding: 10px;
                margin: 10px;
            }
            QPushButton:hover {
                background-color: #0b5ed7;
            }
        """)
        add_btn.clicked.connect(self.show_add_note_dialog)
        
        sidebar_layout.addWidget(app_title)
        sidebar_layout.addWidget(add_btn)
        sidebar_layout.addStretch()
        sidebar.setLayout(sidebar_layout)
        
        # محتوای اصلی
        content = QWidget()
        content_layout = QVBoxLayout()
        
        # عنوان صفحه
        self.page_title = QLabel("همه یادداشت‌ها")
        self.page_title.setStyleSheet("font-size: 16px; font-weight: bold;")
        self.page_title.setFont(QFont("B Nazanin", 14, QFont.Bold))
        
        # اسکرول برای لیست یادداشت‌ها
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        scroll_content = QWidget()
        self.notes_layout = QVBoxLayout()
        self.notes_layout.setAlignment(Qt.AlignTop)
        
        self.update_notes_list()
        
        scroll_content.setLayout(self.notes_layout)
        scroll.setWidget(scroll_content)
        
        content_layout.addWidget(self.page_title)
        content_layout.addWidget(scroll)
        content.setLayout(content_layout)
        
        main_layout.addWidget(sidebar, stretch=1)
        main_layout.addWidget(content, stretch=4)
        main_widget.setLayout(main_layout)
        
        self.setCentralWidget(main_widget)
    
    def update_notes_list(self):
        # پاک کردن لیست فعلی
        for i in reversed(range(self.notes_layout.count())): 
            self.notes_layout.itemAt(i).widget().setParent(None)
        
        # افزودن یادداشت‌های جدید
        if not self.notes:
            empty_label = QLabel("هیچ یادداشتی وجود ندارد. روی دکمه 'یادداشت جدید' کلیک کنید.")
            empty_label.setStyleSheet("color: #6c757d;")
            empty_label.setAlignment(Qt.AlignCenter)
            self.notes_layout.addWidget(empty_label)
        else:
            for note in self.notes:
                note_card = NoteCard(note, self)
                self.notes_layout.addWidget(note_card)
    
    def show_add_note_dialog(self):
        self.dialog = QWidget()
        self.dialog.setWindowTitle("یادداشت جدید")
        self.dialog.setWindowModality(Qt.ApplicationModal)
        self.dialog.setFixedSize(500, 500)
        
        layout = QVBoxLayout()
        
        # عنوان
        title_label = QLabel("عنوان:")
        self.title_input = QLineEdit()
        self.title_input.setPlaceholderText("عنوان یادداشت را وارد کنید")
        
        # متن
        content_label = QLabel("متن یادداشت:")
        self.content_input = QTextEdit()
        self.content_input.setPlaceholderText("متن یادداشت را وارد کنید")
        
        # تقویم شمسی
        date_label = QLabel("تاریخ:")
        self.calendar = PersianCalendarWidget()
        
        # دکمه‌ها
        btn_layout = QHBoxLayout()
        
        save_btn = QPushButton("ذخیره")
        save_btn.setStyleSheet("""
            QPushButton {
                background-color: #198754;
                color: white;
                border-radius: 5px;
                padding: 8px 15px;
            }
            QPushButton:hover {
                background-color: #157347;
            }
        """)
        save_btn.clicked.connect(self.save_new_note)
        
        cancel_btn = QPushButton("انصراف")
        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border-radius: 5px;
                padding: 8px 15px;
            }
            QPushButton:hover {
                background-color: #5c636a;
            }
        """)
        cancel_btn.clicked.connect(self.dialog.close)
        
        btn_layout.addWidget(save_btn)
        btn_layout.addWidget(cancel_btn)
        
        layout.addWidget(title_label)
        layout.addWidget(self.title_input)
        layout.addWidget(content_label)
        layout.addWidget(self.content_input)
        layout.addWidget(date_label)
        layout.addWidget(self.calendar)
        layout.addLayout(btn_layout)
        
        self.dialog.setLayout(layout)
        self.dialog.show()
    
    def save_new_note(self):
        title = self.title_input.text().strip()
        content = self.content_input.toPlainText().strip()
        selected_date = self.calendar.selectedDate()
        
        if not title:
            QMessageBox.warning(self, "خطا", "لطفاً عنوان یادداشت را وارد کنید.")
            return
        
        jalali_date = JalaliDate.fromgregorian(
            year=selected_date.year(),
            month=selected_date.month(),
            day=selected_date.day()
        )
        
        note = {
            'title': title,
            'content': content,
            'date': {
                'year': jalali_date.year,
                'month': jalali_date.month,
                'day': jalali_date.day
            },
            'notified': False
        }
        
        self.notes.append(note)
        self.save_notes()
        self.update_notes_list()
        self.dialog.close()
        
        QMessageBox.information(self, "موفق", "یادداشت با موفقیت ذخیره شد.")
    
    def show_note_details(self, note):
        self.details_dialog = QWidget()
        self.details_dialog.setWindowTitle(note['title'])
        self.details_dialog.setWindowModality(Qt.ApplicationModal)
        self.details_dialog.setFixedSize(500, 500)
        
        layout = QVBoxLayout()
        
        # عنوان
        title_label = QLabel(note['title'])
        title_label.setStyleSheet("font-size: 16px; font-weight: bold;")
        title_label.setAlignment(Qt.AlignCenter)
        date_str = f"{note['date']['year']}/{note['date']['month']:02d}/{note['date']['day']:02d}"
        date_label = QLabel(date_str)
        date_label.setStyleSheet("color: #6c757d;")
        date_label.setAlignment(Qt.AlignCenter)
        
        # متن
        content_label = QTextEdit()
        content_label.setText(note['content'])
        content_label.setReadOnly(True)
        
        # دکمه بستن
        close_btn = QPushButton("بستن")
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border-radius: 5px;
                padding: 8px 15px;
            }
            QPushButton:hover {
                background-color: #5c636a;
            }
        """)
        close_btn.clicked.connect(self.details_dialog.close)
        
        layout.addWidget(title_label)
        layout.addWidget(date_label)
        layout.addWidget(content_label)
        layout.addWidget(close_btn)
        
        self.details_dialog.setLayout(layout)
        self.details_dialog.show()
    
    def delete_note(self, note):
        self.notes = [n for n in self.notes if n != note]
        self.save_notes()
        self.update_notes_list()
    
    def setup_notification(self):
        self.notification_timer = QTimer()
        self.notification_timer.timeout.connect(self.check_notifications)
        self.notification_timer.start(3600000)  # هر ساعت چک کند
    
    def check_notifications(self):
        now = JalaliDate.today()
        
        for note in self.notes:
            now = JalaliDate.today()
            for note in self.notes:
                note_date = JalaliDate(
                    note['date']['year'],
                    note['date']['month'],
                    note['date']['day']
                )
          # اعلان 1 روز قبل
            if note_date - now == timedelta(days=1) and not note['notified']:
                self.show_notification(note)
                note['notified'] = True
                self.save_notes()
    
    def show_notification(self, note):
        title = "یادآوری یادداشت فردا"
        message = f"یادداشت: {note['title']}\nتاریخ: {note['date']['year']}/{note['date']['month']}/{note['date']['day']}"
        
        notification.notify(
            title=title,
            message=message,
            app_name='یادداشت‌نویس فارسی',
            timeout=10
        )

if __name__ == "__main__":
    
    app = QApplication(sys.argv)
    
    # تنظیم استایل کلی برنامه
    app.setStyle("Fusion")
    
    window = PersianNoteApp()
    window.show()
    
    sys.exit(app.exec_())
