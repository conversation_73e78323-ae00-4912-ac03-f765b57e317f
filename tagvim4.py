import tkinter as tk
import customtkinter as ctk
from persiantools.jdatetime import JalaliDate
import holidays
from tkinter import messagebox
import json
from tkinter import Menu
import pandas as pd
from tkinter import filedialog
from PIL import Image, ImageTk
import os
from plyer import notification
import threading
from datetime import timedelta
import time  # اضافه کردن این خط

# تنظیمات اولیه
ctk.set_appearance_mode("light")  # حالت پیش‌فرض: تیره
ctk.set_default_color_theme("blue")  # تم آبی

class PersianCalendarApp:
    def __init__(self, root):
        self.root = root
        self.root.title("تقویم فارسی هوشمند")
        self.root.geometry("1000x750+100+50")
        
        self.farsi_font = ("Tahoma", 12)
        self.label = ctk.CTkLabel(
        root, 
        text="متن فارسی",
        font=self.farsi_font
                )
        # متغیرهای کنترلی
        self.current_date = JalaliDate.today()
        self.ir_holidays = holidays.IR()
        self.theme_mode = "light"
        self.notes = {}  # دیکشنری برای ذخیره یادداشت‌ها
        
        # ایجاد منوی اصلی
        self.create_menu()
        
        # بارگذاری یادداشت‌ها
        self.load_notes() 
        
        # ایجاد اجزای رابط کاربری
        self.create_widgets()
        self.update_calendar()
        self.update_events()
        self.update_upcoming_notes()
        self.notification_thread = threading.Thread(
        target=self.check_upcoming_events,
        daemon=True
            )
        self.notification_thread.start()
        self.notification_settings = {
        'enable': True,
        'advance_days': [1, 3],  # 1 روز و 3 روز قبل یادآوری
        'check_interval': 6 * 60 * 60  # هر 6 ساعت بررسی شود
            }
    
    def create_menu(self):
        menubar = Menu(self.root)
        # منوی فایل (حتی اگر آیکون خطا داشته باشد باید تعریف شود)
        file_menu = Menu(menubar, tearoff=0)
        # ابتدا بدون آیکون ایجاد می‌کنیم
        file_menu.add_command(label="ورود فایل اکسل", command=self.import_excel)
        try:
            #from PIL import Image, ImageTk
            import_icon = Image.open("import_icon.png")
            import_icon = import_icon.resize((16, 16), Image.LANCZOS)
            self.import_icon = ImageTk.PhotoImage(import_icon)
            
            # حذف گزینه قبلی و اضافه کردن با آیکون
            file_menu.delete(0)  # حذف اولین گزینه
            file_menu.add_command(
                label="ورود فایل اکسل", 
                command=self.import_excel,
                image=self.import_icon,
                compound="left"
            )
        except Exception as e:
            print(f"خطا در بارگذاری آیکون: {e}")
        # بدون آیکون ادامه می‌دهیم

        file_menu.add_separator()
        file_menu.add_command(label="خروج", command=self.root.quit)
        menubar.add_cascade(label="فایل", menu=file_menu)

        # منوی تبدیل تاریخ
        convert_menu = Menu(menubar, tearoff=0)
        convert_menu.add_command(label="شمسی به میلادی", command=self.show_j2g_converter)
        convert_menu.add_command(label="میلادی به شمسی", command=self.show_g2j_converter)
        menubar.add_cascade(label="تبدیل تاریخ", menu=convert_menu)
        
        # منوی تنظیمات
        settings_menu = Menu(menubar, tearoff=0)
        settings_menu.add_command(label="تغییر تم", command=self.toggle_theme)
        settings_menu.add_command(label="تنظیمات یادآوری", command=self.show_notification_settings)
        menubar.add_cascade(label="تنظیمات", menu=settings_menu)
        
        self.root.config(menu=menubar)
    
    def show_notification_settings(self):
        settings_window = ctk.CTkToplevel(self.root)
        settings_window.title("تنظیمات یادآوری")
        settings_window.geometry("500x400")
        settings_window.focus_set()  # تمرکز را به پنجره یادداشت بدهید
        settings_window.grab_set()
        # تنظیم جهت نوشتار راست به چپ
        settings_window.grid_columnconfigure(0, weight=1)
        enable_var = ctk.BooleanVar(value=self.notification_settings['enable'])
        # ایجاد فریم اصلی با جهت RTL
        main_frame = ctk.CTkFrame(settings_window)
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # بخش فعال/غیرفعال کردن
        enable_frame = ctk.CTkFrame(main_frame)
        enable_frame.pack(fill="x", pady=10)
        
        ctk.CTkCheckBox(
            enable_frame,
            text="",
            variable=ctk.BooleanVar(value=self.notification_settings['enable']),
            width=20
        ).pack(side="right")
        
        ctk.CTkLabel(
            enable_frame,
            text="فعال کردن یادآوری‌ها",
            font=self.farsi_font
        ).pack(side="right", padx=5)
        
        # بخش روزهای قبل
        days_frame = ctk.CTkFrame(main_frame)
        days_frame.pack(fill="x", pady=10)
        
        ctk.CTkLabel(
            days_frame,
            text="روزهای قبل از رویداد (با کاما جدا کنید):",
            font=self.farsi_font
        ).pack(side="right")
        
        days_entry = ctk.CTkEntry(
            days_frame,
            placeholder_text="مثال: 1,3,7",
            font=self.farsi_font
        )
        days_entry.pack(side="right", padx=5)
        days_entry.insert(0, ','.join(map(str, self.notification_settings['advance_days'])))
        
        # دکمه ذخیره
        save_btn = ctk.CTkButton(
            main_frame,
            text="ذخیره تنظیمات",
            command=lambda: self.save_settings(enable_var.get(), days_entry.get()),
            font=self.farsi_font
        )
        save_btn.pack(pady=20)
        
        # ذخیره تنظیمات
    def save_settings(self, enable, days_str):
            try:
                self.notification_settings = {
                    'enable': enable,
                    'advance_days': [int(d.strip()) for d in days_str.split(',')],
                    'check_interval': 6 * 60 * 60
                }
                messagebox.showinfo("ذخیره شد", "تنظیمات با موفقیت ذخیره شد")
            except ValueError:
                messagebox.showerror("خطا", "فرمت روزها نامعتبر است. فقط اعداد وارد کنید")
    

    def import_excel(self):
        # باز کردن دیالوگ انتخاب فایل
        file_path = filedialog.askopenfilename(
            title="فایل اکسل را انتخاب کنید",
            filetypes=[("Excel files", "*.xlsx *.xls"), ("All files", "*.*")]
        )
        
        if not file_path:
            return  # اگر کاربر فایلی انتخاب نکرد
        
        try:
            # خواندن فایل اکسل
            df = pd.read_excel(file_path)
            
            # بررسی ستون‌های ضروری
            required_columns = ['تاریخ', 'وقایع']
            for col in required_columns:
                if col not in df.columns:
                    raise ValueError(f"ستون {col} در فایل اکسل وجود ندارد")
            
            # پردازش هر سطر از داده‌ها
            for _, row in df.iterrows():
                date_str = str(row['تاریخ']).strip()
                note = str(row['وقایع']).strip()
                
                # استخراج سال از تاریخ جاری (اگر در اکسل سال وجود نداشت)
                current_year = JalaliDate.today().year
                jalali_date = self.parse_jalali_date(date_str, current_year)
                
                if jalali_date:
                    date_key = jalali_date.strftime("%Y/%m/%d")
                    
                    # اگر تاریخ از قبل یادداشت داشت، یادداشت جدید را به آن اضافه کن
                    if date_key in self.notes:
                        self.notes[date_key] += f"\n\n● {note}"
                    else:
                        self.notes[date_key] = f"● {note}"
                    
                    # اضافه کردن اطلاعات حوزه و اهمیت اگر وجود دارند
                    if 'حوزه' in row and pd.notna(row['حوزه']):
                        self.notes[date_key] += f"\nحوزه: {row['حوزه']}"
                    if 'درجه اهمیت' in row and pd.notna(row['درجه اهمیت']):
                        self.notes[date_key] += f"\nاهمیت: {row['درجه اهمیت']}"
            
            # ذخیره و به‌روزرسانی تقویم
            self.save_notes_to_file()
            self.update_calendar()
            self.update_upcoming_notes()
            messagebox.showinfo("موفق", "داده‌های اکسل با موفقیت وارد تقویم شدند")
        
        except Exception as e:
            messagebox.showerror("خطا", f"خطا در پردازش فایل اکسل:\n{str(e)}")

    def parse_jalali_date(self, date_str, default_year):
        """تبدیل رشته تاریخ به شیء JalaliDate"""
        try:
            # اگر تاریخ شامل سال باشد (مثلا 1403/01/13)
            if '/' in date_str:
                parts = date_str.split('/')
                if len(parts) == 3:
                    return JalaliDate(int(parts[0]), int(parts[1]), int(parts[2]))
                elif len(parts) == 2:
                    return JalaliDate(default_year, int(parts[0]), int(parts[1]))
            
            # اگر تاریخ به صورت "13 فروردین" باشد
            month_names = {
                'فروردین': 1, 'اردیبهشت': 2, 'خرداد': 3,
                'تیر': 4, 'مرداد': 5, 'شهریور': 6,
                'مهر': 7, 'آبان': 8, 'آذر': 9,
                'دی': 10, 'بهمن': 11, 'اسفند': 12
            }
            
            for month_name, month_num in month_names.items():
                if month_name in date_str:
                    day = int(date_str.split()[0])
                    return JalaliDate(default_year, month_num, day)
                    
            return None
        except:
            return None

    def check_upcoming_events(self):
        while True:
            tomorrow = JalaliDate.today() + timedelta(days=1)
            date_str = tomorrow.strftime("%Y/%m/%d")
            
            if date_str in self.notes:
                self.show_notification(
                    "یادآوری رویداد فردا",
                    f"فردا رویداد دارید:\n{self.notes[date_str]}"
                )
            
            # چک هر 2 ساعت
            time.sleep(2 * 60 * 60)

    def show_notification(self,title, message):
        notification.notify(
            title=title,
            message=message,
            app_name='تقویم فارسی',
            timeout=10
        )

    def show_j2g_converter(self):
        """نمایش پنجره تبدیل تاریخ شمسی به میلادی"""
        dialog = ctk.CTkToplevel(self.root)
        dialog.title("تبدیل تاریخ شمسی به میلادی")
        dialog.geometry("400x200")
        dialog.resizable(False, False)
        
        ctk.CTkLabel(
            dialog, 
            text="تاریخ شمسی را وارد کنید (مثال: 1403/05/15):",
            font=("Tahoma", 12)
        ).pack(pady=10)
        
        entry = ctk.CTkEntry(dialog)
        entry.pack(pady=5)
        
        def convert():
            jalali_date = entry.get()
            try:
                year, month, day = map(int, jalali_date.split('/'))
                gregorian_date = JalaliDate(year, month, day).to_gregorian()
                result_label.configure(text=f"تاریخ میلادی: {gregorian_date.strftime('%Y/%m/%d')}")
            except:
                messagebox.showerror("خطا", "فرمت تاریخ نامعتبر!\nمثال صحیح: 1403/05/15")
        
        ctk.CTkButton(
            dialog,
            text="تبدیل",
            command=convert
        ).pack(pady=10)
        
        result_label = ctk.CTkLabel(dialog, text="")
        result_label.pack(pady=10)
    
    def show_g2j_converter(self):
        """نمایش پنجره تبدیل تاریخ میلادی به شمسی"""
        dialog = ctk.CTkToplevel(self.root)
        dialog.title("تبدیل تاریخ میلادی به شمسی")
        dialog.geometry("400x200")
        dialog.resizable(False, False)
        
        ctk.CTkLabel(
            dialog, 
            text="تاریخ میلادی را وارد کنید (مثال: 2024/08/05):",
            font=("Tahoma", 12)
        ).pack(pady=10)
        
        entry = ctk.CTkEntry(dialog)
        entry.pack(pady=5)
        
        def convert():
            gregorian_date = entry.get()
            try:
                year, month, day = map(int, gregorian_date.split('/'))
                jalali_date = JalaliDate.from_gregorian(year, month, day)
                result_label.configure(text=f"تاریخ شمسی: {jalali_date.strftime('%Y/%m/%d')}")
            except:
                messagebox.showerror("خطا", "فرمت تاریخ نامعتبر!\nمثال صحیح: 2024/08/05")
        
        ctk.CTkButton(
            dialog,
            text="تبدیل",
            command=convert
        ).pack(pady=10)
        
        result_label = ctk.CTkLabel(dialog, text="")
        result_label.pack(pady=10)

    def add_note(self, date_str, note_text):
        """اضافه کردن یادداشت برای یک تاریخ"""
        self.notes[date_str] = note_text

    def create_widgets(self):
        # نوار ابزار بالا
        self.toolbar = ctk.CTkFrame(self.root)
        self.toolbar.pack(pady=10, padx=10, fill="x")
        
        # دکمه‌های ناوبری
        self.btn_prev = ctk.CTkButton(
            self.toolbar, 
            text=" ماه قبل 🢂", 
            command=self.prev_month,
            font=("Tahoma", 14, "bold"),
            width=120,
            height=40,
            corner_radius=10
        )
        self.btn_prev.pack(side="right", padx=5)
        
        self.btn_next = ctk.CTkButton(
            self.toolbar, 
            text="🢀 ماه بعد ", 
            command=self.next_month,
            font=("Tahoma", 14, "bold"),
            width=120,
            height=40,
            corner_radius=10
        )
        self.btn_next.pack(side="left", padx=5)
        
        # عنوان ماه
        self.lbl_month = ctk.CTkLabel(
            self.toolbar,
            font=("Tahoma", 18, "bold"),
            text_color="#3498db"
        )
        self.lbl_month.pack(side="top", pady=10)
        

        # فریم اصلی برای تقویم و سایدبار
        main_frame = ctk.CTkFrame(self.root)
        main_frame.pack(fill="both", expand=True, padx=10, pady=5)
        
        # تقویم
        self.cal_frame = ctk.CTkFrame(main_frame)
        self.cal_frame.pack(side="right", fill="both", expand=True, padx=5, pady=5)
        
        # روزهای هفته (از راست به چپ)
        self.day_names = ["شنبه", "یکشنبه", "دوشنبه", "سه‌شنبه", "چهارشنبه", "پنجشنبه", "جمعه"]
        for i, name in enumerate(reversed(self.day_names)):
            lbl = ctk.CTkLabel(
                self.cal_frame,
                text=name,
                font=("Tahoma", 12, "bold"),
                text_color="#e74c3c",
                width=100,
                height=40
            )
            lbl.grid(row=0, column=i, padx=2, pady=2)
        
        # سلول‌های تقویم
        self.day_cells = []
        for row in range(6):
            for col in range(7):
                cell = ctk.CTkFrame(
                    self.cal_frame,
                    width=100,
                    height=100,
                    corner_radius=8,
                    border_width=1,
                    border_color="#34495e"
                )
                cell.grid(row=row+1, column=6-col, padx=2, pady=2)  # چینش از راست به چپ
                self.day_cells.append(cell)
        
        # پنل کناری
        self.sidebar = ctk.CTkFrame(main_frame, width=300)
        self.sidebar.pack(side="left", fill="y", padx=5, pady=5)
        
        # نمایش تاریخ امروز
        today_frame = ctk.CTkFrame(self.sidebar)
        today_frame.pack(fill="x", pady=10, padx=5)
        
        self.lbl_today = ctk.CTkLabel(
            today_frame,
            text="",
            font=("Tahoma", 14, "bold"),
            text_color="#f39c12"
        )
        self.lbl_today.pack()
        
        # نمایش مناسبت‌های امروز
        self.events_frame = ctk.CTkFrame(self.sidebar)
        self.events_frame.pack(fill="x", pady=10, padx=5)
        
        ctk.CTkLabel(
            self.events_frame,
            text="مناسبت‌های امروز:",
            font=("Tahoma", 12, "bold")
        ).pack(pady=5)
        
        self.lbl_events = ctk.CTkLabel(
            self.events_frame,
            text="",
            font=("Tahoma", 11),
            wraplength=280,
            justify="right"
        )
        self.lbl_events.pack(pady=5)
        
        # نمایش یادداشت‌های آینده
        self.upcoming_notes_frame = ctk.CTkFrame(self.sidebar)
        self.upcoming_notes_frame.pack(fill="x", pady=10, padx=5)
        
        ctk.CTkLabel(
            self.upcoming_notes_frame,
            text="یادداشت‌های آینده:",
            font=("Tahoma", 12, "bold")
        ).pack(pady=5)
        
        self.upcoming_notes_list = ctk.CTkTextbox(
            self.upcoming_notes_frame,
            height=150,
            state="disabled",
            wrap="word",
            font=("Tahoma", 11)
        )
        self.upcoming_notes_list.pack(fill="x", pady=5)
        
        # پنل یادداشت‌ها
        self.notes_frame = ctk.CTkFrame(self.sidebar)
        self.notes_frame.pack(fill="x", pady=10, padx=5)
        
        ctk.CTkLabel(
            self.notes_frame,
            text="یادداشت روز:",
            font=("Tahoma", 12, "bold")
        ).pack(pady=5)
        
        self.note_date_entry = ctk.CTkEntry(
            self.notes_frame,
            placeholder_text="1403/05/15",
            font=("Tahoma", 12)
        )
        self.note_date_entry.pack(fill="x", pady=5)
        
        self.note_text = ctk.CTkTextbox(
            self.notes_frame,
            height=100,
            wrap="word",
            font=("Tahoma", 12)
        )
        self.note_text.pack(fill="x", pady=5)
        
        self.btn_save_note = ctk.CTkButton(
            self.notes_frame,
            text="ذخیره یادداشت",
            command=self.save_note,
            fg_color="#27ae60",
            hover_color="#2ecc71",
            font=("Tahoma", 12)
        )
        self.btn_save_note.pack(pady=5)

    def save_note(self):
        date_str = self.note_date_entry.get()
        note = self.note_text.get("1.0", "end").strip()
        
        try:
            year, month, day = map(int, date_str.split('/'))
            jalali_date = JalaliDate(year, month, day)
            self.notes[jalali_date.strftime("%Y/%m/%d")] = note
            self.save_notes_to_file()
            messagebox.showinfo("موفق", "یادداشت با موفقیت ذخیره شد")
            self.update_upcoming_notes()
            self.update_calendar()
        except:
            messagebox.showerror("خطا", "فرمت تاریخ نامعتبر!")

    def load_notes(self):
        try:
            with open("notes.json", "r", encoding="utf-8") as f:
                self.notes = json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            self.notes = {}

    def save_notes_to_file(self):
        with open("notes.json", "w", encoding="utf-8") as f:
            json.dump(self.notes, f, ensure_ascii=False)

    def update_upcoming_notes(self):
        """به روزرسانی لیست یادداشت‌های آینده"""
        today = JalaliDate.today()
        upcoming = []
        
        for date_str, note in self.notes.items():
            try:
                year, month, day = map(int, date_str.split('/'))
                note_date = JalaliDate(year, month, day)
                if note_date >= today:
                    upcoming.append((note_date, note))
            except:
                continue
        
        # مرتب‌سازی بر اساس تاریخ
        upcoming.sort(key=lambda x: x[0])
        
        # نمایش یادداشت‌های آینده
        self.upcoming_notes_list.configure(state="normal")
        self.upcoming_notes_list.delete("1.0", "end")
        
        for date, note in upcoming[:5]:  # نمایش 5 یادداشت آینده
            self.upcoming_notes_list.insert(
                "end", 
                f"{date.strftime('%Y/%m/%d')}:\n{note}\n{'='*20}\n"
            )
        
        self.upcoming_notes_list.configure(state="disabled")
   
    
    def update_calendar(self):
        # پاکسازی سلول‌های قبلی و رویدادها
        for cell in self.day_cells:
            for widget in cell.winfo_children():
                widget.destroy()
            # پاکسازی رویدادهای قبلی
            cell.unbind("<Button-1>")
        
        try:
            # اعتبارسنجی و محاسبات اولیه
            if not (1 <= self.current_date.month <= 12):
                raise ValueError("ماه نامعتبر است")
                
            month_days = self.get_month_days(self.current_date.year, self.current_date.month)
            first_day = JalaliDate(self.current_date.year, self.current_date.month, 1).weekday()
            
            # نمایش روزها
            day_counter = 1  # استفاده از نام متفاوت برای جلوگیری از اشتباه
            for week in range(6):
                for d in range(7):
                    idx = week * 7 + d
                    if (week == 0 and d < first_day) or day_counter > month_days:
                        continue
                    
                    cell = self.day_cells[idx]
                    self.create_day_cell(cell, day_counter)
                    day_counter += 1
            
            # نمایش یادداشتها
            self.show_notes(first_day, month_days)
            
            # به‌روزرسانی اطلاعات ماه
            month_name = JalaliDate(self.current_date.year, self.current_date.month, 1).strftime("%B")
            self.lbl_month.configure(text=f"{month_name} {self.current_date.year}")
            self.lbl_today.configure(text=f"امروز: {JalaliDate.today().strftime('%Y/%m/%d')}")
            
        except ValueError as e:
            messagebox.showerror("خطا", f"خطا در نمایش تقویم: {str(e)}")
            self.current_date = JalaliDate.today()
            self.update_calendar()

    def create_day_cell(self, cell, day):
        """ایجاد سلول یک روز در تقویم"""
        # تعیین رنگ‌ها
        bg_color = "#2d3436" if self.theme_mode == "dark" else "#ecf0f1"
        text_color = "white" if self.theme_mode == "dark" else "black"
        
        # بررسی تعطیلی
        if self.is_holiday(self.current_date.year, self.current_date.month, day):
            bg_color = "#e74c3c" if self.theme_mode == "dark" else "#ff7979"
        
        # بررسی امروز
        today = JalaliDate.today()
        is_today = (day == today.day and 
                self.current_date.month == today.month and 
                self.current_date.year == today.year)
        
        if is_today:
            bg_color = "#3498db" if self.theme_mode == "dark" else "#0984e3"
            text_color = "white"
        
        # ایجاد برچسب روز
        label = ctk.CTkLabel(
            cell,
            text=str(day),
            font=("Tahoma", 14, "bold"),
            text_color=text_color,
            fg_color=bg_color,
            corner_radius=8,
            width=100,
            height=80
        )
        label.pack(fill="both", expand=True)

    def show_notes(self, first_day, month_days):
        """نمایش یادداشتها برای روزهای ماه جاری"""
        for week in range(6):
            for d in range(7):
                idx = week * 7 + d
                day = week * 7 + d - first_day + 1
                if 1 <= day <= month_days:
                    date_str = JalaliDate(
                        self.current_date.year,
                        self.current_date.month,
                        day
                    ).strftime("%Y/%m/%d")
                    
                    if date_str in self.notes:
                        cell = self.day_cells[idx]
                        # اضافه کردن نشانگر یادداشت
                        note_indicator = ctk.CTkLabel(
                            cell,
                            text="📝",
                            font=("Arial", 12),
                            fg_color="transparent"
                        )
                        note_indicator.place(relx=0.8, rely=0.1)
                        
                        
                        
                        # متصل کردن رویداد کلیک به نشانگر یادداشت
                        note_indicator.bind("<Button-1>", 
                        lambda e, d=date_str, n=self.notes[date_str]: self.show_note_on_click(e.widget, d, n))
                    
                    # همچنین می‌توانید نشانگر را قابل کلیک به نظر برسانید
                        note_indicator.configure(cursor="hand2")


    def show_note_on_click(self, widget, date_str, note_text):
            # اگر پنجره از قبل باز است، جلوگیری از ایجاد تکراری
            if hasattr(widget, '_note_window') and widget._note_window.winfo_exists():
                widget._note_window.lift()
                return
                
            note_window = ctk.CTkToplevel(self.root)
            note_window.title(f"یادداشت {date_str}")
            note_window.geometry("500x400")
            widget._note_window = note_window  # ذخیره reference
            
            def save_and_refresh():
                new_text = note_entry.get("1.0", "end-1c")
                self.notes[date_str] = new_text
                self.save_notes_to_file()
                self.update_calendar()  # به‌روزرسانی تقویم
                self.update_upcoming_notes()
                note_window.destroy()
                messagebox.showinfo("ذخیره شد", "یادداشت با موفقیت به‌روزرسانی شد")
            
            # محتوای پنجره
            ctk.CTkLabel(
                note_window,
                text=f"یادداشت برای تاریخ {date_str}:",
                font=("Tahoma", 14)
            ).pack(pady=10)
            
            note_entry = ctk.CTkTextbox(
                note_window,
                wrap="word",
                font=("Tahoma", 12),
                height=250
            )
            note_entry.insert("1.0", note_text)
            note_entry.pack(fill="both", expand=True, padx=10, pady=10)
            
            # دکمه‌های پایین
            btn_frame = ctk.CTkFrame(note_window)
            btn_frame.pack(pady=10)
            
            ctk.CTkButton(
                btn_frame,
                text="ذخیره",
                command=save_and_refresh,
                width=100,
                fg_color="#27ae60"
            ).pack(side="left", padx=10)
            
            ctk.CTkButton(
                btn_frame,
                text="بستن",
                command=note_window.destroy,
                width=100
            ).pack(side="right", padx=10)
            note_window.focus_set()  # تمرکز را به پنجره یادداشت بدهید
            note_window.grab_set()  # مدال بودن پنجره
            note_window.protocol("WM_DELETE_WINDOW", note_window.destroy)
        

    def add_tooltip(self, widget, text):
        # اگر از قبل tooltip وجود دارد، آن را حذف کن
        if hasattr(widget, '_tooltip_label'):
            widget._tooltip_label.destroy()
            del widget._tooltip_label
        
        # ایجاد یک لیبل شناور برای tooltip
        tooltip_label = ctk.CTkLabel(
            self.root,
            text=text,
            fg_color="#fffacd",
            text_color="#000000",
            corner_radius=5,
            font=("Tahoma", 11),
            justify="right",
            wraplength=250,
            padx=10,
            pady=5
        )
        
        # مخفی کردن اولیه
        tooltip_label.place_forget()
        
        # ذخیره reference به عنوان attribute ویجت
        widget._tooltip_label = tooltip_label
        
        def show_tooltip(event):
            # محاسبه موقعیت
            x = widget.winfo_rootx() - self.root.winfo_rootx() + (widget.winfo_width() // 2)
            y = widget.winfo_rooty() - self.root.winfo_rooty() + widget.winfo_height() + 5
            
            # نمایش tooltip
            tooltip_label.place(x=x, y=y)
            tooltip_label.lift()
        
        def hide_tooltip(event):
            tooltip_label.place_forget()
        
        # bind رویدادها
        widget.bind("<Enter>", show_tooltip)
        widget.bind("<Leave>", hide_tooltip)
        widget.bind("<ButtonPress>", hide_tooltip)


    def get_month_days(self, year, month):
        """محاسبه تعداد روزهای ماه شمسی با بررسی سال کبیسه"""
        if 1 <= month <= 6:
            return 31
        elif 7 <= month <= 11:
            return 30
        elif month == 12:
            # استفاده از متد isleap از JalaliDate
            #return 29 if JalaliDate(year, 1, 1).isleap() else 28
            return 29 if ((year + 2346) * 683) % 2820 < 683 else 28
        else:
            raise ValueError("ماه باید بین 1 تا 12 باشد")
                   
                
    def prev_month(self):
        try:
            new_month = self.current_date.month - 1
            new_year = self.current_date.year
            
            if new_month < 1:
                new_month = 12
                new_year -= 1
                
            # بررسی معتبر بودن سال
            if new_year < 1300 or new_year > 1500:  # محدوده ایمن
                raise ValueError("سال خارج از محدوده پشتیبانی شده")
                
            self.current_date = JalaliDate(new_year, new_month, 1)
            self.update_calendar()
            
        except Exception as e:
            import traceback
            traceback.print_exc()  # برای دیباگ
            messagebox.showerror("خطا", f"خطا در تغییر ماه: {str(e)}")
            self.current_date = JalaliDate.today()
            self.update_calendar()

    def next_month(self):
        try:
            new_month = self.current_date.month + 1
            new_year = self.current_date.year
            
            if new_month > 12:
                new_month = 1
                new_year += 1
                
            # بررسی معتبر بودن سال
            if new_year < 1300 or new_year > 1500:
                raise ValueError("سال خارج از محدوده پشتیبانی شده")
                
            self.current_date = JalaliDate(new_year, new_month, 1)
            self.update_calendar()
            
        except Exception as e:
            import traceback
            traceback.print_exc()
            messagebox.showerror("خطا", f"خطا در تغییر ماه: {str(e)}")
            self.current_date = JalaliDate.today()
            self.update_calendar()


    def is_holiday(self, year, month, day):
        try:
            date = JalaliDate(year, month, day).to_gregorian()
            return date in self.ir_holidays
        except:
            return False

    def update_events(self):
        today_events = []
        gregorian_today = JalaliDate.today().to_gregorian()
        
        for date, name in self.ir_holidays.items():
            if date == gregorian_today:
                today_events.append(name)
        
        if today_events:
            self.lbl_events.configure(text="\n".join(today_events))
        else:
            self.lbl_events.configure(text="هیچ مناسبت رسمی وجود ندارد")

    def toggle_theme(self):
        self.theme_mode = "light" if self.theme_mode == "dark" else "dark"
        ctk.set_appearance_mode(self.theme_mode)
        self.update_calendar()

if __name__ == "__main__":
    root = ctk.CTk()
    app = PersianCalendarApp(root)
    root.mainloop()