# بسم الله الرحمن الرحبم
import customtkinter as ctk
import os
from PIL import Image, ImageTk
import sqlite3
from database import Database
from user_login import LoginWindow
from invoice_manager import InvoiceManager
from invoice_viewer import InvoiceViewer

# تنظیم ظاهر برنامه
ctk.set_appearance_mode("System")  # حالت‌ها: "System" (پیش‌فرض)، "Dark"، "Light"
ctk.set_default_color_theme("blue")  # تم‌ها: "blue" (پیش‌فرض)، "green"، "dark-blue"

class JewelryStoreApp(ctk.CTk):
    def __init__(self):
        super().__init__()

        # تنظیمات پنجره اصلی
        self.title("سیستم مدیریت طلافروشی")
        self.geometry("1200x700")
        self.configure(bg="#c9bcfb")
        self.minsize(1000, 600)

        # تنظیم رویداد بستن پنجره
        self.protocol("WM_DELETE_WINDOW", self.on_close)

        # اتصال به پایگاه داده
        self.db = Database()

        # اطلاعات کاربر فعلی
        self.current_user = None
        self.user_permissions = []

        # مدیریت فاکتورها
        self.invoice_manager = InvoiceManager(self.db)

        # ایجاد پوشه assets اگر وجود ندارد
        if not os.path.exists("assets"):
            os.makedirs("assets")

        # نمایش پنجره ورود
        self.withdraw()  # مخفی کردن پنجره اصلی
        self.after(100, self.show_login_window)  # استفاده از after برای اطمینان از نمایش صحیح پنجره ورود

    def on_close(self):
        """رویداد بستن پنجره اصلی"""
        # ثبت خروج کاربر
        if self.current_user:
            self.db.log_activity(self.current_user["id"], "logout", "خروج از سیستم")

        # بستن پنجره
        self.destroy()

    def generate_invoice(self, sale_id):
        """ایجاد فاکتور برای یک فروش"""
        # بررسی دسترسی
        if not self.check_permission("view_sale_details"):
            self.show_permission_error()
            return None

        # بررسی وجود فاکتور قبلی
        existing_invoice_path = self.db.get_sale_invoice_path(sale_id)
        if existing_invoice_path and os.path.exists(existing_invoice_path):
            return existing_invoice_path

        # ایجاد فاکتور جدید
        invoice_path = self.invoice_manager.generate_invoice(sale_id)

        if invoice_path:
            # به‌روزرسانی مسیر فاکتور در پایگاه داده
            self.db.update_sale_invoice_path(sale_id, invoice_path)

            # ثبت فعالیت
            if self.current_user:
                self.db.log_activity(
                    self.current_user["id"],
                    "generate_invoice",
                    f"ایجاد فاکتور برای فروش با شناسه {sale_id}"
                )

        return invoice_path

    def show_invoice(self, sale_id):
        """نمایش فاکتور یک فروش"""
        # بررسی دسترسی
        if not self.check_permission("view_sale_details"):
            self.show_permission_error()
            return

        # ایجاد فاکتور
        invoice_path = self.generate_invoice(sale_id)

        if not invoice_path:
            self.show_error("خطا در ایجاد فاکتور")
            return

        # نمایش پیش‌نمایش فاکتور
        invoice_viewer = InvoiceViewer(
            self,
            invoice_path,
            sale_id,
            on_print=self.invoice_manager.print_invoice
        )

    def print_invoice(self, sale_id):
        """چاپ فاکتور یک فروش"""
        # بررسی دسترسی
        if not self.check_permission("view_sale_details"):
            self.show_permission_error()
            return False

        # ایجاد فاکتور
        invoice_path = self.generate_invoice(sale_id)

        if not invoice_path:
            self.show_error("خطا در ایجاد فاکتور")
            return False

        # چاپ فاکتور
        success = self.invoice_manager.print_invoice(invoice_path)

        if success:
            # ثبت فعالیت
            if self.current_user:
                self.db.log_activity(
                    self.current_user["id"],
                    "print_invoice",
                    f"چاپ فاکتور برای فروش با شناسه {sale_id}"
                )

        return success

    def show_login_window(self):
        """نمایش پنجره ورود به سیستم"""
        # ایجاد پنجره ورود
        login_window = LoginWindow(self, on_login_success=self.on_login_success)

        # منتظر بستن پنجره ورود می‌مانیم
        self.wait_window(login_window)

        # اگر کاربر وارد نشده باشد، برنامه را ببند
        if not self.current_user:
            self.destroy()
            return

        # ایجاد چیدمان اصلی
        self.create_layout()

        # ایجاد منوی اصلی
        self.create_navigation()

        # ایجاد صفحه خانه (پیش‌فرض)
        self.create_home_frame()

        # انتخاب صفحه پیش‌فرض
        self.select_frame_by_name("home")

        # نمایش پنجره اصلی
        self.deiconify()

        # به‌روزرسانی پنجره
        self.update()

    def on_login_success(self, user):
        """فراخوانی پس از ورود موفق کاربر"""
        self.current_user = user
        self.user_permissions = self.db.get_user_permissions(user["id"])

        # ثبت فعالیت ورود
        self.db.log_activity(user["id"], "login", "ورود به سیستم")

        # نمایش پیام خوش‌آمدگویی
        print(f"کاربر {user['username']} با موفقیت وارد شد")

    def check_permission(self, permission_name):
        """بررسی دسترسی کاربر به یک قابلیت خاص"""
        if not self.current_user:
            return False

        # کاربر admin دسترسی کامل دارد
        if self.current_user["role_name"] == "admin":
            return True

        # بررسی وجود مجوز در لیست مجوزهای کاربر
        return permission_name in self.user_permissions

    def show_permission_error(self):
        """نمایش پیام خطای عدم دسترسی"""
        self.show_error("شما مجوز دسترسی به این بخش را ندارید")

    def show_error(self, message):
        """نمایش پیام خطا"""
        from tkinter import messagebox
        messagebox.showerror("خطا", message)

    def show_success(self, message):
        """نمایش پیام موفقیت"""
        from tkinter import messagebox
        messagebox.showinfo("موفقیت", message)

    def create_layout(self):
        """ایجاد چیدمان اصلی برنامه"""
        # تنظیم گرید (2x1)
        self.grid_columnconfigure(0, weight=1)  # ستون محتوا وزن بیشتری دارد
        self.grid_columnconfigure(1, weight=0)  # ستون منو وزن کمتری دارد
        self.grid_rowconfigure(0, weight=1)

    def create_navigation(self):
        """ایجاد منوی ناوبری کناری (سمت راست)"""
        # ایجاد فریم ناوبری
        self.navigation_frame = ctk.CTkFrame(self, corner_radius=0, width=200)  # تنظیم عرض ثابت
        self.navigation_frame.grid(row=0, column=1, sticky="nsew")  # تغییر ستون از 0 به 1
        self.navigation_frame.grid_rowconfigure(7, weight=1)
        self.navigation_frame.grid_propagate(False)  # جلوگیری از تغییر اندازه خودکار

        # عنوان منوی ناوبری
        self.navigation_frame_label = ctk.CTkLabel(
            self.navigation_frame, text="فروشی طلا مدیریت سیستم",  # ترتیب معکوس کلمات
            compound="right",  # تغییر از "left" به "right"
            font=ctk.CTkFont(size=15, weight="bold"),
            anchor="e"  # راست چین کردن متن
        )
        self.navigation_frame_label.grid(row=0, column=0, padx=20, pady=20, sticky="e")

        # دکمه خانه
        self.home_button = ctk.CTkButton(
            self.navigation_frame, corner_radius=0, height=40, border_spacing=10,
            text="صفحه اصلی ",  # ترتیب معکوس کلمات
            font=("Arial", 14,"bold"),
            fg_color="transparent", text_color=("gray10", "gray90"),
            hover_color=("gray70", "gray30"),
            anchor="e",  # تغییر از "w" به "e" برای راست به چپ
            command=self.home_button_event
        )
        self.home_button.grid(row=1, column=0, sticky="ew")

        # دکمه محصولات
        self.products_button = ctk.CTkButton(
            self.navigation_frame, corner_radius=0, height=40, border_spacing=10,
            text="محصولات",  # کلمه تک بخشی نیاز به معکوس کردن ندارد
            font=("Arial", 14,"bold"),
            fg_color="transparent", text_color=("gray10", "gray90"),
            hover_color=("gray70", "gray30"),
            anchor="e",  # تغییر از "w" به "e" برای راست به چپ
            command=self.products_button_event
        )
        self.products_button.grid(row=2, column=0, sticky="ew")

        # دکمه مشتریان
        self.customers_button = ctk.CTkButton(
            self.navigation_frame, corner_radius=0, height=40, border_spacing=10,
            text="مشتریان",  # کلمه تک بخشی نیاز به معکوس کردن ندارد
            font=("Arial", 14,"bold"),
            fg_color="transparent", text_color=("gray10", "gray90"),
            hover_color=("gray70", "gray30"),
            anchor="e",  # تغییر از "w" به "e" برای راست به چپ
            command=self.customers_button_event
        )
        self.customers_button.grid(row=3, column=0, sticky="ew")

        # دکمه فروش
        self.sales_button = ctk.CTkButton(
            self.navigation_frame, corner_radius=0, height=40, border_spacing=10,
            text="فروش",  # کلمه تک بخشی نیاز به معکوس کردن ندارد
            font=("Arial", 14,"bold"),
            fg_color="transparent", text_color=("gray10", "gray90"),
            hover_color=("gray70", "gray30"),
            anchor="e",  # تغییر از "w" به "e" برای راست به چپ
            command=self.sales_button_event
        )
        self.sales_button.grid(row=4, column=0, sticky="ew")

        # دکمه موجودی
        self.inventory_button = ctk.CTkButton(
            self.navigation_frame, corner_radius=0, height=40, border_spacing=10,
            text="موجودی",  # کلمه تک بخشی نیاز به معکوس کردن ندارد
            font=("Arial", 14,"bold"),
            fg_color="transparent", text_color=("gray10", "gray90"),
            hover_color=("gray70", "gray30"),
            anchor="e",  # تغییر از "w" به "e" برای راست به چپ
            command=self.inventory_button_event
        )
        self.inventory_button.grid(row=5, column=0, sticky="ew")

        # دکمه گزارشات
        self.reports_button = ctk.CTkButton(
            self.navigation_frame, corner_radius=0, height=40, border_spacing=10,
            text="گزارشات",  # کلمه تک بخشی نیاز به معکوس کردن ندارد
            font=("Arial", 14,"bold"),
            fg_color="transparent", text_color=("gray10", "gray90"),
            hover_color=("gray70", "gray30"),
            anchor="e",  # تغییر از "w" به "e" برای راست به چپ
            command=self.reports_button_event
        )
        self.reports_button.grid(row=6, column=0, sticky="ew")

        # دکمه پشتیبان‌گیری
        self.backup_button = ctk.CTkButton(
            self.navigation_frame, corner_radius=0, height=40, border_spacing=10,
            text="پشتیبان‌گیری",
            font=("Arial", 14,"bold"),
            fg_color="transparent", text_color=("gray10", "gray90"),
            hover_color=("gray70", "gray30"),
            anchor="e",  # تغییر از "w" به "e" برای راست به چپ
            command=self.backup_button_event
        )
        self.backup_button.grid(row=7, column=0, sticky="ew")

        # دکمه مدیریت کاربران
        self.users_button = ctk.CTkButton(
            self.navigation_frame, corner_radius=0, height=40, border_spacing=10,
            text="مدیریت کاربران",
            font=("Arial", 14,"bold"),
            fg_color="transparent", text_color=("gray10", "gray90"),
            hover_color=("gray70", "gray30"),
            anchor="e",  # تغییر از "w" به "e" برای راست به چپ
            command=self.users_button_event
        )
        self.users_button.grid(row=8, column=0, sticky="ew")

        # منوی تغییر حالت ظاهری
        self.appearance_mode_menu = ctk.CTkOptionMenu(
            self.navigation_frame, values=["روشن", "تاریک", "سیستم"],  # کلمات تک بخشی نیاز به معکوس کردن ندارند
            command=self.change_appearance_mode_event,
            anchor="e"  # راست چین کردن متن
        )
        self.appearance_mode_menu.grid(row=9, column=0, padx=20, pady=20, sticky="se")

    def select_frame_by_name(self, name):
        """انتخاب صفحه با نام و به‌روزرسانی رنگ دکمه‌ها"""
        # تنظیم رنگ دکمه برای دکمه انتخاب شده
        self.home_button.configure(fg_color=("gray75", "gray25") if name == "home" else "transparent")
        self.products_button.configure(fg_color=("gray75", "gray25") if name == "products" else "transparent")
        self.customers_button.configure(fg_color=("gray75", "gray25") if name == "customers" else "transparent")
        self.sales_button.configure(fg_color=("gray75", "gray25") if name == "sales" else "transparent")
        self.inventory_button.configure(fg_color=("gray75", "gray25") if name == "inventory" else "transparent")
        self.reports_button.configure(fg_color=("gray75", "gray25") if name == "reports" else "transparent")
        self.backup_button.configure(fg_color=("gray75", "gray25") if name == "backup" else "transparent")
        self.users_button.configure(fg_color=("gray75", "gray25") if name == "users" else "transparent")

        # نمایش صفحه انتخاب شده
        if name == "home":
            self.home_frame.grid(row=0, column=0, sticky="nsew")  # تغییر ستون از 1 به 0
        else:
            self.home_frame.grid_forget()

        if name == "products":
            self.create_products_frame()
            self.products_frame.grid(row=0, column=0, sticky="nsew")  # تغییر ستون از 1 به 0
        elif hasattr(self, "products_frame"):
            self.products_frame.grid_forget()

        if name == "customers":
            self.create_customers_frame()
            self.customers_frame.grid(row=0, column=0, sticky="nsew")  # تغییر ستون از 1 به 0
        elif hasattr(self, "customers_frame"):
            self.customers_frame.grid_forget()

        if name == "sales":
            self.create_sales_frame()
            self.sales_frame.grid(row=0, column=0, sticky="nsew")  # تغییر ستون از 1 به 0
        elif hasattr(self, "sales_frame"):
            self.sales_frame.grid_forget()

        if name == "inventory":
            self.create_inventory_frame()
            self.inventory_frame.grid(row=0, column=0, sticky="nsew")  # تغییر ستون از 1 به 0
        elif hasattr(self, "inventory_frame"):
            self.inventory_frame.grid_forget()

        if name == "reports":
            self.create_reports_frame()
            self.reports_frame.grid(row=0, column=0, sticky="nsew")  # تغییر ستون از 1 به 0
        elif hasattr(self, "reports_frame"):
            self.reports_frame.grid_forget()

        if name == "backup":
            self.create_backup_frame()
            self.backup_frame.grid(row=0, column=0, sticky="nsew")
        elif hasattr(self, "backup_frame"):
            self.backup_frame.grid_forget()

        if name == "users":
            self.create_users_frame()
            self.users_frame.grid(row=0, column=0, sticky="nsew")
        elif hasattr(self, "users_frame"):
            self.users_frame.grid_forget()

    def home_button_event(self):
        self.select_frame_by_name("home")

    def products_button_event(self):
        if self.check_permission("view_products"):
            self.select_frame_by_name("products")
        else:
            self.show_permission_error()

    def customers_button_event(self):
        if self.check_permission("view_customers"):
            self.select_frame_by_name("customers")
        else:
            self.show_permission_error()

    def sales_button_event(self):
        if self.check_permission("view_sales"):
            self.select_frame_by_name("sales")
        else:
            self.show_permission_error()

    def inventory_button_event(self):
        if self.check_permission("view_inventory"):
            self.select_frame_by_name("inventory")
        else:
            self.show_permission_error()

    def reports_button_event(self):
        if self.check_permission("view_reports"):
            self.select_frame_by_name("reports")
        else:
            self.show_permission_error()

    def backup_button_event(self):
        if self.check_permission("backup_database"):
            self.select_frame_by_name("backup")
        else:
            self.show_permission_error()

    def users_button_event(self):
        if self.check_permission("view_users"):
            self.select_frame_by_name("users")
        else:
            self.show_permission_error()

    def change_appearance_mode_event(self, new_appearance_mode):
        if new_appearance_mode == "روشن":
            ctk.set_appearance_mode("Light")
        elif new_appearance_mode == "تاریک":
            ctk.set_appearance_mode("Dark")
        else:
            ctk.set_appearance_mode("System")

    def create_home_frame(self):
        """ایجاد صفحه خانه با داشبورد"""
        self.home_frame = ctk.CTkFrame(self, corner_radius=0, fg_color="transparent")
        self.home_frame.grid_columnconfigure(0, weight=1)
        # عنوان خوش‌آمدگویی
        self.home_frame_large_label = ctk.CTkLabel(
            self.home_frame, text="تستی طلافروشی مدیریت سیستم ",  # ترتیب معکوس کلمات
            font=ctk.CTkFont(size=20, weight="bold")
        )
        self.home_frame_large_label.grid(row=0, column=0, padx=20, pady=20)

        # فریم داشبورد
        self.dashboard_frame = ctk.CTkFrame(self.home_frame)
        self.dashboard_frame.grid(row=1, column=0, padx=20, pady=20, sticky="nsew")
        self.dashboard_frame.grid_columnconfigure((0, 1, 2), weight=1)

        # کارت‌های داشبورد
        # کارت خلاصه فروش
        self.sales_card = ctk.CTkFrame(self.dashboard_frame)
        self.sales_card.grid(row=0, column=2, padx=10, pady=10, sticky="n")

        self.sales_title = ctk.CTkLabel(
            self.sales_card, text=" امروز فروش ",
            fg_color="#28a745",
            #hover_color="#218838",
            #font=("Arial", 16,"bold")
            font=ctk.CTkFont(size=16, weight="bold")

        )
        self.sales_title.grid(row=0, column=0, padx=10, pady=5)

        self.sales_amount = ctk.CTkLabel(
            self.sales_card, text="0 تومان",
            fg_color="#dc3545",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        self.sales_amount.grid(row=1, column=0, padx=10, pady=5)

        # کارت خلاصه موجودی
        self.inventory_card = ctk.CTkFrame(self.dashboard_frame)
        self.inventory_card.grid(row=0, column=1, padx=10, pady=10, sticky="n")

        self.inventory_title = ctk.CTkLabel(
            self.inventory_card, text="محصولات تعداد ",
            fg_color="#28a745",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        self.inventory_title.grid(row=0, column=0, padx=10, pady=5)

        self.inventory_count = ctk.CTkLabel(
            self.inventory_card, text="0",
            fg_color="#dc3545",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        self.inventory_count.grid(row=1, column=0, padx=10, pady=5)

        # کارت خلاصه مشتریان
        self.customers_card = ctk.CTkFrame(self.dashboard_frame)
        self.customers_card.grid(row=0, column=0, padx=10, pady=10, sticky="n")

        self.customers_title = ctk.CTkLabel(
            self.customers_card, text="مشتریان تعداد ",
            fg_color="#28a745",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        self.customers_title.grid(row=0, column=0, padx=10, pady=5)

        self.customers_count = ctk.CTkLabel(
            self.customers_card, text="0",
            fg_color="#dc3545",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        self.customers_count.grid(row=1, column=0, padx=10, pady=5)

    def create_products_frame(self):
        """ایجاد صفحه مدیریت محصولات"""
        if not hasattr(self, "products_frame"):
            self.products_frame = ctk.CTkFrame(self, corner_radius=0, fg_color="transparent")
            self.products_frame.grid_columnconfigure(0, weight=1,)

            # عنوان محصولات
            self.products_label = ctk.CTkLabel(
                self.products_frame, text="مدیریت محصولات",
                #font=ctk.CTkFont(size=20, weight="bold")
                font=("Arial", 20,"bold")
            )
            self.products_label.grid(row=0, column=0, padx=20, pady=20)

            # ایجاد فریم اصلی محصولات
            self.products_main_frame = ctk.CTkFrame(self.products_frame)
            self.products_main_frame.grid(row=1, column=0, padx=20, pady=20, sticky="n")
            self.products_main_frame.grid_columnconfigure(0, weight=1)
            self.products_main_frame.grid_columnconfigure(1, weight=3)

             # فریم سمت راست برای افزودن/ویرایش محصول
            self.product_form_frame = ctk.CTkFrame(self.products_main_frame)
            self.product_form_frame.grid(row=0, column=1, padx=10, pady=10, sticky="w")


            # عنوان فرم
            self.product_form_title = ctk.CTkLabel(
                self.product_form_frame, text="افزودن محصول جدید",
                #font=ctk.CTkFont(size=16, weight="bold")
                font=("Arial", 16,"bold")
            )
            self.product_form_title.grid(row=0, padx=10, pady=10, sticky="e")

            # فیلدهای فرم
            # کد محصول
            self.code_label = ctk.CTkLabel(self.product_form_frame,font=("Arial", 12,"bold"), text="کد محصول")
            self.code_label.grid(row=1, column=1, padx=10, pady=5, sticky="e")
            self.code_entry = ctk.CTkEntry(self.product_form_frame, width=200,font=("Arial", 12,"bold"),justify="right")
            self.code_entry.grid(row=1, column=0, padx=10, pady=5, sticky="e")

            # نام محصول
            self.name_label = ctk.CTkLabel(self.product_form_frame,font=("Arial", 12,"bold"), text="نام محصول")
            self.name_label.grid(row=2, column=1, padx=10, pady=5, sticky="e")
            self.name_entry = ctk.CTkEntry(self.product_form_frame, width=200,justify="right",font=("Arial", 12,"bold"))
            self.name_entry.grid(row=2, column=0, padx=10, pady=5, sticky="e")

            # دسته‌بندی
            self.category_label = ctk.CTkLabel(self.product_form_frame,font=("Arial", 12,"bold"), text="دسته‌بندی")
            self.category_label.grid(row=3, column=1, padx=10, pady=5, sticky="e")
            self.category_combobox = ctk.CTkComboBox(self.product_form_frame, width=200)
            self.category_combobox.grid(row=3, column=0, padx=10, pady=5, sticky="e")

            # وزن
            self.weight_label = ctk.CTkLabel(self.product_form_frame,font=("Arial", 12,"bold"), text="وزن (گرم)")
            self.weight_label.grid(row=4, column=1, padx=10, pady=5, sticky="e")
            self.weight_entry = ctk.CTkEntry(self.product_form_frame, width=200)
            self.weight_entry.grid(row=4, column=0, padx=10, pady=5, sticky="e")

            # عیار
            self.purity_label = ctk.CTkLabel(self.product_form_frame,font=("Arial", 12,"bold"), text="عیار")
            self.purity_label.grid(row=5, column=1, padx=10, pady=5, sticky="e")
            self.purity_entry = ctk.CTkEntry(self.product_form_frame, width=200)
            self.purity_entry.grid(row=5, column=0, padx=10, pady=5, sticky="e")

            # قیمت خرید
            self.purchase_price_label = ctk.CTkLabel(self.product_form_frame,font=("Arial", 12,"bold"), text="قیمت خرید (تومان)")
            self.purchase_price_label.grid(row=6, column=1, padx=10, pady=5, sticky="e")
            self.purchase_price_entry = ctk.CTkEntry(self.product_form_frame, width=200)
            self.purchase_price_entry.grid(row=6, column=0, padx=10, pady=5, sticky="e")

            # قیمت فروش
            self.selling_price_label = ctk.CTkLabel(self.product_form_frame,font=("Arial", 12,"bold"), text="قیمت فروش (تومان)")
            self.selling_price_label.grid(row=7, column=1, padx=10, pady=5, sticky="e")
            self.selling_price_entry = ctk.CTkEntry(self.product_form_frame, width=200)
            self.selling_price_entry.grid(row=7, column=0, padx=10, pady=5, sticky="e")

            # توضیحات
            self.description_label = ctk.CTkLabel(self.product_form_frame,font=("Arial", 12,"bold"), text="توضیحات")
            self.description_label.grid(row=8, column=1, padx=10, pady=5, sticky="e")
            self.description_textbox = ctk.CTkTextbox(self.product_form_frame, width=200, height=100)
            self.description_textbox.grid(row=8, column=0, padx=10, pady=5, sticky="e")

            # موجودی
            self.quantity_label = ctk.CTkLabel(self.product_form_frame,font=("Arial", 12,"bold"), text="موجودی")
            self.quantity_label.grid(row=9, column=1, padx=10, pady=5, sticky="e")
            self.quantity_entry = ctk.CTkEntry(self.product_form_frame, width=200)
            self.quantity_entry.grid(row=9, column=0, padx=10, pady=5, sticky="e")

            # دکمه‌های عملیات
            self.buttons_frame = ctk.CTkFrame(self.product_form_frame, fg_color="transparent")
            self.buttons_frame.grid(row=10, column=0, columnspan=2, padx=10, pady=20, sticky="ew")
            self.buttons_frame.grid_columnconfigure((0, 1, 2), weight=1)

            self.save_button = ctk.CTkButton(
                self.buttons_frame, text="ذخیره",
                fg_color="#28a745", hover_color="#218838",
                font=("Arial", 13,"bold"),
                command=self.save_product
            )
            self.save_button.grid(row=0, column=2, padx=5, pady=5, sticky="ew")

            self.clear_button = ctk.CTkButton(
                self.buttons_frame, text="پاک کردن",
                fg_color="#6c757d", hover_color="#5a6268",
                font=("Arial", 13,"bold"),
                command=self.clear_product_form
            )
            self.clear_button.grid(row=0, column=1, padx=5, pady=5, sticky="ew")

            self.delete_button = ctk.CTkButton(
                self.buttons_frame, text="حذف",
                fg_color="#dc3545", hover_color="#c82333",
                font=("Arial", 13,"bold"),
                command=self.delete_product
            )
            self.delete_button.grid(row=0, column=0, padx=5, pady=5, sticky="ew")


            # فریم سمت چپ برای نمایش لیست محصولات
            self.products_list_frame = ctk.CTkFrame(self.products_main_frame)
            self.products_list_frame.grid(row=0, column=0, padx=10, pady=10, sticky="e")

            # عنوان لیست
            self.products_list_title = ctk.CTkLabel(
                self.products_list_frame, text="لیست محصولات",
                #font=ctk.CTkFont(size=16, weight="bold")
                font=("Arial", 16,"bold")
            )
            self.products_list_title.grid(row=0, column=0, padx=10, pady=10, sticky="n")

            # جستجو
            self.search_frame = ctk.CTkFrame(self.products_list_frame, fg_color="transparent")
            self.search_frame.grid(row=1, column=0, padx=10, pady=5, sticky="w")

            self.search_label = ctk.CTkLabel(self.search_frame,font=("Arial", 13,"bold"), text="جستجو")
            self.search_label.grid(row=0, column=2, padx=5, pady=5, sticky="e")

            self.search_entry = ctk.CTkEntry(self.search_frame, width=200)
            self.search_entry.grid(row=0, column=1, padx=5, pady=5, sticky="e")

            self.search_button = ctk.CTkButton(
                self.search_frame, text="جستجو",
                fg_color="#007bff", hover_color="#0069d9",
                command=self.search_products
            )
            self.search_button.grid(row=0, column=0, padx=5, pady=5, sticky="w")

            # لیست محصولات
            self.products_listbox_frame = ctk.CTkFrame(self.products_list_frame)
            self.products_listbox_frame.grid(row=2, column=0, padx=10, pady=10, sticky="e")

            # ستون‌های جدول
            self.columns_frame = ctk.CTkFrame(self.products_listbox_frame, fg_color="#343a40")
            self.columns_frame.grid(row=0, column=0, sticky="e")
            self.columns_frame.grid_columnconfigure((0, 1, 2, 3, 4, 5), weight=1,minsize=70)

            self.col_code = ctk.CTkLabel(self.columns_frame, text="کد", text_color="white",font=("Arial", 12,"bold"))
            self.col_code.grid(row=0, column=5, padx=5, pady=5, sticky="w")

            self.col_name = ctk.CTkLabel(self.columns_frame, text="نام", text_color="white", font=ctk.CTkFont(weight="bold"))
            self.col_name.grid(row=0, column=4, padx=5, pady=5, sticky="w")

            self.col_weight = ctk.CTkLabel(self.columns_frame, text="وزن", text_color="white", font=ctk.CTkFont(weight="bold"))
            self.col_weight.grid(row=0, column=3, padx=5, pady=5, sticky="w")

            self.col_purity = ctk.CTkLabel(self.columns_frame, text="عیار", text_color="white", font=ctk.CTkFont(weight="bold"))
            self.col_purity.grid(row=0, column=2, padx=5, pady=5, sticky="w")

            self.col_price = ctk.CTkLabel(self.columns_frame, text="قیمت فروش", text_color="white", font=("Arial", 12,"bold"))
            self.col_price.grid(row=0, column=1, padx=5, pady=5, sticky="w")

            self.col_quantity = ctk.CTkLabel(self.columns_frame, text="موجودی", text_color="white", font=ctk.CTkFont(weight="bold"))
            self.col_quantity.grid(row=0, column=0, padx=5, pady=5, sticky="w")

            # محتوای جدول
            self.products_table_frame = ctk.CTkScrollableFrame(self.products_listbox_frame, height=400)
            self.products_table_frame.grid(row=1, column=0, sticky="nsew")
            self.products_table_frame.grid_columnconfigure((0, 1, 2, 3, 4, 5), weight=1)

            # بارگذاری دسته‌بندی‌ها
            self.load_categories()

            # بارگذاری محصولات
            self.load_products()

            # متغیر برای نگهداری شناسه محصول در حال ویرایش
            self.current_product_id = None

    def load_categories(self):
        """بارگذاری دسته‌بندی‌ها در کومبوباکس"""
        categories = self.db.get_all_categories()
        category_names = [category["name"] for category in categories]

        if not category_names:
            # اگر دسته‌بندی وجود نداشت، دسته‌بندی‌های پیش‌فرض را اضافه کنیم
            default_categories = ["انگشتر", "گردنبند", "دستبند", "گوشواره", "ساعت", "سکه", "سایر"]
            for cat in default_categories:
                self.db.add_category(cat)

            # دوباره دسته‌بندی‌ها را بارگذاری کنیم
            categories = self.db.get_all_categories()
            category_names = [category["name"] for category in categories]

        self.category_combobox.configure(values=category_names)
        if category_names:
            self.category_combobox.set(category_names[0])

    def load_products(self):
        """بارگذاری محصولات در جدول"""
        # پاک کردن جدول فعلی
        for widget in self.products_table_frame.winfo_children():
            widget.destroy()

        # دریافت همه محصولات
        products = self.db.get_all_products()

        if not products:
            no_data_label = ctk.CTkLabel(
                self.products_table_frame,
                text="هیچ محصولی یافت نشد",
                font=ctk.CTkFont(size=12)
            )
            no_data_label.grid(row=0, column=0, columnspan=6, padx=10, pady=20)
            return

        # نمایش محصولات در جدول
        for i, product in enumerate(products):
            row_frame = ctk.CTkFrame(self.products_table_frame)
            row_frame.grid(row=i, column=0, columnspan=6, sticky="ew", padx=5, pady=2)
            row_frame.grid_columnconfigure((0, 1, 2, 3, 4, 5), weight=1)

            # رنگ پس‌زمینه متناوب برای سطرها
            bg_color = "#f8f9fa" if i % 2 == 0 else "#e9ecef"
            row_frame.configure(fg_color=bg_color)

            code_label = ctk.CTkLabel(row_frame, text=product["code"])
            code_label.grid(row=0, column=5, padx=5, pady=5, sticky="w")

            name_label = ctk.CTkLabel(row_frame, text=product["name"],font=("Arial", 12,"bold"))
            name_label.grid(row=0, column=4, padx=5, pady=5, sticky="w")

            weight_label = ctk.CTkLabel(row_frame, text=str(product["weight"]))
            weight_label.grid(row=0, column=3, padx=5, pady=5, sticky="w")

            purity_label = ctk.CTkLabel(row_frame, text=product["purity"])
            purity_label.grid(row=0, column=2, padx=5, pady=5, sticky="w")

            price_label = ctk.CTkLabel(row_frame,font=("Arial", 13,"bold"), text=f"{product['selling_price']:,} ")
            price_label.grid(row=0, column=1, padx=5, pady=5, sticky="w")

            quantity_label = ctk.CTkLabel(row_frame, text=str(product["quantity"]))
            quantity_label.grid(row=0, column=0, padx=5, pady=5, sticky="w")

            # اضافه کردن رویداد کلیک برای انتخاب محصول
            row_frame.bind("<Button-1>", lambda event, p=product: self.select_product(p))
            for widget in row_frame.winfo_children():
                widget.bind("<Button-1>", lambda event, p=product: self.select_product(p))

    def select_product(self, product):
        """انتخاب محصول برای ویرایش"""
        self.current_product_id = product["id"]

        # پر کردن فرم با اطلاعات محصول
        self.code_entry.delete(0, "end")
        self.code_entry.insert(0, product["code"])

        self.name_entry.delete(0, "end")
        self.name_entry.insert(0, product["name"])

        if product["category_name"]:
            self.category_combobox.set(product["category_name"])

        self.weight_entry.delete(0, "end")
        self.weight_entry.insert(0, str(product["weight"]))

        self.purity_entry.delete(0, "end")
        self.purity_entry.insert(0, product["purity"])

        self.purchase_price_entry.delete(0, "end")
        self.purchase_price_entry.insert(0, str(product["purchase_price"]))

        self.selling_price_entry.delete(0, "end")
        self.selling_price_entry.insert(0, str(product["selling_price"]))

        self.description_textbox.delete("0.0", "end")
        if product["description"]:
            self.description_textbox.insert("0.0", product["description"])

        self.quantity_entry.delete(0, "end")
        self.quantity_entry.insert(0, str(product["quantity"]))

        # تغییر عنوان فرم
        self.product_form_title.configure(text=f"ویرایش محصول: {product['name']}")

    def clear_product_form(self):
        """پاک کردن فرم محصول"""
        self.current_product_id = None

        self.code_entry.delete(0, "end")
        self.name_entry.delete(0, "end")

        categories = self.category_combobox.cget("values")
        if categories:
            self.category_combobox.set(categories[0])

        self.weight_entry.delete(0, "end")
        self.purity_entry.delete(0, "end")
        self.purchase_price_entry.delete(0, "end")
        self.selling_price_entry.delete(0, "end")
        self.description_textbox.delete("0.0", "end")
        self.quantity_entry.delete(0, "end")

        # تغییر عنوان فرم
        self.product_form_title.configure(text="افزودن محصول جدید")

    def save_product(self):
        """ذخیره محصول (افزودن یا ویرایش)"""
        # بررسی دسترسی
        if self.current_product_id and not self.check_permission("edit_product"):
            self.show_permission_error()
            return
        elif not self.current_product_id and not self.check_permission("add_product"):
            self.show_permission_error()
            return

        # دریافت مقادیر از فرم
        code = self.code_entry.get()
        name = self.name_entry.get()
        category_name = self.category_combobox.get()

        # بررسی مقادیر اجباری
        if not code or not name:
            self.show_error("کد و نام محصول اجباری هستند")
            return

        # تبدیل مقادیر عددی
        try:
            weight = float(self.weight_entry.get()) if self.weight_entry.get() else 0
            purchase_price = float(self.purchase_price_entry.get()) if self.purchase_price_entry.get() else 0
            selling_price = float(self.selling_price_entry.get()) if self.selling_price_entry.get() else 0
            quantity = int(self.quantity_entry.get()) if self.quantity_entry.get() else 0
        except ValueError:
            self.show_error("مقادیر عددی نامعتبر")
            return

        purity = self.purity_entry.get()
        description = self.description_textbox.get("0.0", "end").strip()

        # دریافت شناسه دسته‌بندی
        categories = self.db.get_all_categories()
        category_id = None
        for category in categories:
            if category["name"] == category_name:
                category_id = category["id"]
                break

        if self.current_product_id:
            # ویرایش محصول موجود
            success = self.db.update_product(
                self.current_product_id, code, name, category_id, description,
                weight, purity, purchase_price, selling_price
            )

            if success:
                # به‌روزرسانی موجودی
                self.db.update_inventory(self.current_product_id, quantity)
                self.show_success(f"محصول {name} با موفقیت به‌روزرسانی شد")

                # ثبت فعالیت
                self.db.log_activity(
                    self.current_user["id"],
                    "edit_product",
                    f"ویرایش محصول: {name} (کد: {code})"
                )
        else:
            # افزودن محصول جدید
            product_id = self.db.add_product(
                code, name, category_id, description,
                weight, purity, purchase_price, selling_price
            )

            if product_id:
                # به‌روزرسانی موجودی
                self.db.update_inventory(product_id, quantity)
                self.show_success(f"محصول {name} با موفقیت اضافه شد")

                # ثبت فعالیت
                self.db.log_activity(
                    self.current_user["id"],
                    "add_product",
                    f"افزودن محصول جدید: {name} (کد: {code})"
                )

        # پاک کردن فرم و بارگذاری مجدد محصولات
        self.clear_product_form()
        self.load_products()

    def delete_product(self):
        """حذف محصول"""
        # بررسی دسترسی
        if not self.check_permission("delete_product"):
            self.show_permission_error()
            return

        if not self.current_product_id:
            self.show_error("ابتدا یک محصول را انتخاب کنید")
            return

        # دریافت اطلاعات محصول برای ثبت در لاگ
        product = None
        products = self.db.get_all_products()
        for p in products:
            if p["id"] == self.current_product_id:
                product = p
                break

        # حذف محصول
        success = self.db.delete_product(self.current_product_id)

        if success:
            self.show_success("محصول با موفقیت حذف شد")

            # ثبت فعالیت
            if product:
                self.db.log_activity(
                    self.current_user["id"],
                    "delete_product",
                    f"حذف محصول: {product['name']} (کد: {product['code']})"
                )

            # پاک کردن فرم و بارگذاری مجدد محصولات
            self.clear_product_form()
            self.load_products()

    def search_products(self):
        """جستجوی محصولات"""
        search_term = self.search_entry.get().strip()

        if not search_term:
            # اگر عبارت جستجو خالی باشد، همه محصولات را نمایش می‌دهیم
            self.load_products()
            return

        # پاک کردن جدول فعلی
        for widget in self.products_table_frame.winfo_children():
            widget.destroy()

        # دریافت همه محصولات
        products = self.db.get_all_products()

        # فیلتر کردن محصولات بر اساس عبارت جستجو
        filtered_products = []
        for product in products:
            # جستجو در کد، نام، دسته‌بندی، عیار و توضیحات
            if (search_term.lower() in product["code"].lower() or
                search_term.lower() in product["name"].lower() or
                (product["category_name"] and search_term.lower() in product["category_name"].lower()) or
                (product["purity"] and search_term.lower() in product["purity"].lower()) or
                (product["description"] and search_term.lower() in product["description"].lower())):
                filtered_products.append(product)

        if not filtered_products:
            no_data_label = ctk.CTkLabel(
                self.products_table_frame,
                text="هیچ محصولی یافت نشد",
                font=("Arial", 12,"bold")
            )
            no_data_label.grid(row=0, column=0, columnspan=6, padx=10, pady=20)
            return

        # نمایش محصولات فیلتر شده در جدول
        for i, product in enumerate(filtered_products):
            row_frame = ctk.CTkFrame(self.products_table_frame)
            row_frame.grid(row=i, column=0, columnspan=6, sticky="ew", padx=5, pady=2)
            row_frame.grid_columnconfigure((0, 1, 2, 3, 4, 5), weight=1)

            # رنگ پس‌زمینه متناوب برای سطرها
            bg_color = "#f8f9fa" if i % 2 == 0 else "#e9ecef"
            row_frame.configure(fg_color=bg_color)

            code_label = ctk.CTkLabel(row_frame, text=product["code"])
            code_label.grid(row=0, column=5, padx=5, pady=5, sticky="w")

            name_label = ctk.CTkLabel(row_frame, text=product["name"], font=("Arial", 12,"bold"))
            name_label.grid(row=0, column=4, padx=5, pady=5, sticky="w")

            weight_label = ctk.CTkLabel(row_frame, text=str(product["weight"]))
            weight_label.grid(row=0, column=3, padx=5, pady=5, sticky="w")

            purity_label = ctk.CTkLabel(row_frame, text=product["purity"])
            purity_label.grid(row=0, column=2, padx=5, pady=5, sticky="w")

            price_label = ctk.CTkLabel(row_frame, font=("Arial", 13,"bold"), text=f"{product['selling_price']:,} ")
            price_label.grid(row=0, column=1, padx=5, pady=5, sticky="w")

            quantity_label = ctk.CTkLabel(row_frame, text=str(product["quantity"]))
            quantity_label.grid(row=0, column=0, padx=5, pady=5, sticky="w")

            # اضافه کردن رویداد کلیک برای انتخاب محصول
            row_frame.bind("<Button-1>", lambda event, p=product: self.select_product(p))
            for widget in row_frame.winfo_children():
                widget.bind("<Button-1>", lambda event, p=product: self.select_product(p))

    def load_customers(self):
        """بارگذاری مشتریان در جدول"""
        # پاک کردن جدول فعلی
        for widget in self.customers_table_frame.winfo_children():
            widget.destroy()

        # دریافت همه مشتریان
        customers = self.db.get_all_customers()

        if not customers:
            no_data_label = ctk.CTkLabel(
                self.customers_table_frame,
                text="هیچ مشتری‌ای یافت نشد",
                font=("Arial", 12,"bold")
            )
            no_data_label.grid(row=0, column=0, columnspan=4, padx=10, pady=20)
            return

        # نمایش مشتریان در جدول
        for i, customer in enumerate(customers):
            row_frame = ctk.CTkFrame(self.customers_table_frame)
            row_frame.grid(row=i, column=0, columnspan=4, sticky="ew", padx=5, pady=2)
            row_frame.grid_columnconfigure((0, 1, 2, 3), weight=1)

            # رنگ پس‌زمینه متناوب برای سطرها
            bg_color = "#f8f9fa" if i % 2 == 0 else "#e9ecef"
            row_frame.configure(fg_color=bg_color)

            id_label = ctk.CTkLabel(row_frame, text=str(customer["id"]))
            id_label.grid(row=0, column=3, padx=5, pady=5, sticky="w")

            name_label = ctk.CTkLabel(row_frame, text=customer["name"], font=("Arial", 12,"bold"))
            name_label.grid(row=0, column=2, padx=5, pady=5, sticky="w")

            phone_label = ctk.CTkLabel(row_frame, text=customer["phone"] if customer["phone"] else "-")
            phone_label.grid(row=0, column=1, padx=5, pady=5, sticky="w")

            email_label = ctk.CTkLabel(row_frame, text=customer["email"] if customer["email"] else "-")
            email_label.grid(row=0, column=0, padx=5, pady=5, sticky="w")

            # اضافه کردن رویداد کلیک برای انتخاب مشتری
            row_frame.bind("<Button-1>", lambda event, c=customer: self.select_customer(c))
            for widget in row_frame.winfo_children():
                widget.bind("<Button-1>", lambda event, c=customer: self.select_customer(c))

    def select_customer(self, customer):
        """انتخاب مشتری برای ویرایش"""
        self.current_customer_id = customer["id"]

        # پر کردن فرم با اطلاعات مشتری
        self.customer_name_entry.delete(0, "end")
        self.customer_name_entry.insert(0, customer["name"])

        self.customer_phone_entry.delete(0, "end")
        if customer["phone"]:
            self.customer_phone_entry.insert(0, customer["phone"])

        self.customer_email_entry.delete(0, "end")
        if customer["email"]:
            self.customer_email_entry.insert(0, customer["email"])

        self.customer_address_textbox.delete("0.0", "end")
        if customer["address"]:
            self.customer_address_textbox.insert("0.0", customer["address"])

        # تغییر عنوان فرم
        self.customer_form_title.configure(text=f"ویرایش مشتری: {customer['name']}")

    def clear_customer_form(self):
        """پاک کردن فرم مشتری"""
        self.current_customer_id = None

        self.customer_name_entry.delete(0, "end")
        self.customer_phone_entry.delete(0, "end")
        self.customer_email_entry.delete(0, "end")
        self.customer_address_textbox.delete("0.0", "end")

        # تغییر عنوان فرم
        self.customer_form_title.configure(text="افزودن مشتری جدید")

    def save_customer(self):
        """ذخیره مشتری (افزودن یا ویرایش)"""
        # دریافت مقادیر از فرم
        name = self.customer_name_entry.get()
        phone = self.customer_phone_entry.get()
        email = self.customer_email_entry.get()
        address = self.customer_address_textbox.get("0.0", "end").strip()

        # بررسی مقادیر اجباری
        if not name:
            self.show_error("نام مشتری اجباری است")
            return

        if self.current_customer_id:
            # ویرایش مشتری موجود
            success = self.db.update_customer(self.current_customer_id, name, phone, email, address)
            if success:
                self.show_success(f"مشتری {name} با موفقیت به‌روزرسانی شد")
        else:
            # افزودن مشتری جدید
            customer_id = self.db.add_customer(name, phone, email, address)
            if customer_id:
                self.show_success(f"مشتری {name} با موفقیت اضافه شد")

        # پاک کردن فرم و بارگذاری مجدد مشتریان
        self.clear_customer_form()
        self.load_customers()

    def delete_customer(self):
        """حذف مشتری"""
        if not self.current_customer_id:
            self.show_error("ابتدا یک مشتری را انتخاب کنید")
            return

        # حذف مشتری
        success = self.db.delete_customer(self.current_customer_id)

        if success:
            self.show_success(f"مشتری با موفقیت حذف شد")
        else:
            self.show_error("این مشتری دارای فاکتور فروش است و نمی‌توان آن را حذف کرد")

        # پاک کردن فرم و بارگذاری مجدد مشتریان
        self.clear_customer_form()
        self.load_customers()

    def search_customers(self):
        """جستجوی مشتریان"""
        search_term = self.customer_search_entry.get().strip()

        if not search_term:
            # اگر عبارت جستجو خالی باشد، همه مشتریان را نمایش می‌دهیم
            self.load_customers()
            return

        # پاک کردن جدول فعلی
        for widget in self.customers_table_frame.winfo_children():
            widget.destroy()

        # دریافت همه مشتریان
        customers = self.db.get_all_customers()

        # فیلتر کردن مشتریان بر اساس عبارت جستجو
        filtered_customers = []
        for customer in customers:
            # جستجو در نام، شماره تماس و ایمیل
            if (search_term.lower() in customer["name"].lower() or
                (customer["phone"] and search_term.lower() in customer["phone"].lower()) or
                (customer["email"] and search_term.lower() in customer["email"].lower())):
                filtered_customers.append(customer)

        if not filtered_customers:
            no_data_label = ctk.CTkLabel(
                self.customers_table_frame,
                text="هیچ مشتری‌ای یافت نشد",
                font=("Arial", 12,"bold")
            )
            no_data_label.grid(row=0, column=0, columnspan=4, padx=10, pady=20)
            return

        # نمایش مشتریان فیلتر شده در جدول
        for i, customer in enumerate(filtered_customers):
            row_frame = ctk.CTkFrame(self.customers_table_frame)
            row_frame.grid(row=i, column=0, columnspan=4, sticky="ew", padx=5, pady=2)
            row_frame.grid_columnconfigure((0, 1, 2, 3), weight=1)

            # رنگ پس‌زمینه متناوب برای سطرها
            bg_color = "#f8f9fa" if i % 2 == 0 else "#e9ecef"
            row_frame.configure(fg_color=bg_color)

            id_label = ctk.CTkLabel(row_frame, text=str(customer["id"]))
            id_label.grid(row=0, column=3, padx=5, pady=5, sticky="w")

            name_label = ctk.CTkLabel(row_frame, text=customer["name"], font=("Arial", 12,"bold"))
            name_label.grid(row=0, column=2, padx=5, pady=5, sticky="w")

            phone_label = ctk.CTkLabel(row_frame, text=customer["phone"] if customer["phone"] else "-")
            phone_label.grid(row=0, column=1, padx=5, pady=5, sticky="w")

            email_label = ctk.CTkLabel(row_frame, text=customer["email"] if customer["email"] else "-")
            email_label.grid(row=0, column=0, padx=5, pady=5, sticky="w")

            # اضافه کردن رویداد کلیک برای انتخاب مشتری
            row_frame.bind("<Button-1>", lambda event, c=customer: self.select_customer(c))
            for widget in row_frame.winfo_children():
                widget.bind("<Button-1>", lambda event, c=customer: self.select_customer(c))

    def load_customers_for_sale(self):
        """بارگذاری مشتریان در کومبوباکس فروش"""
        customers = self.db.get_all_customers()

        # ساخت لیست نام مشتریان با شناسه آنها
        self.customer_names = []
        self.customer_ids = {}

        for customer in customers:
            customer_name = f"{customer['name']} ({customer['phone'] if customer['phone'] else 'بدون شماره'})"
            self.customer_names.append(customer_name)
            self.customer_ids[customer_name] = customer['id']

        # تنظیم مقادیر کومبوباکس
        self.customer_combobox.configure(values=self.customer_names)
        if self.customer_names:
            self.customer_combobox.set(self.customer_names[0])

    def load_products_for_sale(self):
        """بارگذاری محصولات در کومبوباکس فروش"""
        products = self.db.get_all_products()

        # ساخت لیست نام محصولات با شناسه آنها
        self.product_names = []
        self.product_data = {}

        for product in products:
            if product['quantity'] > 0:  # فقط محصولات موجود را نمایش می‌دهیم
                product_name = f"{product['name']} ({product['code']})"
                self.product_names.append(product_name)
                self.product_data[product_name] = {
                    'id': product['id'],
                    'name': product['name'],
                    'code': product['code'],
                    'price': product['selling_price'],
                    'quantity': product['quantity']
                }

        # تنظیم مقادیر کومبوباکس
        self.product_combobox.configure(values=self.product_names)
        if self.product_names:
            self.product_combobox.set(self.product_names[0])

    def add_product_to_sale(self):
        """افزودن محصول به لیست فروش"""
        # دریافت محصول انتخاب شده
        selected_product_name = self.product_combobox.get()

        if not selected_product_name or selected_product_name not in self.product_data:
            print("لطفاً یک محصول معتبر انتخاب کنید")
            return

        # دریافت تعداد
        try:
            quantity = int(self.quantity_entry.get())
            if quantity <= 0:
                print("تعداد باید بزرگتر از صفر باشد")
                return
        except ValueError:
            print("لطفاً یک عدد معتبر برای تعداد وارد کنید")
            return

        # دریافت اطلاعات محصول
        product_info = self.product_data[selected_product_name]

        # بررسی موجودی
        if quantity > product_info['quantity']:
            print(f"موجودی ناکافی. موجودی فعلی: {product_info['quantity']}")
            return

        # بررسی تکراری بودن محصول
        for product in self.selected_products:
            if product['id'] == product_info['id']:
                print("این محصول قبلاً به لیست اضافه شده است")
                return

        # محاسبه قیمت کل
        total_price = quantity * product_info['price']

        # افزودن به لیست محصولات انتخاب شده
        self.selected_products.append({
            'id': product_info['id'],
            'name': product_info['name'],
            'code': product_info['code'],
            'price': product_info['price'],
            'quantity': quantity,
            'total_price': total_price
        })

        # به‌روزرسانی نمایش محصولات انتخاب شده
        self.update_selected_products_display()

        # به‌روزرسانی مجموع قیمت
        self.update_total_price()

    def update_selected_products_display(self):
        """به‌روزرسانی نمایش محصولات انتخاب شده"""
        # پاک کردن جدول فعلی
        for widget in self.selected_products_table_frame.winfo_children():
            widget.destroy()

        if not self.selected_products:
            no_data_label = ctk.CTkLabel(
                self.selected_products_table_frame,
                text="هیچ محصولی انتخاب نشده است",
                font=("Arial", 12,"bold")
            )
            no_data_label.grid(row=0, column=0, columnspan=5, padx=10, pady=20)
            return

        # نمایش محصولات انتخاب شده در جدول
        for i, product in enumerate(self.selected_products):
            row_frame = ctk.CTkFrame(self.selected_products_table_frame)
            row_frame.grid(row=i, column=0, columnspan=5, sticky="ew", padx=5, pady=2)
            row_frame.grid_columnconfigure((0, 1, 2, 3, 4), weight=1)

            # رنگ پس‌زمینه متناوب برای سطرها
            bg_color = "#f8f9fa" if i % 2 == 0 else "#e9ecef"
            row_frame.configure(fg_color=bg_color)

            name_label = ctk.CTkLabel(row_frame, text=product["name"])
            name_label.grid(row=0, column=3, padx=5, pady=5, sticky="w")

            price_label = ctk.CTkLabel(row_frame, text=f"{product['price']:,} تومان")
            price_label.grid(row=0, column=2, padx=5, pady=5, sticky="w")

            quantity_label = ctk.CTkLabel(row_frame, text=str(product["quantity"]))
            quantity_label.grid(row=0, column=1, padx=5, pady=5, sticky="w")

            total_label = ctk.CTkLabel(row_frame, text=f"{product['total_price']:,} تومان")
            total_label.grid(row=0, column=0, padx=5, pady=5, sticky="w")

            # دکمه حذف
            remove_button = ctk.CTkButton(
                row_frame, text="حذف",
                fg_color="#dc3545", hover_color="#c82333",
                width=60, height=24,
                command=lambda p=product: self.remove_product_from_sale(p)
            )
            remove_button.grid(row=0, column=4, padx=5, pady=5, sticky="w")

    def remove_product_from_sale(self, product):
        """حذف محصول از لیست فروش"""
        self.selected_products = [p for p in self.selected_products if p['id'] != product['id']]

        # به‌روزرسانی نمایش محصولات انتخاب شده
        self.update_selected_products_display()

        # به‌روزرسانی مجموع قیمت
        self.update_total_price()

    def update_total_price(self):
        """به‌روزرسانی مجموع قیمت"""
        total = sum(product['total_price'] for product in self.selected_products)
        self.total_price_value.configure(text=f"{total:,} تومان")

    def clear_sale_form(self):
        """پاک کردن فرم فروش"""
        self.current_sale_id = None

        self.invoice_number_entry.delete(0, "end")

        if self.customer_names:
            self.customer_combobox.set(self.customer_names[0])

        self.payment_method_combobox.set("نقدی")

        self.sale_notes_textbox.delete("0.0", "end")

        # پاک کردن لیست محصولات انتخاب شده
        self.selected_products = []
        self.update_selected_products_display()
        self.update_total_price()

        # تغییر عنوان فرم
        self.sale_form_title.configure(text="ثبت فروش جدید")

    def save_sale(self):
        """ذخیره فروش (افزودن یا ویرایش)"""
        # دریافت مقادیر از فرم
        invoice_number = self.invoice_number_entry.get()
        customer_name = self.customer_combobox.get()
        payment_method = self.payment_method_combobox.get()
        notes = self.sale_notes_textbox.get("0.0", "end").strip()

        # بررسی مقادیر اجباری
        if not invoice_number:
            print("شماره فاکتور اجباری است")
            return

        if not customer_name or customer_name not in self.customer_ids:
            print("لطفاً یک مشتری معتبر انتخاب کنید")
            return

        if not self.selected_products:
            print("لطفاً حداقل یک محصول به فروش اضافه کنید")
            return

        # دریافت شناسه مشتری
        customer_id = self.customer_ids[customer_name]

        # محاسبه مجموع قیمت
        total_amount = sum(product['total_price'] for product in self.selected_products)

        # ذخیره فروش
        sale_id = self.db.add_sale(invoice_number, customer_id, total_amount, payment_method, notes)

        if sale_id:
            # ذخیره اقلام فروش
            for product in self.selected_products:
                self.db.add_sale_item(sale_id, product['id'], product['quantity'], product['price'])

            print(f"فروش با شماره فاکتور {invoice_number} با موفقیت ثبت شد")

            # پاک کردن فرم و بارگذاری مجدد فروش‌ها
            self.clear_sale_form()
            self.load_sales()

            # به‌روزرسانی لیست محصولات برای فروش (به دلیل تغییر در موجودی)
            self.load_products_for_sale()

    def load_sales(self):
        """بارگذاری فروش‌ها در جدول"""
        # پاک کردن جدول فعلی
        for widget in self.sales_table_frame.winfo_children():
            widget.destroy()

        # دریافت فروش‌های اخیر
        sales = self.db.get_recent_sales()

        if not sales:
            no_data_label = ctk.CTkLabel(
                self.sales_table_frame,
                text="هیچ فروشی یافت نشد",
                font=("Arial", 12,"bold")
            )
            no_data_label.grid(row=0, column=0, columnspan=5, padx=10, pady=20)
            return

        # نمایش فروش‌ها در جدول
        for i, sale in enumerate(sales):
            row_frame = ctk.CTkFrame(self.sales_table_frame)
            row_frame.grid(row=i, column=0, columnspan=6, sticky="ew", padx=5, pady=2)
            row_frame.grid_columnconfigure((0, 1, 2, 3, 4, 5), weight=1)

            # رنگ پس‌زمینه متناوب برای سطرها
            bg_color = "#f8f9fa" if i % 2 == 0 else "#e9ecef"
            row_frame.configure(fg_color=bg_color)

            invoice_label = ctk.CTkLabel(row_frame, text=sale["invoice_number"])
            invoice_label.grid(row=0, column=4, padx=5, pady=5, sticky="w")

            customer_label = ctk.CTkLabel(row_frame, text=sale["customer_name"] if sale["customer_name"] else "-")
            customer_label.grid(row=0, column=3, padx=5, pady=5, sticky="w")

            # تبدیل تاریخ به فرمت مناسب
            date_str = sale["sale_date"].split(" ")[0] if " " in sale["sale_date"] else sale["sale_date"]
            date_label = ctk.CTkLabel(row_frame, text=date_str)
            date_label.grid(row=0, column=2, padx=5, pady=5, sticky="w")

            amount_label = ctk.CTkLabel(row_frame, text=f"{sale['total_amount']:,} تومان")
            amount_label.grid(row=0, column=1, padx=5, pady=5, sticky="w")

            payment_label = ctk.CTkLabel(row_frame, text=sale["payment_method"])
            payment_label.grid(row=0, column=0, padx=5, pady=5, sticky="w")

            # دکمه فاکتور
            invoice_button = ctk.CTkButton(
                row_frame,
                text="فاکتور",
                font=("Arial", 11),
                fg_color="#28a745",
                hover_color="#218838",
                width=60,
                height=25,
                command=lambda s=sale: self.show_invoice(s['id'])
            )
            invoice_button.grid(row=0, column=5, padx=5, pady=5, sticky="e")

            # اضافه کردن رویداد کلیک برای نمایش جزئیات فروش
            row_frame.bind("<Button-1>", lambda event, s=sale: self.show_sale_details(s))
            for widget in row_frame.winfo_children():
                if widget != invoice_button:  # به جز دکمه فاکتور
                    widget.bind("<Button-1>", lambda event, s=sale: self.show_sale_details(s))

    def show_sale_details(self, sale):
        """نمایش جزئیات فروش"""
        # دریافت جزئیات فروش از پایگاه داده
        sale_details = self.db.get_sale_details(sale['id'])

        if not sale_details:
            self.show_error("خطا در دریافت جزئیات فروش")
            return

        # ایجاد پنجره جزئیات فروش
        details_window = ctk.CTkToplevel(self)
        details_window.title(f"جزئیات فروش - فاکتور {sale['invoice_number']}")
        details_window.geometry("800x600")
        details_window.resizable(True, True)
        details_window.grab_set()  # مدال کردن پنجره

        # فریم اصلی
        main_frame = ctk.CTkFrame(details_window)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # بخش اطلاعات فروش
        sale_info_frame = ctk.CTkFrame(main_frame)
        sale_info_frame.pack(fill="x", padx=10, pady=10)

        # عنوان
        title_label = ctk.CTkLabel(
            sale_info_frame,
            text=f"فاکتور فروش شماره {sale_details['sale']['invoice_number']}",
            font=("Arial", 18, "bold")
        )
        title_label.pack(pady=10)

        # اطلاعات فروش
        sale_date = sale_details['sale']['sale_date'].split(" ")[0] if " " in sale_details['sale']['sale_date'] else sale_details['sale']['sale_date']

        info_text = f"""
        تاریخ فروش: {sale_date}
        مشتری: {sale_details['sale']['customer_name'] if sale_details['sale']['customer_name'] else 'بدون نام'}
        شماره تماس: {sale_details['sale']['customer_phone'] if sale_details['sale']['customer_phone'] else '-'}
        روش پرداخت: {sale_details['sale']['payment_method']}
        مبلغ کل: {sale_details['sale']['total_amount']:,} تومان
        """

        if sale_details['sale']['notes']:
            info_text += f"\nتوضیحات: {sale_details['sale']['notes']}"

        info_label = ctk.CTkLabel(
            sale_info_frame,
            text=info_text,
            font=("Arial", 12),
            justify="right"
        )
        info_label.pack(pady=10, padx=20, anchor="e")

        # خط جداکننده
        separator = ctk.CTkFrame(main_frame, height=2, fg_color="#dee2e6")
        separator.pack(fill="x", padx=10, pady=10)

        # عنوان جدول آیتم‌ها
        items_title = ctk.CTkLabel(
            main_frame,
            text="اقلام فروش",
            font=("Arial", 16, "bold")
        )
        items_title.pack(pady=10)

        # جدول آیتم‌های فروش
        items_frame = ctk.CTkScrollableFrame(main_frame, height=300)
        items_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # ستون‌های جدول
        headers_frame = ctk.CTkFrame(items_frame)
        headers_frame.pack(fill="x", padx=5, pady=5)

        headers = ["ردیف", "کد محصول", "نام محصول", "تعداد", "قیمت واحد (تومان)", "قیمت کل (تومان)"]
        widths = [50, 100, 200, 80, 150, 150]

        for i, header in enumerate(headers):
            header_label = ctk.CTkLabel(
                headers_frame,
                text=header,
                font=("Arial", 12, "bold"),
                width=widths[i]
            )
            header_label.grid(row=0, column=len(headers)-i-1, padx=5, pady=5)

        # آیتم‌های فروش
        total_amount = 0

        for i, item in enumerate(sale_details['items']):
            row_frame = ctk.CTkFrame(items_frame)
            row_frame.pack(fill="x", padx=5, pady=2)

            # رنگ پس‌زمینه متناوب برای سطرها
            bg_color = "#f8f9fa" if i % 2 == 0 else "#e9ecef"
            row_frame.configure(fg_color=bg_color)

            # محاسبه قیمت کل آیتم
            item_total = item['quantity'] * item['price']
            total_amount += item_total

            # ستون‌های آیتم
            index_label = ctk.CTkLabel(row_frame, text=str(i+1), width=widths[0])
            index_label.grid(row=0, column=5, padx=5, pady=5)

            code_label = ctk.CTkLabel(row_frame, text=item['product_code'], width=widths[1])
            code_label.grid(row=0, column=4, padx=5, pady=5)

            name_label = ctk.CTkLabel(row_frame, text=item['product_name'], width=widths[2])
            name_label.grid(row=0, column=3, padx=5, pady=5)

            quantity_label = ctk.CTkLabel(row_frame, text=str(item['quantity']), width=widths[3])
            quantity_label.grid(row=0, column=2, padx=5, pady=5)

            price_label = ctk.CTkLabel(row_frame, text=f"{item['price']:,}", width=widths[4])
            price_label.grid(row=0, column=1, padx=5, pady=5)

            total_label = ctk.CTkLabel(row_frame, text=f"{item_total:,}", width=widths[5])
            total_label.grid(row=0, column=0, padx=5, pady=5)

        # خط جداکننده
        separator2 = ctk.CTkFrame(main_frame, height=2, fg_color="#dee2e6")
        separator2.pack(fill="x", padx=10, pady=10)

        # جمع کل
        total_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
        total_frame.pack(fill="x", padx=20, pady=10)

        total_label = ctk.CTkLabel(
            total_frame,
            text=f"جمع کل: {total_amount:,} تومان",
            font=("Arial", 14, "bold")
        )
        total_label.pack(side="left", padx=20)

        # فریم دکمه‌ها
        buttons_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
        buttons_frame.pack(pady=20)

        # دکمه نمایش فاکتور
        invoice_button = ctk.CTkButton(
            buttons_frame,
            text="نمایش فاکتور",
            font=("Arial", 12, "bold"),
            fg_color="#007bff",
            hover_color="#0069d9",
            width=150,
            command=lambda: self.show_invoice(sale_details['sale']['id'])
        )
        invoice_button.grid(row=0, column=0, padx=10)

        # دکمه چاپ فاکتور
        print_button = ctk.CTkButton(
            buttons_frame,
            text="چاپ فاکتور",
            font=("Arial", 12, "bold"),
            fg_color="#28a745",
            hover_color="#218838",
            width=150,
            command=lambda: self.print_invoice(sale_details['sale']['id'])
        )
        print_button.grid(row=0, column=1, padx=10)

        # دکمه بستن
        close_button = ctk.CTkButton(
            buttons_frame,
            text="بستن",
            font=("Arial", 12, "bold"),
            fg_color="#6c757d",
            hover_color="#5a6268",
            width=150,
            command=details_window.destroy
        )
        close_button.grid(row=0, column=2, padx=10)

    def search_sales(self):
        """جستجوی فروش‌ها"""
        search_term = self.sale_search_entry.get().strip()

        if not search_term:
            # اگر عبارت جستجو خالی باشد، همه فروش‌ها را نمایش می‌دهیم
            self.load_sales()
            return

        # پاک کردن جدول فعلی
        for widget in self.sales_table_frame.winfo_children():
            widget.destroy()

        # دریافت فروش‌های اخیر
        sales = self.db.get_recent_sales(limit=100)  # افزایش محدودیت برای جستجوی بهتر

        # فیلتر کردن فروش‌ها بر اساس عبارت جستجو
        filtered_sales = []
        for sale in sales:
            # جستجو در شماره فاکتور، نام مشتری و روش پرداخت
            if (search_term.lower() in sale["invoice_number"].lower() or
                (sale["customer_name"] and search_term.lower() in sale["customer_name"].lower()) or
                search_term.lower() in sale["payment_method"].lower() or
                search_term in str(sale["total_amount"])):
                filtered_sales.append(sale)

        if not filtered_sales:
            no_data_label = ctk.CTkLabel(
                self.sales_table_frame,
                text="هیچ فروشی یافت نشد",
                font=("Arial", 12,"bold")
            )
            no_data_label.grid(row=0, column=0, columnspan=6, padx=10, pady=20)
            return

        # نمایش فروش‌های فیلتر شده در جدول
        for i, sale in enumerate(filtered_sales):
            row_frame = ctk.CTkFrame(self.sales_table_frame)
            row_frame.grid(row=i, column=0, columnspan=6, sticky="ew", padx=5, pady=2)
            row_frame.grid_columnconfigure((0, 1, 2, 3, 4, 5), weight=1)

            # رنگ پس‌زمینه متناوب برای سطرها
            bg_color = "#f8f9fa" if i % 2 == 0 else "#e9ecef"
            row_frame.configure(fg_color=bg_color)

            invoice_label = ctk.CTkLabel(row_frame, text=sale["invoice_number"], font=("Arial", 12,"bold"))
            invoice_label.grid(row=0, column=4, padx=5, pady=5, sticky="w")

            customer_label = ctk.CTkLabel(row_frame, text=sale["customer_name"] if sale["customer_name"] else "-")
            customer_label.grid(row=0, column=3, padx=5, pady=5, sticky="w")

            # تبدیل تاریخ به فرمت مناسب
            date_str = sale["sale_date"].split(" ")[0] if " " in sale["sale_date"] else sale["sale_date"]
            date_label = ctk.CTkLabel(row_frame, text=date_str)
            date_label.grid(row=0, column=2, padx=5, pady=5, sticky="w")

            amount_label = ctk.CTkLabel(row_frame, text=f"{sale['total_amount']:,} تومان", font=("Arial", 12,"bold"))
            amount_label.grid(row=0, column=1, padx=5, pady=5, sticky="w")

            payment_label = ctk.CTkLabel(row_frame, text=sale["payment_method"])
            payment_label.grid(row=0, column=0, padx=5, pady=5, sticky="w")

            # دکمه فاکتور
            invoice_button = ctk.CTkButton(
                row_frame,
                text="فاکتور",
                font=("Arial", 11),
                fg_color="#28a745",
                hover_color="#218838",
                width=60,
                height=25,
                command=lambda s=sale: self.show_invoice(s['id'])
            )
            invoice_button.grid(row=0, column=5, padx=5, pady=5, sticky="e")

            # اضافه کردن رویداد کلیک برای نمایش جزئیات فروش
            row_frame.bind("<Button-1>", lambda event, s=sale: self.show_sale_details(s))
            for widget in row_frame.winfo_children():
                if widget != invoice_button:  # به جز دکمه فاکتور
                    widget.bind("<Button-1>", lambda event, s=sale: self.show_sale_details(s))

    def search_inventory(self):
        """جستجو در موجودی"""
        search_term = self.inventory_search_entry.get()
        filter_category = self.inventory_category_combobox.get()
        filter_low_stock = self.low_stock_var.get()

        if filter_category == "همه دسته‌بندی‌ها":
            filter_category = None

        self.load_inventory(filter_category, filter_low_stock, search_term)

    def load_categories_for_inventory(self):
        """بارگذاری دسته‌بندی‌ها در کومبوباکس موجودی"""
        categories = self.db.get_all_categories()

        # ساخت لیست نام دسته‌بندی‌ها
        category_names = ["همه دسته‌بندی‌ها"]
        for category in categories:
            category_names.append(category["name"])

        # تنظیم مقادیر کومبوباکس
        self.inventory_category_combobox.configure(values=category_names)
        self.inventory_category_combobox.set(category_names[0])

    def load_inventory(self, filter_category=None, filter_low_stock=False, search_term=None):
        """بارگذاری موجودی در جدول"""
        # پاک کردن جدول فعلی
        for widget in self.inventory_items_frame.winfo_children():
            widget.destroy()

        # دریافت همه محصولات
        products = self.db.get_all_products()

        # اعمال فیلترها
        filtered_products = []
        for product in products:
            # فیلتر دسته‌بندی
            if filter_category and filter_category != "همه دسته‌بندی‌ها" and product["category_name"] != filter_category:
                continue

            # فیلتر موجودی کم
            if filter_low_stock and product["quantity"] > 5:  # آستانه موجودی کم: 5
                continue

            # فیلتر جستجو
            if search_term:
                search_term = search_term.lower()
                if (search_term not in product["name"].lower() and
                    search_term not in product["code"].lower() and
                    (not product["description"] or search_term not in product["description"].lower())):
                    continue

            filtered_products.append(product)

        if not filtered_products:
            no_data_label = ctk.CTkLabel(
                self.inventory_items_frame,
                text="هیچ محصولی یافت نشد",
                font=("Arial", 12,"bold")
            )
            no_data_label.grid(row=0, column=0, columnspan=7, padx=10, pady=20)

            # به‌روزرسانی آمار موجودی
            self.update_inventory_stats([], filter_low_stock)
            return

        # نمایش محصولات در جدول
        for i, product in enumerate(filtered_products):
            row_frame = ctk.CTkFrame(self.inventory_items_frame)
            row_frame.grid(row=i, column=0, columnspan=7, sticky="w", padx=5, pady=2)
            row_frame.grid_columnconfigure((0, 1, 2, 3, 4, 5, 6), weight=1,minsize=125)

            # رنگ پس‌زمینه متناوب برای سطرها
            bg_color = "#f8f9fa" if i % 2 == 0 else "#e9ecef"

            # رنگ قرمز برای محصولات با موجودی کم
            if product["quantity"] <= 5:
                bg_color = "#ffcccc"  # رنگ قرمز کمرنگ

            row_frame.configure(fg_color=bg_color)

            code_label = ctk.CTkLabel(row_frame, text=product["code"])
            code_label.grid(row=0, column=6, padx=5, pady=5, sticky="n")

            name_label = ctk.CTkLabel(row_frame, text=product["name"])
            name_label.grid(row=0, column=5, padx=5, pady=5, sticky="n")

            category_label = ctk.CTkLabel(row_frame, text=product["category_name"] if product["category_name"] else "-")
            category_label.grid(row=0, column=4, padx=5, pady=5, sticky="n")

            quantity_label = ctk.CTkLabel(row_frame, text=str(product["quantity"]))
            quantity_label.grid(row=0, column=3, padx=5, pady=5, sticky="n")

            price_label = ctk.CTkLabel(row_frame, text=f"{product['selling_price']:,} تومان")
            price_label.grid(row=0, column=2, padx=5, pady=5, sticky="n")

            # محاسبه ارزش موجودی
            stock_value = product["quantity"] * product["selling_price"]
            value_label = ctk.CTkLabel(row_frame, text=f"{stock_value:,} تومان")
            value_label.grid(row=0, column=1, padx=5, pady=5, sticky="n")

            # دکمه به‌روزرسانی موجودی
            update_button = ctk.CTkButton(
                row_frame, text="به‌روزرسانی",
                font=("Arial", 12,"bold"),
                fg_color="#28a745", hover_color="#218838",
                width=80, height=24,
                command=lambda p=product: self.show_update_inventory_dialog(p)
            )
            update_button.grid(row=0, column=0, padx=5, pady=5, sticky="n")

        # به‌روزرسانی آمار موجودی
        self.update_inventory_stats(filtered_products, filter_low_stock)

    def update_inventory_stats(self, products, filter_low_stock=False):
        """به‌روزرسانی آمار موجودی"""
        # اگر فیلتر اعمال شده، آمار کلی را نمایش می‌دهیم
        if filter_low_stock or len(products) == 0:
            all_products = self.db.get_all_products()
        else:
            all_products = products

        # تعداد کل محصولات
        total_products = len(all_products)
        self.total_products_count.configure(text=str(total_products))

        # تعداد کل موجودی
        total_stock = sum(product["quantity"] for product in all_products)
        self.total_stock_count.configure(text=str(total_stock))

        # تعداد محصولات با موجودی کم
        low_stock_count = sum(1 for product in all_products if product["quantity"] <= 5)
        self.low_stock_count.configure(text=str(low_stock_count))

        # ارزش کل موجودی
        total_value = sum(product["quantity"] * product["selling_price"] for product in all_products)
        self.stock_value_amount.configure(text=f"{total_value:,} تومان")

    def filter_low_stock(self):
        """فیلتر کردن محصولات با موجودی کم"""
        filter_low_stock = self.low_stock_var.get()
        filter_category = self.inventory_category_combobox.get()
        search_term = self.inventory_search_entry.get()

        if filter_category == "همه دسته‌بندی‌ها":
            filter_category = None

        self.load_inventory(filter_category, filter_low_stock, search_term)

    def show_update_inventory_dialog(self, product=None):
        """نمایش دیالوگ به‌روزرسانی موجودی"""
        # ایجاد پنجره دیالوگ
        dialog = ctk.CTkToplevel(self)
        dialog.title("به‌روزرسانی موجودی")
        dialog.geometry("400x300")
        dialog.resizable(False, False)
        dialog.grab_set()  # مدال کردن پنجره

        # فریم اصلی
        main_frame = ctk.CTkFrame(dialog)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # عنوان
        title_label = ctk.CTkLabel(
            main_frame, text="به‌روزرسانی موجودی",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        title_label.pack(pady=10)

        # انتخاب محصول (اگر محصول از قبل انتخاب نشده باشد)
        if not product:
            # دریافت همه محصولات
            products = self.db.get_all_products()

            # ساخت لیست نام محصولات با شناسه آنها
            product_names = []
            product_ids = {}

            for p in products:
                product_name = f"{p['name']} ({p['code']})"
                product_names.append(product_name)
                product_ids[product_name] = p['id']

            # انتخاب محصول
            product_label = ctk.CTkLabel(main_frame, text="انتخاب محصول:")
            product_label.pack(anchor="w", padx=20, pady=5)

            product_combobox = ctk.CTkComboBox(main_frame, values=product_names, width=300)
            product_combobox.pack(padx=20, pady=5)

            if product_names:
                product_combobox.set(product_names[0])
                selected_product = products[0]
            else:
                dialog.destroy()
                return
        else:
            # نمایش اطلاعات محصول انتخاب شده
            selected_product = product

            product_info_label = ctk.CTkLabel(
                main_frame,
                text=f"محصول: {product['name']} (کد: {product['code']})\nموجودی فعلی: {product['quantity']}",
                font=ctk.CTkFont(size=12)
            )
            product_info_label.pack(anchor="w", padx=20, pady=10)

        # موجودی جدید
        quantity_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
        quantity_frame.pack(fill="x", padx=20, pady=10)

        quantity_label = ctk.CTkLabel(quantity_frame, text="موجودی جدید:")
        quantity_label.pack(side="left", padx=5)

        quantity_entry = ctk.CTkEntry(quantity_frame, width=100)
        quantity_entry.pack(side="left", padx=5)

        if product:
            quantity_entry.insert(0, str(product["quantity"]))

        # توضیحات
        notes_label = ctk.CTkLabel(main_frame, text="توضیحات:")
        notes_label.pack(anchor="w", padx=20, pady=5)

        notes_textbox = ctk.CTkTextbox(main_frame, width=300, height=80)
        notes_textbox.pack(padx=20, pady=5)

        # دکمه‌های عملیات
        buttons_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
        buttons_frame.pack(fill="x", padx=20, pady=20)

        cancel_button = ctk.CTkButton(
            buttons_frame, text="انصراف",
            fg_color="#6c757d", hover_color="#5a6268",
            command=dialog.destroy
        )
        cancel_button.pack(side="left", padx=5, expand=True, fill="x")

        save_button = ctk.CTkButton(
            buttons_frame, text="ذخیره",
            fg_color="#28a745", hover_color="#218838",
            command=lambda: self.update_product_inventory(
                dialog,
                selected_product["id"] if product else product_ids[product_combobox.get()],
                quantity_entry.get(),
                notes_textbox.get("0.0", "end").strip()
            )
        )
        save_button.pack(side="left", padx=5, expand=True, fill="x")

    def show_error(self, message):
        """نمایش پیام خطا به کاربر"""
        error_window = ctk.CTkToplevel(self)
        error_window.title("خطا")
        error_window.geometry("400x200")
        error_window.resizable(False, False)
        error_window.grab_set()  # مدال کردن پنجره

        error_label = ctk.CTkLabel(
            error_window, text=message,
            font=("Arial", 14, "bold"),
            text_color="#dc3545"
        )
        error_label.pack(padx=20, pady=20)

        ok_button = ctk.CTkButton(
            error_window, text="تایید",
            fg_color="#dc3545", hover_color="#c82333",
            font=("Arial", 12, "bold"),
            command=error_window.destroy
        )
        ok_button.pack(pady=10)

    def show_success(self, message):
        """نمایش پیام موفقیت به کاربر"""
        success_window = ctk.CTkToplevel(self)
        success_window.title("موفقیت")
        success_window.geometry("400x200")
        success_window.resizable(False, False)
        success_window.grab_set()  # مدال کردن پنجره

        success_label = ctk.CTkLabel(
            success_window, text=message,
            font=("Arial", 14, "bold"),
            text_color="#28a745"
        )
        success_label.pack(padx=20, pady=20)

        ok_button = ctk.CTkButton(
            success_window, text="تایید",
            fg_color="#28a745", hover_color="#218838",
            font=("Arial", 12, "bold"),
            command=success_window.destroy
        )
        ok_button.pack(pady=10)

    def update_product_inventory(self, dialog, product_id, quantity, notes):
        """به‌روزرسانی موجودی محصول"""
        try:
            quantity = int(quantity)
            if quantity < 0:
                self.show_error("موجودی نمی‌تواند منفی باشد")
                return
        except ValueError:
            self.show_error("لطفاً یک عدد معتبر برای موجودی وارد کنید")
            return

        # به‌روزرسانی موجودی
        success = self.db.update_inventory(product_id, quantity)

        if success:
            self.show_success(f"موجودی محصول به {quantity} به‌روزرسانی شد")

            # بستن دیالوگ
            dialog.destroy()

            # به‌روزرسانی نمایش موجودی
            filter_category = self.inventory_category_combobox.get()
            filter_low_stock = self.low_stock_var.get()
            search_term = self.inventory_search_entry.get()

            if filter_category == "همه دسته‌بندی‌ها":
                filter_category = None

            self.load_inventory(filter_category, filter_low_stock, search_term)

    def create_customers_frame(self):
        """ایجاد صفحه مدیریت مشتریان"""
        if not hasattr(self, "customers_frame"):
            self.customers_frame = ctk.CTkFrame(self, corner_radius=0, fg_color="transparent")
            self.customers_frame.grid_columnconfigure(0, weight=1)

            # عنوان مشتریان
            self.customers_label = ctk.CTkLabel(
                self.customers_frame, text="مدیریت مشتریان",
                font=("Arial", 20,"bold")
            )
            self.customers_label.grid(row=0, column=0, padx=20, pady=20)

            # ایجاد فریم اصلی مشتریان
            self.customers_main_frame = ctk.CTkFrame(self.customers_frame)
            self.customers_main_frame.grid(row=1, column=0, padx=20, pady=20, sticky="nsew")
            self.customers_main_frame.grid_columnconfigure(0, weight=1)
            self.customers_main_frame.grid_columnconfigure(1, weight=3)

            # فریم سمت راست برای افزودن/ویرایش مشتری
            self.customer_form_frame = ctk.CTkFrame(self.customers_main_frame)
            self.customer_form_frame.grid(row=0, column=1, padx=10, pady=10, sticky="nsew")

            # عنوان فرم
            self.customer_form_title = ctk.CTkLabel(
                self.customer_form_frame, text="افزودن مشتری جدید",
                font=("Arial", 20,"bold")
            )
            self.customer_form_title.grid(row=0, column=0, padx=10, pady=10, sticky="e")

            # فیلدهای فرم
            # نام مشتری
            self.customer_name_label = ctk.CTkLabel(self.customer_form_frame, text="نام مشتری",font=("Arial", 12,"bold"))
            self.customer_name_label.grid(row=1, column=1, padx=10, pady=5, sticky="e")
            self.customer_name_entry = ctk.CTkEntry(self.customer_form_frame, width=200,font=("Arial", 12,"bold"),justify="right")
            self.customer_name_entry.grid(row=1, column=0, padx=10, pady=5, sticky="e")

            # شماره تماس
            self.customer_phone_label = ctk.CTkLabel(self.customer_form_frame, text="شماره تماس",font=("Arial", 12,"bold"))
            self.customer_phone_label.grid(row=2, column=1, padx=10, pady=5, sticky="e")
            self.customer_phone_entry = ctk.CTkEntry(self.customer_form_frame, width=200,font=("Arial", 12,"bold"),justify="right")
            self.customer_phone_entry.grid(row=2, column=0, padx=10, pady=5, sticky="e")

            # ایمیل
            self.customer_email_label = ctk.CTkLabel(self.customer_form_frame, text="ایمیل")
            self.customer_email_label.grid(row=3, column=1, padx=10, pady=5, sticky="e")
            self.customer_email_entry = ctk.CTkEntry(self.customer_form_frame, width=200)
            self.customer_email_entry.grid(row=3, column=0, padx=10, pady=5, sticky="e")

            # آدرس
            self.customer_address_label = ctk.CTkLabel(self.customer_form_frame, text="آدرس", font=("Arial", 12,"bold"))
            self.customer_address_label.grid(row=4, column=1, padx=10, pady=5, sticky="e")
            self.customer_address_textbox = ctk.CTkTextbox(self.customer_form_frame, width=200, height=100, font=("Arial", 12,"bold"))
            self.customer_address_textbox.grid(row=4, column=0, padx=10, pady=5, sticky="e")

            # دکمه‌های عملیات
            self.customer_buttons_frame = ctk.CTkFrame(self.customer_form_frame, fg_color="transparent")
            self.customer_buttons_frame.grid(row=5, column=0, columnspan=2, padx=10, pady=20, sticky="ew")
            self.customer_buttons_frame.grid_columnconfigure((0, 1, 2), weight=1)

            self.customer_save_button = ctk.CTkButton(
                self.customer_buttons_frame, text="ذخیره",
                fg_color="#28a745", hover_color="#218838",
                font=("Arial", 12,"bold"),
                command=self.save_customer
            )
            self.customer_save_button.grid(row=0, column=2, padx=5, pady=5, sticky="ew")

            self.customer_clear_button = ctk.CTkButton(
                self.customer_buttons_frame, text="پاک کردن",
                fg_color="#6c757d", hover_color="#5a6268",
                font=("Arial", 12,"bold"),
                command=self.clear_customer_form
            )
            self.customer_clear_button.grid(row=0, column=1, padx=5, pady=5, sticky="ew")

            self.customer_delete_button = ctk.CTkButton(
                self.customer_buttons_frame, text="حذف",
                fg_color="#dc3545", hover_color="#c82333",
                font=("Arial", 12,"bold"),
                command=self.delete_customer
            )
            self.customer_delete_button.grid(row=0, column=0, padx=5, pady=5, sticky="ew")

            # فریم سمت چپ برای نمایش لیست مشتریان
            self.customers_list_frame = ctk.CTkFrame(self.customers_main_frame)
            self.customers_list_frame.grid(row=0, column=0, padx=10, pady=10, sticky="nsew")

            # عنوان لیست
            self.customers_list_title = ctk.CTkLabel(
                self.customers_list_frame, text="لیست مشتریان",
                font=("Arial", 20,"bold")
            )
            self.customers_list_title.grid(row=0, column=0, padx=10, pady=10, sticky="n")

            # جستجو
            self.customer_search_frame = ctk.CTkFrame(self.customers_list_frame, fg_color="transparent")
            self.customer_search_frame.grid(row=1, column=0, padx=10, pady=5, sticky="ew")

            self.customer_search_label = ctk.CTkLabel(self.customer_search_frame, text="جستجو")
            self.customer_search_label.grid(row=0, column=2, padx=5, pady=5, sticky="w")

            self.customer_search_entry = ctk.CTkEntry(self.customer_search_frame, width=200)
            self.customer_search_entry.grid(row=0, column=1, padx=5, pady=5, sticky="w")

            self.customer_search_button = ctk.CTkButton(
                self.customer_search_frame, text="جستجو",
                fg_color="#007bff", hover_color="#0069d9",
                command=self.search_customers
            )
            self.customer_search_button.grid(row=0, column=0, padx=5, pady=5, sticky="w")

            # لیست مشتریان
            self.customers_listbox_frame = ctk.CTkFrame(self.customers_list_frame)
            self.customers_listbox_frame.grid(row=2, column=0, padx=10, pady=10, sticky="e")

            # ستون‌های جدول
            self.customer_columns_frame = ctk.CTkFrame(self.customers_listbox_frame, fg_color="#343a40")
            self.customer_columns_frame.grid(row=0, column=0, sticky="ew")
            self.customer_columns_frame.grid_columnconfigure((0, 1, 2, 3), weight=1,minsize=100)

            self.customer_col_id = ctk.CTkLabel(self.customer_columns_frame, text="شناسه", text_color="white", font=ctk.CTkFont(weight="bold"))
            self.customer_col_id.grid(row=0, column=3, padx=5, pady=5, sticky="w")

            self.customer_col_name = ctk.CTkLabel(self.customer_columns_frame, text="نام", text_color="white", font=ctk.CTkFont(weight="bold"))
            self.customer_col_name.grid(row=0, column=2, padx=5, pady=5, sticky="w")

            self.customer_col_phone = ctk.CTkLabel(self.customer_columns_frame, text="شماره تماس", text_color="white", font=("Arial", 12,"bold"))
            self.customer_col_phone.grid(row=0, column=1, padx=5, pady=5, sticky="w")

            self.customer_col_email = ctk.CTkLabel(self.customer_columns_frame, text="ایمیل", text_color="white", font=ctk.CTkFont(weight="bold"))
            self.customer_col_email.grid(row=0, column=0, padx=5, pady=5, sticky="w")

            # محتوای جدول
            self.customers_table_frame = ctk.CTkScrollableFrame(self.customers_listbox_frame, height=400)
            self.customers_table_frame.grid(row=1, column=0, sticky="nsew")
            self.customers_table_frame.grid_columnconfigure((0, 1, 2, 3), weight=1,minsize=100)

            # بارگذاری مشتریان
            self.load_customers()

            # متغیر برای نگهداری شناسه مشتری در حال ویرایش
            self.current_customer_id = None

    def create_sales_frame(self):
        """ایجاد صفحه مدیریت فروش"""
        if not hasattr(self, "sales_frame"):
            self.sales_frame = ctk.CTkFrame(self, corner_radius=0, fg_color="transparent")
            self.sales_frame.grid_columnconfigure(0, weight=1)

            # عنوان فروش
            self.sales_label = ctk.CTkLabel(
                self.sales_frame, text="مدیریت فروش",
                font=("Arial", 20,"bold")
            )
            self.sales_label.grid(row=0, column=0, padx=5, pady=5)

            # ایجاد فریم اصلی فروش
            self.sales_main_frame = ctk.CTkFrame(self.sales_frame)
            self.sales_main_frame.grid(row=1, column=0, padx=5, pady=5, sticky="n")
            self.sales_main_frame.grid_columnconfigure(0, weight=1)
            self.sales_main_frame.grid_columnconfigure(1, weight=3)

            # فریم سمت راست برای ثبت فروش جدید
            self.sale_form_frame = ctk.CTkFrame(self.sales_main_frame)
            self.sale_form_frame.grid(row=0, column=1, padx=10, pady=10, sticky="n")

            # عنوان فرم
            self.sale_form_title = ctk.CTkLabel(
                self.sale_form_frame, text="ثبت فروش جدید",
                font=("Arial", 16,"bold")
            )
            self.sale_form_title.grid(row=0, column=0, padx=10, pady=10, sticky="e")

            # فیلدهای فرم
            # شماره فاکتور
            self.invoice_number_label = ctk.CTkLabel(self.sale_form_frame, text="شماره فاکتور",font=("Arial", 12,"bold"))
            self.invoice_number_label.grid(row=1, column=1, padx=10, pady=5, sticky="e")
            self.invoice_number_entry = ctk.CTkEntry(self.sale_form_frame, width=200)
            self.invoice_number_entry.grid(row=1, column=0, padx=10, pady=5, sticky="e")

            # انتخاب مشتری
            self.customer_label = ctk.CTkLabel(self.sale_form_frame, text="مشتری",font=("Arial", 12,"bold"))
            self.customer_label.grid(row=2, column=1, padx=10, pady=5, sticky="e")
            self.customer_combobox = ctk.CTkComboBox(self.sale_form_frame, width=200)
            self.customer_combobox.grid(row=2, column=0, padx=10, pady=5, sticky="e")

            # روش پرداخت
            self.payment_method_label = ctk.CTkLabel(self.sale_form_frame, text="روش پرداخت",font=("Arial", 12,"bold"))
            self.payment_method_label.grid(row=3, column=1, padx=10, pady=5, sticky="e")
            self.payment_method_combobox = ctk.CTkComboBox(self.sale_form_frame, width=200,
                                                         values=["نقدی", "کارت بانکی", "انتقال وجه", "چک"])
            self.payment_method_combobox.grid(row=3, column=0, padx=10, pady=5, sticky="e")

            # توضیحات
            self.sale_notes_label = ctk.CTkLabel(self.sale_form_frame, text="توضیحات",font=("Arial", 12,"bold"))
            self.sale_notes_label.grid(row=4, column=1, padx=10, pady=5, sticky="e")
            self.sale_notes_textbox = ctk.CTkTextbox(self.sale_form_frame, width=200, height=50)
            self.sale_notes_textbox.grid(row=4, column=0, padx=10, pady=5, sticky="e")

            # بخش انتخاب محصولات
            self.products_selection_label = ctk.CTkLabel(
                self.sale_form_frame, text="انتخاب محصولات",
                font=("Arial", 12,"bold")
            )
            self.products_selection_label.grid(row=5, column=0, columnspan=2, padx=10, pady=10, sticky="e")

            # فریم انتخاب محصول
            self.product_selection_frame = ctk.CTkFrame(self.sale_form_frame)
            self.product_selection_frame.grid(row=6, column=0, columnspan=2, padx=10, pady=5, sticky="e")

            # انتخاب محصول
            self.product_label = ctk.CTkLabel(self.product_selection_frame, text="محصول")
            self.product_label.grid(row=0, column=4, padx=5, pady=5, sticky="e")
            self.product_combobox = ctk.CTkComboBox(self.product_selection_frame, width=150)
            self.product_combobox.grid(row=0, column=3, padx=5, pady=5, sticky="e")

            # تعداد
            self.quantity_label = ctk.CTkLabel(self.product_selection_frame, text="تعداد")
            self.quantity_label.grid(row=0, column=2, padx=5, pady=5, sticky="e")
            self.quantity_entry = ctk.CTkEntry(self.product_selection_frame, width=50)
            self.quantity_entry.grid(row=0, column=1, padx=5, pady=5, sticky="e")
            self.quantity_entry.insert(0, "1")

            # دکمه افزودن محصول
            self.add_product_button = ctk.CTkButton(
                self.product_selection_frame, text="افزودن",
                fg_color="#28a745", hover_color="#218838",
                width=80, command=self.add_product_to_sale
            )
            self.add_product_button.grid(row=0, column=0, padx=5, pady=5, sticky="w")

            # لیست محصولات انتخاب شده
            self.selected_products_frame = ctk.CTkFrame(self.sale_form_frame)
            self.selected_products_frame.grid(row=7, column=0, columnspan=2, padx=10, pady=5, sticky="ew")

            # ستون‌های جدول محصولات انتخاب شده
            self.selected_products_columns_frame = ctk.CTkFrame(self.selected_products_frame, fg_color="#343a40")
            self.selected_products_columns_frame.grid(row=0, column=0, sticky="ew")
            self.selected_products_columns_frame.grid_columnconfigure((0, 1, 2, 3, 4), weight=1,minsize=80)

            self.selected_product_col_name = ctk.CTkLabel(self.selected_products_columns_frame,font=("Arial", 12,"bold"), text="نام محصول", text_color="white")
            self.selected_product_col_name.grid(row=0, column=4, padx=5, pady=5, sticky="w")

            self.selected_product_col_price = ctk.CTkLabel(self.selected_products_columns_frame,font=("Arial", 12,"bold"), text="قیمت واحد", text_color="white")
            self.selected_product_col_price.grid(row=0, column=3, padx=5, pady=5, sticky="w")

            self.selected_product_col_quantity = ctk.CTkLabel(self.selected_products_columns_frame, text="تعداد", text_color="white", font=ctk.CTkFont(weight="bold"))
            self.selected_product_col_quantity.grid(row=0, column=2, padx=5, pady=5, sticky="w")

            self.selected_product_col_total = ctk.CTkLabel(self.selected_products_columns_frame,font=("Arial", 12,"bold"), text="قیمت کل", text_color="white")
            self.selected_product_col_total.grid(row=0, column=1, padx=5, pady=5, sticky="w")

            self.selected_product_col_action = ctk.CTkLabel(self.selected_products_columns_frame, text="عملیات", text_color="white", font=ctk.CTkFont(weight="bold"))
            self.selected_product_col_action.grid(row=0, column=0, padx=5, pady=5, sticky="w")

            # محتوای جدول محصولات انتخاب شده
            self.selected_products_table_frame = ctk.CTkScrollableFrame(self.selected_products_frame, height=150)
            self.selected_products_table_frame.grid(row=1, column=0, sticky="nsew")
            self.selected_products_table_frame.grid_columnconfigure((0, 1, 2, 3, 4), weight=1,minsize=80)

            # مجموع قیمت
            self.total_price_frame = ctk.CTkFrame(self.sale_form_frame)
            self.total_price_frame.grid(row=8, column=0, columnspan=2, padx=2, pady=2, sticky="ew")

            self.total_price_label = ctk.CTkLabel(
                self.total_price_frame, text="مجموع قیمت:",
                font=("Arial", 14,"bold")
            )
            self.total_price_label.grid(row=0, column=0, padx=10, pady=5, sticky="w")

            self.total_price_value = ctk.CTkLabel(
                self.total_price_frame, text="0 تومان",
                font=("Arial", 14,"bold")
            )
            self.total_price_value.grid(row=0, column=1, padx=10, pady=5, sticky="e")

            # دکمه‌های عملیات
            self.sale_buttons_frame = ctk.CTkFrame(self.sale_form_frame, fg_color="transparent")
            self.sale_buttons_frame.grid(row=9, column=0, columnspan=2, padx=2, pady=2, sticky="ew")
            self.sale_buttons_frame.grid_columnconfigure((0, 1), weight=1)

            self.save_sale_button = ctk.CTkButton(
                self.sale_buttons_frame, text="ثبت فروش",
                fg_color="#28a745", hover_color="#218838",
                font=("Arial", 12,"bold"),
                command=self.save_sale
            )
            self.save_sale_button.grid(row=0, column=1, padx=5, pady=5, sticky="ew")

            self.clear_sale_button = ctk.CTkButton(
                self.sale_buttons_frame, text="پاک کردن",
                fg_color="#6c757d", hover_color="#5a6268",
                font=("Arial", 12,"bold"),
                command=self.clear_sale_form
            )
            self.clear_sale_button.grid(row=0, column=0, padx=5, pady=5, sticky="ew")

            # فریم سمت چپ برای نمایش لیست فروش‌ها
            self.sales_list_frame = ctk.CTkFrame(self.sales_main_frame)
            self.sales_list_frame.grid(row=0, column=0, padx=10, pady=10, sticky="nsew")

            # عنوان لیست
            self.sales_list_title = ctk.CTkLabel(
                self.sales_list_frame, text="لیست فروش‌ها",
                font=("Arial", 16,"bold")
            )
            self.sales_list_title.grid(row=0, column=0, padx=10, pady=10, sticky="n")

            # جستجو
            self.sale_search_frame = ctk.CTkFrame(self.sales_list_frame, fg_color="transparent")
            self.sale_search_frame.grid(row=1, column=0, padx=10, pady=5, sticky="ew")

            self.sale_search_label = ctk.CTkLabel(self.sale_search_frame, text="جستجو")
            self.sale_search_label.grid(row=0, column=2, padx=5, pady=5, sticky="e")

            self.sale_search_entry = ctk.CTkEntry(self.sale_search_frame, width=200)
            self.sale_search_entry.grid(row=0, column=1, padx=5, pady=5, sticky="w")

            self.sale_search_button = ctk.CTkButton(
                self.sale_search_frame, text="جستجو",
                fg_color="#007bff", hover_color="#0069d9",
                command=self.search_sales
            )
            self.sale_search_button.grid(row=0, column=0, padx=5, pady=5, sticky="w")

            # لیست فروش‌ها
            self.sales_listbox_frame = ctk.CTkFrame(self.sales_list_frame)
            self.sales_listbox_frame.grid(row=2, column=0, padx=10, pady=10, sticky="nsew")

            # ستون‌های جدول
            self.sale_columns_frame = ctk.CTkFrame(self.sales_listbox_frame, fg_color="#343a40")
            self.sale_columns_frame.grid(row=0, column=0, sticky="ew")
            self.sale_columns_frame.grid_columnconfigure((0, 1, 2, 3, 4), weight=1,minsize=75)

            self.sale_col_invoice = ctk.CTkLabel(self.sale_columns_frame, text="شماره فاکتور", text_color="white", font=("Arial", 12,"bold"))
            self.sale_col_invoice.grid(row=0, column=4, padx=5, pady=5, sticky="w")

            self.sale_col_customer = ctk.CTkLabel(self.sale_columns_frame, text="مشتری", text_color="white", font=("Arial", 12,"bold"))
            self.sale_col_customer.grid(row=0, column=3, padx=5, pady=5, sticky="w")

            self.sale_col_date = ctk.CTkLabel(self.sale_columns_frame, text="تاریخ", text_color="white", font=("Arial", 12,"bold"))
            self.sale_col_date.grid(row=0, column=2, padx=5, pady=5, sticky="w")

            self.sale_col_amount = ctk.CTkLabel(self.sale_columns_frame, text="مبلغ کل", text_color="white", font=("Arial", 12,"bold"))
            self.sale_col_amount.grid(row=0, column=1, padx=5, pady=5, sticky="w")

            self.sale_col_payment = ctk.CTkLabel(self.sale_columns_frame,font=("Arial", 12,"bold"), text="روش پرداخت", text_color="white")
            self.sale_col_payment.grid(row=0, column=0, padx=5, pady=5, sticky="w")

            # محتوای جدول
            self.sales_table_frame = ctk.CTkScrollableFrame(self.sales_listbox_frame, height=300)
            self.sales_table_frame.grid(row=1, column=0, sticky="nsew")
            self.sales_table_frame.grid_columnconfigure((0, 1, 2, 3, 4), weight=1,minsize=80)

            # بارگذاری مشتریان و محصولات
            self.load_customers_for_sale()
            self.load_products_for_sale()

            # بارگذاری فروش‌ها
            self.load_sales()

            # لیست محصولات انتخاب شده برای فروش
            self.selected_products = []

            # متغیر برای نگهداری شناسه فروش در حال ویرایش
            self.current_sale_id = None

    def create_inventory_frame(self):
        """ایجاد صفحه مدیریت موجودی"""
        if not hasattr(self, "inventory_frame"):
            self.inventory_frame = ctk.CTkFrame(self, corner_radius=0, fg_color="transparent")
            self.inventory_frame.grid_columnconfigure(0, weight=1)

            # عنوان موجودی
            self.inventory_label = ctk.CTkLabel(
                self.inventory_frame, text="مدیریت موجودی",
                font=("Arial", 20,"bold")
            )
            self.inventory_label.grid(row=0, column=0, padx=5, pady=5)

            # ایجاد فریم اصلی موجودی
            self.inventory_main_frame = ctk.CTkFrame(self.inventory_frame)
            self.inventory_main_frame.grid(row=1, column=0, padx=5, pady=5, sticky="nsew")
            self.inventory_main_frame.grid_columnconfigure(0, weight=1)

            # فریم بالایی برای فیلترها و جستجو
            self.inventory_filters_frame = ctk.CTkFrame(self.inventory_main_frame)
            self.inventory_filters_frame.grid(row=0, column=0, padx=10, pady=10, sticky="e")

            # جستجو
            self.inventory_search_label = ctk.CTkLabel(self.inventory_filters_frame, text="جستجو")
            self.inventory_search_label.grid(row=0, column=6, padx=5, pady=5, sticky="w")

            self.inventory_search_entry = ctk.CTkEntry(self.inventory_filters_frame, width=200)
            self.inventory_search_entry.grid(row=0, column=5, padx=5, pady=5, sticky="w")

            self.inventory_search_button = ctk.CTkButton(
                self.inventory_filters_frame, text="جستجو",
                fg_color="#007bff", hover_color="#0069d9",
                command=lambda: self.search_inventory()
            )
            self.inventory_search_button.grid(row=0, column=4, padx=5, pady=5, sticky="w")

            # فیلتر دسته‌بندی
            self.inventory_category_label = ctk.CTkLabel(self.inventory_filters_frame,font=("Arial", 14,"bold"), text="دسته‌بندی")
            self.inventory_category_label.grid(row=0, column=3, padx=5, pady=5, sticky="w")

            self.inventory_category_combobox = ctk.CTkComboBox(self.inventory_filters_frame, width=150, command=lambda x: self.filter_low_stock())
            self.inventory_category_combobox.grid(row=0, column=2, padx=5, pady=5, sticky="w")

            # فیلتر موجودی کم
            self.low_stock_var = ctk.BooleanVar(value=False)
            self.low_stock_checkbox = ctk.CTkCheckBox(
                self.inventory_filters_frame, text="فقط موجودی کم",
                font=("Arial", 16,"bold"),
                variable=self.low_stock_var,
                command=self.filter_low_stock
            )
            self.low_stock_checkbox.grid(row=0, column=1, padx=5, pady=5, sticky="w")

            # دکمه به‌روزرسانی موجودی
            self.update_inventory_button = ctk.CTkButton(
                self.inventory_filters_frame, text="به‌روزرسانی موجودی",
                font=("Arial", 16,"bold"),
                fg_color="#28a745", hover_color="#218838",
                command=self.show_update_inventory_dialog
            )
            self.update_inventory_button.grid(row=0, column=0, padx=5, pady=5, sticky="w")

            # فریم آمار موجودی
            self.inventory_stats_frame = ctk.CTkFrame(self.inventory_main_frame)
            self.inventory_stats_frame.grid(row=1, column=0, padx=10, pady=10, sticky="ew")
            self.inventory_stats_frame.grid_columnconfigure((0, 1, 2, 3), weight=1)

            # کارت تعداد کل محصولات
            self.total_products_card = ctk.CTkFrame(self.inventory_stats_frame)
            self.total_products_card.grid(row=0, column=3, padx=10, pady=10, sticky="nsew")

            self.total_products_title = ctk.CTkLabel(
                self.total_products_card, text="تعداد کل محصولات",
                font=("Arial", 14,"bold")
            )
            self.total_products_title.grid(row=0, column=0, padx=10, pady=5)

            self.total_products_count = ctk.CTkLabel(
                self.total_products_card, text="0",
                font=ctk.CTkFont(size=20, weight="bold")
            )
            self.total_products_count.grid(row=1, column=0, padx=10, pady=5)

            # کارت تعداد کل موجودی
            self.total_stock_card = ctk.CTkFrame(self.inventory_stats_frame)
            self.total_stock_card.grid(row=0, column=2, padx=10, pady=10, sticky="nsew")

            self.total_stock_title = ctk.CTkLabel(
                self.total_stock_card, text="تعداد کل موجودی",
                font=("Arial", 14,"bold")
            )
            self.total_stock_title.grid(row=0, column=0, padx=10, pady=5)

            self.total_stock_count = ctk.CTkLabel(
                self.total_stock_card, text="0",
                font=("Arial", 20,"bold")
            )
            self.total_stock_count.grid(row=1, column=0, padx=10, pady=5)

            # کارت محصولات با موجودی کم
            self.low_stock_card = ctk.CTkFrame(self.inventory_stats_frame)
            self.low_stock_card.grid(row=0, column=1, padx=10, pady=10, sticky="nsew")

            self.low_stock_title = ctk.CTkLabel(
                self.low_stock_card, text="محصولات با موجودی کم",
                font=("Arial", 14,"bold")
            )
            self.low_stock_title.grid(row=0, column=0, padx=10, pady=5)

            self.low_stock_count = ctk.CTkLabel(
                self.low_stock_card, text="0",
                font=ctk.CTkFont(size=20, weight="bold")
            )
            self.low_stock_count.grid(row=1, column=0, padx=10, pady=5)

            # کارت ارزش موجودی
            self.stock_value_card = ctk.CTkFrame(self.inventory_stats_frame)
            self.stock_value_card.grid(row=0, column=0, padx=10, pady=10, sticky="nsew")

            self.stock_value_title = ctk.CTkLabel(
                self.stock_value_card, text="ارزش کل موجودی",
                font=("Arial", 14,"bold")
            )
            self.stock_value_title.grid(row=0, column=0, padx=10, pady=5)

            self.stock_value_amount = ctk.CTkLabel(
                self.stock_value_card, text="0 تومان",
                font=("Arial", 20,"bold")
            )
            self.stock_value_amount.grid(row=1, column=0, padx=10, pady=5)

            # فریم جدول موجودی
            self.inventory_table_frame = ctk.CTkFrame(self.inventory_main_frame)
            self.inventory_table_frame.grid(row=2, column=0, padx=10, pady=10, sticky="n")

            # ستون‌های جدول
            self.inventory_columns_frame = ctk.CTkFrame(self.inventory_table_frame, fg_color="#343a40")
            self.inventory_columns_frame.grid(row=0, column=0, sticky="nsew")
            self.inventory_columns_frame.grid_columnconfigure((0, 1, 2, 3, 4, 5, 6), weight=1,minsize=125)

            self.inventory_col_code = ctk.CTkLabel(self.inventory_columns_frame, text="کد", text_color="white", font=ctk.CTkFont(weight="bold"))
            self.inventory_col_code.grid(row=0, column=6, padx=5, pady=5, sticky="n")

            self.inventory_col_name = ctk.CTkLabel(self.inventory_columns_frame, text="نام محصول", text_color="white", font=("Arial", 12,"bold"))
            self.inventory_col_name.grid(row=0, column=5, padx=5, pady=5, sticky="n")

            self.inventory_col_category = ctk.CTkLabel(self.inventory_columns_frame, text="دسته‌بندی", text_color="white", font=("Arial", 12,"bold"))
            self.inventory_col_category.grid(row=0, column=4, padx=5, pady=5, sticky="n")

            self.inventory_col_quantity = ctk.CTkLabel(self.inventory_columns_frame, text="موجودی", text_color="white", font=ctk.CTkFont(weight="bold"))
            self.inventory_col_quantity.grid(row=0, column=3, padx=5, pady=5, sticky="n")

            self.inventory_col_price = ctk.CTkLabel(self.inventory_columns_frame, text="قیمت فروش", text_color="white", font=("Arial", 12,"bold"))
            self.inventory_col_price.grid(row=0, column=2, padx=5, pady=5, sticky="n")

            self.inventory_col_value = ctk.CTkLabel(self.inventory_columns_frame, text="ارزش موجودی", text_color="white", font=("Arial", 12,"bold"))
            self.inventory_col_value.grid(row=0, column=1, padx=5, pady=5, sticky="n")

            self.inventory_col_action = ctk.CTkLabel(self.inventory_columns_frame, text="عملیات", text_color="white", font=ctk.CTkFont(weight="bold"))
            self.inventory_col_action.grid(row=0, column=0, padx=5, pady=5, sticky="n")

            # محتوای جدول
            self.inventory_items_frame = ctk.CTkScrollableFrame(self.inventory_table_frame, height=400)
            self.inventory_items_frame.grid(row=1, column=0, sticky="nsew")
            self.inventory_items_frame.grid_columnconfigure((0, 1, 2, 3, 4, 5, 6), weight=1)

            # بارگذاری دسته‌بندی‌ها
            self.load_categories_for_inventory()

            # بارگذاری موجودی
            self.load_inventory()

    def create_reports_frame(self):
        """ایجاد صفحه گزارشات و تحلیل‌ها"""
        if not hasattr(self, "reports_frame"):
            self.reports_frame = ctk.CTkFrame(self, corner_radius=1, fg_color="transparent")
            self.reports_frame.grid_columnconfigure(0, weight=1)

            # عنوان گزارشات
            self.reports_label = ctk.CTkLabel(
                self.reports_frame, text="گزارشات و تحلیل‌ها",
                font=("Arial", 20,"bold")
            )
            self.reports_label.grid(row=0, column=0, padx=5, pady=5)

            # ایجاد فریم اصلی گزارشات
            self.reports_main_frame = ctk.CTkFrame(self.reports_frame)
            self.reports_main_frame.grid(row=1, column=0, padx=5, pady=5, sticky="nsew")
            self.reports_main_frame.grid_columnconfigure(0, weight=1)

            # فریم انتخاب نوع گزارش
            self.report_type_frame = ctk.CTkFrame(self.reports_main_frame)
            self.report_type_frame.grid(row=0, column=1, padx=10, pady=10, sticky="e")

            # عنوان انتخاب نوع گزارش
            self.report_type_label = ctk.CTkLabel(
                self.report_type_frame, text="انتخاب نوع گزارش",
                font=("Arial", 16,"bold")
            )
            self.report_type_label.grid(row=0, column=0, padx=10, pady=10, sticky="n")

            # دکمه‌های انتخاب نوع گزارش
            self.report_buttons_frame = ctk.CTkFrame(self.report_type_frame, fg_color="transparent")
            self.report_buttons_frame.grid(row=1, column=0, padx=10, pady=5, sticky="e")

            self.sales_report_button = ctk.CTkButton(
                self.report_buttons_frame, text="گزارش فروش",
                font=("Arial", 14,"bold"),
                fg_color="#007bff", hover_color="#0069d9",
                command=lambda: self.show_report("sales")
            )
            self.sales_report_button.grid(row=0, column=3, padx=5, pady=5, sticky="ew")

            self.products_report_button = ctk.CTkButton(
                self.report_buttons_frame, text="گزارش محصولات",
                font=("Arial", 14,"bold"),
                fg_color="#6c757d", hover_color="#5a6268",
                command=lambda: self.show_report("products")
            )
            self.products_report_button.grid(row=0, column=2, padx=5, pady=5, sticky="ew")

            self.customers_report_button = ctk.CTkButton(
                self.report_buttons_frame, text="گزارش مشتریان",
                font=("Arial", 14,"bold"),
                fg_color="#6c757d", hover_color="#5a6268",
                command=lambda: self.show_report("customers")
            )
            self.customers_report_button.grid(row=0, column=1, padx=5, pady=5, sticky="ew")

            self.inventory_report_button = ctk.CTkButton(
                self.report_buttons_frame, text="گزارش موجودی",
                font=("Arial", 14,"bold"),
                fg_color="#6c757d", hover_color="#5a6268",
                command=lambda: self.show_report("inventory")
            )
            self.inventory_report_button.grid(row=0, column=0, padx=5, pady=5, sticky="ew")

            # فریم فیلترهای گزارش
            self.report_filters_frame = ctk.CTkFrame(self.reports_main_frame)
            self.report_filters_frame.grid(row=1, column=1, padx=10, pady=10, sticky="ew")

            # فیلترهای گزارش فروش (پیش‌فرض)
            self.sales_report_filters_frame = ctk.CTkFrame(self.report_filters_frame, fg_color="transparent")
            self.sales_report_filters_frame.grid(row=0, column=0, sticky="e")

            # بازه زمانی
            self.date_range_label = ctk.CTkLabel(self.sales_report_filters_frame,font=("Arial", 14,"bold"), text="بازه زمانی")
            self.date_range_label.grid(row=0, column=4, padx=5, pady=5, sticky="w")

            self.date_range_combobox = ctk.CTkComboBox(
                self.sales_report_filters_frame,
                font=("Arial", 14,"bold"),
                values=["امروز", "هفته جاری", "ماه جاری", "سال جاری", "همه زمان‌ها"],
                width=150
            )
            self.date_range_combobox.grid(row=0, column=3, padx=5, pady=5, sticky="w")
            self.date_range_combobox.set("ماه جاری")

            # مشتری
            self.report_customer_label = ctk.CTkLabel(self.sales_report_filters_frame, text="مشتری")
            self.report_customer_label.grid(row=0, column=2, padx=5, pady=5, sticky="w")

            self.report_customer_combobox = ctk.CTkComboBox(
                self.sales_report_filters_frame,
                values=["همه مشتریان"],
                width=150
            )
            self.report_customer_combobox.grid(row=0, column=1, padx=5, pady=5, sticky="w")
            self.report_customer_combobox.set("همه مشتریان")

            # دکمه اعمال فیلتر
            self.apply_filter_button = ctk.CTkButton(
                self.sales_report_filters_frame, text="اعمال فیلتر",
                font=("Arial", 14,"bold"),
                fg_color="#28a745", hover_color="#218838",
                command=self.apply_report_filters
            )
            self.apply_filter_button.grid(row=0, column=0, padx=5, pady=5, sticky="w")

            # فریم نتایج گزارش
            self.report_results_frame = ctk.CTkFrame(self.reports_main_frame)
            self.report_results_frame.grid(row=2, column=1, padx=10, pady=10, sticky="e")

            # عنوان نتایج گزارش
            self.report_results_title = ctk.CTkLabel(
                self.report_results_frame, text="گزارش فروش",
                font=("Arial", 16,"bold"),
            )
            self.report_results_title.grid(row=0, column=0, padx=10, pady=10, sticky="e")

            # خلاصه گزارش
            self.report_summary_frame = ctk.CTkFrame(self.report_results_frame)
            self.report_summary_frame.grid(row=1, column=0, padx=10, pady=10, sticky="ew")
            self.report_summary_frame.grid_columnconfigure((0, 1, 2), weight=1)

            # کارت تعداد فروش
            self.sales_count_card = ctk.CTkFrame(self.report_summary_frame)
            self.sales_count_card.grid(row=0, column=2, padx=10, pady=10, sticky="nsew")

            self.sales_count_title = ctk.CTkLabel(
                self.sales_count_card, text="تعداد فروش",
                font=("Arial", 14,"bold")
            )
            self.sales_count_title.grid(row=0, column=0, padx=10, pady=5)

            self.sales_count_value = ctk.CTkLabel(
                self.sales_count_card, text="0",
                font=("Arial", 20,"bold"),
            )
            self.sales_count_value.grid(row=1, column=0, padx=10, pady=5)

            # کارت مجموع فروش
            self.sales_total_card = ctk.CTkFrame(self.report_summary_frame)
            self.sales_total_card.grid(row=0, column=1, padx=10, pady=10, sticky="nsew")

            self.sales_total_title = ctk.CTkLabel(
                self.sales_total_card, text="مجموع فروش",
                font=("Arial", 14,"bold"),
            )
            self.sales_total_title.grid(row=0, column=0, padx=10, pady=5)

            self.sales_total_value = ctk.CTkLabel(
                self.sales_total_card, text="0 تومان",
                font=("Arial", 20,"bold"),
            )
            self.sales_total_value.grid(row=1, column=0, padx=10, pady=5)

            # کارت میانگین فروش
            self.sales_average_card = ctk.CTkFrame(self.report_summary_frame)
            self.sales_average_card.grid(row=0, column=0, padx=10, pady=10, sticky="nsew")

            self.sales_average_title = ctk.CTkLabel(
                self.sales_average_card, text="میانگین فروش",
                font=("Arial", 14,"bold")
            )
            self.sales_average_title.grid(row=0, column=0, padx=10, pady=5)

            self.sales_average_value = ctk.CTkLabel(
                self.sales_average_card, text="0 تومان",
                font=("Arial", 20,"bold"),
            )
            self.sales_average_value.grid(row=1, column=0, padx=10, pady=5)

            # جدول نتایج گزارش
            self.report_table_frame = ctk.CTkFrame(self.report_results_frame)
            self.report_table_frame.grid(row=2, column=0, padx=10, pady=10, sticky="e")

            # ستون‌های جدول گزارش فروش (پیش‌فرض)
            self.report_columns_frame = ctk.CTkFrame(self.report_table_frame, fg_color="#343a40")
            self.report_columns_frame.grid(row=0, column=0, sticky="n")
            self.report_columns_frame.grid_columnconfigure((0, 1, 2, 3, 4), weight=1,minsize=100)

            self.report_col_invoice = ctk.CTkLabel(self.report_columns_frame,font=("Arial", 12,"bold"), text="شماره فاکتور", text_color="white")
            self.report_col_invoice.grid(row=0, column=4, padx=5, pady=5, sticky="n")

            self.report_col_customer = ctk.CTkLabel(self.report_columns_frame, text="مشتری", text_color="white", font=ctk.CTkFont(weight="bold"))
            self.report_col_customer.grid(row=0, column=3, padx=5, pady=5, sticky="n")

            self.report_col_date = ctk.CTkLabel(self.report_columns_frame, text="تاریخ", text_color="white", font=ctk.CTkFont(weight="bold"))
            self.report_col_date.grid(row=0, column=2, padx=5, pady=5, sticky="n")

            self.report_col_amount = ctk.CTkLabel(self.report_columns_frame, text="مبلغ", text_color="white", font=ctk.CTkFont(weight="bold"))
            self.report_col_amount.grid(row=0, column=1, padx=5, pady=5, sticky="n")

            self.report_col_payment = ctk.CTkLabel(self.report_columns_frame, text="روش پرداخت", text_color="white", font=("Arial", 12,"bold"),)
            self.report_col_payment.grid(row=0, column=0, padx=5, pady=5, sticky="n")

            # محتوای جدول
            self.report_items_frame = ctk.CTkScrollableFrame(self.report_table_frame, height=200)
            self.report_items_frame.grid(row=1, column=0, sticky="nsew")
            self.report_items_frame.grid_columnconfigure((0, 1, 2, 3, 4), weight=1)

            # دکمه‌های عملیات
            self.report_actions_frame = ctk.CTkFrame(self.report_results_frame, fg_color="transparent")
            self.report_actions_frame.grid(row=3, column=0, padx=10, pady=10, sticky="n")

            self.export_report_button = ctk.CTkButton(
                self.report_actions_frame, text="خروجی گزارش",
                font=("Arial", 14,"bold"),
                fg_color="#17a2b8", hover_color="#138496",
                command=self.export_report
            )
            self.export_report_button.pack(side="left", padx=5, pady=5)

            self.print_report_button = ctk.CTkButton(
                self.report_actions_frame, text="چاپ گزارش",
                font=("Arial", 14,"bold"),
                fg_color="#6c757d", hover_color="#5a6268",
                command=self.print_report
            )
            self.print_report_button.pack(side="left", padx=5, pady=5)

            # بارگذاری مشتریان برای فیلتر گزارش
            self.load_customers_for_report()

            # نمایش گزارش فروش به صورت پیش‌فرض
            self.show_report("sales")

    def load_customers_for_report(self):
        """بارگذاری مشتریان در کومبوباکس گزارش"""
        customers = self.db.get_all_customers()

        # ساخت لیست نام مشتریان
        customer_names = ["همه مشتریان"]
        self.report_customer_ids = {}

        for customer in customers:
            customer_name = f"{customer['name']} ({customer['phone'] if customer['phone'] else 'بدون شماره'})"
            customer_names.append(customer_name)
            self.report_customer_ids[customer_name] = customer['id']

        # تنظیم مقادیر کومبوباکس
        self.report_customer_combobox.configure(values=customer_names)
        self.report_customer_combobox.set(customer_names[0])

    def show_report(self, report_type):
        """نمایش گزارش بر اساس نوع"""
        # تغییر رنگ دکمه‌های گزارش
        self.sales_report_button.configure(fg_color="#6c757d")
        self.products_report_button.configure(fg_color="#6c757d")
        self.customers_report_button.configure(fg_color="#6c757d")
        self.inventory_report_button.configure(fg_color="#6c757d")

        # تنظیم دکمه انتخاب شده
        if report_type == "sales":
            self.sales_report_button.configure(fg_color="#007bff")
            self.report_results_title.configure(text="گزارش فروش")

            # نمایش فیلترهای مناسب
            self.sales_report_filters_frame.grid(row=0, column=0, sticky="ew")

            # تنظیم ستون‌های جدول
            self.report_columns_frame.grid_columnconfigure((0, 1, 2, 3, 4), weight=1)

            self.report_col_invoice.grid(row=0, column=0, padx=5, pady=5, sticky="w")
            self.report_col_customer.grid(row=0, column=1, padx=5, pady=5, sticky="w")
            self.report_col_date.grid(row=0, column=2, padx=5, pady=5, sticky="w")
            self.report_col_amount.grid(row=0, column=3, padx=5, pady=5, sticky="w")
            self.report_col_payment.grid(row=0, column=4, padx=5, pady=5, sticky="w")

            # تنظیم عناوین کارت‌های خلاصه
            self.sales_count_title.configure(text="تعداد فروش")
            self.sales_total_title.configure(text="مجموع فروش")
            self.sales_average_title.configure(text="میانگین فروش")

            # بارگذاری گزارش فروش
            self.load_sales_report()

        elif report_type == "products":
            self.products_report_button.configure(fg_color="#007bff")
            self.report_results_title.configure(text="گزارش محصولات")

            # نمایش فیلترهای مناسب
            self.sales_report_filters_frame.grid_forget()

            # تنظیم ستون‌های جدول
            self.report_columns_frame.grid_columnconfigure((0, 1, 2, 3, 4), weight=1)

            self.report_col_invoice.configure(text="کد محصول")
            self.report_col_customer.configure(text="نام محصول")
            self.report_col_date.configure(text="دسته‌بندی")
            self.report_col_amount.configure(text="تعداد فروش")
            self.report_col_payment.configure(text="مبلغ فروش")

            # تنظیم عناوین کارت‌های خلاصه
            self.sales_count_title.configure(text="تعداد محصولات")
            self.sales_total_title.configure(text="تعداد کل فروش")
            self.sales_average_title.configure(text="مبلغ کل فروش")

            # بارگذاری گزارش محصولات
            self.load_products_report()

        elif report_type == "customers":
            self.customers_report_button.configure(fg_color="#007bff")
            self.report_results_title.configure(text="گزارش مشتریان")

            # نمایش فیلترهای مناسب
            self.sales_report_filters_frame.grid_forget()

            # تنظیم ستون‌های جدول
            self.report_columns_frame.grid_columnconfigure((0, 1, 2, 3, 4), weight=1)

            self.report_col_invoice.configure(text="شناسه مشتری")
            self.report_col_customer.configure(text="نام مشتری")
            self.report_col_date.configure(text="شماره تماس")
            self.report_col_amount.configure(text="تعداد خرید")
            self.report_col_payment.configure(text="مبلغ کل خرید")

            # تنظیم عناوین کارت‌های خلاصه
            self.sales_count_title.configure(text="تعداد مشتریان")
            self.sales_total_title.configure(text="تعداد کل خرید")
            self.sales_average_title.configure(text="مبلغ کل خرید")

            # بارگذاری گزارش مشتریان
            self.load_customers_report()

        elif report_type == "inventory":
            self.inventory_report_button.configure(fg_color="#007bff")
            self.report_results_title.configure(text="گزارش موجودی")

            # نمایش فیلترهای مناسب
            self.sales_report_filters_frame.grid_forget()

            # تنظیم ستون‌های جدول
            self.report_columns_frame.grid_columnconfigure((0, 1, 2, 3, 4), weight=1)

            self.report_col_invoice.configure(text="کد محصول")
            self.report_col_customer.configure(text="نام محصول")
            self.report_col_date.configure(text="دسته‌بندی")
            self.report_col_amount.configure(text="موجودی")
            self.report_col_payment.configure(text="ارزش موجودی")

            # تنظیم عناوین کارت‌های خلاصه
            self.sales_count_title.configure(text="تعداد محصولات")
            self.sales_total_title.configure(text="تعداد کل موجودی")
            self.sales_average_title.configure(text="ارزش کل موجودی")

            # بارگذاری گزارش موجودی
            self.load_inventory_report()

    def apply_report_filters(self):
        """اعمال فیلترهای گزارش"""
        # بررسی نوع گزارش فعلی
        if self.sales_report_button.cget("fg_color") == "#007bff":
            self.load_sales_report()
        elif self.products_report_button.cget("fg_color") == "#007bff":
            self.load_products_report()
        elif self.customers_report_button.cget("fg_color") == "#007bff":
            self.load_customers_report()
        elif self.inventory_report_button.cget("fg_color") == "#007bff":
            self.load_inventory_report()

    def load_sales_report(self):
        """بارگذاری گزارش فروش"""
        # پاک کردن جدول فعلی
        for widget in self.report_items_frame.winfo_children():
            widget.destroy()

        # دریافت فیلترها
        date_range = self.date_range_combobox.get()
        customer_name = self.report_customer_combobox.get()

        # تبدیل بازه زمانی به تاریخ
        from datetime import datetime, timedelta
        today = datetime.now().date()

        if date_range == "امروز":
            start_date = today
        elif date_range == "هفته جاری":
            start_date = today - timedelta(days=today.weekday())
        elif date_range == "ماه جاری":
            start_date = today.replace(day=1)
        elif date_range == "سال جاری":
            start_date = today.replace(month=1, day=1)
        else:  # همه زمان‌ها
            start_date = None

        # تبدیل مشتری به شناسه
        if customer_name == "همه مشتریان":
            customer_id = None
        else:
            customer_id = self.report_customer_ids.get(customer_name)

        # دریافت فروش‌ها با فیلتر
        sales = self.db.get_sales_report(start_date, customer_id)

        if not sales:
            no_data_label = ctk.CTkLabel(
                self.report_items_frame,
                text="هیچ فروشی یافت نشد",
                font=ctk.CTkFont(size=12)
            )
            no_data_label.grid(row=0, column=0, columnspan=5, padx=10, pady=20)

            # به‌روزرسانی آمار
            self.sales_count_value.configure(text="0")
            self.sales_total_value.configure(text="0 تومان")
            self.sales_average_value.configure(text="0 تومان")
            return

        # نمایش فروش‌ها در جدول
        for i, sale in enumerate(sales):
            row_frame = ctk.CTkFrame(self.report_items_frame)
            row_frame.grid(row=i, column=0, columnspan=5, sticky="ew", padx=5, pady=2)
            row_frame.grid_columnconfigure((0, 1, 2, 3, 4), weight=1)

            # رنگ پس‌زمینه متناوب برای سطرها
            bg_color = "#f8f9fa" if i % 2 == 0 else "#e9ecef"
            row_frame.configure(fg_color=bg_color)

            invoice_label = ctk.CTkLabel(row_frame, text=sale["invoice_number"])
            invoice_label.grid(row=0, column=0, padx=5, pady=5, sticky="w")

            customer_label = ctk.CTkLabel(row_frame, text=sale["customer_name"] if sale["customer_name"] else "-")
            customer_label.grid(row=0, column=1, padx=5, pady=5, sticky="w")

            # تبدیل تاریخ به فرمت مناسب
            date_str = sale["sale_date"].split(" ")[0] if " " in sale["sale_date"] else sale["sale_date"]
            date_label = ctk.CTkLabel(row_frame, text=date_str)
            date_label.grid(row=0, column=2, padx=5, pady=5, sticky="w")

            amount_label = ctk.CTkLabel(row_frame, text=f"{sale['total_amount']:,} تومان")
            amount_label.grid(row=0, column=3, padx=5, pady=5, sticky="w")

            payment_label = ctk.CTkLabel(row_frame, text=sale["payment_method"])
            payment_label.grid(row=0, column=4, padx=5, pady=5, sticky="w")

        # به‌روزرسانی آمار
        total_sales = len(sales)
        total_amount = sum(sale["total_amount"] for sale in sales)
        average_amount = total_amount / total_sales if total_sales > 0 else 0

        self.sales_count_value.configure(text=str(total_sales))
        self.sales_total_value.configure(text=f"{total_amount:,} تومان")
        self.sales_average_value.configure(text=f"{average_amount:,.0f} تومان")

    def load_products_report(self):
        """بارگذاری گزارش محصولات"""
        # پاک کردن جدول فعلی
        for widget in self.report_items_frame.winfo_children():
            widget.destroy()

        # دریافت گزارش محصولات
        products = self.db.get_products_report()

        if not products:
            no_data_label = ctk.CTkLabel(
                self.report_items_frame,
                text="هیچ محصولی یافت نشد",
                font=ctk.CTkFont(size=12)
            )
            no_data_label.grid(row=0, column=0, columnspan=5, padx=10, pady=20)

            # به‌روزرسانی آمار
            self.sales_count_value.configure(text="0")
            self.sales_total_value.configure(text="0")
            self.sales_average_value.configure(text="0 تومان")
            return

        # نمایش محصولات در جدول
        for i, product in enumerate(products):
            row_frame = ctk.CTkFrame(self.report_items_frame)
            row_frame.grid(row=i, column=0, columnspan=5, sticky="ew", padx=5, pady=2)
            row_frame.grid_columnconfigure((0, 1, 2, 3, 4), weight=1)

            # رنگ پس‌زمینه متناوب برای سطرها
            bg_color = "#f8f9fa" if i % 2 == 0 else "#e9ecef"
            row_frame.configure(fg_color=bg_color)

            code_label = ctk.CTkLabel(row_frame, text=product["code"])
            code_label.grid(row=0, column=0, padx=5, pady=5, sticky="w")

            name_label = ctk.CTkLabel(row_frame, text=product["name"])
            name_label.grid(row=0, column=1, padx=5, pady=5, sticky="w")

            category_label = ctk.CTkLabel(row_frame, text=product["category_name"] if product["category_name"] else "-")
            category_label.grid(row=0, column=2, padx=5, pady=5, sticky="w")

            sales_count_label = ctk.CTkLabel(row_frame, text=str(product["sales_count"]))
            sales_count_label.grid(row=0, column=3, padx=5, pady=5, sticky="w")

            sales_amount_label = ctk.CTkLabel(row_frame, text=f"{product['sales_amount']:,} تومان")
            sales_amount_label.grid(row=0, column=4, padx=5, pady=5, sticky="w")

        # به‌روزرسانی آمار
        total_products = len(products)
        total_sales_count = sum(product["sales_count"] for product in products)
        total_sales_amount = sum(product["sales_amount"] for product in products)

        self.sales_count_value.configure(text=str(total_products))
        self.sales_total_value.configure(text=str(total_sales_count))
        self.sales_average_value.configure(text=f"{total_sales_amount:,} تومان")

    def load_customers_report(self):
        """بارگذاری گزارش مشتریان"""
        # پاک کردن جدول فعلی
        for widget in self.report_items_frame.winfo_children():
            widget.destroy()

        # دریافت گزارش مشتریان
        customers = self.db.get_customers_report()

        if not customers:
            no_data_label = ctk.CTkLabel(
                self.report_items_frame,
                text="هیچ مشتری‌ای یافت نشد",
                font=ctk.CTkFont(size=12)
            )
            no_data_label.grid(row=0, column=0, columnspan=5, padx=10, pady=20)

            # به‌روزرسانی آمار
            self.sales_count_value.configure(text="0")
            self.sales_total_value.configure(text="0")
            self.sales_average_value.configure(text="0 تومان")
            return

        # نمایش مشتریان در جدول
        for i, customer in enumerate(customers):
            row_frame = ctk.CTkFrame(self.report_items_frame)
            row_frame.grid(row=i, column=0, columnspan=5, sticky="ew", padx=5, pady=2)
            row_frame.grid_columnconfigure((0, 1, 2, 3, 4), weight=1)

            # رنگ پس‌زمینه متناوب برای سطرها
            bg_color = "#f8f9fa" if i % 2 == 0 else "#e9ecef"
            row_frame.configure(fg_color=bg_color)

            id_label = ctk.CTkLabel(row_frame, text=str(customer["id"]))
            id_label.grid(row=0, column=0, padx=5, pady=5, sticky="w")

            name_label = ctk.CTkLabel(row_frame, text=customer["name"])
            name_label.grid(row=0, column=1, padx=5, pady=5, sticky="w")

            phone_label = ctk.CTkLabel(row_frame, text=customer["phone"] if customer["phone"] else "-")
            phone_label.grid(row=0, column=2, padx=5, pady=5, sticky="w")

            sales_count_label = ctk.CTkLabel(row_frame, text=str(customer["sales_count"]))
            sales_count_label.grid(row=0, column=3, padx=5, pady=5, sticky="w")

            sales_amount_label = ctk.CTkLabel(row_frame, text=f"{customer['sales_amount']:,} تومان")
            sales_amount_label.grid(row=0, column=4, padx=5, pady=5, sticky="w")

        # به‌روزرسانی آمار
        total_customers = len(customers)
        total_sales_count = sum(customer["sales_count"] for customer in customers)
        total_sales_amount = sum(customer["sales_amount"] for customer in customers)

        self.sales_count_value.configure(text=str(total_customers))
        self.sales_total_value.configure(text=str(total_sales_count))
        self.sales_average_value.configure(text=f"{total_sales_amount:,} تومان")

    def load_inventory_report(self):
        """بارگذاری گزارش موجودی"""
        # پاک کردن جدول فعلی
        for widget in self.report_items_frame.winfo_children():
            widget.destroy()

        # دریافت گزارش موجودی
        products = self.db.get_all_products()

        if not products:
            no_data_label = ctk.CTkLabel(
                self.report_items_frame,
                text="هیچ محصولی یافت نشد",
                font=ctk.CTkFont(size=12)
            )
            no_data_label.grid(row=0, column=0, columnspan=5, padx=10, pady=20)

            # به‌روزرسانی آمار
            self.sales_count_value.configure(text="0")
            self.sales_total_value.configure(text="0")
            self.sales_average_value.configure(text="0 تومان")
            return

        # نمایش محصولات در جدول
        for i, product in enumerate(products):
            row_frame = ctk.CTkFrame(self.report_items_frame)
            row_frame.grid(row=i, column=0, columnspan=5, sticky="ew", padx=5, pady=2)
            row_frame.grid_columnconfigure((0, 1, 2, 3, 4), weight=1)

            # رنگ پس‌زمینه متناوب برای سطرها
            bg_color = "#f8f9fa" if i % 2 == 0 else "#e9ecef"

            # رنگ قرمز برای محصولات با موجودی کم
            if product["quantity"] <= 5:
                bg_color = "#ffcccc"  # رنگ قرمز کمرنگ

            row_frame.configure(fg_color=bg_color)

            code_label = ctk.CTkLabel(row_frame, text=product["code"])
            code_label.grid(row=0, column=0, padx=5, pady=5, sticky="w")

            name_label = ctk.CTkLabel(row_frame, text=product["name"])
            name_label.grid(row=0, column=1, padx=5, pady=5, sticky="w")

            category_label = ctk.CTkLabel(row_frame, text=product["category_name"] if product["category_name"] else "-")
            category_label.grid(row=0, column=2, padx=5, pady=5, sticky="w")

            quantity_label = ctk.CTkLabel(row_frame, text=str(product["quantity"]))
            quantity_label.grid(row=0, column=3, padx=5, pady=5, sticky="w")

            # محاسبه ارزش موجودی
            stock_value = product["quantity"] * product["selling_price"]
            value_label = ctk.CTkLabel(row_frame, text=f"{stock_value:,} تومان")
            value_label.grid(row=0, column=4, padx=5, pady=5, sticky="w")

        # به‌روزرسانی آمار
        total_products = len(products)
        total_quantity = sum(product["quantity"] for product in products)
        total_value = sum(product["quantity"] * product["selling_price"] for product in products)

        self.sales_count_value.configure(text=str(total_products))
        self.sales_total_value.configure(text=str(total_quantity))
        self.sales_average_value.configure(text=f"{total_value:,} تومان")

    def export_report(self):
        """خروجی گزارش به فایل CSV"""
        import csv
        from tkinter import filedialog

        # تعیین نوع گزارش فعلی
        if self.sales_report_button.cget("fg_color") == "#007bff":
            report_type = "sales"
            filename = "گزارش_فروش.csv"
            headers = ["شماره فاکتور", "مشتری", "تاریخ", "مبلغ", "روش پرداخت"]
        elif self.products_report_button.cget("fg_color") == "#007bff":
            report_type = "products"
            filename = "گزارش_محصولات.csv"
            headers = ["کد محصول", "نام محصول", "دسته‌بندی", "تعداد فروش", "مبلغ فروش"]
        elif self.customers_report_button.cget("fg_color") == "#007bff":
            report_type = "customers"
            filename = "گزارش_مشتریان.csv"
            headers = ["شناسه مشتری", "نام مشتری", "شماره تماس", "تعداد خرید", "مبلغ کل خرید"]
        elif self.inventory_report_button.cget("fg_color") == "#007bff":
            report_type = "inventory"
            filename = "گزارش_موجودی.csv"
            headers = ["کد محصول", "نام محصول", "دسته‌بندی", "موجودی", "ارزش موجودی"]

        # دریافت مسیر ذخیره فایل
        file_path = filedialog.asksaveasfilename(
            defaultextension=".csv",
            filetypes=[("CSV files", "*.csv")],
            initialfile=filename
        )

        if not file_path:
            return

        # دریافت داده‌های گزارش
        if report_type == "sales":
            # دریافت فیلترها
            date_range = self.date_range_combobox.get()
            customer_name = self.report_customer_combobox.get()

            # تبدیل بازه زمانی به تاریخ
            from datetime import datetime, timedelta
            today = datetime.now().date()

            if date_range == "امروز":
                start_date = today
            elif date_range == "هفته جاری":
                start_date = today - timedelta(days=today.weekday())
            elif date_range == "ماه جاری":
                start_date = today.replace(day=1)
            elif date_range == "سال جاری":
                start_date = today.replace(month=1, day=1)
            else:  # همه زمان‌ها
                start_date = None

            # تبدیل مشتری به شناسه
            if customer_name == "همه مشتریان":
                customer_id = None
            else:
                customer_id = self.report_customer_ids.get(customer_name)

            # دریافت فروش‌ها با فیلتر
            data = self.db.get_sales_report(start_date, customer_id)
            rows = [
                [
                    sale["invoice_number"],
                    sale["customer_name"] if sale["customer_name"] else "-",
                    sale["sale_date"].split(" ")[0] if " " in sale["sale_date"] else sale["sale_date"],
                    f"{sale['total_amount']:,}",
                    sale["payment_method"]
                ]
                for sale in data
            ]
        elif report_type == "products":
            data = self.db.get_products_report()
            rows = [
                [
                    product["code"],
                    product["name"],
                    product["category_name"] if product["category_name"] else "-",
                    str(product["sales_count"]),
                    f"{product['sales_amount']:,}"
                ]
                for product in data
            ]
        elif report_type == "customers":
            data = self.db.get_customers_report()
            rows = [
                [
                    str(customer["id"]),
                    customer["name"],
                    customer["phone"] if customer["phone"] else "-",
                    str(customer["sales_count"]),
                    f"{customer['sales_amount']:,}"
                ]
                for customer in data
            ]
        elif report_type == "inventory":
            data = self.db.get_all_products()
            rows = [
                [
                    product["code"],
                    product["name"],
                    product["category_name"] if product["category_name"] else "-",
                    str(product["quantity"]),
                    f"{product['quantity'] * product['selling_price']:,}"
                ]
                for product in data
            ]

        # نوشتن داده‌ها در فایل CSV
        try:
            with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                writer = csv.writer(csvfile)
                writer.writerow(headers)
                writer.writerows(rows)

            print(f"گزارش با موفقیت در مسیر {file_path} ذخیره شد")
        except Exception as e:
            print(f"خطا در ذخیره گزارش: {e}")
    #ناقص
    def print_report(self):
        """چاپ گزارش"""
        # این متد در نسخه‌های بعدی پیاده‌سازی خواهد شد
        print("قابلیت چاپ گزارش در نسخه‌های بعدی اضافه خواهد شد")

    def create_backup_frame(self):
        """ایجاد صفحه پشتیبان‌گیری و بازیابی"""
        if not hasattr(self, "backup_frame"):
            self.backup_frame = ctk.CTkFrame(self, corner_radius=0, fg_color="transparent")
            self.backup_frame.grid_columnconfigure(0, weight=1)

            # عنوان صفحه
            self.backup_title = ctk.CTkLabel(
                self.backup_frame, text="پشتیبان‌گیری و بازیابی اطلاعات",
                font=("Arial", 20, "bold")
            )
            self.backup_title.grid(row=0, column=1, padx=5, pady=5)

            # فریم اصلی
            self.backup_main_frame = ctk.CTkFrame(self.backup_frame)
            self.backup_main_frame.grid(row=1, column=1, padx=5, pady=5, sticky="nsew")
            self.backup_main_frame.grid_columnconfigure(0, weight=1)

            # بخش پشتیبان‌گیری
            self.backup_section = ctk.CTkFrame(self.backup_main_frame)
            self.backup_section.grid(row=0, column=0, padx=5, pady=5, sticky="we")

            self.backup_section_title = ctk.CTkLabel(
                self.backup_section, text="تهیه نسخه پشتیبان",
                font=("Arial", 16, "bold")
            )
            self.backup_section_title.grid(row=0, column=0, padx=5, pady=2, sticky="e")

            self.backup_description = ctk.CTkLabel(
                self.backup_section,
                text="با استفاده از این بخش می‌توانید از اطلاعات فروشگاه خود نسخه پشتیبان تهیه کنید ",
                font=("Arial", 14,"bold"),
                wraplength=700,
                justify="right"
            )
            self.backup_description.grid(row=1, column=0, padx=5, pady=2, sticky="e")

            self.backup_button_frame = ctk.CTkFrame(self.backup_section, fg_color="transparent")
            self.backup_button_frame.grid(row=2, column=0, padx=5, pady=5, sticky="e")

            self.create_backup_button = ctk.CTkButton(
                self.backup_button_frame, text="تهیه نسخه پشتیبان",
                font=("Arial", 14, "bold"),
                fg_color="#28a745", hover_color="#8E23B8",
                width=200, height=40,
                command=self.create_backup
            )
            self.create_backup_button.grid(row=0, column=0, padx=2, pady=2)

            # بخش بازیابی
            self.restore_section = ctk.CTkFrame(self.backup_main_frame)
            self.restore_section.grid(row=1, column=0, padx=5, pady=5, sticky="e")

            self.restore_section_title = ctk.CTkLabel(
                self.restore_section, text="بازیابی اطلاعات",
                font=("Arial", 16, "bold")
            )
            self.restore_section_title.grid(row=0, column=0, padx=5, pady=2, sticky="e")

            self.restore_description = ctk.CTkLabel(
                self.restore_section,
                text="با استفاده از این بخش می‌توانید اطلاعات فروشگاه خود را از یک نسخه پشتیبان بازیابی کنید. "
                     "توجه داشته باشید که با بازیابی اطلاعات، تمام اطلاعات فعلی جایگزین خواهند شد.",
                font=("Arial", 12),
                wraplength=700,
                justify="right"
            )
            self.restore_description.grid(row=1, column=0, padx=5, pady=2, sticky="e")

            self.restore_button_frame = ctk.CTkFrame(self.restore_section, fg_color="transparent")
            self.restore_button_frame.grid(row=2, column=0, padx=5, pady=5, sticky="e")

            self.select_backup_button = ctk.CTkButton(
                self.restore_button_frame, text="انتخاب فایل پشتیبان",
                font=("Arial", 14, "bold"),
                fg_color="#007bff", hover_color="#0069d9",
                width=200, height=40,
                command=self.select_backup_file
            )
            self.select_backup_button.grid(row=0, column=0, padx=2, pady=2)

            # بخش نسخه‌های پشتیبان موجود
            self.backups_section = ctk.CTkFrame(self.backup_main_frame)
            self.backups_section.grid(row=2, column=0, padx=5, pady=5, sticky="e")

            self.backups_section_title = ctk.CTkLabel(
                self.backups_section, text="نسخه‌های پشتیبان موجود",
                font=("Arial", 16, "bold")
            )
            self.backups_section_title.grid(row=0, column=0, padx=5, pady=2, sticky="e")

            # لیست نسخه‌های پشتیبان
            self.backups_list_frame = ctk.CTkScrollableFrame(
                self.backups_section, width=700, height=200
            )
            self.backups_list_frame.grid(row=1, column=0, padx=5, pady=2, sticky="e")

            # دکمه بازخوانی لیست
            self.refresh_backups_button = ctk.CTkButton(
                self.backups_section, text="بازخوانی لیست",
                font=("Arial", 12),
                fg_color="#6c757d", hover_color="#5a6268",
                width=150, height=30,
                command=self.load_backup_files
            )
            self.refresh_backups_button.grid(row=2, column=0, padx=5, pady=2, sticky="e")

            # بارگذاری لیست نسخه‌های پشتیبان
            self.load_backup_files()

    def load_backup_files(self):
        """بارگذاری لیست فایل‌های پشتیبان"""
        import os

        # پاک کردن لیست فعلی
        for widget in self.backups_list_frame.winfo_children():
            widget.destroy()

        # بررسی وجود پوشه پشتیبان
        backup_dir = "backups"
        if not os.path.exists(backup_dir):
            os.makedirs(backup_dir)

        # دریافت لیست فایل‌های پشتیبان
        backup_files = []
        for file in os.listdir(backup_dir):
            if file.startswith("backup_") and file.endswith(".db"):
                backup_files.append(file)

        if not backup_files:
            no_backups_label = ctk.CTkLabel(
                self.backups_list_frame,
                text="هیچ نسخه پشتیبانی یافت نشد",
                font=("Arial", 12)
            )
            no_backups_label.grid(row=0, column=0, padx=20, pady=20)
            return

        # مرتب‌سازی فایل‌ها بر اساس تاریخ (جدیدترین اول)
        backup_files.sort(reverse=True)

        # نمایش فایل‌ها در لیست
        for i, file in enumerate(backup_files):
            # استخراج تاریخ و زمان از نام فایل
            try:
                date_part = file.split("_")[1]
                time_part = file.split("_")[2]
                date_str = f"{date_part[:4]}/{date_part[4:6]}/{date_part[6:8]}"
                time_str = f"{time_part[:2]}:{time_part[2:4]}:{time_part[4:6]}"
                datetime_str = f"{date_str} {time_str}"
            except:
                datetime_str = file

            row_frame = ctk.CTkFrame(self.backups_list_frame)
            row_frame.grid(row=i, column=0, padx=5, pady=5, sticky="ew")

            # رنگ پس‌زمینه متناوب برای سطرها
            bg_color = "#f8f9fa" if i % 2 == 0 else "#e9ecef"
            row_frame.configure(fg_color=bg_color)

            file_label = ctk.CTkLabel(
                row_frame, text=f"{i+1}. {datetime_str}",
                font=("Arial", 12)
            )
            file_label.grid(row=0, column=0, padx=10, pady=5, sticky="w")

            restore_button = ctk.CTkButton(
                row_frame, text="بازیابی",
                font=("Arial", 12),
                fg_color="#dc3545", hover_color="#c82333",
                width=100,
                command=lambda f=file: self.restore_backup(os.path.join(backup_dir, f))
            )
            restore_button.grid(row=0, column=1, padx=10, pady=5, sticky="e")

    def create_backup(self):
        """تهیه نسخه پشتیبان از پایگاه داده"""
        backup_path = self.db.backup_database()

        if backup_path:
            self.show_success(f"نسخه پشتیبان با موفقیت در مسیر {backup_path} ذخیره شد")
            # بازخوانی لیست نسخه‌های پشتیبان
            self.load_backup_files()
        else:
            self.show_error("خطا در تهیه نسخه پشتیبان")

    def select_backup_file(self):
        """انتخاب فایل پشتیبان برای بازیابی"""
        from tkinter import filedialog
        import os

        # تعیین پوشه پیش‌فرض
        backup_dir = os.path.join(os.getcwd(), "backups")
        if not os.path.exists(backup_dir):
            os.makedirs(backup_dir)

        # انتخاب فایل
        file_path = filedialog.askopenfilename(
            title="انتخاب فایل پشتیبان",
            initialdir=backup_dir,
            filetypes=[("Database files", "*.db"), ("All files", "*.*")]
        )

        if file_path:
            # پرسش از کاربر برای تأیید بازیابی
            self.confirm_restore(file_path)

    def confirm_restore(self, backup_path):
        """تأیید بازیابی از فایل پشتیبان"""
        # ایجاد پنجره تأیید
        confirm_window = ctk.CTkToplevel(self)
        confirm_window.title("تأیید بازیابی")
        confirm_window.geometry("400x250")
        confirm_window.resizable(False, False)
        confirm_window.grab_set()  # مدال کردن پنجره

        # پیام هشدار
        warning_label = ctk.CTkLabel(
            confirm_window,
            text="هشدار!",
            font=("Arial", 18, "bold"),
            text_color="#dc3545"
        )
        warning_label.pack(pady=(20, 10))

        message_label = ctk.CTkLabel(
            confirm_window,
            text="با بازیابی اطلاعات از نسخه پشتیبان، تمام اطلاعات فعلی جایگزین خواهند شد. "
                 "این عملیات غیرقابل بازگشت است. آیا مطمئن هستید؟",
            font=("Arial", 12),
            wraplength=350,
            justify="center"
        )
        message_label.pack(padx=20, pady=10)

        # دکمه‌های تأیید و انصراف
        buttons_frame = ctk.CTkFrame(confirm_window, fg_color="transparent")
        buttons_frame.pack(pady=20)

        cancel_button = ctk.CTkButton(
            buttons_frame, text="انصراف",
            font=("Arial", 12),
            fg_color="#6c757d", hover_color="#5a6268",
            width=120,
            command=confirm_window.destroy
        )
        cancel_button.grid(row=0, column=0, padx=10)

        confirm_button = ctk.CTkButton(
            buttons_frame, text="بازیابی",
            font=("Arial", 12, "bold"),
            fg_color="#dc3545", hover_color="#c82333",
            width=120,
            command=lambda: self.restore_backup(backup_path, confirm_window)
        )
        confirm_button.grid(row=0, column=1, padx=10)

    def restore_backup(self, backup_path, confirm_window=None):
        """بازیابی اطلاعات از فایل پشتیبان"""
        # بستن پنجره تأیید اگر باز است
        if confirm_window:
            confirm_window.destroy()

        # بازیابی اطلاعات
        success = self.db.restore_database(backup_path)

        if success:
            self.show_success("اطلاعات با موفقیت بازیابی شدند")
            # بازخوانی اطلاعات در صفحات مختلف
            if hasattr(self, "products_frame"):
                self.load_products()
            if hasattr(self, "customers_frame"):
                self.load_customers()
            if hasattr(self, "sales_frame"):
                self.load_sales()
            if hasattr(self, "inventory_frame"):
                self.load_inventory()
        else:
            self.show_error("خطا در بازیابی اطلاعات")

    def create_users_frame(self):
        """ایجاد صفحه مدیریت کاربران"""
        if not hasattr(self, "users_frame"):
            self.users_frame = ctk.CTkFrame(self, corner_radius=0, fg_color="transparent")
            self.users_frame.grid_columnconfigure(0, weight=1)

            # عنوان صفحه
            self.users_title = ctk.CTkLabel(
                self.users_frame, text="مدیریت کاربران و دسترسی‌ها",
                font=("Arial", 20, "bold")
            )
            self.users_title.grid(row=0, column=0, padx=5, pady=5)

            # فریم اصلی
            self.users_main_frame = ctk.CTkFrame(self.users_frame)
            self.users_main_frame.grid(row=1, column=0, padx=5, pady=5, sticky="nsew")
            self.users_main_frame.grid_columnconfigure(0, weight=1)
            self.users_main_frame.grid_columnconfigure(1, weight=3)

            # فریم سمت راست برای افزودن/ویرایش کاربر
            self.user_form_frame = ctk.CTkFrame(self.users_main_frame)
            self.user_form_frame.grid(row=0, column=1, padx=2, pady=2, sticky="nsew")

            # عنوان فرم
            self.user_form_title = ctk.CTkLabel(
                self.user_form_frame, text="افزودن کاربر جدید",
                font=("Arial", 16, "bold")
            )
            self.user_form_title.grid(row=0, column=0, padx=10, pady=10, sticky="e")

            # فیلدهای فرم
            # نام کاربری
            self.username_label = ctk.CTkLabel(
                self.user_form_frame, text="نام کاربری:",
                font=("Arial", 12, "bold")
            )
            self.username_label.grid(row=1, column=0, padx=10, pady=5, sticky="w")

            self.username_entry = ctk.CTkEntry(
                self.user_form_frame, width=300,
                font=("Arial", 12)
            )
            self.username_entry.grid(row=2, column=0, padx=10, pady=5, sticky="w")

            # رمز عبور
            self.password_label = ctk.CTkLabel(
                self.user_form_frame, text="رمز عبور:",
                font=("Arial", 12, "bold")
            )
            self.password_label.grid(row=3, column=0, padx=10, pady=5, sticky="w")

            self.password_entry = ctk.CTkEntry(
                self.user_form_frame, width=300,
                font=("Arial", 12),
                show="•"
            )
            self.password_entry.grid(row=4, column=0, padx=10, pady=5, sticky="w")

            # نام کامل
            self.fullname_label = ctk.CTkLabel(
                self.user_form_frame, text="نام کامل:",
                font=("Arial", 12, "bold")
            )
            self.fullname_label.grid(row=5, column=0, padx=10, pady=5, sticky="w")

            self.fullname_entry = ctk.CTkEntry(
                self.user_form_frame, width=300,
                font=("Arial", 12)
            )
            self.fullname_entry.grid(row=6, column=0, padx=10, pady=5, sticky="w")

            # ایمیل
            self.email_label = ctk.CTkLabel(
                self.user_form_frame, text="ایمیل:",
                font=("Arial", 12, "bold")
            )
            self.email_label.grid(row=7, column=0, padx=10, pady=5, sticky="w")

            self.email_entry = ctk.CTkEntry(
                self.user_form_frame, width=300,
                font=("Arial", 12)
            )
            self.email_entry.grid(row=8, column=0, padx=10, pady=5, sticky="w")

            # تلفن
            self.phone_label = ctk.CTkLabel(
                self.user_form_frame, text="تلفن:",
                font=("Arial", 12, "bold")
            )
            self.phone_label.grid(row=9, column=0, padx=10, pady=5, sticky="w")

            self.phone_entry = ctk.CTkEntry(
                self.user_form_frame, width=300,
                font=("Arial", 12)
            )
            self.phone_entry.grid(row=10, column=0, padx=10, pady=5, sticky="w")

            # نقش
            self.role_label = ctk.CTkLabel(
                self.user_form_frame, text="نقش:",
                font=("Arial", 12, "bold")
            )
            self.role_label.grid(row=11, column=0, padx=10, pady=5, sticky="w")

            self.role_combobox = ctk.CTkComboBox(
                self.user_form_frame, width=300,
                font=("Arial", 12)
            )
            self.role_combobox.grid(row=12, column=0, padx=10, pady=5, sticky="w")

            # وضعیت
            self.status_label = ctk.CTkLabel(
                self.user_form_frame, text="وضعیت:",
                font=("Arial", 12, "bold")
            )
            self.status_label.grid(row=13, column=0, padx=10, pady=5, sticky="w")

            self.status_var = ctk.StringVar(value="فعال")
            self.status_switch = ctk.CTkSwitch(
                self.user_form_frame, text="فعال",
                font=("Arial", 12),
                variable=self.status_var, onvalue="فعال", offvalue="غیرفعال",
                command=self.toggle_status
            )
            self.status_switch.grid(row=14, column=0, padx=10, pady=5, sticky="w")

            # دکمه‌های عملیات
            self.user_buttons_frame = ctk.CTkFrame(self.user_form_frame, fg_color="transparent")
            self.user_buttons_frame.grid(row=15, column=0, padx=10, pady=20, sticky="ew")

            self.save_user_button = ctk.CTkButton(
                self.user_buttons_frame, text="ذخیره",
                font=("Arial", 14, "bold"),
                fg_color="#28a745", hover_color="#218838",
                width=150, height=40,
                command=self.save_user
            )
            self.save_user_button.grid(row=0, column=0, padx=10, pady=10)

            self.clear_user_button = ctk.CTkButton(
                self.user_buttons_frame, text="پاک کردن",
                font=("Arial", 14),
                fg_color="#6c757d", hover_color="#5a6268",
                width=150, height=40,
                command=self.clear_user_form
            )
            self.clear_user_button.grid(row=0, column=1, padx=10, pady=10)

            # فریم سمت چپ برای نمایش لیست کاربران
            self.users_list_frame = ctk.CTkFrame(self.users_main_frame)
            self.users_list_frame.grid(row=0, column=0, padx=10, pady=10, sticky="nsew")

            # عنوان لیست
            self.users_list_title = ctk.CTkLabel(
                self.users_list_frame, text="لیست کاربران",
                font=("Arial", 16, "bold")
            )
            self.users_list_title.grid(row=0, column=0, padx=10, pady=10)

            # لیست کاربران
            self.users_table_frame = ctk.CTkScrollableFrame(
                self.users_list_frame, width=400, height=500
            )
            self.users_table_frame.grid(row=1, column=0, padx=10, pady=10, sticky="nsew")

            # بارگذاری نقش‌ها
            self.load_roles()

            # بارگذاری کاربران
            self.load_users()

            # متغیر برای نگهداری شناسه کاربر در حال ویرایش
            self.current_user_id = None

    def toggle_status(self):
        """تغییر وضعیت کاربر (فعال/غیرفعال)"""
        if self.status_var.get() == "فعال":
            self.status_switch.configure(text="فعال")
        else:
            self.status_switch.configure(text="غیرفعال")

    def load_roles(self):
        """بارگذاری نقش‌ها در کومبوباکس"""
        roles = self.db.get_all_roles()
        role_names = [role["name"] for role in roles]

        self.role_combobox.configure(values=role_names)
        if role_names:
            self.role_combobox.set(role_names[0])

    def load_users(self):
        """بارگذاری کاربران در جدول"""
        # پاک کردن جدول فعلی
        for widget in self.users_table_frame.winfo_children():
            widget.destroy()

        # دریافت همه کاربران
        users = self.db.get_all_users()

        if not users:
            no_data_label = ctk.CTkLabel(
                self.users_table_frame,
                text="هیچ کاربری یافت نشد",
                font=("Arial", 12)
            )
            no_data_label.grid(row=0, column=0, padx=10, pady=20)
            return

        # نمایش کاربران در جدول
        for i, user in enumerate(users):
            row_frame = ctk.CTkFrame(self.users_table_frame)
            row_frame.grid(row=i, column=0, sticky="ew", padx=5, pady=2)

            # رنگ پس‌زمینه متناوب برای سطرها
            bg_color = "#f8f9fa" if i % 2 == 0 else "#e9ecef"
            row_frame.configure(fg_color=bg_color)

            # نام کاربری و نام کامل
            user_info = ctk.CTkLabel(
                row_frame,
                text=f"{user['username']} ({user['full_name']})",
                font=("Arial", 12, "bold")
            )
            user_info.grid(row=0, column=0, padx=10, pady=5, sticky="w")

            # نقش
            role_label = ctk.CTkLabel(
                row_frame,
                text=f"نقش: {user['role_name']}",
                font=("Arial", 10)
            )
            role_label.grid(row=1, column=0, padx=10, pady=2, sticky="w")

            # وضعیت
            status_text = "فعال" if user["is_active"] else "غیرفعال"
            status_color = "#28a745" if user["is_active"] else "#dc3545"

            status_label = ctk.CTkLabel(
                row_frame,
                text=f"وضعیت: {status_text}",
                font=("Arial", 10),
                text_color=status_color
            )
            status_label.grid(row=1, column=1, padx=10, pady=2, sticky="w")

            # دکمه‌های عملیات
            edit_button = ctk.CTkButton(
                row_frame,
                text="ویرایش",
                font=("Arial", 11),
                fg_color="#007bff",
                hover_color="#0069d9",
                width=80,
                height=25,
                command=lambda u=user: self.select_user(u)
            )
            edit_button.grid(row=0, column=1, padx=5, pady=5, sticky="e")

            # اضافه کردن رویداد کلیک برای انتخاب کاربر
            row_frame.bind("<Button-1>", lambda event, u=user: self.select_user(u))
            for widget in row_frame.winfo_children():
                if widget != edit_button:  # به جز دکمه ویرایش
                    widget.bind("<Button-1>", lambda event, u=user: self.select_user(u))

    def select_user(self, user):
        """انتخاب کاربر برای ویرایش"""
        # بررسی دسترسی
        if not self.check_permission("edit_user"):
            self.show_permission_error()
            return

        self.current_user_id = user["id"]

        # پر کردن فرم با اطلاعات کاربر
        self.username_entry.delete(0, "end")
        self.username_entry.insert(0, user["username"])
        self.username_entry.configure(state="disabled")  # نام کاربری قابل ویرایش نیست

        self.password_entry.delete(0, "end")
        self.password_entry.configure(placeholder_text="برای تغییر رمز عبور، رمز جدید را وارد کنید")

        self.fullname_entry.delete(0, "end")
        self.fullname_entry.insert(0, user["full_name"])

        self.email_entry.delete(0, "end")
        if user["email"]:
            self.email_entry.insert(0, user["email"])

        self.phone_entry.delete(0, "end")
        if user["phone"]:
            self.phone_entry.insert(0, user["phone"])

        # تنظیم نقش
        if user["role_name"]:
            self.role_combobox.set(user["role_name"])

        # تنظیم وضعیت
        status_value = "فعال" if user["is_active"] else "غیرفعال"
        self.status_var.set(status_value)
        self.status_switch.configure(text=status_value)

        # تغییر عنوان فرم
        self.user_form_title.configure(text=f"ویرایش کاربر: {user['username']}")

    def clear_user_form(self):
        """پاک کردن فرم کاربر"""
        self.current_user_id = None

        self.username_entry.configure(state="normal")
        self.username_entry.delete(0, "end")

        self.password_entry.delete(0, "end")
        self.password_entry.configure(placeholder_text="")

        self.fullname_entry.delete(0, "end")
        self.email_entry.delete(0, "end")
        self.phone_entry.delete(0, "end")

        # تنظیم نقش
        roles = self.role_combobox.cget("values")
        if roles:
            self.role_combobox.set(roles[0])

        # تنظیم وضعیت
        self.status_var.set("فعال")
        self.status_switch.configure(text="فعال")

        # تغییر عنوان فرم
        self.user_form_title.configure(text="افزودن کاربر جدید")

    def save_user(self):
        """ذخیره کاربر (افزودن یا ویرایش)"""
        # بررسی دسترسی
        if self.current_user_id and not self.check_permission("edit_user"):
            self.show_permission_error()
            return
        elif not self.current_user_id and not self.check_permission("add_user"):
            self.show_permission_error()
            return

        # دریافت مقادیر از فرم
        username = self.username_entry.get().strip()
        password = self.password_entry.get()
        full_name = self.fullname_entry.get().strip()
        email = self.email_entry.get().strip()
        phone = self.phone_entry.get().strip()
        role_name = self.role_combobox.get()
        is_active = 1 if self.status_var.get() == "فعال" else 0

        # بررسی مقادیر اجباری
        if not username or not full_name or not role_name:
            self.show_error("نام کاربری، نام کامل و نقش اجباری هستند")
            return

        # دریافت شناسه نقش
        role_id = None
        roles = self.db.get_all_roles()
        for role in roles:
            if role["name"] == role_name:
                role_id = role["id"]
                break

        if not role_id:
            self.show_error("نقش انتخاب شده معتبر نیست")
            return

        if self.current_user_id:
            # ویرایش کاربر موجود
            success = self.db.update_user(
                self.current_user_id, full_name, role_id, email, phone, is_active
            )

            # تغییر رمز عبور اگر وارد شده باشد
            if password:
                self.db.change_password(self.current_user_id, password)

            if success:
                self.show_success(f"کاربر {username} با موفقیت به‌روزرسانی شد")

                # ثبت فعالیت
                self.db.log_activity(
                    self.current_user["id"],
                    "edit_user",
                    f"ویرایش کاربر: {username}"
                )
        else:
            # افزودن کاربر جدید
            if not password:
                self.show_error("رمز عبور برای کاربر جدید اجباری است")
                return

            user_id = self.db.add_user(
                username, password, full_name, role_id, email, phone
            )

            if user_id:
                self.show_success(f"کاربر {username} با موفقیت اضافه شد")

                # ثبت فعالیت
                self.db.log_activity(
                    self.current_user["id"],
                    "add_user",
                    f"افزودن کاربر جدید: {username}"
                )

        # پاک کردن فرم و بارگذاری مجدد کاربران
        self.clear_user_form()
        self.load_users()

if __name__ == "__main__":
    app = JewelryStoreApp()
    app.mainloop()