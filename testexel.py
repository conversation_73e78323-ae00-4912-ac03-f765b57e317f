import tkinter as tk  
from tkinter import filedialog, messagebox, ttk  
import pandas as pd  

def load_files():  
    global file_paths  
    file_paths = filedialog.askopenfilenames(filetypes=[("Excel files", "*.xlsx;*.xls")])  
    file1_label.config(text="\n".join(file_paths))  # نمایش مسیرهای انتخاب شده  

def process_files():  
    try:  
        all_data = []  # لیست برای نگهداری DataFrame های هر فایل  

        for file_path in file_paths:  
            df = pd.read_excel(file_path)  

            if 'شماره پذیرنده' not in df.columns:  
                raise ValueError(f"ستون 'شماره پذیرنده' در فایل {file_path} موجود نیست.")  

            df_unique = df.drop_duplicates(subset='شماره پذیرنده')  
            all_data.append(df_unique)  # اضافه کردن DataFrame به لیست  

        # ترکیب تمام DataFrame ها در یک DataFrame واحد  
        combined_df = pd.concat(all_data, ignore_index=True)  

        # ادغام داده‌ها برای محاسبه تعداد تکرار  
        merged_df = combined_df.drop_duplicates(subset='شماره پذیرنده')  

        # محاسبه تعداد تکرار هر شماره پذیرنده و نام شرکت  
        result = (  
            merged_df.groupby('شماره پذیرنده')  
            .agg({'شرکت پذیرنده': 'first', 'شماره پذیرنده': 'count'})  # فرض بر این است که نام شرکت در یکی از ستون‌ها وجود دارد  
            .rename(columns={'شماره پذیرنده': 'تکرار'})  
            .reset_index()  
        )  

        result = result.sort_values(by='تکرار', ascending=False)  

        # پاک کردن داده‌های قبلی در Treeview  
        for row in treeview.get_children():  
            treeview.delete(row)  

        # اضافه کردن داده‌های جدید به Treeview  
        for index, row in result.iterrows():  
            treeview.insert("", "end", values=(row['شماره پذیرنده'], row['شرکت پذیرنده'], row['تکرار']))  

    except Exception as e:  
        messagebox.showerror("Error", str(e))  

app = tk.Tk()  
app.title("Excel File Loader and Processor")  

file_paths = []  # لیست برای ذخیره مسیرهای فایل  

load_files_button = tk.Button(app, text="Load Excel Files", command=load_files)  
load_files_button.pack(pady=10)  

file1_label = tk.Label(app, text="No files selected")  
file1_label.pack(pady=10)  

process_button = tk.Button(app, text="Process Files", command=process_files)  
process_button.pack(pady=20)  

# ایجاد یک Treeview برای نمایش خروجی  
columns = ('شماره پذیرنده', 'شرکت پذیرنده', 'تکرار')  
treeview = ttk.Treeview(app, columns=columns, show='headings')  
treeview.pack(pady=10)  

# تعریف سرستون‌ها  
for col in columns:  
    treeview.heading(col, text=col)  

app.mainloop()  