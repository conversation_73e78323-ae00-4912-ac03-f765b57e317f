import tkinter as tk  
import tkinter.ttk as ttk  
import ttkbootstrap as tb  
import mysql.connector  
from mysql.connector import Error  
import re  
from tkinter import messagebox  
from datetime import datetime 


# اطلاعات اتصال به پایگاه داده (جایگزین کنید)  
db_config = {  
    'user': 'root',  
    'password': '123456',  
    'host': 'localhost',  
    'database': 'school_management'  
} 


#*********************************************

def validate_name(name):  
    """  
    اعتبارسنجی نام و نام خانوادگی.  
    """  
    if not name:  
        return False, "نام نمی تواند خالی باشد."  
    if not re.match("^[a-zA-Z\u0600-\u06FF]+$", name):  # تطبیق با حروف فارسی و انگلیسی  
        return False, "نام باید فقط شامل حروف باشد."  
    if len(name) < 2:  
        return False, "نام باید حداقل 2 حرف داشته باشد."  
    return True, ""  

def validate_phone_number(phone):  
    """  
    اعتبارسنجی شماره تلفن.  
    """  
    if not phone:  
        return False, "شماره تلفن نمی تواند خالی باشد."  
    if not re.match("^09\d{9}$", phone):  
        return False, "شماره تلفن باید 11 رقم باشد و با 09 شروع شود."  
    return True, ""  

def connect_to_database():  
    """  
    اتصال به پایگاه داده MySQL.  
    """  
    try:  
        connection = mysql.connector.connect(**db_config)  
        print("Connected to database successfully!")  
        return connection  
    except Error as err:  
        print(f"Error connecting to database: {err}")  
        return None  

def add_student():  
    """  
    تابعی برای اضافه کردن دانش آموز به پایگاه داده.  
    """  
    first_name = first_name_entry.get()  
    last_name = last_name_entry.get()  
    birth_date_str = birth_date_entry.get()  
    gender = gender_var.get()  
    address = address_entry.get("1.0", tk.END)  
    phone_number = phone_number_entry.get()  

    # اعتبارسنجی نام  
    is_valid, message = validate_name(first_name)  
    if not is_valid:  
        messagebox.showerror("Error", message)  
        return  

    # اعتبارسنجی نام خانوادگی  
    is_valid, message = validate_name(last_name)  
    if not is_valid:  
        messagebox.showerror("Error", message)  
        return  

    # اعتبارسنجی شماره تلفن  
    is_valid, message = validate_phone_number(phone_number)  
    if not is_valid:  
        messagebox.showerror("Error", message)  
        return  

    try:  
        connection = connect_to_database()  
        if connection:  
            cursor = connection.cursor()  

            # تبدیل رشته تاریخ به فرمت مناسب  
            try:  
                birth_date = datetime.strptime(birth_date_str, "%Y-%m-%d").date()  
                birth_date = birth_date.strftime("%Y-%m-%d")  
            except ValueError:  
                messagebox.showerror("Error", "فرمت تاریخ نامعتبر است. لطفا از فرمت YYYY-MM-DD استفاده کنید.")  
                return  

            query = "INSERT INTO students (first_name, last_name, date_of_birth, gender, address, phone_number) VALUES (%s, %s, %s, %s, %s, %s)"  
            values = (first_name, last_name, birth_date, gender, address, phone_number)  
            cursor.execute(query, values)  
            connection.commit()  
            print("Student added successfully!")  
            messagebox.showinfo("Success", "Student added successfully!")  
            connection.close()  

            # پاک کردن فرم بعد از ثبت  
            first_name_entry.delete(0, tk.END)  
            last_name_entry.delete(0, tk.END)  
            birth_date_entry.delete(0, tk.END)  
            address_entry.delete("1.0", tk.END)  
            phone_number_entry.delete(0, tk.END)  

            # بروزرسانی Treeview  
            update_student_list()  

    except Error as err:  
        print(f"Error adding student: {err}")  
        messagebox.showerror("Error", f"Error adding student: {err}")  

def fetch_students():  
    """  
    واکشی اطلاعات دانش آموزان از پایگاه داده.  
    """  
    try:  
        connection = connect_to_database()  
        if connection:  
            cursor = connection.cursor()  
            query = "SELECT * FROM students"  
            cursor.execute(query)  
            students = cursor.fetchall()  
            connection.close()  
            return students  
        else:  
            return []  
    except Error as e:  
        print(f"Error fetching students: {e}")  
        messagebox.showerror("Error", f"Error fetching students: {e}")  
        return []  

def update_student_list():  
    """  
    بروزرسانی لیست دانش آموزان در Treeview.  
    """  
    # پاک کردن اطلاعات قبلی Treeview  
    for item in students_tree.get_children():  
        students_tree.delete(item)  

    students = fetch_students()  
    for student in students:  
        students_tree.insert("", tk.END, values=(student[0], student[1], student[2], student[3], student[4], student[5], student[6]))  

def select_student(event):  
    """  
    نمایش اطلاعات دانش آموز انتخاب شده در فرم.  
    """  
    selected_item = students_tree.selection()[0]  
    student = students_tree.item(selected_item)['values']  

    # پر کردن فرم با اطلاعات دانش آموز انتخاب شده  
    first_name_entry.delete(0, tk.END)  
    first_name_entry.insert(0, student[1])  

    last_name_entry.delete(0, tk.END)  
    last_name_entry.insert(0, student[2])  

    birth_date_entry.delete(0, tk.END)  
    birth_date_entry.insert(0, student[3])  

    gender_var.set(student[4])  

    address_entry.delete("1.0", tk.END)  
    address_entry.insert("1.0", student[5])  

    phone_number_entry.delete(0, tk.END)  
    phone_number_entry.insert(0, student[6])  

def update_student():  
    """  
    به روز رسانی اطلاعات دانش آموز در پایگاه داده.  
    """  
    selected_item = students_tree.selection()[0]  
    student_id = students_tree.item(selected_item)['values'][0]  

    first_name = first_name_entry.get()  
    last_name = last_name_entry.get()  
    birth_date_str = birth_date_entry.get()  
    gender = gender_var.get()  
    address = address_entry.get("1.0", tk.END)  
    phone_number = phone_number_entry.get()  

    # اعتبارسنجی نام  
    is_valid, message = validate_name(first_name)  
    if not is_valid:  
        messagebox.showerror("Error", message)  
        return  

    # اعتبارسنجی نام خانوادگی  
    is_valid, message = validate_name(last_name)  
    if not is_valid:  
        messagebox.showerror("Error", message)  
        return  

    # اعتبارسنجی شماره تلفن  
    is_valid, message = validate_phone_number(phone_number)  
    if not is_valid:  
        messagebox.showerror("Error", message)  
        return  

    try:  
        connection = connect_to_database()  
        if connection:  
            cursor = connection.cursor()  

            # تبدیل رشته تاریخ به فرمت مناسب  
            try:  
                birth_date = datetime.strptime(birth_date_str, "%Y-%m-%d").date()  
                birth_date = birth_date.strftime("%Y-%m-%d")  
            except ValueError:  
                messagebox.showerror("Error", "فرمت تاریخ نامعتبر است. لطفا از فرمت YYYY-MM-DD استفاده کنید.")  
                return  

            query = "UPDATE students SET first_name = %s, last_name = %s, date_of_birth = %s, gender = %s, address = %s, phone_number = %s WHERE student_id = %s"  
            values = (first_name, last_name, birth_date, gender, address, phone_number, student_id)  
            cursor.execute(query, values)  
            connection.commit()  
            print("Student updated successfully!")  
            messagebox.showinfo("Success", "Student updated successfully!")  
            connection.close()  

            # بروزرسانی Treeview  
            update_student_list()  

    except Error as err:  
        print(f"Error updating student: {err}")  
        messagebox.showerror("Error", f"Error updating student: {err}")  

def delete_student():  
    """  
    حذف دانش آموز از پایگاه داده.  
    """  
    selected_item = students_tree.selection()[0]  
    student_id = students_tree.item(selected_item)['values'][0]  

    if messagebox.askyesno("Confirm", "آیا مطمئن هستید که می خواهید این دانش آموز را حذف کنید؟"):  
        try:  
            connection = connect_to_database()  
            if connection:  
                cursor = connection.cursor()  
                query = "DELETE FROM students WHERE student_id = %s"  
                values = (student_id,)  
                cursor.execute(query, values)  
                connection.commit()  
                print("Student deleted successfully!")  
                messagebox.showinfo("Success", "Student deleted successfully!")  
                connection.close()  

                # بروزرسانی Treeview  
                update_student_list()  

                # پاک کردن فرم  
                first_name_entry.delete(0, tk.END)  
                last_name_entry.delete(0, tk.END)  
                birth_date_entry.delete(0, tk.END)  
                address_entry.delete("1.0", tk.END)  
                phone_number_entry.delete(0, tk.END)  

        except Error as err:  
            print(f"Error deleting student: {err}")  
            messagebox.showerror("Error", f"Error deleting student: {err}")  

#**********************************************



def open_students_form(): 
   


    """باز کردن فرم دانش آموزان."""  
    students_window = tk.Toplevel(root)  
    students_window.geometry('400x700')

    # ایجاد یک فریم برای محتوای اصلی برنامه (در سمت چپ)  
    students_frame = tb.Frame(students_window)  
    students_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True) 

    #  اضافه کردن محتوای اصلی برنامه به main_content_frame  
    #main_label = tb.Label(students_frame, text="محتوای اصلی برنامه در اینجا قرار می گیرد")  
    #main_label.pack(padx=20, pady=20) 


    students_label = tb.Label(students_frame, text="فرم مدیریت دانش آموزان")  
    students_label.pack(padx=5, pady=5) 
    # --- فرم ثبت دانش آموز ---  
    #student_form_frame = tk.Frame(root)  
    #student_form_frame.pack(fill="x")  


    # برچسب و ورودی نام  
    first_name_label = tb.Label(students_frame, text="First Name:")  
    first_name_label.pack(padx=5, pady=5)  
    first_name_entry = tb.Entry(students_frame)  
    first_name_entry.pack(padx=5, pady=5)  

    # برچسب و ورودی نام خانوادگی  
    last_name_label = tb.Label(students_frame, text="Last Name:")  
    last_name_label.pack(padx=5, pady=5)  
    last_name_entry = tb.Entry(students_frame)  
    last_name_entry.pack(padx=5, pady=5)  

    # برچسب و ورودی تاریخ تولد  
    birth_date_label = tb.Label(students_frame, text="Birth Date (YYYY-MM-DD):")  
    birth_date_label.pack(padx=5, pady=5)   
    birth_date_entry = tb.Entry(students_frame)  
    birth_date_entry.pack(padx=5, pady=5) 

    # برچسب و انتخاب جنسیت  
    gender_label = tb.Label(students_frame, text="Gender:")  
    gender_label.pack(padx=5, pady=5)   
    gender_var = tb.StringVar(value="Male")  
    male_radio = tb.Radiobutton(students_frame, text="Male", variable=gender_var, value="Male")  
    male_radio.pack(padx=5, pady=5)    
    female_radio = tb.Radiobutton(students_frame, text="Female", variable=gender_var, value="Female")  
    female_radio.pack(padx=5, pady=5)    

    # برچسب و ورودی آدرس  
    address_label = tb.Label(students_frame, text="Address:")  
    address_label.pack(padx=5, pady=5)  
    address_entry = tb.Text(students_frame, height=3, width=25)  
    address_entry.pack(padx=5, pady=5)    

    # برچسب و ورودی شماره تلفن  
    phone_number_label = tb.Label(students_frame, text="Phone Number:")  
    phone_number_label.pack(padx=5, pady=5)   
    phone_number_entry = tb.Entry(students_frame)  
    phone_number_entry.pack(padx=5, pady=5)  
    # دکمه ها  
    add_button = tb.Button(students_frame, text="Add Student", command=add_student)  
    add_button.pack(padx=5, pady=5,side='top')   
    update_button = tb.Button(students_frame, text="Update Student", command=update_student)  
    update_button.pack(padx=5, pady=5,side='left')
    delete_button = tb.Button(students_frame, text="Delete Student", command=delete_student)  
    delete_button.pack(padx=5, pady=5,side='right')    

    # --- Treeview برای نمایش لیست دانش آموزان ---  
    columns = ("ID", "First Name", "Last Name", "Birth Date", "Gender", "Address", "Phone Number")  
    students_tree = tb.Treeview(students_frame)  

    for col in columns:  
        students_tree.heading(col, text=col)  
        students_tree.column(col, width=120, anchor="center")  # تنظیم عرض ستون ها  

    students_tree.pack(fill="both", expand=True, padx=20, pady=10)  

    # اتصال تابع select_student به رویداد انتخاب در Treeview  
    students_tree.bind("<ButtonRelease-1>", select_student)  

    # نمایش لیست اولیه دانش آموزان  
    #update_student_list()
    root.mainloop() 
    #students_window.destroy() 


def open_courses_form():  
    """باز کردن فرم دروس."""  
    courses_window = tb.Toplevel(root, title="Courses Management")  
    courses_label = tb.Label(courses_window, text="فرم مدیریت دروس")  
    courses_label.pack(padx=20, pady=20)  

def open_grades_form():  
    """باز کردن فرم نمرات."""  
    grades_window = tb.Toplevel(root, title="Grades Management")  
    grades_label = tb.Label(grades_window, text="فرم مدیریت نمرات")  
    grades_label.pack(padx=20, pady=20)  





# ***************************************************************
def main(): 
    global root, first_name_entry, last_name_entry, birth_date_entry, address_entry, phone_number_entry, gender_var, students_tree  
    root = tb.Window(themename="darkly")  
    root.title("School Management System")  
    root.geometry("800x600")  

    # ایجاد یک فریم برای نگهداری دکمه های منو در سمت راست  
    right_menu_frame = tb.Frame(root, width=200, height=400)  
    right_menu_frame.pack(side=tk.RIGHT, fill=tk.Y)  
    # ایجاد دکمه ها برای منو  
    students_button = tb.Button(right_menu_frame, text="دانش آموزان", command=open_students_form)  
    students_button.pack(pady=10, padx=5, fill=tk.X)  

    courses_button = tb.Button(right_menu_frame, text="دروس", command=open_courses_form)  
    courses_button.pack(pady=10, padx=5, fill=tk.X)  

    grades_button = tb.Button(right_menu_frame, text="نمرات", command=open_grades_form)  
    grades_button.pack(pady=10, padx=5, fill=tk.X)  

    exit_button = tb.Button(right_menu_frame, text="خروج", command=root.quit)  
    exit_button.pack(pady=10, padx=5, fill=tk.X)  

    # ایجاد یک فریم برای محتوای اصلی برنامه (در سمت چپ)  
    main_content_frame = tb.Frame(root)  
    main_content_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True) 

    #  اضافه کردن محتوای اصلی برنامه به main_content_frame  
    main_label = tb.Label(main_content_frame, text="محتوای اصلی برنامه در اینجا قرار می گیرد")  
    main_label.pack(padx=20, pady=20)  


    root.mainloop()
     

    


if __name__ == "__main__":  
    main()  