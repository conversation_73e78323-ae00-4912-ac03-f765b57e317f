import tkinter as tk  
from tkinter import messagebox, simpledialog  
import mysql.connector  
import bcrypt  

# اطلاعات اتصال به دیتابیس  
mydb = mysql.connector.connect(  
    host="localhost",  
    user="root",  
    password="123456",  
    database="login"  
)  

mycursor = mydb.cursor()  

# تابع برای ایجاد جدول users  
def create_users_table():  
    mycursor.execute("""  
        CREATE TABLE IF NOT EXISTS users (  
            id INT AUTO_INCREMENT PRIMARY KEY,  
            username VARCHAR(255) UNIQUE NOT NULL,  
            password VARCHAR(255) NOT NULL  
        )  
    """)  
    mydb.commit()  
    print("Table 'users' created successfully (if it didn't exist).")  

# تابع برای بررسی وجود نام کاربری  
def check_username(username):  
    sql = "SELECT * FROM users WHERE username = %s"  
    val = (username,)  
    mycursor.execute(sql, val)  
    return mycursor.fetchone()  

# تابع برای ثبت‌نام کاربر جدید  
def register_user(username, password):   
    if not username or not password:  
        messagebox.showerror("خطا", "نام کاربری و رمز عبور نمی‌تواند خالی باشد.")  
        return  

    if check_username(username):  
        messagebox.showerror("خطا", "این نام کاربری قبلاً ثبت‌نام شده است.")  
        return  
    
    hashed_password = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt())  
    sql = "INSERT INTO users (username, password) VALUES (%s, %s)"  
    val = (username, hashed_password)  
    
    try:  
        mycursor.execute(sql, val)  
        mydb.commit()  
        messagebox.showinfo("ثبت نام", f"کاربر '{username}' با موفقیت ثبت‌نام شد.")  
        registration_window.destroy()  # بستن پنجره ثبت‌نام  
    except mysql.connector.Error as err:  
        messagebox.showerror("خطا", f"خطا در ثبت‌نام: {err}")  

# تابع برای نمایش پنجره ثبت‌نام  
def open_registration_window():  
    global registration_window  
    registration_window = tk.Toplevel(root)  
    registration_window.title("Registration Form")  

    # فیلد ورودی برای نام کاربری  
    username_label = tk.Label(registration_window, text="Username:")  
    username_label.grid(row=0, column=0, sticky=tk.W)  
    username_entry = tk.Entry(registration_window)  
    username_entry.grid(row=0, column=1)  

    # فیلد ورودی برای رمز عبور  
    password_label = tk.Label(registration_window, text="Password:")  
    password_label.grid(row=1, column=0, sticky=tk.W)  
    password_entry = tk.Entry(registration_window, show="*")  
    password_entry.grid(row=1, column=1)  

    # دکمه ثبت‌نام  
    register_button = tk.Button(registration_window, text="Register",   
                                 command=lambda: register_user(username_entry.get(), password_entry.get()))  
    register_button.grid(row=2, column=1, sticky=tk.E)  

# تابع برای ورود  
def login():  
    username = username_entry.get()  
    password = password_entry.get()  
    sql = "SELECT password FROM users WHERE username = %s"  
    val = (username,)  
    mycursor.execute(sql, val)  
    result = mycursor.fetchone()  

    if result:  
        hashed_password = result[0]  
        if bcrypt.checkpw(password.encode('utf-8'), hashed_password.encode('utf-8')):  
            messagebox.showinfo("Login Successful", "Welcome!")  
        else:  
            messagebox.showerror("Login Failed", "Incorrect password.")  
    else:  
        messagebox.showerror("Login Failed", "User not found.")  

# تابع برای نمایش کاربران  
def show_users():  
    users_window = tk.Toplevel(root)  
    users_window.title("Manage Users")  
    
    global user_listbox  
    user_listbox = tk.Listbox(users_window, width=50)  
    user_listbox.pack(padx=10, pady=10)  

    mycursor.execute("SELECT * FROM users")  
    users = mycursor.fetchall()  
    for user in users:  
        user_listbox.insert(tk.END, f"Username: {user[1]},     Password: {user[0]} ")     
    
    
    
    delete_button = tk.Button(users_window, text="حذف کاربر", command=delete_user)  
    delete_button.pack(pady=5)  

    edit_button = tk.Button(users_window, text="ویرایش رمز عبور", command=edit_user)  
    edit_button.pack(pady=5)  

# تابع ویرایش و تغییر رمز عبور کاربر  
def edit_user():  
    try:  
        selected_user = user_listbox.get(user_listbox.curselection())  
        new_password = simpledialog.askstring("تغییر رمز عبور", f"رمز عبور جدید برای '{selected_user}' را وارد کنید:")  
        if new_password:  
            hashed_password = bcrypt.hashpw(new_password.encode('utf-8'), bcrypt.gensalt())  
            sql = "UPDATE users SET password = %s WHERE username = %s"  
            val = (hashed_password, selected_user)  
            mycursor.execute(sql, val)  
            mydb.commit()  
            messagebox.showinfo("تغییر رمز عبور", "رمز عبور با موفقیت تغییر یافت.")  
    except tk.TclError:  
        messagebox.showwarning("انتخاب نامعتبر", "لطفا یک کاربر را انتخاب کنید.")  

# تابع حذف کاربر انتخاب شده  
def delete_user():  
    try:  
        selected_user = user_listbox.get(user_listbox.curselection())  
        confirm = messagebox.askyesno("تأیید حذف", f"آیا مطمئن هستید که می‌خواهید '{selected_user}' را حذف کنید؟")  
        if confirm:  
            sql = "DELETE FROM users WHERE username = %s"  
            val = (selected_user,)  
            mycursor.execute(sql, val)  
            mydb.commit()  
            user_listbox.delete(user_listbox.curselection())  
    except tk.TclError:  
        messagebox.showwarning("انتخاب نامعتبر", "لطفا یک کاربر را انتخاب کنید.")  

# تابع برای نمایش پنجره مدیریت کاربران  
def open_user_management_window():  
    show_users()  

# ایجاد پنجره اصلی  
root = tk.Tk()  
root.title("Login Form")  

frame = tk.Frame(root, padx=20, pady=20)  
frame.pack()  

# لیبل و فیلد ورودی برای نام کاربری  
username_label = tk.Label(frame, text="Username:")  
username_label.grid(row=0, column=0, sticky=tk.W)  
username_entry = tk.Entry(frame)  
username_entry.grid(row=0, column=1)  

# لیبل و فیلد ورودی برای رمز عبور  
password_label = tk.Label(frame, text="Password:")  
password_label.grid(row=1, column=0, sticky=tk.W)  
password_entry = tk.Entry(frame, show="*")  
password_entry.grid(row=1, column=1)  

# دکمه ورود  
login_button = tk.Button(frame, text="Login", command=login)  
login_button.grid(row=2, column=0, sticky=tk.E)  

# دکمه ثبت‌نام  
register_button = tk.Button(frame, text="Register", command=open_registration_window)  
register_button.grid(row=2, column=1, sticky=tk.W)  

# دکمه مدیریت کاربران  
manage_users_button = tk.Button(frame, text="Manage Users", command=open_user_management_window)  
manage_users_button.grid(row=3, column=1, sticky=tk.W)  

# ایجاد جدول کاربران در دیتابیس  
create_users_table()  

# اجرای برنامه  
root.mainloop()  